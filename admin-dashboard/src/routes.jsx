import { createBrowserRouter } from "react-router-dom";

import AppLayout from "./AppLayout";
import Dashboard from "./screens/dashboard/Dashboard";
import SignUp from "./screens/signup/SignUp";
import Login from "./screens/login/Login";
import Announcements from "./screens/announcements/Announcements";
import ResultsPage from "./screens/results/Results";
import AssignPlayers from "./screens/assignPlayers/AssignPlayers";
import EditPlayersProfile from "./screens/editPlayers/EditPlayersProfile";
import FeedsQueue from "./screens/feedsQueue/FeedsQueue";
//competition pages
import Main from "./screens/competitions/Main"
import CompetitionPost from "./screens/competitions/CompetitionPost";
import VotingPost from "./screens/competitions/VotingPost";
import WinnerPost from "./screens/competitions/WinnerPost";
import CompetitionStatus from "./screens/competitions/CompetitionStatus.jsx";
import PinnedPost from "./screens/pinnedPost/PinnedPost.jsx";


export const router = createBrowserRouter([
  {
    path: "/",
    element: <Login />,
  },
  {
    path: "/signup",
    element: <SignUp />,
  },
  {
    element: <AppLayout />,
    path: "/admin",
    children: [
      {
        index: true,
        element: <Dashboard />,
      },
      {
        path: "/admin/announcements",
        element: <Announcements />,
      },
      {
        path: "/admin/results",
        element: <ResultsPage />,
      },
      {
        path: "/admin/feeds-queue",
        element: <FeedsQueue />,
      },
      {
        path: "/admin/pinned-post",
        element: <PinnedPost />,
      },
      {
        path: "/admin/assign-players",
        element: <AssignPlayers />,
      },
      {
        path: "/admin/edit-palyers",
        element: <EditPlayersProfile />,
      },
      {
        path: "/admin/competitions/main",
        element: <Main />,
      },
      {
        path: "/admin/competitions/competition-post",
        element: <CompetitionPost />,
      },
      {
        path: "/admin/competitions/voting-post",
        element: <VotingPost />,
      },
      {
        path: "/admin/competitions/winner-post",
        element: <WinnerPost />,
      },
      {
        path: "/admin/competitions/competition-status",
        element: <CompetitionStatus />,
      },
    ],
  },
]);
