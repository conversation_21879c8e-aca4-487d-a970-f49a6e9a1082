import { Navigate, Outlet } from "react-router-dom";
import { useSelector } from "react-redux";

import Layout from "./components/Layout";

const AppLayout = () => {
  const { userInfo } = useSelector((state) => state.auth.authUser);

  if (!userInfo.id) return <Navigate to={"/"} replace />;

  return (
    <>
      <Layout>
        <Outlet />
      </Layout>
    </>
  );
};

export default AppLayout;
