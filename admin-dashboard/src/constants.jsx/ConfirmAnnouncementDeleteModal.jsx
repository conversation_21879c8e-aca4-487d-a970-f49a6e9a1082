import { Box, Button, Modal } from "@mui/material";
import { dispatch } from "../redux/store";
import { useState } from "react";
import PropTypes from "prop-types";
import Spinner from "../components/reusuable/spinner/Spinner";
import { DeleteForever } from "@mui/icons-material";
import { useSelector } from "react-redux";

export function DeleteModal({ data }) {
  const [open, setOpen] = useState(false);

  const deletingAnnouncement = useSelector(
    ({ loading }) => loading.effects.announcements.deleteAnnouncement
  );

  const handleOpen = () => {
    setOpen(true);
  };
  const handleClose = () => {
    setOpen(false);
  };
  const handleDelete = async () => {
    const res = await dispatch.announcements.deleteAnnouncement(data.id);
    if (res === 1) {
      handleClose();
    }
  };

  return (
    <div>
      <Button onClick={handleOpen}>
        <DeleteForever />
      </Button>
      <Modal
        open={open}
        onClose={handleClose}
        aria-labelledby="child-modal-title"
        aria-describedby="child-modal-description"
      >
        <Box className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white py-7 px-7 rounded-md">
          <p className="w-full text-center text-black">
            Are you sure you want to delete this announcement?
          </p>
          <div className="flex justify-center mt-5 gap-5 w-full">
            <button
              className="px-5 py-2 bg-green-400 rounded-md"
              onClick={handleClose}
            >
              No
            </button>
            <button
              className="px-5 py-2 bg-red-500 rounded-md"
              onClick={handleDelete}
            >
              {deletingAnnouncement ? <Spinner /> : "Yes"}
            </button>
          </div>
        </Box>
      </Modal>
    </div>
  );
}

DeleteModal.propTypes = {
  data: PropTypes.object,
};
