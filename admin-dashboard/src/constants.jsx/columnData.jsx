import { useRef, useState } from "react";
import Box from "@mui/material/Box";
import Modal from "@mui/material/Modal";
import Button from "@mui/material/Button";
import { notifyError, readableTimeStamp } from "../utils/helpers";
import DateTimePicker from "react-datetime-picker";
import { dispatch } from "../redux/store";
import { DeleteModal } from "./ConfirmAnnouncementDeleteModal";
import { useSelector } from "react-redux";
import S3BucketManger from "../utils/s3.configUpdated";
import Spinner from "../components/reusuable/spinner/Spinner";
import { Edit } from "@mui/icons-material";

export function EditModal(data) {
  const { data: announcementInfo } = data;
  const [open, setOpen] = useState(false);
  const [startDateTimeValue, setStartDateTimeValue] = useState(
    announcementInfo.startDateTime
  );
  const [endDateTimeValue, setEndDateTimeValue] = useState(
    announcementInfo.endDateTime
  );
  const [title, setTitle] = useState("");
  const [subtitle, setSubtitle] = useState("");
  const [uploadingAsset, setUploadingAsset] = useState(false);
  const [uploadingLogo, setUploadingLogo] = useState(false);

  const assetRef = useRef(null);
  const logoRef = useRef(null);

  const updatingAnnouncement = useSelector(
    ({ loading }) => loading.effects.announcements.updateAnnouncement
  );

  const handleOpen = () => {
    setOpen(true);
  };
  const handleClose = () => {
    setOpen(false);
  };
  const onClickUpdate = async () => {
    const values = {
      title: title || announcementInfo.title,
      subtitle: subtitle || announcementInfo.subtitle,
      startDateTime:
        new Date(startDateTimeValue).getTime() ||
        announcementInfo.startDateTime,
      endDateTime:
        new Date(endDateTimeValue).getTime() || announcementInfo.endDateTime,
      id: announcementInfo.id,
    };
    await dispatch.announcements.updateAnnouncement(values);
  };

  const handleAssetUpload = async (event) => {
    setUploadingAsset(true);

    const s3BucketManger = new S3BucketManger();
    try {
      const s3 = await s3BucketManger.uploadAssets(
        event.target.files[0],
        "player-assets",
        "",
        "photo"
      );
      if (s3) {
        await dispatch.announcements.updateAnnouncement({
          assetUrl: s3.assetUrl,
          id: announcementInfo?.id,
        });
      }
      setUploadingAsset(false);
    } catch (error) {
      console.log("INSPECT ERROR", error);
      setUploadingAsset(false);
      notifyError("Upload Failed");
    }
  };
  const handleLogoUpload = async (event) => {
    setUploadingLogo(true);

    const s3BucketManger = new S3BucketManger();
    try {
      const s3 = await s3BucketManger.uploadAssets(
        event.target.files[0],
        "player-assets",
        "",
        "photo"
      );
      if (s3) {
        await dispatch.announcements.updateAnnouncement({
          logoUrl: s3.assetUrl,
          id: announcementInfo?.id,
        });
      }
      setUploadingLogo(false);
    } catch (error) {
      console.log("INSPECT ERROR", error);
      setUploadingLogo(false);
      notifyError("Upload Failed");
    }
  };

  return (
    <div>
      <Button onClick={handleOpen}>
        <Edit />
      </Button>
      <Modal
        open={open}
        onClose={handleClose}
        aria-labelledby="parent-modal-title"
        aria-describedby="parent-modal-description"
      >
        <Box className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white py-7 px-7 rounded-md">
          <div className="w-[100%] flex flex-col items-center overlow-auto">
            <div className="grid grid-cols-2 gap-x-5 max-w-[630px]">
              <div>
                {announcementInfo.type === "PHOTO" ? (
                  <div className="relative">
                    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                      {uploadingAsset ? <Spinner /> : ""}
                    </div>
                    <img
                      className={`${
                        uploadingAsset ? "opacity-50" : "opacity-100"
                      } w-full h-full object-cover rounded-[32px]`}
                      src={announcementInfo.assetUrl}
                      alt="image"
                    />
                  </div>
                ) : (
                  <div>
                    <a
                      href={announcementInfo.assetUrl}
                      target="_blank"
                      rel="noreferrer"
                    >
                      Open Video
                    </a>
                  </div>
                )}
                <div
                  onClick={() => {
                    assetRef.current.click();
                  }}
                  className="text-blue-600 mt-3 cursor-pointer"
                >
                  Change Image/Video?
                </div>
                <input
                  ref={assetRef}
                  accept="video/*,image/*"
                  type="file"
                  style={{ display: "none" }}
                  onChange={(event) => handleAssetUpload(event)}
                />
              </div>
              <div>
                <div className="relative">
                  <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                    {uploadingLogo ? <Spinner /> : ""}
                  </div>
                  <img
                    className={`${
                      uploadingLogo ? "opacity-50" : "opacity-100"
                    } w-full h-full object-cover rounded-[32px]`}
                    src={announcementInfo.logoUrl}
                    alt="image"
                  />
                </div>
                <div
                  onClick={() => {
                    logoRef.current.click();
                  }}
                  className="text-blue-600 mt-3 cursor-pointer"
                >
                  Change Logo
                </div>
                <input
                  ref={logoRef}
                  accept="video/*,image/*"
                  type="file"
                  style={{ display: "none" }}
                  onChange={(event) => handleLogoUpload(event)}
                />
              </div>
            </div>

            <form className="grid grid-cols-2 mt-5 gap-x-10 w-full">
              <div className="">
                <label className="block text-gray-500 font-bold mb-1 md:mb-0 pr-4">
                  Title
                </label>
                <input
                  className="bg-gray-200 appearance-none border-2 border-gray-200 rounded w-full py-2 px-4 text-gray-700 leading-tight focus:outline-none focus:bg-white focus:border-purple-500"
                  id="title"
                  type="text"
                  value={title || announcementInfo.title}
                  onChange={(e) => setTitle(e.target.value)}
                />
              </div>
              <div className="">
                <label className="block text-gray-500 font-bold mb-1 md:mb-0 pr-4">
                  Subtitle
                </label>
                <textarea
                  className="bg-gray-200 appearance-none border-2 border-gray-200 rounded w-full py-2 px-4 text-gray-700 leading-tight focus:outline-none focus:bg-white focus:border-purple-500"
                  id="subtitle"
                  type="text"
                  value={subtitle || announcementInfo.subtitle}
                  onChange={(e) => setSubtitle(e.target.value)}
                />
              </div>
              <div className="">
                <label className="block text-gray-500 font-bold mb-1 md:mb-0 pr-4">
                  Start Date
                </label>
                <DateTimePicker
                  className="w-full"
                  value={startDateTimeValue}
                  onChange={(newValue) => setStartDateTimeValue(newValue)}
                />
              </div>
              <div className="">
                <label className="block text-gray-500 font-bold mb-1 md:mb-0 pr-4">
                  End Date
                </label>
                <DateTimePicker
                  className="w-full"
                  value={endDateTimeValue}
                  onChange={(newValue) => setEndDateTimeValue(newValue)}
                />
              </div>
            </form>
          </div>
          <div className="flex justify-center mt-5 gap-5 w-full">
            <button
              className="px-5 py-2 bg-green-400 rounded-md"
              onClick={onClickUpdate}
            >
              {updatingAnnouncement ? <Spinner /> : "Update"}
            </button>
          </div>
          {/* <ConfirmAnnouncementDeleteModal
            onClickUpdate={onClickUpdate}
            announcementInfo={announcementInfo}
            deletingAnnouncement={deletingAnnouncement}
            updatingAnnouncement={updatingAnnouncement}
            closeParentModal={handleClose}
          /> */}
        </Box>
      </Modal>
    </div>
  );
}

export const announcementsColumn = [
  {
    field: "title",
    headerName: "Title",
    flex: 1,
    renderCell: (params) => {
      return <div className="break-all">{params.row.title || "--"}</div>;
    },
  },
  {
    field: "subtitle",
    headerName: "Subtitle",
    flex: 1,
    renderCell: (params) => {
      return <div className="break-all">{params.row.subtitle || "--"}</div>;
    },
  },
  {
    field: "expired",
    headerName: "Expired",
    flex: 1,
    renderCell: (params) => {
      return (
        <div className="break-all">
          {params.row.expired === true ? "Yes" : "No"}
        </div>
      );
    },
  },
  {
    field: "isActive",
    headerName: "Active",
    flex: 1,
    renderCell: (params) => {
      return (
        <div className="break-all">
          {params.row.isActive === true ? "Yes" : "No"}
        </div>
      );
    },
  },

  {
    field: "startDateTime",
    headerName: "Start Date",
    flex: 1,
    renderCell: (params) => {
      return (
        <div className="break-all">
          {readableTimeStamp(params.row.startDateTime) || "--"}
        </div>
      );
    },
  },
  {
    field: "endDateTime",
    headerName: "End Date",
    flex: 1,
    renderCell: (params) => {
      return (
        <div className="break-all">
          {readableTimeStamp(params.row.endDateTime) || "--"}
        </div>
      );
    },
  },
  {
    field: "type",
    headerName: "Media Type",
    flex: 1,
    renderCell: (params) => {
      return <div className="break-all">{params.row.type || "--"}</div>;
    },
  },
  {
    field: "assetUrl",
    headerName: "Image/Video",
    flex: 1,
    renderCell: (params) => {
      return (
        <div className="break-all">
          {params.row.assetUrl ? (
            params.row.type === "VIDEO" ? (
              <a href={params.row.assetUrl} target="_blank" rel="noreferrer">
                View Video
              </a>
            ) : (
              <a href={params.row.assetUrl} target="_blank" rel="noreferrer">
                View Image
              </a>
            )
          ) : (
            "--"
          )}
        </div>
      );
    },
  },
  {
    field: " ",
    headerName: " ",
    sortable: false,
    renderCell: (data) => {
      return (
        <>
          <EditModal data={data.row} /> | <DeleteModal data={data.row} />
        </>
      );
    },
  },
];
