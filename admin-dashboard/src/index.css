@import url("https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600;700&family=Pacifico&family=Quicksand:wght@400;500;600&family=Raleway:wght@400;600;700;800;900&family=Roboto:wght@400;500&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: Inter, Avenir, Helvetica, Arial, sans-serif;
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #fff;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

a {
  font-weight: 500;
  color: inherit;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

/* body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
} */

body {
  background-color: #fff;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}

.feed-status-button {
  @apply flex flex-col justify-center gap-y-3 items-center  h-[300px] w-[300px] rounded-md shadow-2xl  hover:border-none focus:outline-none hover:bg-slate-500 hover:text-white
}

.hr-content::before {
  content: "Login OR Signup";
  display: block;
  padding: 10px;
  background-color: white;
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  right: 50%;

  transform: translate(50%, -50%);
}

button:focus,
button:focus-visible {
  outline: none;
}

button.dropdown:active {
  background-color: #cbcbcba2;
}
button.dropdown {
  padding: 10px;

  border-radius: 100%;
}

@layer utilities {
  /* Hide scrollbar for Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
}
