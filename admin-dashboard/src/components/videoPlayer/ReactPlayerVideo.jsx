import ReactPlayer from "react-player/lazy";
import { useCallback, useRef, useState } from "react";
import { useInView } from "react-intersection-observer";
import PlayIcon from "../svgicons/PlayIcon";
import Spinner from "../reusuable/spinner/Spinner";

const ReactPlayerVideo = ({ highlightId, highlight }) => {
  const [isReady, setIsReady] = useState(false);
  const [volume, setVolume] = useState(false);
  const [playing, setPlaying] = useState(false);
  const [inview, setinview] = useState({
    isInviewPort: false,
    id: "",
    url: "",
    thumbnail: ""
  });

  const ref = useRef();

  const { ref: inViewRef, inView: isComponentInview } = useInView({
    onChange: (inView) => {
      if (inView) {
        setinview({
          isInviewPort: true,
          id: highlightId,
          url: getVideoUrl("index.m3u8"),
          thumbnail: getVideoUrl("thumbnail.png")
        });
        setPlaying(isReady);
      } else {
        setinview((prev) => ({ ...prev, isInviewPort: false }));
        setPlaying(false);
      }
    }
  });

  // Use `useCallback` so we don't recreate the function on each render
  const setRefs = useCallback(
    (node) => {
      // Ref's from useRef needs to have the node assigned to `current`
      ref.current = node;
      // Callback refs, like the one from `useInView`, is a function that takes the node as an argument
      inViewRef(node);
    },
    [inViewRef]
  );

  const handleReady = () => {
    setTimeout(() => {
      setIsReady(inview.id === highlightId);
    }, 300);
  };

  const getVideoUrl = (type) => {
    if (highlightId) {
      if (highlight?.streamUrl?.baseUrl) {
        return `${highlight?.streamUrl?.baseUrl}/${
          highlight?.streamUrl?.key?.split("--")[1]
        }/${type}`;
      }
      return highlightId ? highlight?.url : highlight?.assetUrl;
    }
    return "";
  };

  return (
    <div ref={setRefs} className="w-full h-full mt-7 relative">
      {!isReady && (
        <>
          <div className="absolute top-0 right-0 bottom-0 left-0 flex justify-center items-center z-40">
            <Spinner />
          </div>
          <img
            className="h-[500px] absolute w-full object-cover rounded-[0px]"
            src={inview.thumbnail}
          />
        </>
      )}
      <div className="h-[500px] flex justify-center items-center rounded-[0px]">
        {inview.isInviewPort && inview.id === highlightId ? (
          <ReactPlayer
            onReady={handleReady}
            url={inview.url}
            onPlay={() => {
              setPlaying(true);
            }}
            onPause={() => setPlaying(false)}
            playing={playing}
            controls={false}
            volume={Number(volume)}
            // ref={videoRef}
            // stopOnUnmount={true}
            width="100%"
            height="100%"
            poster={inview.thumbnail}
            className="react-player"
            fallback={
              <img
                className="h-[500px] absolute w-full object-cover rounded-[0px]"
                src={inview.thumbnail}
              />
            }
          />
        ) : (
          <img
            className="h-[500px] absolute w-full object-cover rounded-[0px]"
            src={inview.thumbnail}
          />
        )}
      </div>

      {/* Play video */}
      {playing && isReady ? (
        <div
          onClick={() => {
            setPlaying(false);
          }}
          className="absolute top-0 right-0 bottom-0 left-0 flex justify-center items-center z-40"
        />
      ) : isReady && (
        <div
          onClick={() => {
            setPlaying(true);
          }}
          className="absolute top-0 right-0 bottom-0 left-0 flex justify-center items-center"
        >
          <div className="w-[50px] h-[50px] bg-gray-100 rounded-full flex justify-center items-center">
            <PlayIcon />
          </div>
        </div>
      )}
      {highlightId == inview.id && (
        <button
          onClick={() => setVolume(!volume)}
          className="absolute top-[80%] right-[10px] z-40 bg-transparent border-none outline-none focus:outline-none focus:border-none hover:outline-none hover:border-none"
        >
          <img
            src={!volume ? "/images/mute.svg" : "/images/volume.svg"}
            className="w-[30px] h-[30px] bg-gray-800 p-2 rounded-full shadow-2xl"
          />
        </button>
      )}
    </div>
  );
};

export default ReactPlayerVideo;
