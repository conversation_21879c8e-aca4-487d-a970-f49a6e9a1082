import { useState } from "react";
import Sidebar from "./Sidebar";
import Backdrop from "@mui/material/Backdrop";
import CircularProgress from "@mui/material/CircularProgress";
import PropTypes from "prop-types";
import { dispatch } from "../redux/store";
import { useNavigate } from "react-router-dom";

const Layout = ({ children }) => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [loading] = useState(false);

  const navigate = useNavigate();

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  return (
    <div className="flex pt-20">
      {/* Sidebar */}
      <div
        className={`hidden md:block bg-gray-800 text-white h-full fixed left-0 top-0 bottom-0 z-50 ${
          isSidebarOpen ? "w-72" : "w-20"
        }`}
      >
        <Sidebar isSidebarOpen={isSidebarOpen} />
      </div>
      <div
        className={`block md:hidden bg-gray-800 text-white h-full fixed left-0 top-0 bottom-0 z-50 ${
          isSidebarOpen ? "w-72" : "hidden"
        }`}
      >
        <span
          className="text-white cursor-pointer focus:outline-none p-4 float-right"
          onClick={toggleSidebar}
        >
          {isSidebarOpen ? (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M10 19l-7-7m0 0l7-7m-7 7h18"
              />
            </svg>
          ) : (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 6h16M4 12h8m-8 6h16"
              />
            </svg>
          )}
        </span>
        <Sidebar isSidebarOpen={isSidebarOpen} />
      </div>

      <div
        className={`flex flex-col flex-1 ${
          isSidebarOpen ? "ml:0 md:ml-72" : "md:ml-20 ml-0"
        }`}
      >
        {/* Header */}
        <header
          className={`bg-gray-700 text-white py-4 fixed top-0 left-0 right-0 z-40 flex items-center justify-between ${
            isSidebarOpen ? "ml:0 md:ml-72" : "md:ml-20 ml-0"
          }`}
        >
          <div className="flex items-center justify-between w-full">
            <span
              className="text-white cursor-pointer focus:outline-none px-4 py-1"
              onClick={toggleSidebar}
            >
              {isSidebarOpen ? (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M10 19l-7-7m0 0l7-7m-7 7h18"
                  />
                </svg>
              ) : (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h8m-8 6h16"
                  />
                </svg>
              )}
            </span>
            <div className="hidden md:flex items-center gap-16 mr-16">
              <input
                type="text"
                placeholder="Search"
                disabled
                className="bg-gray-600 text-white rounded-lg py-2 px-4 focus:outline-none"
              />
              <span className="ml-4">
                {/* Updated user icon */}
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-10 w-10"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="white"
                  strokeWidth="1"
                >
                  <circle cx="12" cy="12" r="10" />
                  <circle cx="12" cy="8" r="3" />
                  <path
                    d="M3.829 17.97a9 9 0 0 1 16.342 0"
                    strokeLinecap="round"
                  />
                </svg>
              </span>
              <button
                onClick={() => {
                  dispatch.auth.logout()
                  navigate("/")
                  
                }}
                className="bg-red-400 active: scale-95 shadow-md"
              >
                Logout
              </button>
            </div>
            <div className="md:hidden flex items-center">
              <input
                type="text"
                placeholder="Search"
                className="bg-gray-600 max-[300px]:w-[140px] max-w-[200px] text-white rounded-lg p-2 focus:outline-none"
              />
            </div>
            <span className="md:hidden flex items-center pr-4">
              {/* Updated user icon */}
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-10 w-10"
                viewBox="0 0 24 24"
                fill="none"
                stroke="white"
                strokeWidth="1"
              >
                <circle cx="12" cy="12" r="10" />
                <circle cx="12" cy="8" r="3" />
                <path
                  d="M3.829 17.97a9 9 0 0 1 16.342 0"
                  strokeLinecap="round"
                />
              </svg>
            </span>
          </div>
        </header>

        <Backdrop
          sx={{ color: "#fff", zIndex: (theme) => theme.zIndex.drawer + 1 }}
          open={loading}
        >
          <CircularProgress color="inherit" />
        </Backdrop>
        {/* Main Content */}
        <div className="w-full">
          <div className="overflow-y-auto w-[100vw] md:w-full">{children}</div>
        </div>
      </div>
    </div>
  );
};

Layout.propTypes = {
  children: PropTypes.any,
};

export default Layout;
