import Spinner from "./spinner/Spinner";
import PropTypes from "prop-types";

const Button = ({
  text,
  bg,
  height,
  py,
  border_gray,
  icon,
  type,
  isLoading,
  isDisabled = false
}) => {
  return (
    <div>
      <button
        type={type}
        style={{
          height: `${height ? height : "48px"}`,
          paddingBlock: `${py ? py : "14px"}`
        }}
        className={` ${
          bg === "white"
            ? "bg-btnGray"
            : bg === "transparent"
            ? "bg-transparent"
            : "bg-[#52FF00] font-bold shadow-none hover:bg-[#51ff00af] text-black border-none"
        }
        w-full
        ${
          bg === "transparent" &&
          !border_gray &&
          "border border-secondary text-secondary"
        }
        ${
          bg === "transparent" &&
          border_gray &&
          "border border-primary text-secondary"
        }
        ${bg === "white" && "text-secondary border-none"}
        px-6 rounded-curve outline-none flex items-center justify-center font-quicksand font-bold text-base leading-5
      `}
        disabled={isLoading || isDisabled}
      >
        {icon ? (
          <div className="flex items-center justify-center">{icon}</div>
        ) : isLoading ? (
          <>
            <Spinner /> <span className="">{text ? text : "Register"}...</span>
          </>
        ) : (
          <span className="">{text ? text : "Register"}</span>
        )}
      </button>
    </div>
  );
};

Button.propTypes = {
  text: PropTypes.string, // Define the prop type for 'text'
  bg: PropTypes.string,
  height: PropTypes.string,
  py: PropTypes.string,
  border_gray: PropTypes.bool,
  icon: PropTypes.element,
  type: PropTypes.string,
  isLoading: PropTypes.bool,
  isDisabled: PropTypes.bool
};

export default Button;
