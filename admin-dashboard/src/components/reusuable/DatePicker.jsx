/* eslint-disable react/prop-types */
import { MobileDatePicker } from '@mui/x-date-pickers/MobileDatePicker';
import { TimePicker } from '@mui/x-date-pickers/TimePicker';
import dayjs from "dayjs";


export const DatePickerComponent = ({setValue, defaultValue = new Date() }) => {
    const handleDateChange = (newValue) => {
        setValue(newValue.$d);
    };

    return (
        <MobileDatePicker
            defaultValue={dayjs(defaultValue)}
            onChange={handleDateChange}
            value={dayjs(defaultValue)}
            slotProps={{ textField: { size: 'small' } }}
            format="DD-MM-YYYY"
        />
    );
};

export const TimePickerComponent = ({setValue, defaultValue = new Date()}) => {
    const handleTimeChange = (newValue) => {        
        setValue(newValue.$d);
    }

    return (
        <TimePicker
            onChange={handleTimeChange}
            defaultValue={dayjs(defaultValue)}
            value={dayjs(defaultValue)}
            slotProps={{ textField: { size: 'small' } }}
        />
    )
}