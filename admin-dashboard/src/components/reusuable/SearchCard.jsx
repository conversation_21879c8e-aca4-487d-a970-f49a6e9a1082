import Close from "../svgicons/Close.jsx";
import { useNavigate } from "react-router-dom";

const SearchCard = ({ data, removeFromRecent, isRecent = true }) => {
  const navigate = useNavigate();

  const addToRecentSearch = (currentUser) => {
    const recent = JSON.parse(localStorage.getItem("searchResult"));
    const userInRecentSearch = recent?.filter(
      (item) => item.id === currentUser.id
    );
    if (userInRecentSearch?.length > 0) {
      return;
    }

    const newRecent = [...(recent || []), currentUser];
    localStorage.setItem("searchResult", JSON.stringify(newRecent));
  };

  return (
    <div className="w-full flex justify-between items-center py-1 mb-3">
      <div
        className="flex gap-5 items-center"
        // onClick={() => {
        //   addToRecentSearch(data);
        //   if (data.type === "user") {
        //     navigate({ pathname: "/profile", search: `?id=${data?.id}` });
        //   } else {
        //     navigate(
        //       `/team-dashboard?teamId=${data.id}&clubId=${data.clubId}`
        //     );
        //   }
        // }}
      >
        {data.type === "user" ? (
          <img
            src={`${
              data?.photoUrl?.length > 5
                ? data?.photoUrl
                : "/images/profile.png"
            }`}
            alt="profile pix"
            className="w-[46px] h-[46px] object-cover rounded-full"
          />
        ) : (
          <img
            src={`${
              data?.logoUrl?.length > 5 ? data?.logoUrl : "/images/ball.png"
            }`}
            alt="profile pix"
            className="w-[46px] h-[46px] object-cover rounded-full"
          />
        )}
        <div>
          {data.type === "user" && (
            <p className="text-[14px] font-medium">
              @{data.firstName} {data.lastName}
            </p>
          )}
          <p className="text-[14px] font-medium opacity-50">
            {(data.teamName !== "N/A" && data.teamName) || data?.clubName}
          </p>
        </div>
      </div>
      {isRecent ? (
        <button
          onClick={() => removeFromRecent(data?.id)}
          className="bg-transparent "
        >
          <Close />
        </button>
      ) : (
        ""
      )}
    </div>
  );
};

export default SearchCard;
