import { ResponsiveBar } from "@nivo/bar";
import { useTheme } from "@mui/material";
import { tokens } from "../styles/theme";
import { states } from "../admin/states";

const BarChart = ({ isDashboard = false, customers }) => {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);

  const organiseStates = customers.map((customer) => {
    const stateString = states.find((state) => {
      if (customer.state) {
        if (
          customer.state
            .trim()
            .toLowerCase()
            .includes(state.trim().toLowerCase()) ||
          state
            .trim()
            .toLowerCase()
            .includes(customer.state.trim().toLowerCase())
        ) {
          return true;
        }
        return false;
      }
      return false;
    });

    return { ...customer, state: stateString === undefined ? "Not Specified" : stateString };
  });

  const countStates = organiseStates.reduce((acc, curr) => {
    const state = curr.state;
    if (acc[state]) {
      acc[state] += 1;
    } else {
      acc[state] = 1;
    }
    return acc;
  }, {});

  const stateWithCounts = Object.entries(countStates).map(
    ([state, count], i) => {
      return { state, count };
    }
  );

  return (
    <ResponsiveBar
      data={stateWithCounts}
      theme={{
        // added
        axis: {
          domain: {
            line: {
              stroke: colors.grey[100],
            },
          },
          legend: {
            text: {
              fill: colors.grey[100],
            },
          },
          ticks: {
            line: {
              stroke: colors.grey[100],
              strokeWidth: 1,
            },
            text: {
              fill: colors.grey[100],
            },
          },
        },
        legends: {
          text: {
            fill: colors.grey[500],
          },
        },
      }}
      keys={[...states, "count"]}
      indexBy="state"
      padding={0.3}
      colors={{ scheme: "nivo" }}
      valueScale={{ type: "linear" }}
      indexScale={{ type: "band", round: true }}
      margin={{ top: 50, right: 130, bottom: 50, left: 60 }}
      defs={[
        {
          id: "dots",
          type: "patternDots",
          background: "inherit",
          color: "#38bcb2",
          size: 4,
          padding: 1,
          stagger: true,
        },
        {
          id: "lines",
          type: "patternLines",
          background: "inherit",
          color: "#eed312",
          rotation: -45,
          lineWidth: 6,
          spacing: 10,
        },
      ]}
      borderColor={{
        from: "color",
        modifiers: [["darker", "1.6"]],
      }}
      axisTop={null}
      axisRight={null}
      axisBottom={{
        tickSize: 5,
        tickPadding: 5,
        tickRotation: 0,
        legend: isDashboard ? undefined : "state", // changed
        legendPosition: "middle",
        legendOffset: 32,
        ticksPosition: "after",
        renderTick: (e) => {
          return (
            <tspan {...e} rotate={90}>
              {" "}
              {e.value}
            </tspan>
          );
        },
      }}
      axisLeft={{
        tickSize: 5,
        tickPadding: 5,
        tickRotation: 0,
        legend: isDashboard ? undefined : "state", // changed
        legendPosition: "middle",
        legendOffset: -40,
      }}
      enableLabel={true}
      labelSkipWidth={12}
      labelSkipHeight={12}
      labelTextColor={{
        from: "color",
        modifiers: [["darker", 1.6]],
      }}
      legends={[
        {
          dataFrom: "keys",
          anchor: "bottom-right",
          direction: "column",
          justify: false,
          translateX: 120,
          translateY: 0,
          itemsSpacing: 2,
          itemWidth: 100,
          itemHeight: 20,
          itemDirection: "left-to-right",
          itemOpacity: 0.85,
          symbolSize: 20,
          effects: [
            {
              on: "hover",
              style: {
                itemOpacity: 1,
              },
            },
          ],
        },
      ]}
      role="application"
      barAriaLabel={function (e) {
        return e.id + ": " + e.formattedValue + " in country: " + e.indexValue;
      }}
    />
  );
};

export default BarChart;
