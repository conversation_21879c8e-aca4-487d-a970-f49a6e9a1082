import { AdminPanelSettingsSharp, SportsSoccer, <PERSON>, Queue, PushPin } from "@mui/icons-material";
import AnnouncementIcon from "../assets/svg/AnnouncementIcon";
import DashboardIcon from "../assets/svg/DashboardIcon";
import PropTypes from "prop-types";
import { useLocation } from "react-router-dom";

const Sidebar = ({ isSidebarOpen }) => {
  const currentParams = useLocation().pathname;
  
  return (
    <div className="h-full w-full p-5">
      <img
        className="w-24 md:w-32 rounded-md"
        src={
          "https://uploads-ssl.webflow.com/631cc6e3d44a43fa298b923a/633b098aad1e8178f0c91cfb_playerapp-white.png"
        }
      ></img>
      <ul
        className={`mt-16 flex flex-col justify-center ${
          !isSidebarOpen && "items-center"
        } gap-8 text-xl font-semibold`}
      >
        <li className="w-max">
          <a href="/admin" className={`flex gap-4 ${currentParams === "/admin" ? "bg-slate-400 px-4 py-2 rounded-md" : ""}`}>
            <DashboardIcon />
            {isSidebarOpen && <div>Dashboard</div>}
          </a>
        </li>
        <li className="w-max">
          <a href="/admin/feeds-queue" className={`flex gap-4 ${currentParams === "/admin/feeds-queue" ? "bg-slate-400 px-4 py-2 rounded-md" : ""}`}>
            <Queue />
            {isSidebarOpen && <div>Feeds Queue</div>}
          </a>
        </li>
        <li className="w-max">
          <a href="/admin/pinned-post" className={`flex gap-4 ${currentParams === "/admin/pinned-post" ? "bg-slate-400 px-4 py-2 rounded-md" : ""}`}>
            <PushPin />
            {isSidebarOpen && <div>Pinned Post</div>}
          </a>
        </li>
        {/* <li className="w-max">
          <a href="/admin/announcements" className={`flex gap-4 ${currentParams === "/admin/announcements" ? "bg-slate-400 px-4 py-2 rounded-md" : ""}`}>
            <AnnouncementIcon />
            {isSidebarOpen && <div>Announcements</div>}
          </a>
        </li> */}
        <li className="w-max">
          <a href="/admin/competitions/main" className={`flex gap-4 ${currentParams === "/admin/competitions/main" ? "bg-slate-400 px-4 py-2 rounded-md" : ""}`}>
            <AnnouncementIcon />
            {isSidebarOpen && <div>Competitions</div>}
          </a>
        </li>
        <li className="w-max">
          <a href="/admin/results" className={`flex gap-4 ${currentParams === "/admin/results" ? "bg-slate-400 px-4 py-2 rounded-md" : ""}`}>
            <SportsSoccer/>
            {isSidebarOpen && <div>Results</div>}
          </a>
        </li>
        <li className="w-max">
          <a href="/admin/assign-players" className={`flex gap-4 ${currentParams === "/admin/assign-players" ? "bg-slate-400 px-4 py-2 rounded-md" : ""}`}>
            <AdminPanelSettingsSharp/>
            {isSidebarOpen && <div>Club Admins</div>}
          </a>
        </li>
        <li className="w-max">
          <a href="/admin/edit-palyers" className={`flex gap-4 ${currentParams === "/admin/edit-palyers" ? "bg-slate-400 px-4 py-2 rounded-md" : ""}`}>
            <Edit/>
            {isSidebarOpen && <div>Edit Players</div>}
          </a>
        </li>
      </ul>
    </div>
  );
};

Sidebar.propTypes = {
  isSidebarOpen: PropTypes.bool,
};

export default Sidebar;
