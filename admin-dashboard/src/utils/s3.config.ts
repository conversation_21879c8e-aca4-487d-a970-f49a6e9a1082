import {
  S3Client,
  PutObjectCommandInput,
  PutObjectCommand
} from "@aws-sdk/client-s3";
import Compressor from "compressorjs";

enum MediaTypes {
  PHOTO = "photo",
  VIDEO = "video"
}
class S3BucketManger {
  // On local/dev Please change region to eu for photo uploads and us-east-1 for video uploads
  private client = new S3Client({
    region: import.meta.env.VITE_REGION,
    // region: "eu-west-2",
    credentials: {
      accessKeyId: import.meta.env.VITE_APP_AWS_ACCESS_ID,
      secretAccessKey: import.meta.env.VITE_APP_AWS_SECRET_KEY
    }
  });

  /**
   * A method used to upload files to s3 bucket
   * @param {string} bucketName the s3 bucket name to be used for the upload
   * @param {File} file the file to be uploaded
   * @param {string} keyName the key name to be used for the upload
   * @param {MediaTypes} type the type of media to be uploaded
   * @returns {Promise<{ assetUrl: string; streamUrls: {key: string, baseUrl: string}  }>} the url of the uploaded file
   * @throws {Error} if the file size is more than 30mb
   * @throws {Error} if the upload fails
   * @throws {Error} if the file type is not supported
   * @example 
   * import S3BucketManger from "../utils/s3.config";
   * 
   *   const handleUpload = async (event) => {
    const s3BucketManger = new S3BucketManger();
    try {
      const s3 = await s3BucketManger.uploadAssets(event.target.files[0], "player-assets", keyName, "photo");
      console.log(s3);  
    //   {
    //     "assetUrl": "https://player-assets.s3.eu-west-2.amazonaws.com/16772266.pdf"
    // }
    } catch (error) {
      console.log(error);
    }
  };

  <input type="file" onChange={handleUpload} />
   */
  async uploadAssets(
    file: File,
    bucketName?: string,
    keyName?: string,
    type?: MediaTypes
  ): Promise<{ assetUrl: string, streamUrls: {key: string; baseUrl: string} }> {
    // if (file.size > 502000000) {
    //   throw new Error("The file size is more than 60mb");
    // }

    const compressAndUpload = async (file: File): Promise<string> => {
      return new Promise((resolve, reject) => {
        new Compressor(file, {
          quality: 0.6,
          async success(result) {
            const key = `${Date.now()}-${file.name}`.replace(/[^\w\s]|/g, "").replace(/\s+/g, "");
            const uploadParams: PutObjectCommandInput = {
              Bucket: import.meta.env.VITE_BUCKETNAME,
              Key: keyName || key,
              ContentType: file.type,
              ACL: "public-read",
              Body: result
            };

            if (import.meta.env.VITE_APP_ENV !== "dev") {
              delete uploadParams.ACL;
            }
              // console.log(uploadParams);

            try {
              await new S3BucketManger().client.send(
                new PutObjectCommand(uploadParams)
              );
              const assetUrl = `${import.meta.env.VITE_S3_BASE_PHOTO_URL}/${
                keyName || key
              }`;
              resolve(assetUrl);
            } catch (error) {
              reject(error);
            }
          },
          error(err) {
            reject(err);
          }
        });
      });
    };

    try {
      let assetUrl;
      let streamUrls;
      if (type === "photo") {
        assetUrl = await compressAndUpload(file);
      } else {
        const inputKey = `videosOnly/--${Date.now()}--` + file.name.replace(/[^\w\s]|/g, "").replace(/\s+/g, "");

        const uploadParams: PutObjectCommandInput = {
          Bucket: import.meta.env.VITE_S3_VIDEO_UPLOAD_BASE_BUCKET,
          Key: inputKey,
          ContentType: file.type,
          ACL: "public-read",
          Body: file,
        };

        if (import.meta.env.VITE_APP_ENV !== "dev") {
          delete uploadParams.ACL;
        }

        // if (file.size > 502000000) {
        //   throw new Error("The file size is more than 60mb");
        // }

        await this.client.send(new PutObjectCommand(uploadParams));
        assetUrl = `${import.meta.env.VITE_S3_VIDEO_UPLOAD_BASE_URL}/${inputKey}`;
        streamUrls = {
          key: inputKey,
          baseUrl: import.meta.env.VITE_S3_VIDEO_OUTPUT_BASE_URL,
        }
      }

      return { assetUrl, streamUrls };
    } catch (error) {
      throw new Error(error);
    }
  }
}

export default S3BucketManger;

