/* eslint-disable no-useless-escape */
/* eslint-disable no-unused-vars */
/* eslint-disable react-refresh/only-export-components */
import { toast } from "react-toastify";

import store from "../redux/store";
import moment from "moment";

export const getAllModels = () => {
  return store.getState();
};

export const getSingleModel = (model) => {
  const currentState = store.getState();
  return currentState?.[model];
};

export const reduxDispatch = () => store.dispatch;

export const formatCurrency = (value) => {
  return new Intl.NumberFormat("en-US", { minimumFractionDigits: 2 }).format(
    value
  );
};

export const notifySuccess = (message) =>
  toast.success(message, {
    position: "top-right",
    // autoClose: 2000,
    hideProgressBar: true,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
    progress: undefined
  });

export const notifyError = (message) =>
  toast.error(message, {
    position: "top-right",
    // autoClose: 2000,
    hideProgressBar: true,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
    progress: undefined
  });

export const notifyWarn = (message) =>
  toast.warn(message, {
    position: "top-right",
    // autoClose: 2000,
    hideProgressBar: true,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
    progress: undefined
  });

export const calculateAge = (dateOfBirth) => {
  if (!dateOfBirth) {
    return "";
  }
  const age = Math.floor(
    moment(new Date()).diff(moment(dateOfBirth, "DD/MM/YYYY"), "years", true)
  );
  return age < 0 ? 0 : age;
};

export const cleanDate = (dateOfBirth) => {
  const dob = dateOfBirth || "";
  const splitDate = dob.split("/");
  const birthdateTimeStamp = new Date(
    splitDate[2],
    splitDate[1] - 1,
    splitDate[0]
  );
  return birthdateTimeStamp;
};

export const PlayerPostion = {
  GOAL_KEEPERS: "Goalkeeper",
  CENTRE_BACK: "Centre Back",
  LEFT_BACK: "Left Back",
  RIGHT_BACK: "Right Back",
  WING_BACK: "Wing Back",
  DEFENSIVE_MIDFIELD: "Defensive Midfield",
  CENTRAL_MIDFIELD: "Central Midfield",
  ATTACKING_MIDFIELD: "Attacking Midfield",
  WINGER: "Winger",
  FORWARD: "Forward",
  STRIKER: "Striker"
};

export const PreferredFoot = {
  RIGHT_FOOTED: "Right Footed",
  LEFT_FOOTED: "Left Footed",
  BOTH_FOOTED: "Both Footed"
};

export const readablePosition = (position) => {
  if (PlayerPostion[position]) {
    return PlayerPostion[position];
  }
  return position?.split("_").join(" ").toLowerCase();
};

export const trim150 = (str) => {
  return str?.length > 150 ? str.substring(0, 150) + "..." : str;
};

export const trim80 = (str, highlightId, userId) => {
  return str?.length > 80 ? (
    <div>
      {str.substring(0, 80)}...{" "}
      <a
        className="text-gray-500"
        href={`/user/comment?highlightId=${highlightId}&userId=${userId}`}
      >
        more
      </a>
    </div>
  ) : (
    str
  );
};

export const removeEmpty = (obj) => {
  return Object.fromEntries(
    Object.entries(obj).filter(([_, v]) => v != "" || null || undefined)
  );
};

export function validateURL(textval) {
  var urlregex = new RegExp(
    "^(http|https|ftp)://[a-zA-Z0-9-.]+.[a-zA-Z]{2,3}(:[a-zA-Z0-9]*)?/?([a-zA-Z0-9-._?,'/\\+&amp;%$#=~])*$"
  );
  return urlregex.test(textval);
}

/**
 * Long numbers to appear in a shorter, and more eaisier to understand way
 * @param {number} num the number to be shortened
 * @param {number} decimalToDisplay is the number of decimal place to display in the abbreviation, not the input number
 * @returns
 */
export function getLikeShortNumberRound(num, decimdalToDisplay) {
  let x = ("" + num).length;
  let p = Math.pow;
  decimdalToDisplay = p(10, decimdalToDisplay);
  x -= x % 3;
  return (
    Math.round((num * decimdalToDisplay) / p(10, x)) / decimdalToDisplay +
    " kMGTPE"[x / 3]
  );
}

export function getTotalLikes(reactions) {
  const totalLikes = reactions?.reduce((prev, curr) => {
    return { count: (prev.count || 0) + (curr.count || 0) };
  }, [])?.count;
  const totalReactions = getLikeShortNumberRound(totalLikes, 1);
  return totalLikes === 0 ? "" : totalReactions;
}

export const removeDuplicates = (array, property) => {
  const uniqueIds = [];

  const unique = array.filter((element) => {
    const isDuplicate = uniqueIds.includes(element[property]);

    if (!isDuplicate) {
      uniqueIds.push(element[property]);

      return true;
    }

    return false;
  });

  return unique;
};

export const getVideoDuration = async (file) => {
  const video = document.createElement("video");
  video.preload = "metadata";

  video.onloadedmetadata = () => {
    window.URL.revokeObjectURL(video.src);
    const duration = video.duration;
    return duration;
  };

  video.src = URL.createObjectURL(file);
};

export const findMatchingObject = (array1, array2) => {
  for (let obj1 of array1) {
    for (let obj2 of array2) {
      if (obj1.id === obj2.id) {
        return obj1;
      }
    }
  }
  return null;
};

export const getTimeAgo = (timestamp) => {
  const currentTime = new Date();
  const targetTime = new Date(timestamp);

  const timeDiff = currentTime.getTime() - targetTime.getTime();
  const seconds = Math.floor(timeDiff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  const months = Math.floor(days / 30);
  const years = Math.floor(days / 365);

  if (seconds < 60) {
    return "just now";
  } else if (minutes < 60) {
    return `${minutes} ${pluralize("minute", minutes)} ago`;
  } else if (hours < 24) {
    return `${hours} ${pluralize("hour", hours)} ago`;
  } else if (days < 30) {
    return `${days} ${pluralize("day", days)} ago`;
  } else if (days < 365) {
    return `${months} ${pluralize("month", months)} ago`;
  } else {
    return `${years} ${pluralize("year", years)} ago`;
  }
};

const pluralize = (word, count) => {
  return count === 1 ? word : `${word}s`;
};

export const handleCopyToClipBoard = (text) => {
  navigator.clipboard.writeText(text);
  notifySuccess("URL has been copied to your clipboard");
};

export function linkify(text) {
  const urlRegex =
    /(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/gi;

  const newTextWithLink = text.replace(urlRegex, function (url) {
    return `<a href='${url}' target="_blank">
        ${url}
      </a>`;
  });
  return <span dangerouslySetInnerHTML={{ __html: newTextWithLink }} />;
}

export const readableTimeStamp = (timestamp) => {
  const date = new Date(timestamp);

  const options = {
    year: "numeric",
    month: "long",
    day: "numeric",
    hour: "numeric",
    minute: "numeric",
    second: "numeric",
    timeZoneName: "short"
  };

  const formattedDateTime = date.toLocaleString("en-US", options);
  return formattedDateTime;
};

/**
 * @description This function is used to search an array of objects or make a call to the API
 * @param {object} param
 * @param {Array} param.array
 * @param {string} param.searchTerm
 * @param {[Func]} param.makeSearchCall
 * @returns {Array<Promise>}
 */
export async function searchArrayOrMakeCallToAPI({
  array = [],
  searchTerm = "",
  makeSearchCall
}) {
  if (!searchTerm) {
    return array;
  }

  const searchResults = array.filter((item) => {
    for (const key in item) {
      if (
        typeof item[key] === "string" &&
        item[key].toLowerCase().includes(searchTerm.toLowerCase())
      ) {
        return true;
      }
    }
    return false;
  });

  if (searchResults.length > 0) {
    return searchResults;
  } else {
    try {
      if (makeSearchCall.length > 0) {
        const searchPromises = makeSearchCall.map((funcs) => funcs(searchTerm));
        const searchResponses = await Promise.all(searchPromises);

        const mergedData = [
          ...searchResponses[0]?.data,
          ...searchResponses[1]?.data
        ]?.map((data) => {
          return {
            ...data
          };
        });
        return mergedData;
      } else {
        return [];
      }
    } catch (error) {
      console.error("Error fetching data from API:", error);
      return [];
    }
  }
}

export const reorder = (list = [], startIndex, endIndex) => {
  const result = Array.from(list);
  const [removed] = result.splice(startIndex, 1);
  result.splice(endIndex, 0, removed);

  return result;
};

/**
 * Pick items from an array of objects based on the keys provided
 * @param {Array<T>} array 
 * @param {Array<Strings>} keys 
 * @returns 
 */
export function pickFromArrayOfObjects(array, keys) {
  return array.map(function(item) { return {
    ...keys.reduce((obj, key) => {
      if (item[key]) {
        obj[key] = item[key];
      }
      return obj;
    }, {})
  } });
}

export const urlRegex = new RegExp("^(http[s]?:\\/\\/(www\\.)?|ftp:\\/\\/(www\\.)?|www\\.){1}([0-9A-Za-z-\\.@:%_\+~#=]+)+((\\.[a-zA-Z]{2,3})+)(/(.)*)?(\\?(.)*)?");
