import {
  S3<PERSON>lient,
  PutObjectCommand,
  CreateMultipartUploadCommand,
  Upload<PERSON>artCommand,
  CompleteMultipartUploadCommand,
  UploadPartCommandInput,
  UploadPartCommandOutput,
  PutObjectCommandInput,
  ObjectCannedACL,
  CreateMultipartUploadCommandInput
} from "@aws-sdk/client-s3";
import Compressor from "compressorjs";
import { photoBucket, uploadedStreamsBaseUrl } from "./constants";

enum MediaTypes {
  PHOTO = "photo",
  VIDEO = "video"
}

class S3BucketManager {
  private client = new S3Client({
    region: import.meta.env.VITE_REGION,
    useAccelerateEndpoint: true,
    credentials: {
      accessKeyId: import.meta.env.VITE_APP_AWS_ACCESS_ID,
      secretAccessKey: import.meta.env.VITE_APP_AWS_SECRET_KEY
    },
    maxAttempts: 5,
    retryMode: "standard"
  });

  private async uploadMultipart(
    file: File,
    bucket: string,
    key: string
  ): Promise<string> {
    const multipartUploadParams: CreateMultipartUploadCommandInput = {
      Bucket: bucket,
      Key: key,
      ACL: ObjectCannedACL.public_read,
      ContentType: file.type
    };

    if (import.meta.env.VITE_APP_ENV !== "dev") {
      delete multipartUploadParams.ACL;
    }

    const createResponse = await this.client.send(
      new CreateMultipartUploadCommand(multipartUploadParams)
    );

    const uploadId = createResponse.UploadId;
    if (!uploadId) throw new Error("Failed to initiate multipart upload.");

    const partSize = 10 * 1024 * 1024; // MB
    const parts: any = [];

    try {
      for (
        let partNumber = 1, start = 0;
        start < file.size;
        start += partSize, partNumber++
      ) {
        const end = Math.min(start + partSize, file.size);
        const part = file.slice(start, end);

        const uploadPartParams: UploadPartCommandInput = {
          Bucket: bucket,
          Key: key,
          UploadId: uploadId,
          PartNumber: partNumber,
          Body: part
        };

        const uploadPartResponse: UploadPartCommandOutput =
          await this.client.send(new UploadPartCommand(uploadPartParams));
        parts.push({
          ETag: uploadPartResponse.ETag,
          PartNumber: partNumber
        });
      }

      const completeResponse = await this.client.send(
        new CompleteMultipartUploadCommand({
          Bucket: bucket,
          Key: key,
          UploadId: uploadId,
          MultipartUpload: { Parts: parts }
        })
      );

      return completeResponse.Location || `${bucket}/${key}`;
    } catch (error) {
      // Abort the multipart upload if something goes wrong
      // await this.client.send(
      //   new AbortMultipartUploadCommand({
      //     Bucket: bucket,
      //     Key: key,
      //     UploadId: uploadId,
      //   })
      // );
      throw new Error(`Multipart upload failed: ${error.message}`);
    }
  }

  private compressAndUpload(
    file: File,
    bucket: string,
    key: string
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      const s3Client = this.client;

      new Compressor(file, {
        quality: 0.6,
        async success(result) {
          try {
            const uploadParams: PutObjectCommandInput = {
              Bucket: bucket,
              Key: key,
              ContentType: file.type,
              ACL: "public-read",
              Body: result
            };

            if (import.meta.env.VITE_APP_ENV !== "dev") {
              delete uploadParams.ACL;
            }

            await s3Client.send(new PutObjectCommand(uploadParams));

            resolve(`${import.meta.env.VITE_S3_BASE_PHOTO_URL}/${key}`);
          } catch (error) {
            console.log(error);

            reject(error);
          }
        },
        error(err) {
          reject(err);
        }
      });
    });
  }

  async uploadAssets(
    file: File,
    bucketName: string,
    keyName: string,
    type: MediaTypes
  ): Promise<{ assetUrl: string; streamUrls?: any }> {
    try {
      let assetUrl: string;
      let streamUrls: { key: string; baseUrl: any };
      const ext = file.name.split(".")[file.name.split(".").length - 1];
      const key = `${Date.now()}.${ext}`;

      if (file.size < 4) {
        throw new Error("Not a valid file");
      }

      if (type === MediaTypes.PHOTO) {
        assetUrl = await this.compressAndUpload(file, photoBucket, key);
      } else {
        const videoBucket = import.meta.env.VITE_S3_VIDEO_UPLOAD_BASE_BUCKET;
        const videoKey = `videosOnly/--${Date.now()}--${key}`;

        // if file size is greater than 20mb, upload using multipart upload
        if (file.size > 20 * 1024 * 1024) {
          assetUrl = await this.uploadMultipart(file, videoBucket, videoKey);
        } else {
          const uploadParams: PutObjectCommandInput = {
            Bucket: videoBucket,
            Key: videoKey,
            ContentType: file.type,
            ACL: ObjectCannedACL.public_read,
            Body: file
          };

          if (import.meta.env.VITE_APP_ENV !== "dev") {
            delete uploadParams.ACL;
          }
          await this.client.send(new PutObjectCommand(uploadParams));
          assetUrl = `${
            import.meta.env.VITE_S3_VIDEO_UPLOAD_BASE_URL
          }/${videoKey}`;
        }

        streamUrls = {
          key: videoKey,
          baseUrl: uploadedStreamsBaseUrl
        };
        return { assetUrl, streamUrls };
      }

      return { assetUrl, streamUrls: null };
    } catch (error) {
      throw new Error(error.message || "Upload failed.");
    }
  }
}

export default S3BucketManager;
