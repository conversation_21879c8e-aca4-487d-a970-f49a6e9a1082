import { DataGrid, GridToolbar } from "@mui/x-data-grid";
import { playersColumn } from "./constants";
import Backdrop from "@mui/material/Backdrop";
import CircularProgress from "@mui/material/CircularProgress";
import { useEffect, useState } from "react";
import { Typography } from "@mui/material";
import { dispatch } from "../../redux/store";
import { useSelector } from "react-redux";
import { PlayersApi } from "../../services";
import { notifyError, notifySuccess } from "../../utils/helpers";

const EditPlayersProfile = () => {
  // const [updating, setLoading] = useState(false);
  const [open, setOpen] = useState(false);

  const { allPlayers, loading } = useSelector(({ players, loading }) => ({
    allPlayers: players.allPlayers,
    loading: loading?.effects?.players?.getAllPlayers
  }));

  const removePlayerFromTeam = async (player) => {
    console.log(player);
    const playerObj = { ...player };

    for (const key in playerObj) {
      if (Object.hasOwnProperty.call(playerObj, key)) {
        if (!playerObj[key]) {
          delete playerObj[key];
        }
      }
    }

    setOpen(true);

    try {
      await PlayersApi.update({
        ...playerObj,
        teamId: "",
        isManager: typeof playerObj.isManager === "boolean" ? playerObj.isManager : false,
        cludId: "",
        teamName: player.teamName,
        clubName: player.teamName
      }, true);

      notifySuccess("Successful");
      await dispatch?.players?.getAllPlayers();
    } catch (error) {
      notifyError("An error occured")
      console.log(error);
    } finally {
      setOpen(false);
    }
  };

  useEffect(() => {
    dispatch.players?.getAllPlayers();
  }, []);

  return (
    <div className="mx-10 my-20">
      <Typography variant="h3" my={5}>
        List of all the Players
      </Typography>
      <DataGrid
        getRowHeight={() => "auto"}
        rows={allPlayers || []}
        loading={loading}
        columns={playersColumn(removePlayerFromTeam)}
        slots={{ Toolbar: GridToolbar }}
      />

      <Backdrop
        sx={{ color: "#fff", zIndex: (theme) => theme.zIndex.drawer + 1 }}
        open={open}
      >
        <CircularProgress color="inherit" />
      </Backdrop>
    </div>
  );
};

export default EditPlayersProfile;
