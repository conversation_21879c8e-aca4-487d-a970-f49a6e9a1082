import { MoreVert } from "@mui/icons-material";
import BasicPopover from "../../components/PopOver";
import { PlayerPostion, PreferredFoot, readableTimeStamp } from "../../utils/helpers";

export const playersColumn = removePlayerFromTeam => [
  {
    field: "firstName",
    headerName: "First Name",
    flex: 1,
    renderCell: (params) => {
      return <div className="break-all">{params.row.firstName || "--"}</div>;
    }
  },
  {
    field: "lastName",
    headerName: "Last Name",
    flex: 1,
    renderCell: (params) => {
      return <div className="break-all">{params.row.lastName || "--"}</div>;
    }
  },

  {
    field: "Email",
    headerName: "email",
    flex: 1,
    renderCell: (params) => {
      return <div className="break-all">{params.row.email || "--"}</div>;
    }
  },
  {
    field: "birthday",
    headerName: "Birthday",
    flex: 1,
    renderCell: (params) => {
      return <div className="break-all">{params.row.birthday || "--"}</div>;
    }
  },
  {
    field: "clubName",
    headerName: "Club Name",
    flex: 1,
    renderCell: (params) => {
      return <div className="break-all">{params.row.clubName || "--"}</div>;
    }
  },
  {
    field: "teamName",
    headerName: "Team Name",
    flex: 1,
    renderCell: (params) => {
      return <div className="break-all">{params.row.teamName || "--"}</div>;
    }
  },
  {
    field: "position",
    headerName: "Position",
    flex: 1,
    renderCell: (params) => {
      return (
        <>
          <div className="break-all">{PlayerPostion[params.row.position] || "--"}</div>
        </>
      );
    }
  },
  {
    field: "photoUrl",
    headerName: "Photo",
    flex: 1,
    renderCell: (params) => {
      return (
        <>
          <div className="break-all">
            {params.row.photoUrl && (
              <a href={params.row.photoUrl} target="_blank" rel="noreferrer">
                <img src={params.row.photoUrl} width={"200px"} />
              </a>
            ) || "--"}
          </div>
        </>
      );
    }
  },
  {
    field: "preferredFoot",
    headerName: "Preferred Foot",
    flex: 1,
    renderCell: (params) => {
      return (
        <>
          <div className="break-all">{PreferredFoot[params.row.preferredFoot] || "--"}</div>
        </>
      );
    }
  },
  {
    field: "gender",
    headerName: "Gender",
    flex: 1,
    renderCell: (params) => {
      return (
        <>
          <div className="break-all">{params.row.gender || "--"}</div>
        </>
      );
    }
  },
  {
    field: "isManager",
    headerName: "User Types",
    flex: 1,
    renderCell: (params) => {
      return (
        <>
          <div className="break-all">
            {params.row.isManager ? "Manager" : "Player" || "--"}
          </div>
        </>
      );
    }
  },
  {
    field: "createdDate",
    headerName: "Created At",
    flex: 1,
    renderCell: (params) => {
      return (
        <div className="break-all py-4">
          {readableTimeStamp(params.row.createdDate) || "--"}
        </div>
      );
    }
  },
  {
    field: "",
    headerName: "Action",
    flex: 1,
    renderCell: (params) => {
      return (
        <BasicPopover icon={<MoreVert />}>
          <div className="break-all p-5">
          {<button className="bg-white text-black border border-black hover:bg-slate-500" onClick={async () => await removePlayerFromTeam(params.row)}>Remove from current team </button>}
        </div>
        </BasicPopover>
      );
    }
  }
];
