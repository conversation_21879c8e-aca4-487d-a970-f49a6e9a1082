import { readableTimeStamp } from "../../utils/helpers";
import PlayersModal from "./PlayersModal";

export const teamsColumn = [
  {
    field: "teamName",
    headerName: "Team Name",
    flex: 1,
    renderCell: (params) => {
      return <div className="break-all">{params.row.teamName|| "--"}</div>;
    },
  },
    {
      field: "isActive",
      headerName: "Is Active",
      flex: 1,
      renderCell: (params) => {
        return <div className="break-all">{params.row.isActive ? 'Active' : 'Inactive' || "--"}</div>;
      },
    },

    {
      field: "logoUrl",
      headerName: "Logo",
      flex: 1,
      renderCell: (params) => {
        return <img src={params.row.logoUrl} className="break-all w-[100px] h-[100px]" />;
      },
    },
    {
      field: "createdAt",
      headerName: "Created At",
      flex: 1,
      renderCell: (params) => {
        return <div className="break-all py-4">{readableTimeStamp(params.row.createdAt) || "--"}</div>
      },
    },
    {
      field: "updatedAt",
      headerName: "Updated At",
      flex: 1,
      renderCell: (params) => {
        return <div className="break-all py-4">{readableTimeStamp(params.row.updatedAt) || "--"}</div>
      },
    },
    {
      field: "",
      headerName: "Action",
      flex: 1,
      renderCell: (params) => {
        return <PlayersModal teamData={params.row} />
      },
    }
  ];