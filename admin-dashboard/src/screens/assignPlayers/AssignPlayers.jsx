import { memo, useEffect, useState } from "react";
import { TeamsApi } from "../../services";
import { notifyError } from "../../utils/helpers";
import { Typography } from "@mui/material";
import { DataGrid, GridToolbar } from "@mui/x-data-grid";
import { teamsColumn } from "./constants";

const AssignPlayers = memo(() => {
  const [results, setResults] = useState([]);
  const [gettingTeams, setgettingTeams] = useState(false);

  const getAllTeams = async () => {
    setgettingTeams(true);
    try {
      const { data } = await TeamsApi.getAllTeams();
      console.log(data?.data?.Items);
      setResults(data?.data?.Items || []);
    } catch (error) {
      notifyError(error.response.data.message || error.message);
    }

    setgettingTeams(false);
  };

  useEffect(() => {
    getAllTeams();
  }, []);

  return (
    <div className="mx-10 my-20">
      <Typography variant="h3" my={5}>
        List of all the teams
      </Typography>
      <DataGrid
        getRowHeight={() => "auto"}
        rows={results || []}
        loading={gettingTeams}
        columns={teamsColumn}
        slots={{ Toolbar: GridToolbar }}
      />
    </div>
  );
});

export default AssignPlayers;

AssignPlayers.displayName = "AssignPlayers";
