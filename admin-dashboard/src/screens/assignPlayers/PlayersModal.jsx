/* eslint-disable react/prop-types */
import { memo, useState, forwardRef, useEffect } from "react";
import Button from "@mui/material/Button";
import Slide from "@mui/material/Slide";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import AppBar from "@mui/material/AppBar";
import Toolbar from "@mui/material/Toolbar";
import Tooltip from "@mui/material/Tooltip";
import IconButton from "@mui/material/IconButton";
import Typography from "@mui/material/Typography";
import CloseIcon from "@mui/icons-material/Close";
import Spinner from "../../components/reusuable/spinner/Spinner";
import { dispatch } from "../../redux/store";
import { useSelector } from "react-redux";
import {
  notifySuccess,
  readableTimeStamp,
  searchArrayOrMakeCallToAPI
} from "../../utils/helpers";
import { PlayersApi } from "../../services";

const Transition = forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

const PlayersModal = memo((props) => {
  const [open, setOpen] = useState(false);
  const [updating, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState([]);

  const { allPlayers, loading } = useSelector(({ players, loading }) => ({
    allPlayers: players.allPlayers,
    loading: loading?.effects?.players?.getAllPlayers
  }));

  const handleClickOpen = async () => {
    setOpen(true);

    if (allPlayers.length === 0) {
      await dispatch?.players?.getAllPlayers();
    }
  };
  
  const handleSearchPlayers = async ({ target }) => {
    const results = await searchArrayOrMakeCallToAPI({
      array: allPlayers,
      makeSearchCall: [],
      searchTerm: target.value
    });

    setDataSource(results);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const updateUserProfile = async (team, player) => {
    const playerObj = { ...player };

    for (const key in playerObj) {
      if (Object.hasOwnProperty.call(playerObj, key)) {
        if (!playerObj[key]) {
          delete playerObj[key];
        }
      }
    }

    setLoading(true);

    try {
      await PlayersApi.update({
        ...playerObj,
        isManager: true,
        teamId: team.id,
        cludId: team.cludId,
        teamName: team.teamName,
        clubName: team.teamName
      });
      await dispatch?.players?.getAllPlayers();
      notifySuccess("Successful");
    } catch (error) {
      console.log(error);
    }

    setLoading(false);
  };

  useEffect(() => {
    setDataSource(allPlayers);
  }, [allPlayers]);

  return (
    <>
      <Button variant="outlined" onClick={handleClickOpen}>
        Assign admin
      </Button>
      <Dialog
        fullScreen
        open={open}
        onClose={handleClose}
        TransitionComponent={Transition}
        PaperProps={{ sx: { width: "90%", height: "100%" } }}
      >
        <AppBar sx={{ position: "relative", backgroundColor: "black" }}>
          <Toolbar>
            <IconButton
              edge="start"
              color="inherit"
              onClick={handleClose}
              aria-label="close"
            >
              <CloseIcon />
            </IconButton>
            <Typography sx={{ ml: 2, flex: 1 }} variant="h6" component="div">
              List of players
            </Typography>
          </Toolbar>
        </AppBar>
        <div className="my-7 mx-10 flex justify-end">
          <input
            onChange={handleSearchPlayers}
            placeholder="Search Players"
            className="w-[40%] border py-4 px-5"
          />
        </div>
        <DialogContent>
          {loading ||
            (updating && (
              <div className="absolute mx-auto w-full">
                <Spinner big />
              </div>
            ))}
          {!loading && (
            <table>
              <tr className="bg-slate-400">
                <td className="w-[10%] px-5 font-extrabold ">First Name</td>
                <td className="w-[10%] px-5 font-extrabold ">Last Name</td>
                <td className="w-[10%] px-5 font-extrabold ">Email</td>
                <td className="w-[10%] px-5 font-extrabold ">Preferred Foot</td>
                <td className="w-[10%] px-5 font-extrabold ">Position</td>
                <td className="w-[10%] px-5 font-extrabold ">Current Team</td>
                <td className="w-[10%] px-5 font-extrabold ">Photo</td>
                <td className="w-[10%] px-5 font-extrabold ">isManager</td>
                <td className="w-[10%] px-5 font-extrabold ">Location</td>
                <td className="w-[10%] px-5 font-extrabold ">Created Date</td>
                <td className="w-[10%] px-5 font-extrabold ">Action</td>
              </tr>
              {dataSource.map((player, idx) => (
                <tr
                  className={`${idx % 2 === 0 ? "bg-slate-100" : ""}`}
                  key={player.id}
                >
                  <td className="pl-5">{player.firstName}</td>
                  <td className="pl-5">{player.lastName}</td>
                  <td className="pl-5">{player.email}</td>
                  <td className="pl-5">{player.preferredFoot}</td>
                  <td className="pl-5">{player.position}</td>
                  <td className="pl-5">{player.teamName || "--"}</td>
                  <td className="pl-5">
                    {player.photoUrl ? (
                      <a
                        href={player.photoUrl}
                        target="_blank"
                        rel="noreferrer"
                      >
                        <img
                          onClick={handleClickOpen}
                          src={player.photoUrl}
                          width={"200px"}
                        />
                      </a>
                    ) : (
                      "--"
                    )}
                  </td>
                  <td className="pl-5">{player.isManager}</td>
                  <td className="pl-5">{player.location}</td>
                  <td className="pl-5 py-3">
                    {readableTimeStamp(player.createdDate)}
                  </td>
                  <td className="pl-5 py-3">
                    <Button
                      onClick={() => updateUserProfile(props.teamData, player)}
                    >
                      <Tooltip
                        title={`This action will switch this player from ${player.teamName} to ${props.teamData.teamName}`}
                      >
                        Assign
                      </Tooltip>
                    </Button>
                  </td>
                </tr>
              ))}
            </table>
          )}
        </DialogContent>
        <DialogActions>
          <Button variant="contained" onClick={handleClose}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
});

export default PlayersModal;

PlayersModal.displayName = "PlayersModal";
