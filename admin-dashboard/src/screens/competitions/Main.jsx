import { useNavigate } from "react-router-dom";
const Main = () => {
    const navigate = useNavigate();
    const options = [
        { title: 'CREATE: NEW COMPETITION POST', active: true, nav:'competition-post' },
        { title: 'CREATE: VOTING POST', active: true, nav:'voting-post' },
        { title: 'CREATE: WINNER POST', active: true, nav: 'winner-post' },
        { title: 'COMPETITION STATUS', active: false, nav:'competition-status' },
    ];

    return (
        <div className="max-w-xl mx-auto p-3">
            <h2 className="text-sm font-semibold mb-3 text-[#000000]">
                WHAT SORT OF PINNED POST WOULD YOU LIKE TO CREATE?
            </h2>
            <div className="grid grid-cols-2 gap-3">
                {options.map((option, index) => (
                    <div
                        key={index}
                        onClick={() => navigate(`/admin/competitions/${option.nav}`)}
                        className={`
                          p-3
                          border-2 
                          border-[#A5A5A5]
                          aspect-square 
                          flex 
                          items-center 
                          justify-center 
                          text-center
                          cursor-pointer
                          transition-all
                          hover:border-[#858585]
                          ${option.active ? 'bg-gray-100 hover:bg-gray-200' : 'bg-white text-gray-400'}
                        `}
                    >
                        <p className="font-medium text-xs text-[#000000]">{option.title}</p>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default Main;