import { useCallback, useEffect, useRef, useState } from "react";
import { Add } from "@mui/icons-material";
import { useNavigate, useLocation } from "react-router-dom";
import S3BucketManger from "../../utils/s3.configUpdated";
import {
  DatePickerComponent,
  TimePickerComponent
} from "../../components/reusuable/DatePicker";
import { notifyError, urlRegex } from "../../utils/helpers.jsx";
import { dispatch } from "../../redux/store.jsx";
import { useSelector } from "react-redux";
import Spinner from "../../components/reusuable/spinner/Spinner.jsx";
import { combineDateAndTime } from "../../utils/combinedDateTime.js";

const CompetitionPost = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const assetRef = useRef(null);
  const videoEl = useRef();

  const [isUpdate, setIsUpdate] = useState(false);
  const [updateData, setUpdateData] = useState(null);
  const [currentFile, setCurrentFile] = useState("");
  const [file, setFile] = useState({ src: "", preview: "" });
  const [s3Loading, setS3Loading] = useState(false);
  const [startDate, setStartDate] = useState(new Date());
  const [startTime, setStartTime] = useState(new Date());
  const [endDate, setEndDate] = useState(new Date());
  const [endTime, setEndTime] = useState(new Date());
  const [title, setTitle] = useState("");
  const [subtitle, setSubtitle] = useState("");
  const [uploadType, setUploadType] = useState("");
  const [disabledVideoUpload, setDisabledVideoUpload] = useState(false);
  const [videoDuration, setVideoDuration] = useState(0);
  const [streamUrlObject, setStreamUrlObject] = useState(null);

  const creatingAnnouncement = useSelector(
    ({ loading }) => loading.effects.announcements.createAnnouncement
  );

  const handleLoadedMetadata = useCallback(() => {{
    const video = videoEl.current;
    if (!video) return;
    if (video.duration > 900) {
      notifyError("Video cannot be more than 15 mins");
      setDisabledVideoUpload(true);

      return;
    }
    setVideoDuration(video.duration);
    setDisabledVideoUpload(false);
      return;
    }
  }, []);

  const handleFileChange = (e) => {
    e.preventDefault();
    const fileSelected = e.target.files[0];
    setCurrentFile(fileSelected);
  };

  const handleUploadAsset = async () => {
    const s3BucketManger = new S3BucketManger();

    if (disabledVideoUpload || !file.src) {
      notifyError("Please upload another video/image");
      return;
    }
    try {
      setS3Loading(true);
      let uploadedDetails;
      if (uploadType === "VIDEO") {
        uploadedDetails = await s3BucketManger.uploadAssets(
          file.src,
          "player-assets",
          `${Date.now()}-${file.src.name}`,
          "video"
        );
      } else {
        uploadedDetails = await s3BucketManger.uploadAssets(
          file.src,
          "player-assets",
          "",
          "photo"
        );
      }
      setStreamUrlObject({...uploadedDetails.streamUrls, duration: videoDuration});
      URL.revokeObjectURL(currentFile);

      setS3Loading(false);
      return uploadedDetails;
    } catch (error) {
      setS3Loading(false);
      notifyError(error.message || "Upload Failed");
    }
  };

  const onSubmit = async (e) => {
    e.preventDefault(file.src);

    try {
      const startDateTime = combineDateAndTime(startDate, startTime).getTime();
      const endDateTime = combineDateAndTime(endDate, endTime).getTime();

      //   Validate start and end date
      if (startDateTime >= endDateTime) {
        notifyError(
          "End date and time should be greater than start date and time"
        );
        return;
      }

      let assetUploaded;

      // Check if src is url or File. if its url make assetUploaded to be a type of boolean
      if (!urlRegex.test(file.src)) {
        assetUploaded = await handleUploadAsset();
      } else {
        assetUploaded = true;
      }

      // If asset Url is undefined, then use streamUrl for payload
      // WHEN THE announcementType IS COMPETITION AND type is VIDEO, USE streamUrl.
      // ELSE USE assetUrl AS KEYS FOR THE REQUEST PAYLOAD

      if (assetUploaded) {
        if ((assetUploaded, title, subtitle, startTime, endTime, uploadType)) {
          //TYPE PHOTO
          const values = {
            assetUrl: assetUploaded?.assetUrl?.replace(/\s+/g, ""),
            title: title,
            subtitle: subtitle,
            commentBoardId: "",
            totalCommentCount: 0,
            startDateTime: startDateTime,
            endDateTime: endDateTime,
            type: uploadType,
            reactedByUsers: [],
            announcementType: "COMPETITION"
          };
          //TYPE VIDEO
          const values2 = {
            streamUrl: streamUrlObject,
            title: title,
            subtitle: subtitle,
            commentBoardId: "",
            totalCommentCount: 0,
            startDateTime: combineDateAndTime(startDate, startTime).getTime(),
            endDateTime: combineDateAndTime(endDate, endTime).getTime(),
            type: uploadType,
            reactedByUsers: [],
            announcementType: "COMPETITION"
          };

          // If the asset is not uploaded, delete the streamUrl and assetUrl from the values object
          // Don't update it
          if (typeof assetUploaded === "boolean") {
            delete values2.streamUrl;
            delete values.assetUrl;
          }

          // Don't update these because they might have been changed from player frontend
          if (isUpdate) {
            delete values2.totalCommentCount;
            delete values.totalCommentCount;
            delete values.reactedByUsers;
            delete values2.reactedByUsers;
            delete values.commentBoardId;
            delete values2.commentBoardId;
          }

          const res = isUpdate
            ? await dispatch.announcements.updateAnnouncement({
                ...(uploadType === "PHOTO" ? values : values2),
                id: updateData.id
              })
            : await dispatch.announcements.createAnnouncement(
                uploadType === "PHOTO" ? values : values2
              );
          if (res === 1) {
            setTitle("");
            setSubtitle("");
            setUploadType("");
            setFile({ src: "", preview: "" });
            setCurrentFile("");
            setStartTime(new Date());
            setStartDate(new Date());
            setEndDate(new Date());
            setEndTime(new Date());
          }
        } else {
          notifyError("All fields are required");
        }
      }
    } catch (error) {
      notifyError(
        error.response.data.message ||
          error.response.message ||
          error.message ||
          error
      );
    }
  };

  async function fetchUpdateData(id) {
    const data = await dispatch.announcements.getSingleAnnouncement(id);
    if (data) {
      setUpdateData(data);
      const isVideo = data.type === "VIDEO";

      const fileUrl = isVideo
        ? data.streamUrl.baseUrl +
          "/" +
          data.streamUrl.key?.split("--")[1] +
          "/thumbnail.png"
        : data.assetUrl;

      const preview = (
        <img
          src={fileUrl}
          alt="feed"
          className="w-full h-full object-cover rounded-[32px] "
        />
      );

      setTitle(data.title);
      setSubtitle(data.subtitle);

      setEndDate(data.endDateTime);
      setEndTime(data.endDateTime);

      setStartDate(data.startDateTime);
      setStartTime(data.startDateTime);

      setUploadType(data.type);

      setFile({
        src: fileUrl,
        preview: preview
      });
    }
  }

  useEffect(() => {
    const imageReg = /[\/.](gif|jpg|jpeg|tiff|png)$/i;
    const ivideoReg = /[\/.](mp4|mov|wmv|avi|mkv|mpeg|webm|flv|quicktime)$/i;
    setFile({ src: "", preview: "" });

    if (ivideoReg.test(currentFile?.type)) {
      const preview = (
        <video
          ref={videoEl}
          key={URL.createObjectURL(currentFile)}
          width={150}
          height={150}
          onLoadedMetadata={handleLoadedMetadata}
        >
          <source
            height={150}
            width={150}
            src={`${URL.createObjectURL(currentFile)}#t=0.001`}
            type="video/mp4"
          />
        </video>
      );
      setUploadType("VIDEO");
      setFile({ src: currentFile, preview });
      return;
    }

    if (imageReg.test(currentFile?.type)) {
      setDisabledVideoUpload(false);
      const preview = (
        <img
          src={URL.createObjectURL(currentFile)}
          alt="feed"
          className="w-full h-full object-cover rounded-[32px] "
        />
      );
      setFile({ src: currentFile, preview });
      setUploadType("PHOTO");
      return;
    }

    if (
      currentFile &&
      (!ivideoReg.test(currentFile?.type) || !imageReg.test(currentFile?.type))
    ) {
      notifyError("Invalid file provided");
    }
  }, [currentFile, handleLoadedMetadata]);

  useEffect(() => {
    const queryParams = new URLSearchParams(location.search);
    const id = queryParams.get("id");

    if (id) {
      setIsUpdate(true);
      fetchUpdateData(id);
    }
  }, [location]);

  return (
    <div>
      {/* Back Link */}
      <div
        className="ml-10 mt-6 text-sm text-[#000000] hover:text-[#000000] cursor-pointer font-bold"
        onClick={() => navigate("/admin/competitions/main")}
      >
        ‹ Back
      </div>
      <div className="max-w-4xl mx-auto p-4 space-y-6">
        {/* Title */}
        <div>
          <h1 className="text-xl text-[#000000] font-semibold mb-2">
            NEW COMPETITION POST
          </h1>
          <p className="text-sm text-[#000000] font-bold">
            Announce a new competition by pinning a post with details to the top
            of the home feed.
          </p>
        </div>

        {/* Form */}
        <form className="">
          {/* Competition Title */}
          <div className="flex space-x-2.5 mb-3">
            <label
              className="text-sm text-[#000000] font-bold"
              htmlFor="competition-title"
            >
              Competition Title:
            </label>
            <input
              type="text"
              id="competition-title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="eg. QOTM of the Month September 2023..."
              className="w-[677px] h-[40px] p-1 bg-gray-100 border border-gray-300 text-sm text-[#000000]"
            />
          </div>

          {/* Pinned Date Range */}

          <div className="flex space-x-2.5 mb-3">
            <label
              className="text-sm text-[#000000] font-bold"
              htmlFor="pinned-from"
            >
              Pinned from Date:
            </label>
            <div className="flex gap-4 items-center space-x-[102px]">
              <DatePickerComponent
                setValue={setStartDate}
                defaultValue={startDate}
              />
              <span className="text-sm text-[#000000] font-bold">At:</span>
              <TimePickerComponent
                setValue={setStartTime}
                defaultValue={startTime}
              />
            </div>
          </div>

          <div className="flex space-x-2.5 mb-3">
            <label
              className="text-sm text-[#000000] font-bold"
              htmlFor="pinned-from"
            >
              Pinned until Date:
            </label>
            <div className="flex gap-4 items-center space-x-[102px]">
              <DatePickerComponent
                defaultValue={endDate}
                setValue={setEndDate}
              />
              <span className="text-sm text-[#000000] font-bold">At:</span>
              <TimePickerComponent
                defaultValue={endTime}
                setValue={setEndTime}
              />
            </div>
          </div>

          {/* Post subtitle */}
          <div className="flex space-x-2.5 mb-3">
            <label
              className="text-sm text-[#000000] font-bold mr-7"
              htmlFor="post-subtitle"
            >
              Post subtitle:
            </label>
            <textarea
              id="post-subtitle"
              placeholder="eg. Submit your highlight to the QOTM competition now! Just tap the button below to get started!"
              className="w-[677px] h-[86px] p-2 bg-gray-100 border border-gray-300 text-sm min-h-[80px] text-[#000000]"
              value={subtitle}
              onChange={(e) => setSubtitle(e.target.value)}
            />
          </div>

          {/* Upload Section */}
          <div className="flex mt-5 space-x-2.5">
            <label className="text-sm text-[#000000] font-bold mr-[20px]">
              Upload Video/
              <br />
              Image:
            </label>
            <div className="flex space-x-0.5 inline-block cursor-pointer">
              <div>
                {file.preview ? (
                  <div className="w-[100px] h-[60px] rounded-b-none">
                    {file.preview}
                    <input
                      ref={assetRef}
                      accept="image/*"
                      type="file"
                      style={{ display: "none" }}
                      onChange={handleFileChange}
                    />
                  </div>
                ) : (
                  <>
                    <div
                      className="p-10 border-[0.5px] border-gray-300  rounded flex items-center justify-center cursor-pointer"
                      onClick={() => {
                        assetRef.current.click();
                      }}
                    >
                      <Add className="w-6 h-6 text-[#000000]" />
                    </div>
                    <input
                      ref={assetRef}
                      accept="image/*"
                      type="file"
                      style={{ display: "none" }}
                      onChange={handleFileChange}
                    />
                  </>
                )}
              </div>
              {file.preview && (
                <div className="flex space-x-2.5 ml-5 mt-[111px]">
                  <div
                    className="text-[#000000] text-sm underline"
                    onClick={() => {
                      URL.revokeObjectURL(file.preview);
                      assetRef.current.click();
                    }}
                  >
                    Replace
                  </div>
                  <div
                    className="text-[#000000] text-sm underline"
                    onClick={() => {
                      URL.revokeObjectURL(file.preview);
                      setFile({ src: "", preview: "" });
                    }}
                  >
                    Delete
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4">
            <div className="px-6 py-2 border border-[#000000] rounded-[12px] hover:bg-gray-50 bg-gray-400  cursor-not-allowed">
              <label className="text-[#000000] cursor-not-allowed font-bold">
                PREVIEW
              </label>
            </div>
            <div
              className="px-6 py-2 cursor-pointer border border-[#000000] rounded-[12px] hover:bg-gray-50"
              onClick={(e) => onSubmit(e)}
            >
              {s3Loading ? (
                <div>
                  <Spinner />{" "}
                  <span className="text-xs text-[#000000]">
                    Uploading Assets
                  </span>
                </div>
              ) : creatingAnnouncement ? (
                <div>
                  <Spinner />{" "}
                  <span className="text-xs text-[#000000]">Submitting</span>
                </div>
              ) : (
                <label className="text-[#000000] font-bold cursor-pointer">
                  SAVE
                </label>
              )}
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CompetitionPost;
