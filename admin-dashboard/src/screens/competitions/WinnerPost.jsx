import { useEffect, useRef, useState, useCallback, useMemo } from "react";
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";
import DraggableIcon from "../../assets/svg/Draggable.jsx";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { notifyError, urlRegex } from "../../utils/helpers.jsx";
import {
  Autocomplete,
  TextField,
  CircularProgress,
  Checkbox
} from "@mui/material";

import {
  DatePickerComponent,
  TimePickerComponent
} from "../../components/reusuable/DatePicker.jsx";
import { Add } from "@mui/icons-material";
import S3BucketManger from "../../utils/s3.configUpdated";
import { combineDateAndTime } from "../../utils/combinedDateTime.js";
import { dispatch } from "../../redux/store.jsx";
import Spinner from "../../components/reusuable/spinner/Spinner.jsx";

const WinnerPost = () => {
  const navigate = useNavigate();
  const assetRef = useRef(null);
  const videoEl = useRef();

  const queryParams = new URLSearchParams(location.search);
  const postId = queryParams.get("id");

  const initialEntries = useMemo(
    () => ({
      id: Date.now().toString(),
      userName: "",
      userId: "",
      text: "",
      orderIndex: 1,
      NotifyShoutOutUser: false
    }),
    []
  );

  const [isUpdate, setIsUpdate] = useState(false);
  const [updateData, setUpdateData] = useState(null);
  const [s3Loading, setS3Loading] = useState(false);
  const [currentFile, setCurrentFile] = useState("");
  const [file, setFile] = useState({ src: "", preview: "" });
  const [title, setTitle] = useState("");
  const [winnerHandle, setWinnerHandle] = useState({
    userId: "",
    userName: ""
  });
  const [subtitle, setSubtitle] = useState("");
  const [startDate, setStartDate] = useState(new Date());
  const [startTime, setStartTime] = useState(new Date());
  const [endDate, setEndDate] = useState(new Date());
  const [endTime, setEndTime] = useState(new Date());
  const [uploadType, setUploadType] = useState("");
  const [disabledVideoUpload, setDisabledVideoUpload] = useState(false);
  const [gettingEntries, setGettingEntries] = useState(false);
  const [videoDuration, setVideoDuration] = useState(0);
  const [streamUrlObject, setStreamUrlObject] = useState(null);
  const [orientation, setVideoOrientation] = useState(null);

  // Entries state
  const [entries, setEntries] = useState([initialEntries]);

  const gettingUsers = useSelector(
    ({ loading }) => loading.effects.user?.userSearch
  );

  const { userList } = useSelector(({ user }) => ({
    userList: user.usersByProjection
  }));

  const fetchAllUserData = async () => {
    await dispatch.user.userSearchByProjection();
  };

  const creatingAnnouncement = useSelector(
    ({ loading }) => loading.effects.announcements.createAnnouncement
  );

  const handleLoadedMetadata = useCallback(() => {
    const video = videoEl.current;
    if (!video) return;
    if (video.duration > 900) {
      notifyError("Video cannot be more than 15 mins");
      setDisabledVideoUpload(true);

      return;
    }
     setVideoOrientation(
      video.videoWidth > video.videoHeight ? "landscape" : "portrait"
    );
    setVideoDuration(video.duration);
    setDisabledVideoUpload(false);
  }, []);

  const handleFileChange = (e) => {
    e.preventDefault();
    const fileSelected = e.target.files[0];
    setCurrentFile(fileSelected);
  };

  const handleUploadAsset = async () => {
    const s3BucketManger = new S3BucketManger();

    if (disabledVideoUpload || !file.src) {
      notifyError("Please upload another video/image");
      return;
    }

    try {
      setS3Loading(true);
      let uploadedDetails;
      if (uploadType === "VIDEO") {
        uploadedDetails = await s3BucketManger.uploadAssets(
          file.src,
          "player-assets",
          `${Date.now()}-${file.src.name}`,
          "video"
        );
        
        uploadedDetails = {
          ...uploadedDetails.streamUrls,
          assetUrl: uploadedDetails.assetUrl,
          duration: videoDuration,
          orientation
        };
        setStreamUrlObject(uploadedDetails);
      } else {
        uploadedDetails = await s3BucketManger.uploadAssets(
          file.src,
          "player-assets",
          "",
          "photo"
        );
      }
     
      URL.revokeObjectURL(currentFile);

      setS3Loading(false);
      return uploadedDetails;
    } catch (error) {
      setS3Loading(false);
      notifyError(error.message || "Upload Failed");
    }
  };

  const handlShoutOutPost = () => {
    try {
      const requestPayloads = entries.map((entry, index) => {
        return {
          userId: entry.userId,
          userName: entry.userName,
          orderIndex: index + 1,
          NotifyShoutOutUser: entry.NotifyShoutOutUser,
          text: entry.text
        };
      });
      return requestPayloads;
    } catch (error) {
      return null;
    }
  };

  const onSubmit = async (e) => {
    e.preventDefault();

    // Validate required fields
    const validateAnyEmpties = entries.some(
      (entry) => !entry.text || !entry.userName || !entry.userId
    );

    if (validateAnyEmpties || !title || !subtitle || !winnerHandle.userId) {
      notifyError("Please fill all the fields and upload all the files");
      return;
    }

    try {
      // Validate dates
      const startDateTime = combineDateAndTime(startDate, startTime).getTime();
      const endDateTime = combineDateAndTime(endDate, endTime).getTime();

      if (startDateTime >= endDateTime) {
        notifyError(
          "End date and time should be greater than start date and time"
        );
        return;
      }

      // Handle asset upload or URL
      let assetData =
      isUpdate && updateData.type === "VIDEO" ? streamUrlObject : null;
      const isUrl = file.src && urlRegex.test(file.src);

      if (!file.src) {
        notifyError("Please provide either a file or URL");
        return;
      }

      if (!isUrl) {
        // Handle file upload
        try {
          assetData = await handleUploadAsset();
          if (!assetData) {
            notifyError("Failed to upload asset");
            return;
          }
        } catch (uploadError) {
          notifyError("Error uploading asset: " + uploadError.message);
          return;
        }
      }

      const shoutOutsRequestPayloads = handlShoutOutPost();

      // Base payload with common properties
      const basePayload = {
        title,
        subtitle,
        commentBoardId: "",
        totalCommentCount: 0,
        startDateTime,
        endDateTime,
        winnerHandle,
        type: uploadType,
        reactedByUsers: [],
        announcementType: "WINNER",
        voteSubmittedAssets: shoutOutsRequestPayloads
      };

      // Create type-specific payload
      let finalPayload;
      if (uploadType === "PHOTO") {
        finalPayload = {
          ...basePayload,
          assetUrl: isUrl ? file.src : assetData?.assetUrl?.replace(/\s+/g, "")
        };
      } else {
        // VIDEO type
        finalPayload = {
          ...basePayload,
          streamUrl: assetData
        };
      }

      // Remove fields that shouldn't be updated
      if (isUpdate) {
        delete finalPayload.totalCommentCount;
        delete finalPayload.reactedByUsers;
        delete finalPayload.commentBoardId;
      }

      // Make API call
      const res = isUpdate
        ? await dispatch.announcements.updateAnnouncement({
            ...finalPayload,
            id: updateData.id
          })
        : await dispatch.announcements.createAnnouncement(finalPayload);

      // Handle successful response
      if (res === 1) {
        // Reset form
        setTitle("");
        setSubtitle("");
        setUploadType("");
        setFile({ src: "", preview: "" }); 
        setCurrentFile("");
        setStartTime(new Date());
        setStartDate(new Date());
        setEndDate(new Date());
        setEndTime(new Date());
        setEntries([initialEntries]);
        navigate("/admin/competitions/competition-status");
      }
    } catch (error) {
      notifyError(
        error?.message || "An error occurred while processing your request"
      );
    }
  };

  async function fetchUpdateData(id) {
    try {
      setGettingEntries(true);
      const data = await dispatch.announcements.getSingleAnnouncement(id);
      if (data) {
        setUpdateData(data);
  
        const isVideo = data.type === "VIDEO";
  
        const fileUrl = isVideo
          ? data.streamUrl.baseUrl +
            "/" +
            data.streamUrl.key?.split("--")[1] +
            "/thumbnail.png"
          : data.assetUrl;
  
        const preview = (
          <img
            src={fileUrl}
            alt="feed"
            className="w-full h-full object-cover rounded-[32px] "
          />
        );
  
        const transformedEntries = data.voteSubmittedAssets.map(
          (asset, index) => ({
            id: Date.now().toString() + index,
            text: asset.text,
            orderIndex: asset.orderIndex,
            userId: asset.userId,
            userName: asset.userName,
            NotifyShoutOutUser: asset.NotifyShoutOutUser
          })
        );
  
        setWinnerHandle({
          userName: data.winnerHandle.userName,
          userId: data.winnerHandle.userId
        }),
          setEntries(transformedEntries);
        setTitle(data.title);
        setSubtitle(data.subtitle);
        setEndDate(data.endDateTime);
        setEndTime(data.endDateTime);
        setStartTime(data.startDateTime);
        setStartDate(data.startDateTime);
        setUploadType(data.type);
        setFile({
          src: fileUrl,
          preview: preview
        });
      }
      setGettingEntries(false);

    } catch (error) {
      console.log(error);
      
      setGettingEntries(false);

    }
   
  }

  const resetState = useCallback(() => {
    setTitle("");
    setSubtitle("");
    setStartTime(new Date());
    setStartDate(new Date());
    setEndDate(new Date());
    setEndTime(new Date());
    setEntries([initialEntries]);
  }, [initialEntries]);

  const onDragEnd = (result) => {
    if (!result.destination) return;

    const items = Array.from(entries);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    setEntries(
      items.map((item, index) => ({
        ...item,
        orderIndex: index + 1
      }))
    );
  };

  const addNewEntry = () => {
    if (entries.some((entry) => !entry?.userName || !entry?.text)) {
      notifyError("Please fill all the fields before adding new entry");
      return;
    }
    setEntries([
      ...entries,
      {
        id: Date.now().toString(),
        userName: "",
        text: "",
        NotifyShoutOutUser: false,
        orderIndex: entries.length,
        userId: "",
        userSearch: ""
      }
    ]);
  };

  const removeEntry = (id) => {
    const entry = entries.find((e) => e.id === id);
    if (entry?.file?.preview) {
      URL.revokeObjectURL(entry.file.preview);
    }
    setEntries(entries.filter((entry) => entry.id !== id));
  };

  /**
   * A function to update the fields of an entry
   * @param {string} id
   * @param {object} fields
   */
  const updateEntry = useCallback((id, fields) => {
    setEntries((prevEntries) =>
      prevEntries.map((entry) =>
        entry.id === id ? { ...entry, ...fields } : entry
      )
    );
  }, []);

  useEffect(() => {
    if (postId) {
      setIsUpdate(true);
      fetchUpdateData(postId);
    }
  }, [postId, resetState]);

  useEffect(() => {
    if (!userList?.length) {
      fetchAllUserData();
    }
  }, [userList]);

  useEffect(() => {
    const imageReg = /[\/.](gif|jpg|jpeg|tiff|png)$/i;
    const ivideoReg = /[\/.](mp4|mov|wmv|avi|mkv|mpeg|webm|flv|quicktime)$/i;
    setFile({ src: "", preview: "" });

    if (ivideoReg.test(currentFile?.type)) {
      const preview = (
        <video
          ref={videoEl}
          key={URL.createObjectURL(currentFile)}
          width={150}
          height={100}
          onLoadedMetadata={handleLoadedMetadata}
        >
          <source
            height={100}
            width={150}
            src={`${URL.createObjectURL(currentFile)}#t=0.001`}
            type="video/mp4"
          />
        </video>
      );
      setUploadType("VIDEO");
      setFile({ src: currentFile, preview });
      return;
    }

    if (imageReg.test(currentFile?.type)) {
      setDisabledVideoUpload(false);
      const preview = (
        <img
          src={URL.createObjectURL(currentFile)}
          alt="feed"
          className="w-full h-full object-cover rounded-[32px] "
        />
      );
      setFile({ src: currentFile, preview });
      setUploadType("PHOTO");
      return;
    }

    if (
      currentFile &&
      (!ivideoReg.test(currentFile?.type) || !imageReg.test(currentFile?.type))
    ) {
      notifyError("Invalid file provided");
    }
  }, [currentFile, handleLoadedMetadata]);

  if (postId && gettingEntries) {
    return (
      <div className="flex justify-center items-center h-screen text-2xl">
        Loading...
      </div>
    );
  }

  return (
    <div>
      {/* Back Link */}
      <div
        className="ml-10 mt-6 text-sm text-[#000000] hover:text-[#000000] cursor-pointer font-bold"
        onClick={() => navigate("/admin/competitions/main")}
      >
        ‹ Back
      </div>
      <div className="max-w-4xl mx-auto p-4 space-y-6">
        {/* Title */}
        <div>
          <h1 className="text-xl text-[#000000] font-semibold mb-2">
            WINNER POST
          </h1>
          <p className="text-sm text-[#000000] font-bold">
            Announce the winner by pinning their winning submission to the top
            of the home feed
          </p>
        </div>

        {/* Form */}
        <form className="">
          {/* Competition Title */}
          <div className="flex space-x-2.5 mb-3">
            <label
              className="text-sm text-[#000000] font-bold"
              htmlFor="competition-title"
            >
              Competition Title:
            </label>
            <input
              type="text"
              id="competition-title"
              placeholder="eg. QOTM of the Month September 2023..."
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="w-[677px] h-[40px] p-1 bg-gray-100 border border-gray-300 text-sm text-[#000000]"
            />
          </div>

          {/* Pinned Date Range */}

          <div className="flex space-x-2.5 mb-3">
            <label
              className="text-sm text-[#000000] font-bold"
              htmlFor="pinned-from"
            >
              Pinned from Date:
            </label>
            <div className="flex gap-4 items-center space-x-[102px]">
              <DatePickerComponent
                setValue={setStartDate}
                defaultValue={startDate}
              />
              <span className="text-sm text-[#000000] font-bold">At:</span>

              <TimePickerComponent
                setValue={setStartTime}
                defaultValue={startTime}
              />
            </div>
          </div>
          <div className="flex space-x-2.5 mb-3">
            <label
              className="text-sm text-[#000000] font-bold"
              htmlFor="pinned-from"
            >
              Pinned until Date:
            </label>
            <div className="flex gap-4 items-center space-x-[102px]">
              <DatePickerComponent
                setValue={setEndDate}
                defaultValue={endDate}
                // isUpdate={isUpdate}
              />
              <span className="text-sm text-[#000000] font-bold">At:</span>

              <TimePickerComponent
                setValue={setEndTime}
                defaultValue={endTime}
                // isUpdate={isUpdate}
              />
            </div>
          </div>

          {/* User handle */}
          <div className="flex space-x-2.5 mb-3">
            <label
              className="text-sm text-[#000000] font-bold mr-8"
              htmlFor="post-subtitle"
            >
              User Handle:
            </label>
            <Autocomplete
              disablePortal
              options={
                userList.map((user) => ({
                  userName: `@${user.firstName}${user.lastName}`,
                  id: user.id
                })) || []
              }
              getOptionLabel={(option) => option.userName}
              sx={{
                width: 450,
                height: 35,
                marginBottom: 2
              }}
              // inputValue={entry.userName}
              value={{
                userId: winnerHandle.userId,
                userName: winnerHandle.userName
              }}
              // onInputChange={(
              //   event,
              //   newInputValue
              // ) => {
              //   if (newInputValue) {
              //     updateEntry(entry.id, {
              //       userName: newInputValue
              //     });
              //   }
              // }}
              // onChange={(event, newValue) => {
              //   if (newValue) {
              //     updateEntry(entry.id, {
              //       userId: newValue.id,
              //       userName: newValue?.userName
              //     });
              //   }
              // }}
              onChange={(event, newValue) => {
                if (newValue) {
                  setWinnerHandle({
                    userId: newValue.id,
                    userName: newValue?.userName
                  });
                }
              }}
              loading={gettingUsers}
              renderInput={(params) => {
                return (
                  <TextField
                    {...params}
                    label={winnerHandle.userName}
                    placeholder={
                      winnerHandle.userName
                        ? winnerHandle.userName
                        : "@userhandle"
                    }
                    InputProps={{
                      ...params.InputProps,
                      endAdornment: (
                        <>
                          {gettingUsers ? (
                            <CircularProgress color="inherit" size={20} />
                          ) : null}
                          {params.InputProps.endAdornment}
                        </>
                      )
                    }}
                  />
                );
              }}
            />
          </div>

          {/* Post subtitle */}
          <div className="flex space-x-2.5 mb-3">
            <label
              className="text-sm text-[#000000] font-bold mr-7"
              htmlFor="post-subtitle"
            >
              Post subtitle:
            </label>
            <textarea
              id="post-subtitle"
              value={subtitle}
              onChange={(e) => setSubtitle(e.target.value)}
              placeholder="eg. Submit your highlight to the QOTM competition now! Just tap the button below to get started!"
              className="w-[677px] h-[86px] p-2 bg-gray-100 border border-gray-300 text-sm min-h-[80px] text-[#000000]"
            />
          </div>

          <div className="flex flex-end space-x-2.5 mb-1 justify-end mr-11">
            <p className="text-xs text-[#000000] mt-3">Notify winner?</p>
            <Checkbox
              // checked={}
              // onChange={(e) => updateEntry(entry.id, 'notifyUser', e.target.checked)}
              sx={{
                color: "#A5A5A5",
                "&.Mui-checked": {
                  color: "#A5A5A5"
                }
              }}
            />
          </div>

          {/* Upload Section */}
          <div className="flex mt-5 space-x-2.5">
            <label className="text-sm text-[#000000] font-bold mr-[20px]">
              Upload Video/
              <br />
              Image:
            </label>
            <div className="flex space-x-0.5 inline-block cursor-pointer">
              <div>
                {file.preview ? (
                  <div className="w-[218px] h-[112px] rounded-b-none">
                    {file.preview}
                    <input
                      ref={assetRef}
                      accept="video/*,image/*"
                      type="file"
                      style={{ display: "none" }}
                      onChange={handleFileChange}
                    />
                  </div>
                ) : (
                  <>
                    <div
                      className="p-7 border-[0.5px] border-gray-300  rounded flex items-center justify-center cursor-pointer"
                      onClick={() => {
                        assetRef.current.click();
                      }}
                    >
                      <Add className="w-6 h-6 text-[#000000]" />
                    </div>
                    <input
                      ref={assetRef}
                      accept="video/*,image/*"
                      type="file"
                      style={{ display: "none" }}
                      onChange={handleFileChange}
                    />
                  </>
                )}
              </div>
              {file.preview && (
                <div className="flex space-x-2.5 ml-5 mt-[111px]">
                  <div
                    className="text-[#000000] text-sm underline"
                    onClick={() => {
                      assetRef.current.click();
                    }}
                  >
                    Replace
                  </div>
                  <div
                    className="text-[#000000] text-sm underline"
                    onClick={() => {
                      setFile({ src: "", preview: "" });
                    }}
                  >
                    Delete
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Special Thanks Section */}
          <div className="flex mt-5 space-x-2.5">
            <label className="text-sm text-[#000000] font-bold mr-[20px]">
              Add Special
              <br />
              Shoutouts
            </label>
            <div className="w-[673px] h-[290px] max-h-[290px] overflow-y-auto no-scrollbar border border-t-0 border-b-0 border-l-0 border-r-0">
              <DragDropContext onDragEnd={onDragEnd}>
                <Droppable droppableId="carousel-entries">
                  {(provided) => (
                    <div
                      {...provided.droppableProps}
                      ref={provided.innerRef}
                      className="space-y-0"
                    >
                      {entries.map((entry, index) => (
                        <Draggable
                          key={entry.id}
                          draggableId={entry.id}
                          index={index}
                        >
                          {(provided) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              className="bg-white shadow pt-4 pr-4 pl-4 pb-2"
                            >
                              <div className="flex items-start gap-x-4 gap-y-0 border border-r-0 border-l-0 border-t-1 border-b-0 border-[#A5A5A5]">
                                <div
                                  {...provided.dragHandleProps}
                                  className="mt-1 -ml-3 cursor-move text-gray-400 hover:text-gray-600"
                                >
                                  <DraggableIcon />
                                </div>

                                <div className="flex-1 space-y-1 mt-3">
                                  <div className="flex space-x-2.5 mb-3">
                                    <label
                                      className="text-sm text-[#000000] font-bold"
                                      htmlFor="User Handle:"
                                    >
                                      User Handle:
                                    </label>
                                    <Autocomplete
                                      disablePortal
                                      options={
                                        userList.map((user) => ({
                                          userName: `@${user.firstName}${user.lastName}`,
                                          id: user.id
                                        })) || []
                                      }
                                      getOptionLabel={(option) =>
                                        option.userName
                                      }
                                      sx={{
                                        width: 450,
                                        height: 35,
                                        marginBottom: 2
                                      }}
                                      // inputValue={entry.userName}
                                      value={{
                                        id: entry.id,
                                        userName: entry.userName
                                      }}
                                      // onInputChange={(
                                      //   event,
                                      //   newInputValue
                                      // ) => {
                                      //   if (newInputValue) {
                                      //     updateEntry(entry.id, {
                                      //       userName: newInputValue
                                      //     });
                                      //   }
                                      // }}
                                      onChange={(event, newValue) => {
                                        if (newValue) {
                                          updateEntry(entry.id, {
                                            userId: newValue.id,
                                            userName: newValue?.userName
                                          });
                                        }
                                      }}
                                      loading={gettingUsers}
                                      renderInput={(params) => {
                                        return (
                                          <TextField
                                            {...params}
                                            label={entry.userName}
                                            placeholder={
                                              entry.userName
                                                ? entry.userName
                                                : "@userhandle"
                                            }
                                            InputProps={{
                                              ...params.InputProps,
                                              endAdornment: (
                                                <>
                                                  {gettingUsers ? (
                                                    <CircularProgress
                                                      color="inherit"
                                                      size={20}
                                                    />
                                                  ) : null}
                                                  {
                                                    params.InputProps
                                                      .endAdornment
                                                  }
                                                </>
                                              )
                                            }}
                                          />
                                        );
                                      }}
                                    />
                                  </div>

                                  <div className="flex space-x-2.5 mb-3">
                                    <label
                                      className="text-sm text-[#000000] font-bold mr-6"
                                      htmlFor="highlightDetails"
                                    >
                                      Special
                                      <br /> Shoutout:
                                    </label>
                                    <textarea
                                      type="text"
                                      id="shoutout"
                                      value={entry.text}
                                      onChange={(e) =>
                                        updateEntry(entry.id, {
                                          text: e.target.value
                                        })
                                      }
                                      placeholder="Enter Shoutout text"
                                      className="w-[450px] h-[35px] p-1 bg-gray-100 border border-gray-300 text-sm text-[#000000]"
                                    />
                                  </div>
                                </div>

                                <p
                                  onClick={() => removeEntry(entry.id)}
                                  className="hover:text-black text-[20px] font-semi-bold text-[#000000] cursor-pointer"
                                >
                                  x
                                </p>
                              </div>
                            </div>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
              </DragDropContext>
            </div>
          </div>

          <p
            onClick={addNewEntry}
            className="text-sm text-[#000000] underline text-center cursor-pointer mt-6"
          >
            + Add new entry
          </p>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4">
            <div className="px-6 py-2 border border-[#000000] rounded-[12px] hover:bg-gray-50 bg-gray-400  cursor-not-allowed">
              <label className="text-[#000000] cursor-not-allowed font-bold">
                PREVIEW
              </label>
            </div>
            <div
              className="px-6 py-2 cursor-pointer border border-[#000000] rounded-[12px] hover:bg-gray-50"
              onClick={(e) => onSubmit(e)}
            >
              {s3Loading ? (
                <div>
                  <Spinner />{" "}
                  <span className="text-xs text-[#000000]">
                    Uploading Assets
                  </span>
                </div>
              ) : creatingAnnouncement ? (
                <div>
                  <Spinner />{" "}
                  <span className="text-xs text-[#000000]">Submitting</span>
                </div>
              ) : (
                <label className="text-[#000000] font-bold cursor-pointer">
                  SAVE
                </label>
              )}
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default WinnerPost;
