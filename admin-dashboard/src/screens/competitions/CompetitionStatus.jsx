import { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { dispatch } from "../../redux/store";
import Spinner from "../../components/reusuable/spinner/Spinner";
import moment from "moment";
import { useNavigate } from "react-router-dom";
import ConfirmationDialog from "../../components/reusuable/Dialog";

const CompetitionStatus = () => {
  const [posts, setPosts] = useState([]);
  const [currentPostUrl, setCurrentPostUrl] = useState("");
  const [activeFilter, setActiveFilter] = useState("all");
  const navigate = useNavigate();
  const now = moment();

  const gettingAnnouncements = useSelector(
    ({ loading }) => loading.effects.announcements.getAllAnnouncements
  );
  const allAnnouncements = useSelector(
    ({ announcements }) => announcements.announcements
  );

  const deletingAnnouncements = useSelector(
    ({ loading }) => loading.effects.announcements.deleteAnnouncement
  );

  const handleFilter = (type) => {
    setActiveFilter(type);

    if (type === "all") {
      setPosts(allAnnouncements);
    }

    if (type === "past") {
      setPosts(allAnnouncements.filter((post) => now > post.endDateTime));
    }

    if (type === "live") {
      setPosts(
        allAnnouncements.filter(
          (post) => now < post.endDateTime && now > post.startDateTime
        )
      );
    }

    if (type === "scheduled") {
      setPosts(allAnnouncements.filter((post) => now < post.startDateTime));
    }

    setCurrentPostUrl("");
  };

  const sortAnnouncements = (type = "byStatus") => {
    const statusRank = {
      SCHEDULED: 1,
      "SUBMISSION LIVE": 2,
      "VOTING LIVE": 2,
      "WINNER LIVE": 2,
      "NO LONGER ACCEPTING SUBMISSION": 3,
      "VOTING ENDED": 3,
      PASSED: 3,
      "Unknown status": 4
    };

    if (type === "byStatus")
      return posts.sort((a, b) => {
        const statusA = getStatus(
          a.announcementType,
          a.startDateTime,
          a.endDateTime
        );
        const statusB = getStatus(
          b.announcementType,
          b.startDateTime,
          b.endDateTime
        );
        return statusRank[statusA] - statusRank[statusB];
      });

    return posts.sort((a, b) => {
      return b.endDateTime - a.endDateTime;
    });
  };

  const unpinAnnouncement = async (id) => {
    // The technique used here is to set the endDateTime to a value that is in the past.
    await dispatch.announcements.updateAnnouncement({
      id,
      endDateTime: Date.now() - 10
    });
    fetchAnnouncements();
  };

  const fetchAnnouncements = async () => {
    const res = await dispatch.announcements.getAllAnnouncements();
    setPosts(res);
    setActiveFilter("all");
  };

  const handleViewPostDetails = async (post) => {
    let baseUrl =
      import.meta.env.VITE_APP_ENV !== "production"
        ? "https://d2upejw2mgdjoj.cloudfront.net/user/announcement?announcementId="
        : "https://playerapp.co/user/announcement?announcementId=";
    setCurrentPostUrl(`${baseUrl}${post.id}`);
  };

  const deleteAnnouncement = async (id) => {
    await dispatch.announcements.deleteAnnouncement(id);
  };

  useEffect(() => {
    fetchAnnouncements();
  }, [deletingAnnouncements]);

  const getStatus = (announcementType, startDateTime, endDateTime) => {
    switch (announcementType) {
      case "COMPETITION":
        // if now is greater than startDateTime and less than endDateTime, return "SUBMISSION LIVE"
        // if now is greater than endDateTime, return "No longer accepting submission"
        // if now is less than startDateTime, return "Scheduled"
        return now > startDateTime && now < endDateTime
          ? "SUBMISSION LIVE"
          : now > endDateTime
          ? "NO LONGER ACCEPTING SUBMISSION"
          : "SCHEDULED";
      case "VOTE":
        // if now is greater than endDateTime, return "VOTING ENDED"
        // if now is less than endDateTime and greater than startDateTime, return "VOTING LIVE"
        // if now is less than startDateTime, return "Scheduled"
        return now > endDateTime
          ? "VOTING ENDED"
          : now < endDateTime && now > startDateTime
          ? "VOTING LIVE"
          : "SCHEDULED";
      case "WINNER":
        // if now is greater than endDateTime, return "PASSED"
        // if now is less than endDateTime and greater than startDateTime, return "WINNER LIVE"
        // if now is less than startDateTime, return "SCHEDULED"
        return now > endDateTime
          ? "PASSED"
          : now < endDateTime && now > startDateTime
          ? "WINNER LIVE"
          : "SCHEDULED";
      case "PINNED_POST":
        // if now is greater than endDateTime, return "PASSED"
        // if now is less than endDateTime and greater than startDateTime, return "WINNER LIVE"
        // if now is less than startDateTime, return "SCHEDULED"
        return now > endDateTime
          ? "PASSED"
          : now < endDateTime && now > startDateTime
          ? "POST IS LIVE"
          : "SCHEDULED";
      default:
        return "Unknown status";
    }
  };

  const onAnnouncementEdit = (item) => {
    switch (item.announcementType) {
      case "COMPETITION":
        navigate(`/admin/competitions/competition-post?id=${item.id}`);
        break;
      case "VOTE":
        navigate(`/admin/competitions/voting-post?id=${item.id}`);
        break;
      case "WINNER":
        navigate(`/admin/competitions/winner-post?id=${item.id}`);
        break;
      case "PINNED_POST":
        navigate(`/admin/pinned-post?id=${item.id}`);
        break;
      default:
        break;
    }
  }

  return (
    <div>
      {/* Back Button */}
      <div
        className="mx-14 my-5 text-sm text-[#000000] hover:text-[#000000] cursor-pointer font-bold"
        onClick={() => navigate("/admin/competitions/main")}
      >
        ‹ Back
      </div>
      <div className="w-full p-4 space-y-4 mx-10">
        <div className="grid grid-cols-[70%_30%] gap-4 ">
          <div className=" text-[#000000] mb-2 flex flex-col">
            <h1 className="text-[24px] font-[700] ">
              {" "}
              REVIEW THE STATUS OF LIVE, PAST AND SCHEDULED PINNED POSTS
            </h1>
            <div className="flex w-[250px] justify-between mt-4 self-end">
              <p
                className={`${
                  activeFilter === "all" ? "underline" : ""
                } cursor-pointer`}
                onClick={() => handleFilter("all")}
              >
                All
              </p>
              <p
                className={`${
                  activeFilter === "past" ? "underline" : ""
                } cursor-pointer`}
                onClick={() => handleFilter("past")}
              >
                Past
              </p>
              <p
                className={`${
                  activeFilter === "live" ? "underline" : ""
                } cursor-pointer`}
                onClick={() => handleFilter("live")}
              >
                live
              </p>
              <p
                className={`${
                  activeFilter === "scheduled" ? "underline" : ""
                } cursor-pointer`}
                onClick={() => handleFilter("scheduled")}
              >
                Scheduled
              </p>
            </div>
          </div>
          <div>
            <p className="text-2xl font-bold text-center">Preview section</p>
          </div>
        </div>

        <div>
          {posts.length < 1 && (
            <p className="text-[#000000]">No Annoucement made yet</p>
          )}
        </div>

        {(gettingAnnouncements || deletingAnnouncements) && (
          <Spinner waitingText={"Loading..."} type={"ring"} />
        )}

        <div className=" grid grid-cols-[70%_27%] gap-4 overflow-y-scroll">
          <div className="h-[75vh] overflow-y-scroll py-10">
            {sortAnnouncements('byEndDateTime').map((item, index) => (
              <div key={index} className=" border-t-[2px] border-[#A5A5A5]">
                <div className="flex justify-between mt-[2px]">
                  <div>
                    <p className="text-[#000000] font-bold text-[16px]">
                      {item.announcementType}: {item.title}
                    </p>
                    <p className="flex text-[#000000] text-[16px]">
                      <p className="font-bold mr-[4px]">Status:</p>
                      {getStatus(
                        item.announcementType,
                        item.startDateTime,
                        item.endDateTime
                      )}
                    </p>
                    <div className="flex">
                      <ConfirmationDialog
                        OpenerComponent={
                          <div className="w-[92px] h-[39px] cursor-pointer border border-[#ACACAC] rounded-[12px] hover:bg-gray-50 flex items-center justify-center mt-3">
                            <label className="text-[#535252] font-bold cursor-pointer text-[12px] text-center">
                              Delete
                            </label>
                          </div>
                        }
                        onSubmit={() => deleteAnnouncement(item.id)}
                        title={"Delete Announcement"}
                        cancelActionText="Cancel"
                      >
                        <p>
                          Are you sure you want to delete this announcement?
                        </p>
                      </ConfirmationDialog>

                      {moment() < item.endDateTime && (
                        <div
                          className="ml-[4px] w-[92px] h-[39px] border border-[#ACACAC] rounded-[12px]  hover:text-black flex items-center justify-center mt-3"
                          onClick={() => unpinAnnouncement(item.id)}
                        >
                          <label className="text-[#ACACAC] hover:text-black focus:text-black font-bold text-[12px] text-center">
                            Unpin Post
                          </label>
                        </div>
                      )}
                    </div>
                  </div>
                  {/**Pinned dates */}
                  <div className="flex flex-col items-end pb-2">
                    <div>
                      <div className="flex">
                        <p className="text-[#000000] text-[16px] font-[700] mr-[3px]">
                          Pinned Start:{" "}
                        </p>
                        <span className="text-[#000000] text-[13px] font-[400]">
                          {moment(item.startDateTime).format("L")}
                        </span>
                        <p className="text-[#000000] text-[16px] font-[700] mr-[3px] ml-[3px]">
                          |
                        </p>
                        <p className="text-[#000000] text-[16px] font-[700] mr-[3px] ml-[3px]">
                          At:{" "}
                        </p>
                        <span className="text-[#000000] text-[13px] font-[400]">
                          {moment(item.startDateTime).format("hh:mm A")}
                        </span>
                      </div>

                      <div className="flex ml-[9px]">
                        <p className="text-[#000000] text-[16px] font-[700] mr-[3px]">
                          Pinned End:{" "}
                        </p>
                        <span className="text-[#000000] text-[13px] font-[400]">
                          {moment(item.endDateTime).format("L")}
                        </span>
                        <p className="text-[#000000] text-[16px] font-[700] mr-[3px] ml-[3px]">
                          |
                        </p>
                        <p className="text-[#000000] text-[16px] font-[700] mr-[3px] ml-[3px]">
                          At:{" "}
                        </p>
                        <span className="text-[#000000] text-[13px] font-[400]">
                          {moment(item.endDateTime).format("hh:mm A")}
                        </span>
                      </div>
                    </div>
                    <div className="flex">
                      <div
                        className="ml-[4px] w-[92px] h-[39px] border border-red-500 rounded-[12px] hover:text-black flex items-center justify-center mt-3 mr-4 cursor-pointer"
                        onClick={() => {
                          onAnnouncementEdit(item);
                        }}
                      >
                        <label className="text-red-500 hover:text-red-500 focus:text-red-500 font-bold text-[12px] text-center cursor-pointer">
                          Edit
                        </label>
                      </div>

                      <div className="w-[127px] h-[39px] cursor-pointer border border-[#000000] rounded-[12px] hover:bg-gray-50 flex items-center justify-center mt-3">
                        <label
                          onClick={() => handleViewPostDetails(item)}
                          className="text-[#000000] font-bold cursor-pointer text-[12px] text-center"
                        >
                          Preview Post
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="border-2 rounded-t-2xl shadow-2xl">
            <iframe
              src={currentPostUrl}
              className="w-[100%] h-[100%] rounded-[12px]"
            >
              COMING SOON
            </iframe>
            <div className="w-[100%] h-[5%] rounded-b-full bg-white shadow-2xl  bottom-0 flex justify-center">
              {" "}
              <div className="border-2 border-black opacity-40 rounded-full w-[30px] h-[30px] flex items-center justify-center" />{" "}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CompetitionStatus;
