import { useEffect, useRef, useState, useCallback, useMemo } from "react";
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";
import { useNavigate, useLocation } from "react-router-dom";
import S3BucketManger from "../../utils/s3.config";
import {
  DatePickerComponent,
  TimePickerComponent
} from "../../components/reusuable/DatePicker.jsx";
import {
  notifyError,
  pickFromArrayOfObjects,
  reorder
} from "../../utils/helpers.jsx";
import DraggableIcon from "../../assets/svg/Draggable.jsx";
import { Add } from "@mui/icons-material";
import {
  Checkbox,
  Autocomplete,
  TextField,
  CircularProgress
} from "@mui/material";
import { combineDateAndTime } from "../../utils/combinedDateTime.js";
import { dispatch } from "../../redux/store.jsx";
import { useSelector } from "react-redux";
import Spinner from "../../components/reusuable/spinner/Spinner.jsx";

const VotingPost = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const fileInputRefs = useRef({});

  const queryParams = new URLSearchParams(location.search);
  const postId = queryParams.get("id");

  const initialEntries = useMemo(
    () => ({
      id: Date.now().toString(),
      text: "",
      file: {
        src: "",
        preview: "",
        type: "",
        duration: 0
      },
      shouldNotifyUser: false,
      orderIndex: 1,
      streamUrl: "",
      assetType: "",
      userId: "",
      userName: "",
      uploaded: false,
      uploadedStatus: false
    }),
    []
  );

  // Form states
  const [isUpdate, setIsUpdate] = useState(false);
  const [updateData, setUpdateData] = useState(null);
  const [title, setTitle] = useState("");
  const [subTitle, setSubTitle] = useState("");
  const [startDate, setStartDate] = useState(new Date());
  const [startTime, setStartTime] = useState(new Date());
  const [endDate, setEndDate] = useState(new Date());
  const [endTime, setEndTime] = useState(new Date());
  const [s3Loading, setS3Loading] = useState(false);
  const [entries, setEntries] = useState([initialEntries]);

  // Selectors
  const gettingUsers = useSelector(
    ({ loading }) => loading.effects.user?.userSearch
  );
  const { userList } = useSelector(({ user }) => ({
    userList: user.usersByProjection || []
  }));
  const creatingAnnouncement = useSelector(
    ({ loading }) =>
      loading.effects.announcements.createAnnouncement ||
      loading.effects.announcements.updateAnnouncement
  );

  const handleLoadedMetadata = (video) => {
    if (video.duration > 900) {
      notifyError("Video cannot be more than 15 mins");
      return false;
    }
    return true;
  };

  const handleFileChange = async (id, e) => {
    e.preventDefault();
    const file = e.target.files[0];
    if (!file) return;

    const isVideo = file.type.includes("video");
    const isImage = file.type.includes("image");

    if (!isVideo && !isImage) {
      notifyError("Please upload only images or videos");
      return;
    }

    const previewUrl = URL.createObjectURL(file);
    const videoElement = document.createElement("video");

    if (isVideo) {
      videoElement.preload = "metadata";

      const validateVideo = () =>
        new Promise((resolve) => {
          videoElement.onloadedmetadata = (data) => {
            const isValid = handleLoadedMetadata(videoElement, file);
            resolve(isValid);
          };
        });

      videoElement.src = previewUrl;
      const isValid = await validateVideo();

      if (!isValid) {
        URL.revokeObjectURL(previewUrl);
        return;
      }
    }

    const duration = (videoElement, file) => {
      return videoElement.duration;
    };

    setEntries(
      entries.map((entry) => {
        const newEntry = {
          ...entry,
          file: {
            src: file,
            preview: previewUrl,
            type: isVideo ? "VIDEO" : "PHOTO",
            duration: duration(videoElement, file)
          },
          assetType: isVideo ? "VIDEO" : "PHOTO",
          uploadedStatus: false
        };

        if (!isVideo) delete newEntry.file.duration;

        return entry.id === id ? newEntry : entry;
      })
    );
  };

  async function uploadAsset(file) {
    const s3BucketManger = new S3BucketManger();

    if (!file.src) {
      notifyError("Please upload another video/image");
      return;
    }

    try {
      setS3Loading(true);
      let uploadedDetails;
      if (file?.type === "VIDEO") {
        uploadedDetails = await s3BucketManger.uploadAssets(
          file.src,
          "player-assets",
          `${Date.now()}-${file?.src.name}`,
          "video"
        );
      } else {
        uploadedDetails = await s3BucketManger.uploadAssets(
          file?.src,
          "player-assets",
          "",
          "photo"
        );
      }

      setS3Loading(false);
      const upload = { ...uploadedDetails, duration: file?.duration };
      return upload;
    } catch (error) {
      setS3Loading(false);
      notifyError(error.message || "Upload Failed");
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault();

    const validateAnyEmpties = entries.some(
      (entry) =>
        !entry.text ||
        !entry.uploadedStatus ||
        !entry.userName ||
        !entry.userId
    );

    if (validateAnyEmpties || !title || !subTitle) {
      notifyError("Please fill all the fields and upload all the files");
      return;
    }

    try {
      const startDateTime = combineDateAndTime(startDate, startTime).getTime();
      const endDateTime = combineDateAndTime(endDate, endTime).getTime();

      const payload = {
        title,
        subtitle: subTitle,
        startDateTime,
        endDateTime,
        announcementType: "VOTE",
        type: "VIDEO"
      };

      if (startDateTime >= endDateTime) {
        notifyError(
          "End date and time should be greater than start date and time"
        );
        return;
      }

      for (const key in payload) {
        if (Object.prototype.hasOwnProperty.call(payload, key)) {
          if (!payload[key]) {
            notifyError("Please fill all fields");
            return;
          }
        }
      }

      const voteSubmittedAssets = entries.map((entry) => ({
        streamUrl: entry.streamUrl,
        assetUrl: entry.assetUrl,
        assetType: entry.assetType,
        userId: entry.userId,
        userName: entry.userName,
        orderIndex: entry.orderIndex,
        shouldNotifyUser: entry.shouldNotifyUser,
        text: entry.text
      }));

      const res = isUpdate
        ? await dispatch.announcements.updateAnnouncement({
            ...payload,
            voteSubmittedAssets,
            id: updateData.id
          })
        : await dispatch.announcements.createAnnouncement({
            ...payload,
            voteSubmittedAssets
          });

      if (res === 1) {
        resetState();
        navigate("/admin/competitions/main");
      }
    } catch (error) {
      notifyError(error);
    }
  };

  const resetState = useCallback(() => {
    setTitle("");
    setSubTitle("");
    setStartTime(new Date());
    setStartDate(new Date());
    setEndDate(new Date());
    setEndTime(new Date());
    setEntries([initialEntries]);
  }, [initialEntries]);

  const onDragEnd = (result) => {
    if (!result.destination) return;

    const items = reorder(
      entries,
      result.source.index,
      result.destination.index
    );

    setEntries(
      items.map((item, index) => ({
        ...item,
        orderIndex: index + 1
      }))
    );
  };

  const addNewEntry = () => {
    if (
      entries.some(
        (entry) =>
          !entry?.userName ||
          !entry?.text ||
          (!entry?.uploadedStatus && !isUpdate)
      )
    ) {
      notifyError(
        "Please fill all the fields and UPLOAD all videos/images before adding new entry"
      );
      return;
    }

    setEntries((prevEntries) => [
      ...prevEntries,
      {
        ...initialEntries,
        orderIndex: prevEntries.length + 1,
        id: Date.now().toString()
      }
    ]);
  };

  const removeEntry = (id) => {
    const entry = entries.find((e) => e.id === id);
    if (entry?.file?.preview) {
      URL.revokeObjectURL(entry.file.preview);
    }
    setEntries(entries.filter((entry) => entry.id !== id));
  };

  const updateEntry = useCallback((id, fields) => {
    setEntries((prevEntries) =>
      prevEntries.map((entry) =>
        entry.id === id ? { ...entry, ...fields } : entry
      )
    );
  }, []);

  const removeFile = (id) => {
    setEntries(
      entries.map((entry) => {
        if (entry.id === id) {
          URL.revokeObjectURL(entry.file.preview);
        }

        return entry.id === id
          ? {
              ...entry,
              file: {
                src: "",
                preview: ""
              },
              uploadedStatus: false,
              streamUrl: "",
              assetUrl: ""
            }
          : entry;
      })
    );
  };

  const fetchAllUserData = async () => {
    await dispatch.user.userSearchByProjection();
  };

  async function fetchUpdateData(id) {
    const data = await dispatch.announcements.getSingleAnnouncement(id);
    if (data) {
      setUpdateData(data);

      // Transform the data for entries
      const transformedEntries = data.voteSubmittedAssets.map(
        (asset, index) => ({
          id: Date.now().toString() + index,
          text: asset.text,
          file: {
            src: "",
            preview: asset.assetUrl || asset?.streamUrl?.baseUrl,
            type: asset.assetType
          },
          shouldNotifyUser: asset.shouldNotifyUser,
          orderIndex: asset.orderIndex,
          streamUrl: asset.streamUrl,
          assetUrl: asset.assetUrl,
          assetType: asset.assetType,
          userId: asset.userId,
          userName: asset.userName,
          uploaded: true,
          uploadedStatus: true
        })
      );

      setEntries(transformedEntries);
      setTitle(data.title);
      setSubTitle(data.subtitle);
      setEndDate(data.endDateTime);
      setEndTime(data.endDateTime);
      setStartTime(data.startDateTime);
      setStartDate(data.startDateTime);
    }
  }

  useEffect(() => {
    if (!userList?.length) {
      fetchAllUserData();
    }
  }, [userList]);

  useEffect(() => {
    if (postId) {
      setIsUpdate(true);
      fetchUpdateData(postId);
    } else {
      resetState();
    }
  }, [location, resetState, postId]);

  // Check for the postid, then check if entries is 1 and uploaded is false. this way we know if its getting the data from the backend or not
  if (postId && entries.length === 1 && !entries[0].uploaded) {
    return (
      <div className="flex justify-center items-center h-screen text-2xl">
        Loading...
      </div>
    );
  }

  return (
    <div>
      {/* Back Link */}
      <div
        className="ml-10 mt-6 text-sm text-[#000000] hover:text-[#000000] cursor-pointer font-bold"
        onClick={() => navigate("/admin/competitions/main")}
      >
        ‹ Back
      </div>
      <div className="max-w-4xl mx-auto p-4 space-y-6">
        {/* Title */}
        <div>
          <h1 className="text-xl text-[#000000] font-semibold mb-2">
            VOTING POST
          </h1>
          <p className="text-sm text-[#000000] font-bold">
            Pin a carousel to the top of the home feed.
          </p>
        </div>

        {/* Form */}
        <form className="">
          {/* Competition Title */}
          <div className="flex space-x-2.5 mb-3">
            <label
              className="text-sm text-[#000000] font-bold"
              htmlFor="competition-title"
            >
              Competition Title:
            </label>
            <input
              type="text"
              id="competition-title"
              placeholder="eg. QOTM of the Month September 2023..."
              className="w-[677px] h-[40px] p-1 bg-gray-100 border border-gray-300 text-sm text-[#000000]"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
            />
          </div>

          {/* Pinned Date Range */}
          <div className="flex space-x-2.5 mb-3">
            <label
              className="text-sm text-[#000000] font-bold"
              htmlFor="pinned-from"
            >
              Pinned from Date:
            </label>
            <div className="flex gap-4 items-center space-x-[102px]">
              <DatePickerComponent
                setValue={setStartDate}
                defaultValue={startDate}
              />
              <span className="text-sm text-[#000000] font-bold">At:</span>
              <TimePickerComponent
                setValue={setStartTime}
                defaultValue={startTime}
              />
            </div>
          </div>

          <div className="flex space-x-2.5 mb-3">
            <label
              className="text-sm text-[#000000] font-bold"
              htmlFor="pinned-from"
            >
              Pinned until Date:
            </label>
            <div className="flex gap-4 items-center space-x-[102px]">
              <DatePickerComponent
                setValue={setEndDate}
                defaultValue={endDate}
                isUpdate={isUpdate}
              />
              <span className="text-sm text-[#000000] font-bold">At:</span>
              <TimePickerComponent
                setValue={setEndTime}
                defaultValue={endTime}
                isUpdate={isUpdate}
              />
            </div>
          </div>

          {/* Caption */}
          <div className="flex space-x-2.5 mb-3">
            <label
              className="text-sm text-[#000000] font-bold mr-11"
              htmlFor="post-caption"
            >
              Base Copy:
            </label>
            <textarea
              id="post-caption"
              placeholder="eg. VOTING NOW OPEN!"
              value={subTitle}
              onChange={(e) => setSubTitle(e.target.value)}
              className="w-[677px] h-[40px] p-2 bg-gray-100 border border-gray-300 text-sm min-h-[40px] text-[#000000]"
            />
          </div>

          <div className="flex flex-end space-x-2.5 mb-3 justify-end mr-11">
            <p className="text-xs text-[#000000] mt-3">
              Randomise carousel order?
            </p>
            <Checkbox
              // checked={}
              // onChange={(e) => updateEntry(entry.id, 'notifyUser', e.target.checked)}
              sx={{
                color: "#A5A5A5",
                "&.Mui-checked": {
                  color: "#A5A5A5"
                }
              }}
            />
          </div>

          {/* Carousel Section */}
          <div className="flex mt-5 space-x-2.5">
            <label className="text-sm text-[#000000] font-bold mr-[20px]">
              Add content to
              <br />
              the Carousel
            </label>
            <div className="w-[673px] h-[290px] max-h-[290px] overflow-y-auto no-scrollbar border border-t-0 border-b-0 border-l-0 border-r-0">
              <DragDropContext onDragEnd={onDragEnd}>
                <Droppable droppableId="carousel-entries">
                  {(provided) => (
                    <div
                      {...provided.droppableProps}
                      ref={provided.innerRef}
                      className="space-y-0"
                    >
                      {entries.map((entry, index) => {
                        console.log(entry);
                        
                        return (
                          <Draggable
                            key={entry.id}
                            draggableId={entry.id}
                            index={index}
                          >
                            {(provided) => (
                              <div
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                className="bg-white shadow pt-4 pr-4 pl-4 pb-2"
                              >
                                <div className="flex items-start gap-x-4 gap-y-0 border border-r-0 border-l-0 border-t-1 border-b-0 border-[#A5A5A5]">
                                  <div
                                    {...provided.dragHandleProps}
                                    className="mt-1 -ml-3 cursor-move text-gray-400 hover:text-gray-600"
                                  >
                                    <DraggableIcon />
                                  </div>

                                  <div className="flex-1 space-y-1 mt-3">
                                    <div className="flex space-x-2.5 mb-3">
                                      <label
                                        className="text-sm text-[#000000] font-bold"
                                        htmlFor="User Handle:"
                                      >
                                        User Handle:
                                      </label>

                                      <Autocomplete
                                        disablePortal
                                        options={
                                          userList.map((user) => ({
                                            userName: `@${user.firstName}${user.lastName}`,
                                            id: user.id
                                          })) || []
                                        }
                                        getOptionLabel={(option) =>
                                          option.userName
                                        }
                                        sx={{
                                          width: 450,
                                          height: 35,
                                          marginBottom: 2
                                        }}
                                        // inputValue={entry.userName}
                                        value={{
                                          id: entry.id,
                                          userName: entry.userName
                                        }}
                                        // onInputChange={(
                                        //   event,
                                        //   newInputValue
                                        // ) => {
                                        //   if (newInputValue) {
                                        //     updateEntry(entry.id, {
                                        //       userName: newInputValue
                                        //     });
                                        //   }
                                        // }}
                                        onChange={(event, newValue) => {
                                          if (newValue) {
                                            updateEntry(entry.id, {
                                              userId: newValue.id,
                                              userName: newValue?.userName
                                            });
                                          }
                                        }}
                                        loading={gettingUsers}
                                        renderInput={(params) => {
                                          return (
                                            <TextField
                                              {...params}
                                              label={entry.userName}
                                              placeholder={
                                                entry.userName
                                                  ? entry.userName
                                                  : "@userhandle"
                                              }
                                              InputProps={{
                                                ...params.InputProps,
                                                endAdornment: (
                                                  <>
                                                    {gettingUsers ? (
                                                      <CircularProgress
                                                        color="inherit"
                                                        size={20}
                                                      />
                                                    ) : null}
                                                    {
                                                      params.InputProps
                                                        .endAdornment
                                                    }
                                                  </>
                                                )
                                              }}
                                            />
                                          );
                                        }}
                                      />
                                    </div>

                                    <div className="flex space-x-2.5 mb-3">
                                      <label
                                        className="text-sm text-[#000000] font-bold mr-6"
                                        htmlFor="highlightDetails"
                                      >
                                        Highlight <br /> details:
                                      </label>
                                      <input
                                        type="text"
                                        id="highlightDetails"
                                        value={entry.text}
                                        onChange={(e) =>
                                          updateEntry(entry.id, {
                                            text: e.target.value
                                          })
                                        }
                                        placeholder="Enter highlight details"
                                        className="w-[450px] h-[35px] p-1 bg-gray-100 border border-gray-300 text-sm text-[#000000]"
                                      />
                                    </div>

                                    {/* Upload Section */}
                                    <div className="flex mt-5 space-x-2.5">
                                      <label className="text-sm text-[#000000] font-bold mr-[20px]">
                                        Upload <br /> Video/Image:
                                      </label>
                                      <div className="flex space-x-0.5 inline-block cursor-pointer">
                                        <div>
                                          {entry?.file?.preview ? (
                                            <div className="w-[134px] h-[80px] rounded-b-none">
                                              {entry?.file.type === "VIDEO" ? (
                                                <video
                                                  className="w-[150px] h-[70px] rounded-b-none"
                                                  // src={entry?.file?.preview}
                                                  src={
                                                    updateData && entry?.uploadedStatus
                                                      ? entry?.assetUrl
                                                      : entry?.file?.preview
                                                  }
                                                  controls={true}
                                                ></video>
                                              ) : (
                                                <img
                                                  src={
                                                    updateData && entry?.uploadedStatus
                                                      ? entry?.assetUrl
                                                      : entry?.file?.preview
                                                  }
                                                  alt="Preview"
                                                  className="w-full h-full object-cover"
                                                />
                                              )}
                                              <input
                                                ref={(el) =>
                                                  (fileInputRefs.current[
                                                    entry.id
                                                  ] = el)
                                                }
                                                accept="video/*,image/*"
                                                type="file"
                                                style={{ display: "none" }}
                                                onChange={(e) =>
                                                  handleFileChange(entry.id, e)
                                                }
                                              />
                                            </div>
                                          ) : (
                                            <>
                                              <div
                                                className="p-7 border-[0.5px] border-gray-300 rounded flex items-center justify-center cursor-pointer"
                                                onClick={() =>
                                                  fileInputRefs.current[
                                                    entry.id
                                                  ]?.click()
                                                }
                                              >
                                                <Add className="w-6 h-6 text-[#000000]" />
                                              </div>
                                              <input
                                                ref={(el) =>
                                                  (fileInputRefs.current[
                                                    entry.id
                                                  ] = el)
                                                }
                                                accept="video/*,image/*"
                                                type="file"
                                                style={{ display: "none" }}
                                                onChange={(e) =>
                                                  handleFileChange(entry.id, e)
                                                }
                                              />
                                            </>
                                          )}
                                        </div>
                                        <div className="flex space-x-2.5">
                                          {entry?.file?.preview && (
                                            <div className="flex space-x-2.5 ml-5">
                                              <div
                                                className="text-[#000000] text-xs underline"
                                                onClick={() => {
                                                  fileInputRefs.current[
                                                    entry.id
                                                  ]?.click();
                                                }}
                                              >
                                                Replace
                                              </div>
                                              <div
                                                className="text-[#000000] text-xs underline"
                                                onClick={() =>
                                                  removeFile(entry.id)
                                                }
                                              >
                                                Delete
                                              </div>
                                              <div
                                                className="text-[#000000] text-xs underline"
                                                onClick={async () => {
                                                  if (!entry.uploadedStatus) {
                                                    const res =
                                                      await uploadAsset(
                                                        entry?.file
                                                      );
                                                    updateEntry(entry.id, {
                                                      assetUrl: res.assetUrl,
                                                      streamUrl: {
                                                        ...res.streamUrls,
                                                        duration: res?.duration
                                                      },
                                                      uploadedStatus: true
                                                    });
                                                  } else {
                                                    notifyError(
                                                      "Can't upload more than one asset per entry."
                                                    );
                                                  }
                                                }}
                                              >
                                                {s3Loading &&
                                                  entries[index].id ===
                                                    entry.id && (
                                                    <div>
                                                      <Spinner />{" "}
                                                      <span className="text-xs text-[#000000]">
                                                        Uploading Assets
                                                      </span>
                                                    </div>
                                                  )}

                                                {entry.uploadedStatus &&
                                                  entries[index].id ===
                                                    entry.id && (
                                                    <label className="text-[#000000] text-xs font-normal cursor-pointer">
                                                      Uploaded
                                                    </label>
                                                  )}

                                                {!entry.uploadedStatus &&
                                                  entries[index].id ===
                                                    entry.id && (
                                                    <label className="text-[#000000] text-xs font-normal cursor-pointer">
                                                      Upload
                                                    </label>
                                                  )}
                                              </div>
                                            </div>
                                          )}
                                          <div className="flex space-x-2.5 mt-[48px] justify-center">
                                            <p
                                              className={
                                                entry?.file?.preview
                                                  ? "text-xs text-[#000000] mt-3 -ml-2"
                                                  : "text-xs text-[#000000] ml-[100px] mt-3"
                                              }
                                            >
                                              Notify user of shortlist inclusion
                                            </p>
                                            <Checkbox
                                              checked={entry.notifyUser}
                                              onChange={(e) =>
                                                updateEntry(entry.id, {
                                                  notifyUser: e.target.checked
                                                })
                                              }
                                              sx={{
                                                color: "#A5A5A5",
                                                "&.Mui-checked": {
                                                  color: "#A5A5A5"
                                                }
                                              }}
                                            />
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>

                                  <p
                                    onClick={() => removeEntry(entry.id)}
                                    className="hover:text-black text-[20px] font-semi-bold text-[#000000] cursor-pointer"
                                  >
                                    x
                                  </p>
                                </div>
                              </div>
                            )}
                          </Draggable>
                        );
                      })}
                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
              </DragDropContext>
            </div>
          </div>

          <p
            onClick={addNewEntry}
            className="text-sm text-[#000000] underline text-center cursor-pointer mt-6"
          >
            + Add new entry
          </p>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4">
            <div className="px-6 py-2 border border-[#000000] rounded-[12px] hover:bg-gray-50 bg-gray-400  cursor-not-allowed">
              <label className="text-[#000000] cursor-not-allowed font-bold">
                PREVIEW
              </label>
            </div>
            <div
              className="px-6 py-2 cursor-pointer border border-[#000000] rounded-[12px] hover:bg-gray-50"
              onClick={(e) => handleSubmit(e)}
            >
              {creatingAnnouncement ? (
                <div>
                  <Spinner />{" "}
                  <span className="text-xs text-[#000000]">Submitting</span>
                </div>
              ) : (
                <label className="text-[#000000] font-bold cursor-pointer">
                  SAVE
                </label>
              )}
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default VotingPost;
