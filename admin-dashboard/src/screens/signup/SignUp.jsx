/* eslint-disable react-refresh/only-export-components */
/* eslint-disable no-restricted-globals */
import { useFormik } from "formik";
import { signupSchema } from "../../utils/formSchema";
import Button from "../../components/reusuable/Button";
import Input from "../../components/reusuable/Input";
import { Link, Form, useNavigate } from "react-router-dom";
import { dispatch } from "../../redux/store";

export async function action({ request }) {
  const formData = await request.formData();
  const formItems = Object.fromEntries(formData);

  return { formItems };
}

const SignUp = () => {
  const navigate = useNavigate();

  const initialValues = {
    email: "",
    password: "",
    firstName: "",
    lastName: "",
  };

  const onSubmit = async (values) => {
    const modifiedValues = { ...values, ["userType"]: "ADMIN" };
    await dispatch.auth.userSignup({ navigate, values: modifiedValues });
  };
  const {
    values,
    errors,
    touched,
    isSubmitting,
    handleSubmit,
    handleBlur,
    handleChange,
  } = useFormik({
    initialValues,
    onSubmit,
    validationSchema: signupSchema,
  });

  return (
    <div className="w-full fixed z-40 right-0 left-0 top-0 bottom-0 backdrop-blur-[8px] bg-white/80 h-screen flex">
      <section className="box-border flex justify-center items-center rounded-3xl max-h-[599px] h-full w-full m-auto">
        <Form
          onSubmit={handleSubmit}
          className="max-w-[1000px] rounded-[22px] gap-12 w-full h-full flex flex-col items-center justify-center"
        >
          <div className="flex flex-col items-center gap-4">
            <div className="w-[80px] h-[80px]">
              <img
                className="w-full h-full object-cover"
                src="/circle-logo.svg"
                alt="PlayerApp logo"
              />
            </div>
            <h3 className="font-raleway font-extrabold text-primary text-[20px] leading-7">
              Create Admin Account
            </h3>
          </div>

          <div className="max-w-[85%] md:max-w-[56.25%] w-full flex flex-col gap-5">
            <div className="relative font-quicksand mb-5 font-normal text-sm text-primary">
              <hr className="hr-content" />
            </div>

            <div className="w-ful flex justify-between">
              <div className="w-[46%]">
                <Input
                  icon={false}
                  placeholder="first name"
                  value={values.firstName}
                  errors={errors.firstName && touched.firstName}
                  setValue={handleChange}
                  type="text"
                  name="firstName"
                />
                <p className="text-red-500 text-xs italic py-1">
                  {errors.firstName && touched.firstName
                    ? errors.firstName
                    : ""}
                </p>
              </div>
              <div className="w-[46%]">
                <Input
                  type="text"
                  name="lastName"
                  icon={false}
                  onBlur={handleBlur}
                  errors={errors.lastName && touched.lastName}
                  placeholder="last name"
                  value={values.lastName}
                  setValue={handleChange}
                />
                <p className="text-red-500 text-xs italic py-1">
                  {errors.lastName && touched.lastName ? errors.lastName : ""}
                </p>
              </div>
            </div>
            <div>
              <Input
                icon={false}
                placeholder="Email address"
                value={values.email}
                errors={errors.email && touched.email}
                setValue={handleChange}
                type="email"
                name="email"
              />
              <p className="text-red-500 text-xs italic py-1">
                {errors.email && touched.email ? errors.email : ""}
              </p>
            </div>
            <div>
              <Input
                type="password"
                name="password"
                icon={false}
                errors={errors.password && touched.password}
                placeholder="Password"
                value={values.password}
                setValue={handleChange}
              />
              <p className="text-red-500 text-xs italic py-1">
                {errors.password && touched.password ? errors.password : ""}
              </p>
            </div>
          </div>
          <div className="max-w-[56.25%] w-full rounded-curve">
            <Button type="submit" text="Signup" isLoading={isSubmitting} />
          </div>
          <p className="font-semibold text-sm leading-[19.07px] font-open_sans">
            Already Have an Acccont?{" "}
            <Link
              to={"/"}
              className="text-secondary hover:underline font-bold "
            >
              Login
            </Link>
          </p>
        </Form>
      </section>
    </div>
  );
};
export default SignUp;
