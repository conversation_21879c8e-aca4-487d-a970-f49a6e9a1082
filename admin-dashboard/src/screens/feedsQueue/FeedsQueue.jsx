import { Typography } from "@mui/material";
import approved from "./images/approved.svg";
import pending from "./images/pending.svg";
import rejected from "./images/reject.svg";
import { useCallback, useEffect, useState } from "react";
import { HighlightsApi } from "../../services";
import Loading from "react-loading";
import ReactPlayerVideo from "../../components/videoPlayer/ReactPlayerVideo";

import moment from "moment";
import DateRangePicker from "../../hooks/DatePicker";

const FEED_STATUS = {
  PENDING: "PENDING",
  APPROVED: "APPROVED",
  REJECTED: "REJECTED"
};

const FeedsQueue = () => {
  const [selected, setSelected] = useState(FEED_STATUS.PENDING);
  const [currentFeeds, setCurrentFeeds] = useState(FEED_STATUS.PENDING);
  const [loading, setLoading] = useState(true);

  const handleSelected = async (value) => {
    setSelected(value);

    setLoading(true);
    try {
      const payloadDetails = {
        limit: 2000,
        filter: value
      };
      const feeds = await HighlightsApi.getAllHighlightsByStatus(
        payloadDetails
      );
      const sortedByNewest = feeds.data.data.Items.sort((a, b) => {
        return moment(b.createdAt).unix() - moment(a.createdAt).unix();
      });

      setCurrentFeeds(sortedByNewest);
    } catch (error) {
      setLoading(false);
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  const getFilteredData = (data) => {
    if (data.length === 0) {
      handleSelected(selected);
    } else {
      setCurrentFeeds(data);
    }
  }

  const handleUpdate = async (feedId, userId, type) => {
    const payload = {
      userId: userId,
      id: feedId,
      feedStatus: type
    };

    try {
      await HighlightsApi.updateHighlightsByStatus(payload);
      setCurrentFeeds(currentFeeds.filter((feed) => feed.id !== feedId));
    } catch (error) {
      console.log(error);
      await handleSelected(selected);
      setLoading(false);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    handleSelected(FEED_STATUS.PENDING);
  }, []);

  return (
    <div className="flex">
      <div className="bg-gray-100 md:px-20 px-10 w-[60%] h-[90vh]">
        <Typography variant="h4" pt={10}>
          Feeds Queue
        </Typography>
        <div className="flex flex-col md:flex-row gap-4 w-full mx-auto justify-between h-[70%] items-center ">
          <button
            className={`feed-status-button ${
              selected === FEED_STATUS.PENDING ? "bg-amber-200" : "bg-gray-200"
            }`}
            onClick={() => handleSelected(FEED_STATUS.PENDING)}
          >
            <img className="w-20 inline" src={pending} />
            <p>PENDING FEEDS</p>
            <p className="text-xl text-amber-800">
              {selected === FEED_STATUS.PENDING && !loading
                ? currentFeeds.length
                : ""}
            </p>
          </button>

          <button
            className={`feed-status-button ${
              selected === FEED_STATUS.APPROVED ? "bg-amber-200" : "bg-gray-200"
            }`}
            onClick={() => handleSelected(FEED_STATUS.APPROVED)}
          >
            <img className="w-20 inline" src={approved} />
            <p>APPROVED FEEDS</p>
            <p className="text-xl text-amber-800">
              {selected === FEED_STATUS.APPROVED && !loading
                ? currentFeeds.length
                : ""}
            </p>
          </button>

          <button
            className={`feed-status-button ${
              selected === FEED_STATUS.REJECTED ? "bg-amber-200" : "bg-gray-200"
            }`}
            onClick={() => handleSelected(FEED_STATUS.REJECTED)}
          >
            <img className="w-20" src={rejected} />
            <p className="text-lg">REJECTED FEEDS</p>
            <p className="text-xl text-amber-800">
              {selected === FEED_STATUS.REJECTED && !loading
                ? currentFeeds.length
                : ""}
            </p>
          </button>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center items-center w-[40%] h-[90vh] ">
          <Loading height={100} width={100} type="balls" color="#000000" />
        </div>
      ) : (
        <div className="bg-gray-100  w-[40%] h-[90vh] shadow-2xl">
          <DateRangePicker
            setFilteredData={getFilteredData}
            feeds={currentFeeds}
          />
          <div className="h-[100%] overflow-y-scroll">
            {currentFeeds.length > 0 ? (
              currentFeeds.map((feed) => (
                <div key={feed.id} className="border-2 m-4 p-4 shadow-2xl">
                  <div>
                    <button
                      hidden={
                        selected !== FEED_STATUS.PENDING &&
                        selected !== FEED_STATUS.APPROVED
                      }
                      className="bg-red-500 text-white p-2 rounded-md mr-2"
                      onClick={() =>
                        handleUpdate(
                          feed.id,
                          feed.user.id,
                          FEED_STATUS.REJECTED
                        )
                      }
                    >
                      Reject
                    </button>
                    <button
                      hidden={
                        selected !== FEED_STATUS.PENDING &&
                        selected !== FEED_STATUS.REJECTED
                      }
                      className="bg-green-700 text-white p-2 rounded-md"
                      onClick={() =>
                        handleUpdate(
                          feed.id,
                          feed.user.id,
                          FEED_STATUS.APPROVED
                        )
                      }
                    >
                      Approve
                    </button>
                  </div>
                  <p>
                    <span className="text-red-500">Created At: </span>{" "}
                    <span className="text-black">
                      {moment(feed.createdAt).format("DD-MM-YYYY HH:mm")}
                    </span>
                  </p>
                  <p>
                    <span className="text-red-500">Description: </span>{" "}
                    <span className="text-black">{feed.comment}</span>
                  </p>
                  <p>
                    <span className="text-red-500">Player Full Name: </span>
                    <span className="text-black">
                      {feed.user.firstName} {feed.user.lastName}
                    </span>
                  </p>
                  <p>
                    <span className="text-red-500">Player Team/Club: </span>{" "}
                    <span className="text-black">
                      {feed.user.teamName || feed.user.clubName}
                    </span>
                  </p>
                  <div>
                    <ReactPlayerVideo highlight={feed} highlightId={feed.id} />
                  </div>
                </div>
              ))
            ) : (
              <p className="text-2xl text-center mt-20">No Feeds {selected}</p>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default FeedsQueue;
