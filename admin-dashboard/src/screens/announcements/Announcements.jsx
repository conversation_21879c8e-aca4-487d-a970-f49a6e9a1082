/* eslint-disable no-useless-escape */
import { Box, Modal, useTheme } from "@mui/material";
import Header from "../../components/Header";
import { DataGrid, GridToolbar } from "@mui/x-data-grid";
import { tokens } from "../../styles/theme";
import { useEffect, useRef, useState } from "react";
import { dispatch } from "../../redux/store";
import { announcementsColumn } from "../../constants.jsx/columnData";
import { useSelector } from "react-redux";
import DateTimePicker from "react-datetime-picker";
import S3BucketManger from "../../utils/s3.configUpdated";
import { notifyError } from "../../utils/helpers";
import Spinner from "../../components/reusuable/spinner/Spinner";

const Announcements = () => {
  const [announcements, setAnnouncements] = useState([]);
  const [open, setOpen] = useState(false);
  const [logoFile, setLogoFile] = useState("");
  const [previewLogoUrl, setPreviewLogoUrl] = useState("");
  const [s3Loading, setS3Loading] = useState(false);

  const [uploadType, setUploadType] = useState("");
  const [disabledVideoUpload, setDisabledVideoUpload] = useState(false);
  const [file, setFile] = useState({ src: "", preview: "" });
  const [currentFile, setCurrentFile] = useState("");

  const [startDateTimeValue, setStartDateTimeValue] = useState(new Date());
  const [endDateTimeValue, setEndDateTimeValue] = useState(new Date());
  const [title, setTitle] = useState("");
  const [subtitle, setSubtitle] = useState("");

  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const assetRef = useRef(null);
  const logoRef = useRef(null);
  const videoEl = useRef();

  const gettingAnnouncements = useSelector(
    ({ loading }) => loading.effects.announcements.getAllAnnouncements
  );
  const updatingAnnouncements = useSelector(
    ({ loading }) => loading.effects.announcements.updateAnnouncement
  );
  const deletingAnnouncements = useSelector(
    ({ loading }) => loading.effects.announcements.deleteAnnouncement
  );
  const creatingAnnouncement = useSelector(
    ({ loading }) => loading.effects.announcements.createAnnouncement
  );

  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);

  const fetchAnnouncements = async () => {
    const res = await dispatch.announcements.getAllAnnouncements();
    setAnnouncements(res);
  };
  const handleLogoChange = async (event) => {
    event.preventDefault();
    const fileSelected = event.target.files[0];
    setPreviewLogoUrl(URL.createObjectURL(fileSelected));
    setLogoFile(fileSelected);
  };

  const handleLoadedMetadata = () => {
    const video = videoEl.current;
    if (!video) return;
    if (video.duration > 900) {
      notifyError("Video cannot be more than 15 mins");
      setDisabledVideoUpload(true);

      return;
    }

    if (currentFile.size > 1024 * 1024 * 900) {
      notifyError("Video cannot be more than 900mb");
      setDisabledVideoUpload(true);

      return;
    }
    setDisabledVideoUpload(false);
  };

  const handleFileChange = (e) => {
    e.preventDefault();
    const fileSelected = e.target.files[0];
    setCurrentFile(fileSelected);
  };

  const handleUploadAsset = async () => {
    const s3BucketManger = new S3BucketManger();

    if (disabledVideoUpload || !file.src) {
      notifyError("Please upload another video/image");
      return;
    }
    try {
      setS3Loading(true);
      let uploadedDetails;
      if (uploadType === "VIDEO") {
        uploadedDetails = await s3BucketManger.uploadAssets(
          file.src,
          "player-assets",
          `${Date.now()}-${file.src.name}`,
          "video"
        );
      } else {
        uploadedDetails = await s3BucketManger.uploadAssets(
          file.src,
          "player-assets",
          "",
          "photo"
        );
      }

      URL.revokeObjectURL(currentFile);

      setS3Loading(false);
      return uploadedDetails.assetUrl;
    } catch (error) {
      console.log(error);
      setS3Loading(false);
      notifyError(error.message || "Upload Failed");
    }
  };

  const handleUploadLogo = async () => {
    const s3BucketManger = new S3BucketManger();

    try {
      setS3Loading(true);
      let uploadedDetails;

      uploadedDetails = await s3BucketManger.uploadAssets(
        logoFile,
        "player-assets",
        "",
        "photo"
      );

      URL.revokeObjectURL(previewLogoUrl);

      setS3Loading(false);
      return uploadedDetails.assetUrl;
    } catch (error) {
      console.log(error);
      setS3Loading(false);
      notifyError(error.message || "Upload Failed");
    }
  };

  const onSubmit = async (e) => {
    e.preventDefault();

    try {
      const assetUploaded = await handleUploadAsset();
      const logoUploaded = await handleUploadLogo();

      if (assetUploaded && logoUploaded) {
        if (
          (
              assetUploaded,
              title,
              subtitle,
              startDateTimeValue,
              endDateTimeValue,
              uploadType,
              logoUploaded
          )
        ) {
          const values = {
            assetUrl: assetUploaded.replace(/\s+/g, ''),
            title: title,
            subtitle: subtitle,
            commentBoardId: "",
            totalCommentCount: 0,
            startDateTime: new Date(startDateTimeValue).getTime(),
            endDateTime: new Date(endDateTimeValue).getTime(),
            type: uploadType,
            logoUrl: logoUploaded,
            reactedByUsers: [],
          };
          const res = await dispatch.announcements.createAnnouncement(values);
          if (res === 1) {
            setTitle("");
            setSubtitle("");
            setLogoFile("");
            setPreviewLogoUrl("");
            setUploadType("");
            setFile({ src: "", preview: "" });
            setCurrentFile("");
            setStartDateTimeValue(new Date());
            setEndDateTimeValue(new Date());
            handleClose();
          }
        } else {
          notifyError("All fields are required");
        }
      }
    } catch (error) {
      notifyError(error);
    }
  };

  useEffect(() => {
    fetchAnnouncements();
  }, [updatingAnnouncements, deletingAnnouncements, creatingAnnouncement]);

  useEffect(() => {
    const imageReg = /[\/.](gif|jpg|jpeg|tiff|png)$/i;
    const ivideoReg = /[\/.](mp4|mov|wmv|avi|mkv|mpeg|webm|flv|quicktime)$/i;
    setFile({ src: "", preview: "" });

    if (ivideoReg.test(currentFile?.type)) {
      const preview = (
        <video
          ref={videoEl}
          key={URL.createObjectURL(currentFile)}
          width="400"
          height="350"
          onLoadedMetadata={handleLoadedMetadata}
        >
          <source
            height={200}
            src={`${URL.createObjectURL(currentFile)}#t=0.001`}
            type="video/mp4"
          />
        </video>
      );
      setUploadType("VIDEO");
      setFile({ src: currentFile, preview });
      return;
    }

    if (imageReg.test(currentFile?.type)) {
      setDisabledVideoUpload(false);
      const preview = (
        <img
          src={URL.createObjectURL(currentFile)}
          alt="feed"
          className="w-full h-full object-cover rounded-[32px] "
        />
      );
      setFile({ src: currentFile, preview });
      setUploadType("PHOTO");
      return;
    }

    if (
      currentFile &&
      (!ivideoReg.test(currentFile?.type) || !imageReg.test(currentFile?.type))
    ) {
      notifyError("Invalid file provided");
    }
  }, [currentFile]);
  return (
    <div className="">
      <Box m="20px">
        <Header title="Announcements" subtitle="" />
        <Box
          m="40px 0 0 0"
          height="75vh"
          sx={{
            "& .MuiDataGrid-columnHeaderTitle": {
              whiteSpace: "normal",
              lineHeight: "normal",
            },
            "& .MuiDataGrid-columnHeader": {
              height: "unset !important",
            },
            "& .MuiDataGrid-columnHeaders": {
              backgroundColor: colors.blueAccent[700],
              borderBottom: "none",
              maxHeight: "168px !important",
            },
            "& .MuiDataGrid-virtualScroller": {
              backgroundColor: colors.primary[400],
            },
          }}
        >
          <div className="flex justify-end w-full mb-3">
            <button
              onClick={handleOpen}
              className="px-5 py-2 bg-green-400 rounded-md"
            >
              {" "}
              + Create Announcement
            </button>
            <Modal
              open={open}
              onClose={handleClose}
              aria-labelledby="modal-modal-title"
              aria-describedby="modal-modal-description"
            >
              <Box className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white py-7 px-7 rounded-md">
                <div className="w-[100%] flex flex-col items-center overlow-auto">
                  <h1 className="text-2xl mb-3 font-bold w-full text-center">
                    Create Announcement
                  </h1>
                  <div>
                    <form>
                      <div className="grid grid-cols-2 mt-5 gap-x-10 gap-y-5 w-full">
                        <div className="">
                          <label className="block text-[#000000] font-bold mb-1 md:mb-0 pr-4">
                            Image/Video
                          </label>
                          <div
                            onClick={() => {
                              assetRef.current.click();
                            }}
                            className="text-blue-600 mt-3 cursor-pointer"
                          >
                            {file.preview ? (
                              <div>{file.preview}</div>
                            ) : (
                              "Upload"
                            )}
                          </div>
                          <input
                            ref={assetRef}
                            accept="video/*,image/*"
                            type="file"
                            style={{ display: "none" }}
                            onChange={handleFileChange}
                          />
                        </div>
                        <div className="">
                          <label className="block text-[#000000] font-bold mb-1 md:mb-0 pr-4">
                            Logo Image
                          </label>
                          <div
                            onClick={() => {
                              logoRef.current.click();
                            }}
                            className="text-blue-600 mt-3 cursor-pointer"
                          >
                            {previewLogoUrl ? (
                              <img
                                className="w-full h-full object-cover rounded-[32px]"
                                src={previewLogoUrl}
                                alt={"asset"}
                              />
                            ) : (
                              "Upload"
                            )}
                          </div>
                          <input
                            ref={logoRef}
                            accept="video/*,image/*"
                            type="file"
                            style={{ display: "none" }}
                            onChange={(event) => handleLogoChange(event)}
                          />
                        </div>
                        <div className="">
                          <label className="block text-[#000000] font-bold mb-1 md:mb-0 pr-4">
                            Title
                          </label>
                          <input
                            className="bg-gray-200 appearance-none border-2 border-gray-200 rounded w-full py-2 px-4 text-[#000000] leading-tight focus:outline-none focus:bg-white focus:border-purple-500"
                            id="title"
                            type="text"
                            value={title}
                            onChange={(e) => setTitle(e.target.value)}
                          />
                        </div>
                        <div className="">
                          <label className="block text-[#000000] font-bold mb-1 md:mb-0 pr-4">
                            Subtitle
                          </label>
                          <textarea
                            className="bg-gray-200 appearance-none border-2 border-gray-200 rounded w-full py-2 px-4 text-[#000000] leading-tight focus:outline-none focus:bg-white focus:border-purple-500"
                            id="subtitle"
                            type="text"
                            value={subtitle}
                            onChange={(e) => setSubtitle(e.target.value)}
                          />
                        </div>
                        <div className="">
                          <label className="block text-gray-500 font-bold mb-1 md:mb-0 pr-4 text-[#000000]">
                            Start Date
                          </label>
                          <DateTimePicker
                            className="w-full text-[#000000]"
                            value={startDateTimeValue}
                            onChange={(newValue) =>
                              setStartDateTimeValue(newValue)
                            }
                          />
                        </div>
                        <div className="">
                          <label className="block text-[#000000] font-bold mb-1 md:mb-0 pr-4">
                            End Date
                          </label>
                          <DateTimePicker
                            className="w-full text-[#000000]"
                            value={endDateTimeValue}
                            onChange={(newValue) =>
                              setEndDateTimeValue(newValue)
                            }
                          />
                        </div>
                      </div>
                      <div className="w-full mt-5 text-center text-[#000000]">
                        <button
                          className="px-5 py-2 bg-green-400 rounded-md"
                          type="submit"
                          onClick={(e) => onSubmit(e)}
                        >
                          {s3Loading ? (
                            <div>
                              <Spinner /> Uploading Assets
                            </div>
                          ) : creatingAnnouncement ? (
                            <div>
                              <Spinner /> Submitting
                            </div>
                          ) : (
                            "Submit"
                          )}
                        </button>
                      </div>
                    </form>
                  </div>
                </div>
              </Box>
            </Modal>
          </div>
          <DataGrid
            getRowHeight={() => "auto"}
            rows={announcements || []}
            loading={gettingAnnouncements}
            columns={announcementsColumn}
            slots={{ Toolbar: GridToolbar }}
          />
        </Box>
      </Box>
    </div>
  );
};

export default Announcements;
