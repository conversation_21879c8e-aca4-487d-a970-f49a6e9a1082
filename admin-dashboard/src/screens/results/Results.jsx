import { DataGrid, GridToolbar } from "@mui/x-data-grid";
import { resultsColumn } from "./constants";
import { useEffect, useState } from "react";
import { ResultsApi } from "../../services";
import { notifyError } from "../../utils/helpers";
import { Typography } from "@mui/material";

const ResultsPage = () => {
  const [results, setResults] = useState([]);
  const [gettingResults, setGettingResults] = useState(false);

  const getAllResults = async () => {
    setGettingResults(true);
    try {
      const { data } = await ResultsApi.getAllResults();
      setResults(data?.data?.Items || []);
    } catch (error) {
      notifyError(error.response.data.message || error.message);
    }

    setGettingResults(false);
  };

  useEffect(() => {
    getAllResults();
  }, []);
  console.log(results);
  return (
    <div className="mx-10 my-20">
      <Typography variant="h3" my={5}>
        List of all the matches
      </Typography>
      <DataGrid
        getRowHeight={() => "auto"}
        rows={results || []}
        loading={gettingResults}
        columns={resultsColumn}
        components={{ Toolbar: GridToolbar }}
      />
    </div>
  );
};

export default ResultsPage;
