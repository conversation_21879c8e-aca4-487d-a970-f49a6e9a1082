import { readableTimeStamp } from "../../utils/helpers";

export const resultsColumn = [
  {
    field: "homeTeam",
    headerName: "Home Team",
    flex: 1,
    renderCell: (params) => {
      return <div className="break-all">{params.row.homeTeam.data.teamName || "--"}</div>;
    },
  },
    {
      field: "awayTeam",
      headerName: "Away Team",
      flex: 1,
      renderCell: (params) => {
        return <div className="break-all">{params.row.awayTeam.data.teamName || "--"}</div>;
      },
    },

    {
      field: "",
      headerName: "Scores",
      flex: 1,
      renderCell: (params) => {
        return <div className="break-all">{params.row.homeTeam.score + "-" + params.row.homeTeam.score  || "--"}</div>;
      },
    },
    {
      field: "seasonTitle",
      headerName: "Season",
      flex: 1,
      renderCell: (params) => {
        return <div className="break-all">{params.row.seasonTitle || "--"}</div>
      },
    },
    {
      field: "dateTimePlayed",
      headerName: "Match Date",
      flex: 1,
      renderCell: (params) => {
        return <div className="break-all">{params.row.dateTimePlayed || "--"}</div>
      },
    },
    {
      field: "publishStatus",
      headerName: "Publish Status",
      flex: 1,
      renderCell: (params) => {
        return <>
         <div className="break-all">{params.row.publishStatus || "--"}</div>
        </>
        
      },
    },
    {
      field: "createdAt",
      headerName: "Created At",
      flex: 1,
      renderCell: (params) => {
        return <div className="break-all py-4">{readableTimeStamp(params.row.createdAt) || "--"}</div>
      },
    },
    // {
    //   field: "subtitle",
    //   headerName: "Subtitle",
    //   flex: 1,
    //   renderCell: (params) => {
    //     return <div className="break-all">{params.row.subtitle || "--"}</div>;
    //   },
    // },
    // {
    //   field: "expired",
    //   headerName: "Expired",
    //   flex: 1,
    //   renderCell: (params) => {
    //     return (
    //       <div className="break-all">
    //         {params.row.expired === true ? "Yes" : "No"}
    //       </div>
    //     );
    //   },
    // },
    // {
    //   field: "isActive",
    //   headerName: "Active",
    //   flex: 1,
    //   renderCell: (params) => {
    //     return (
    //       <div className="break-all">
    //         {params.row.isActive === true ? "Yes" : "No"}
    //       </div>
    //     );
    //   },
    // },
  
    // {
    //   field: "startDateTime",
    //   headerName: "Start Date",
    //   flex: 1,
    //   renderCell: (params) => {
    //     return (
    //       <div className="break-all">
    //         {readableTimeStamp(params.row.startDateTime) || "--"}
    //       </div>
    //     );
    //   },
    // },
    // {
    //   field: "endDateTime",
    //   headerName: "End Date",
    //   flex: 1,
    //   renderCell: (params) => {
    //     return (
    //       <div className="break-all">
    //         {readableTimeStamp(params.row.endDateTime) || "--"}
    //       </div>
    //     );
    //   },
    // },
    // {
    //   field: "type",
    //   headerName: "Media Type",
    //   flex: 1,
    //   renderCell: (params) => {
    //     return <div className="break-all">{params.row.type || "--"}</div>;
    //   },
    // },
    // {
    //   field: "assetUrl",
    //   headerName: "Image/Video",
    //   flex: 1,
    //   renderCell: (params) => {
    //     return (
    //       <div className="break-all">
    //         {params.row.assetUrl ? (
    //           params.row.type === "VIDEO" ? (
    //             <a href={params.row.assetUrl} target="_blank" rel="noreferrer">
    //               View Video
    //             </a>
    //           ) : (
    //             <a href={params.row.assetUrl} target="_blank" rel="noreferrer">
    //               View Image
    //             </a>
    //           )
    //         ) : (
    //           "--"
    //         )}
    //       </div>
    //     );
    //   },
    // },
    // {
    //   field: " ",
    //   headerName: " ",
    //   sortable: false,
    //   renderCell: () => {
    //     return (
    //       <>
    //         <Switch />
    //       </>
    //     );
    //   },
    // },
  ];