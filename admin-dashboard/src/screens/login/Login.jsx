/* eslint-disable react-refresh/only-export-components */
/* eslint-disable no-restricted-globals */
import { useFormik } from "formik";
import { loginSchema } from "../../utils/formSchema";
import Button from "../../components/reusuable/Button";
import Input from "../../components/reusuable/Input";
import { Link, Form, useNavigate } from "react-router-dom";
import { dispatch } from "../../redux/store";

export async function action({ request }) {
  const formData = await request.formData();
  const formItems = Object.fromEntries(formData);

  return { formItems };
}

const Login = () => {
  const navigate = useNavigate();

  const initialValues = {
    email: "",
    password: "",
  };

  const onSubmit = async (values) => {
    await dispatch.auth.userLogin({ navigate, values });
  };
  const {
    values,
    errors,
    touched,
    isSubmitting,
    handleSubmit,
    handleBlur,
    handleChange,
  } = useFormik({
    initialValues,
    onSubmit,
    validationSchema: loginSchema,
  });

  return (
    <div className="w-full fixed z-40 right-0 left-0 top-0 bottom-0 backdrop-blur-[8px] bg-white/80 h-screen flex">
      <section className="box-border flex justify-center items-center rounded-3xl max-h-[599px] h-full w-full m-auto">
        <Form
          onSubmit={handleSubmit}
          className="max-w-[1000px] rounded-[22px] gap-12 w-full h-full flex flex-col items-center justify-center"
        >
          <div className="flex flex-col items-center gap-4">
            <div className="w-[80px] h-[80px]">
              <img
                className="w-full h-full object-cover"
                src="/circle-logo.svg"
                alt="PlayerApp logo"
              />
            </div>
            <h3 className="font-raleway font-extrabold text-primary text-[20px] leading-7">
              Welcome Admin
            </h3>
          </div>

          <div className="max-w-[85%] md:max-w-[56.25%] w-full flex flex-col gap-5">
            <div className="relative font-quicksand mb-5 font-normal text-sm text-primary">
              <hr className="hr-content" />
            </div>
            <div>
              <Input
                icon={false}
                placeholder="Email address"
                value={values.email}
                errors={errors.email && touched.email}
                setValue={handleChange}
                onBlur={handleBlur}
                type="email"
                name="email"
              />
              <p className="text-red-500 text-xs italic py-1">
                {errors.email && touched.email ? errors.email : ""}
              </p>
            </div>
            <div>
              <Input
                type="password"
                name="password"
                icon={false}
                errors={errors.password && touched.password}
                placeholder="Password"
                value={values.password}
                setValue={handleChange}
              />
              <p className="text-red-500 text-xs italic py-1">
                {errors.password && touched.password ? errors.password : ""}
              </p>
            </div>
          </div>
          <div className="max-w-[56.25%] w-full rounded-curve">
            <Button type="submit" text="Login" isLoading={isSubmitting} />
          </div>
          <p className="font-semibold text-sm leading-[19.07px] font-open_sans">
            Create Admin account?{" "}
            <Link
              to={"/signup"}
              className="text-secondary hover:underline font-bold "
            >
              SignUp
            </Link>
          </p>
        </Form>
      </section>
    </div>
  );
};
export default Login;
