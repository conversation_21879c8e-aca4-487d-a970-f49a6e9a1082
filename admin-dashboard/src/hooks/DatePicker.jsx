import React, { useState } from "react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import moment from "moment";
import { notifyWarn } from "../utils/helpers";

const DateRangePicker = React.memo(({ feeds = [], setFilteredData }) => {
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);

  const handleFilter = () => {
    const filteredData = feeds
      .filter((item) => {
        const itemDate = moment(item.updatedAt);

        return (
          (!startDate || itemDate.isSameOrAfter(moment(startDate), "day")) &&
          (!endDate || itemDate.isSameOrBefore(moment(endDate), "day"))
        );
      })
      .sort(
        (a, b) => moment(a.updatedAt).valueOf() - moment(b.updatedAt).valueOf()
      );

    if (filteredData.length > 0) {
      setFilteredData(filteredData);
    } else {
      notifyWarn("No data found for the selected date range");
    }
  };

  return (
    <div className="p-4">
      <div className="flex gap-4 mb-4 bg-slate-400 p-2 rounded-md">
        <DatePicker
          selected={startDate}
          onChange={setStartDate}
          selectsStart
          startDate={startDate}
          endDate={endDate}
          dateFormat="yyyy-MM-dd"
          className="border p-2 rounded"
          placeholderText="Start Date"
        />
        <DatePicker
          selected={endDate}
          onChange={setEndDate}
          selectsEnd
          startDate={startDate}
          endDate={endDate}
          minDate={startDate}
          dateFormat="yyyy-MM-dd"
          className="border p-2 rounded"
          placeholderText="End Date"
        />

        <div className="flex gap-2 mx-2">
          <button
            className="bg-blue-500 text-white p-2 rounded"
            onClick={handleFilter}
          >
            Filter
          </button>
          <button
            className="bg-gray-50 text-black p-2 rounded"
            onClick={() => setFilteredData([])}
          >
            Clear
          </button>
        </div>
      </div>
    </div>
  );
});

DateRangePicker.displayName = "DateRangePicker";
export default DateRangePicker;
