import { createModel } from "@rematch/core";
import { PlayersApi } from "../../services";
import { reducerActions } from "../reducer";

const initialState = {
  allPlayers: [],
};

export const players = createModel()({
  state: initialState,
  reducers: reducerActions,
  effects: (dispatch) => ({
    async getAllPlayers() {
      dispatch.players.setError(null);
      try {
        const { data } = await PlayersApi.getAllPlayers();

        if (data.status === 1) {
          dispatch.players.setState({
            allPlayers: data.data.Items,
          });
        } else {
          console.log(data.message);
        }
        return data.data.Items;
      } catch (err) {
        console.log("ERROR", err);
      }
    },

    async reset() {
      await Promise.all([dispatch.players.setState(initialState)]);
    },
  }),
});
