import { createModel } from "@rematch/core";
import { AuthApi } from "../../services";
import { reducerActions } from "../reducer";
import { notifyError, notifySuc<PERSON> } from "../../utils/helpers";

import { dispatch as globalDispatch } from "../store";

const initialState = {
  confirmSignUpModal: { visible: false, email: "" },
  authUser: {
    token: null,
    userInfo: {},
    authenticated: false
  }
};

export const auth = createModel()({
  state: initialState,
  reducers: reducerActions,
  effects: (dispatch) => ({
    async userLogin(payload) {
      dispatch.auth.setError(null);
      const { navigate, values } = payload;

      try {
        const { data } = await AuthApi.login(values);

        const receivedData = data;

        if (receivedData.status === 0) {
          notifyError(receivedData.message);
          dispatch.auth.logout();
        }
        if (receivedData.data.userType === "ADMIN") {
          dispatch.auth.setState({
            authUser: {
              tokens: receivedData?.data.tokens.access,
              userInfo: data.data,
              authenticated: true
            }
          });
          navigate("/admin");
        } else {
          dispatch.auth.logout();
          notifyError("Only Admins can Login");
        }
      } catch (err) {
        if (err.response) {
          notifyError(err.response.data.message);
          // if (err.response.data.data.name === "UserNotConfirmedException") {
          //   notifyError("Please confirm your Account");
          //   dispatch.auth.confirmSignUpModalBox({
          //     visible: true,
          //     email: values.email,
          //   });
          // } else {
          //   notifyError(err.response.data.message);
          // }
        }
        return null;
      }
    },

    async userSignup(payload) {
      dispatch.auth.setError(false);
      const { navigate, values } = payload;
      try {
        const { data } = await AuthApi.signup(values);

        if (data.status === 1) {
          notifySuccess(data.message);
          navigate("/admin");
        }
      } catch (err) {
        if (
          err.response.data.message
            .toLowerCase()
            .includes("email address is not verified")
        ) {
          notifySuccess("Account Created");
          navigate("/");
        } else {
          notifyError(err?.response?.data?.message || err.message);
        }
      }
    },

    async reset() {
      await Promise.all([dispatch.auth.setState(initialState)]);
    },

    async logout() {
      try {
        await Promise.all([
          globalDispatch.auth.reset(),
          globalDispatch.players.reset(),
          globalDispatch.user.reset()
        ]);
        const allLocalItems = window.localStorage;
        for (const key in allLocalItems) {
          if (Object.hasOwnProperty.call(allLocalItems, key)) {
            window.localStorage.removeItem(key);
          }
        }
        localStorage.clear();
      } catch (error) {
        notifyError(error.message);
      }
    }
  })
});
