import { createModel } from "@rematch/core";
import { AnnouncementApi } from "../../services";
import { reducerActions } from "../reducer";
import { notifyError, notifySuccess } from "../../utils/helpers";

const initialState = {
  announcements: [],
  activeAnnouncements: [],
};

export const announcements = createModel()({
  state: initialState,
  reducers: reducerActions,
  effects: (dispatch) => ({
    async getAllAnnouncements() {
      dispatch.announcements.setError(null);
      try {
        const { data } = await AnnouncementApi.getAllAnnouncements({limit: 1000});

        if (data.status === 1) {
          const activeAnnouncements = data.data.Items.filter(
            (item) => item.isActive === true && item.expired === false
          );
          dispatch.announcements.setState({
            announcements: data.data.Items,
          });
          dispatch.announcements.setState({
            activeAnnouncements: activeAnnouncements,
          });
        } else {
          console.log(data.message);
        }
        return data.data.Items;
      } catch (err) {
        console.log("ERROR", err);
      }
    },

    async updateAnnouncement(payload) {
      dispatch.announcements.setError(null);

      try {
        const { data } = await AnnouncementApi.updateAnnouncement(payload);

        if (data.status === 1) {
          notifySuccess(data.message);
        } else {
          notifyError(data.message);
        }
        return data.data.Attributes;
      } catch (err) {
        notifyError("ERROR", err);
      }
    },

    async getSingleAnnouncement(id) {
      dispatch.announcements.setError(null);

      try {
        const { data } = await AnnouncementApi.getSingleAnnouncement(id);

        if (data.status === 1) {
          dispatch.announcements.setState({
            announcement: data.data.Item,
          });
        } else {
          notifyError(data.message);
        }
        return data.data.Item;
      } catch (err) {
        notifyError(
          err.response.data.message ||
          err.response.message ||
          err.message ||
          err
        );
        return 0
      }
    },

    async createAnnouncement(payload) {
      dispatch.announcements.setError(null);

      try {
        const { data } = await AnnouncementApi.createAnnouncement(payload);

        if (data.status === 1) {
          notifySuccess(data.message);
        } else {
          notifyError(data.message);
        }

        return data.status;
      } catch (err) {
        notifyError(
          err.response.data.message ||
          err.response.message ||
          err.message ||
          err
        );
        return 0
      }
    },

    async deleteAnnouncement(payload) {
      dispatch.announcements.setError(null);

      try {
        const { data } = await AnnouncementApi.deleteAnnouncement(payload);

        if (data.status === 1) {
          notifySuccess(data.message);
        } else {
          notifyError(data.message);
        }
        return data.status;
      } catch (err) {
        notifyError(
          err.response.data.message ||
          err.response.message ||
          err.message ||
          err
        );
      }
    },
  }),
});
