import { createModel } from "@rematch/core";
import { User<PERSON><PERSON> } from "../../services";
import {
    notifyError,
} from "../../utils/helpers";
import { reducerActions } from "../reducer";

import "react-toastify/dist/ReactToastify.css";

const initialState = {
    loaded: false,
    data: null,
    guest: null,
    usersByProjection: []
};
export const user = createModel()({
    state: initialState,
    reducers: reducerActions,
    effects: (dispatch) => ({
        async userSearch(payload) {
            try {
              const { data } = await UserApi.userSearch(payload);
              return data;
            } catch (error) {
              notifyError(error.response.data.message || error.message);
            }
        },

        async userSearchByProjection() {
            try {
                const { data } = await UserApi.userSearchByProjection();
                await dispatch.user.setState({
                    usersByProjection: data.data.Items
                });
                return data.data;
            } catch (error) {
                notifyError(error.response.data.message || error.message);
            }
        },

        async reset() {
            await Promise.all([dispatch.user.setState(initialState)]);
        }
    })
});
