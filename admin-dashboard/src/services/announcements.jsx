import ApiHandler from "./ApiHandler";

export default {
  getAllAnnouncements: (data) => ApiHandler.post("/announcement/get", data),
  createAnnouncement: (data) => ApiHandler.post("/announcement", data),
  getSingleAnnouncement: (data) => ApiHandler.get(`/announcement/${data}`),
  deleteAnnouncement: (data) => ApiHandler.delete(`/announcement/${data}`),
  updateAnnouncement: (data) =>
    ApiHandler.put(`/announcement/${data.id}`, data),
};
