/* eslint-disable no-unused-vars */
/* eslint-disable no-nested-ternary */
import axios from "axios";
import moment from "moment";

import { getSingleModel, notifyError } from "../utils/helpers";
import { dispatch } from "../redux/store";
import { BASE_URL } from "../utils/constants";

const now = moment().unix();

async function handleRequest(req) {
  req.headers["Content-Type"] = "application/json";
  req.headers.Accept = "application/json";

  const authModel = getSingleModel("auth");
  const expTokenTime = authModel?.authUser?.userInfo?.exp;
  // const refreshToken = authModel?.authUser?.refreshToken;

  // if (expTokenTime && expTokenTime <= now) {
  // if (refreshToken && refreshToken.token && refreshToken.details?.exp > now) {
  //   req.headers.Authorization = `Bearer ${refreshToken.token}`;
  //   dispatch.auth.refreshToken();
  //   return {};
  // }
  //   dispatch({ type: "auth/logout" });

  //   return {};
  // }
  if (authModel?.authUser) {
    req.headers.Authorization = `Bearer ${authModel.authUser?.userInfo?.tokens?.access}`;
    return req;
  }
  return req;
}

function handleResponse(res) {
  if (
    res?.response?.status === 401 ||
    res?.response?.data?.message === "jwt expired"
  ) {
    dispatch({ type: "auth/logout" });
    notifyError("User session expired, please login");
    setTimeout(() => {
      window.document.location.href = "/";
    }, 2000);
  }

  return res;
}

/**
 * This is used to generate a new token for api calls
 * @returns {Promise<void>}
 */
// async function refresh() {}

/**
 * This is used to modify the header request and relies on some header constraints
 * to generate some header fields
 */
axios.interceptors.request.use(
  async (req) => {
    const splitedUrl = req.url.split("/");
    const currentEndpoint = splitedUrl[splitedUrl.length - 1];
    if (currentEndpoint === "refresh") {
      const authModel = getSingleModel("auth");
      const refreshToken = authModel?.authUser?.refreshToken;
      req.headers.Authorization = `Bearer ${authModel.authUser?.userInfo?.tokens?.refresh}`;
      return req;
    }
    return handleRequest(req);
  },
  (error) => Promise.reject(error)
);
/**
 * This is used to modify response call to reprocess error 401 and generate new
 * token to use for new and current running request.
 */
axios.interceptors.response.use(
  async (res) => res,
  (err) => Promise.reject(handleResponse(err))
);

/** *
 * The ApiHandler framework with observable
 */
export default {
  post: async (url, data, options) =>
    axios.post(
      options?.fullPath ? url : BASE_URL + url,
      data,
      options && { headers: options }
    ),
  patch: async (url, data, options) =>
    axios.patch(
      options?.fullPath ? url : BASE_URL + url,
      data,
      options && { headers: options }
    ),
  put: async (url, data, options) =>
    axios.put(
      options?.fullPath ? url : BASE_URL + url,
      data,
      options && { headers: options }
    ),
  delete: async (url, data, options) => {
    let modData = data;
    modData = data
      ? data instanceof Object && !Object.keys(data).length
        ? null
        : data
      : null;
    const config = modData ? { headers: options, data } : { headers: options };
    return axios.delete(options?.fullPath ? url : BASE_URL + url, config);
  },
  get: async (url, params, options) => {
    let modParams = params;
    modParams = params
      ? params instanceof Object && !Object.keys(params).length
        ? null
        : params
      : null;
    const config = modParams
      ? { headers: options, params }
      : { headers: options };
    return axios.get(options?.fullPath ? url : BASE_URL + url, config);
  },
};
