{"name": "admin-dashboard", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:host": "vite --host", "build": "vite build", "build:staging": "vite build --mode development", "build:prod": "vite build --mode production", "start:prod": "vite --mode production", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@aws-sdk/client-s3": "^3.278.0", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@fontsource/roboto": "^4.5.8", "@material-ui/core": "^4.12.4", "@mdi/js": "^7.2.96", "@mdi/react": "^1.6.1", "@mui/icons-material": "^5.11.0", "@mui/lab": "^5.0.0-alpha.132", "@mui/material": "^5.13.3", "@mui/x-data-grid": "^6.5.0", "@mui/x-date-pickers": "^6.14.0", "@nivo/bar": "^0.83.0", "@nivo/geo": "^0.83.0", "@nivo/line": "^0.83.0", "@nivo/pie": "^0.83.0", "@rematch/core": "^2.2.0", "@rematch/loading": "^2.1.2", "@rematch/persist": "^2.1.2", "@rematch/select": "^3.1.2", "axios": "^1.5.0", "classnames": "^2.3.2", "compressorjs": "^1.2.1", "dayjs": "^1.11.13", "formik": "^2.4.6", "lodash.groupby": "^4.6.0", "lodash.isequal": "^4.5.0", "lodash.uniqwith": "^4.5.0", "lucide-react": "^0.447.0", "moment": "^2.30.1", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-date-picker": "^11.0.0", "react-datepicker": "^8.0.0", "react-datetime-picker": "^5.5.1", "react-dom": "^18.2.0", "react-intersection-observer": "^9.13.0", "react-loading": "^2.0.3", "react-player": "^2.16.0", "react-query": "^3.39.3", "react-redux": "^8.0.5", "react-router-dom": "^6.8.0", "react-time-picker": "^7.0.0", "react-toastify": "^9.1.3", "sweetalert2": "^11.7.1", "sweetalert2-react-content": "^5.0.7", "yup": "^1.4.0"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/react-redux": "^7.1.25", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.15", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.29", "tailwindcss": "^3.3.3", "vite": "^4.4.9"}}