# VITE_APP_ENV = "staging"
# VITE_S3_BASE_URL = "https://player-assets.s3.eu-west-2.amazonaws.com"
# VITE_APP_AWS_ACCESS_ID="********************"
# VITE_APP_AWS_SECRET_KEY="AqOo4GwGFYuNyHimvdMyxZeq6HdQOqNFevhynbt6"
# VITE_BUCKETNAME = "player-assets"
# VITE_BASE_URL = "https://staging-api.playerapp.co/staging/api"
# VITE_CLIENTID = "2TJkZS6vpXchKZxyio3Vy25qXPp7C1eRqEiAOUaV"

VITE_APP_ENV = "dev"
VITE_APP_AWS_ACCESS_ID="********************"
VITE_APP_AWS_SECRET_KEY="AqOo4GwGFYuNyHimvdMyxZeq6HdQOqNFevhynbt6"
VITE_BASE_URL = "https://rlml11w5wi.execute-api.us-east-1.amazonaws.com/dev/api"
VITE_BUCKETNAME = "dev-player-asset"
VITE_CLIENTID = "uYeYVz0PDOg8iaNpgbU5cOnNKpnxRO7J8FVSoync" 

VITE_S3_BASE_PHOTO_URL = "https://dev-player-asset.s3.us-east-1.amazonaws.com"
# VITE_S3_VIDEO_OUTPUT_BASE_URL="https://dj3lvetxq3d8h.cloudfront.net"
VITE_S3_VIDEO_OUTPUT_BASE_URL="https://playerapp-streaming-assets-dev.global.ssl.fastly.net"

VITE_S3_VIDEO_UPLOAD_BASE_BUCKET=optimized-media-dev
VITE_S3_VIDEO_UPLOAD_BASE_URL=https://optimized-media-dev.s3.amazonaws.com
VITE_REGION="eu-west-2"