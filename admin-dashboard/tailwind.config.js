/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./src/**/*.{js,jsx,ts,tsx}"],
  theme: {
    extend: {
      backgroundColor: {
        primary: "#F9E78F",
        danger: "#EB5757",
        btnGray: "#E9EFF7",
      },
      backgroundImage: {
        secondary: "linear-gradient(180deg, #3E6AA2 0%, #223A59 100%)",
      },
      textColor: {
        primary: "#595959",
        secondary: "#223A59",
        danger: "#EB5757",
        orange: "#BF6B04",
        gray: "#a6a6a6",
      },
      borderColor: {
        primary: "rgba(166, 166, 166, 0.5)",
        secondary: "#223A59",
        gray: "#595959",
        transparentWhite: "rgba(255, 255, 255, 0.1)",
      },
      borderRadius: {
        curve: "200px",
      },
      fontFamily: {
        quicksand: "quicksand",
        open_sans: "open sans",
        pacifico: "pacifico",
        raleway: "raleway",
        roboto: "roboto",
      },
      boxShadow: {
        dash: "0px 2px 24px rgba(0, 0, 0, 0.16)",
      },
    },
  },
  plugins: [],
};
