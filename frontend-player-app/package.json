{"name": "player-app", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "dev:host": "vite --host", "prod": "vite --mode production", "start:staging": "vite --mode staging", "build:dev": "vite build --mode development", "build:prod": "vite build --mode production", "build:staging": "vite build --mode staging", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@livelike/engagementsdk": "^2.49.0", "@livelike/javascript": "^1.3.3", "@rematch/core": "^2.2.0", "@rematch/loading": "^2.1.2", "@rematch/persist": "^2.1.2", "@rematch/select": "^3.1.2", "@sentry/react": "^8.51.0", "@sentry/vite-plugin": "^3.1.0", "axios": "^1.3.3", "axios-retry": "^3.4.0", "compressorjs": "^1.2.1", "daisyui": "^4.7.2", "dayjs": "^1.11.13", "formik": "^2.2.9", "jwt-decode": "^3.1.2", "localforage": "^1.10.0", "lodash.groupby": "^4.6.0", "lodash.isequal": "^4.5.0", "lodash.uniqwith": "^4.5.0", "lucide-react": "^0.451.0", "match-sorter": "^6.3.1", "moment": "^2.29.4", "phone": "^3.1.34", "react": "^18.2.0", "react-alice-carousel": "^2.9.1", "react-cool-inview": "^3.0.1", "react-dom": "^18.2.0", "react-ga4": "^2.1.0", "react-gtm-module": "^2.0.11", "react-intersection-observer": "^9.10.3", "react-material-ui-carousel": "^3.4.2", "react-phone-number-input": "^3.2.19", "react-player": "^2.14.1", "react-redux": "^8.0.5", "react-router-dom": "^6.8.0", "react-share": "^4.4.1", "react-slick": "^0.30.2", "react-toastify": "^9.1.1", "sort-by": "^1.2.0", "uuid": "^11.1.0", "yup": "^1.0.0"}, "devDependencies": {"@types/node": "^18.13.0", "@types/react": "^18.0.26", "@types/react-dom": "^18.0.9", "@types/react-redux": "^7.1.25", "@vitejs/plugin-react": "^3.0.0", "autoprefixer": "^10.4.13", "postcss": "^8.4.21", "tailwindcss": "^3.2.4", "vite": "^4.0.0", "vite-plugin-cross-origin-isolation": "^0.1.6"}}