/* eslint-disable import/no-cycle */
import { createModel } from "@rematch/core";
import moment from "moment";
import { FeedApi } from "../../services";
import { reducerActions } from "../reducer";
import {
  getSingleModel,
  notifyError,
  notify<PERSON>uc<PERSON>,
  removeDuplicates,
  // shuffleArray
} from "../../utils/helpers";

const now = moment().unix();

const initialState = {
  highlights: [],
  profileHighlights: [],
  teamHighlights: [],
  lastEvaluatedKey: null,
  profileLastEvaluatedKey: null,
  currentIndex: 0,
  data: []
};

export const feed = createModel()({
  state: initialState,
  reducers: reducerActions,
  effects: (dispatch) => ({
    async fetchHighlights(queryData) {
      dispatch.feed.setError(null);

      try {
        const feed = getSingleModel("feed");
        let dataItems = feed.data;

        if (!dataItems?.length || feed?.expiredTimestamp < now) {
          const { data } = await Feed<PERSON>pi.highlights({
            limit: queryData?.limit || 1000,
            lastEvaluatedKey: queryData?.lastEvaluatedKey || undefined
          });

          dataItems = data?.data?.Items?.filter((item) => !Boolean(item?.videoProcessingFailed));

          dispatch.feed.setState({
            ...feed,
            data: data.data.Items,
            lastEvaluatedKey: data?.data?.LastEvaluatedKey,
            expiredTimestamp: moment().add(30, "minutes").unix()
          });
        }

        //Start Build offline pagination
        const newHighlights = [...dataItems].sort(
          (a, b) => new Date(b.createdAt) - new Date(a.createdAt)
        );

        let newIndex = feed?.currentIndex;

        const nextHighlights = newHighlights.slice(
          feed?.currentIndex,
          feed?.currentIndex + 10
        );
        newIndex += 10;
        //End Build offline pagination

        const mergeNewFeedToOld =
          feed.highlights.length > 0
            ? [...feed.highlights, ...nextHighlights]
            : nextHighlights;

        const cleaned = removeDuplicates(mergeNewFeedToOld, "id");

        dispatch.feed.setState({
          // highlights: shuffleArray(queryData?.id ? [queryData, ...cleaned] : [...cleaned]),
          highlights: queryData?.id ? [queryData, ...cleaned] : [...cleaned],
          currentIndex: newIndex
        });
        if (queryData?.cb) {
          queryData.cb(false);
        }

        return cleaned;
      } catch (err) {
        notifyError(err?.response?.data?.message || err.message);
      }
    },

    async fetchHighlightsByUserId({ id, requestPayload }) {
      dispatch.feed.setError(null);

      try {
        const { data } = await FeedApi.getUsersHighlight(id, requestPayload);
        const feedState = getSingleModel("feed");

        const mergeNewFeedToOld = [
          ...feedState.profileHighlights,
          ...data.data.Items
        ];

        const cleaned = removeDuplicates(mergeNewFeedToOld, "id");
        // dispatch.feed.setState({
        //   profileHighlights: cleaned,
        //   profileLastEvaluatedKey: data?.data?.LastEvaluatedKey
        // });

        return {
          data: data?.data?.Items,
          lastEvaluatedKey: data?.data?.LastEvaluatedKey
        };
      } catch (err) {
        notifyError(err?.response?.data?.message || err.message);
      }
    },

    async createHighlights(payload) {
      dispatch.feed.setError(null);

      try {
        const { data } = await FeedApi.createHighlight(payload);

        if (data.status === 1) {
          if (payload.type === "VIDEO") {
            notifySuccess("Your video is being processed");
          } else {
            notifySuccess(data?.message || "Highlight created successfully");
            await dispatch.feed.fetchHighlights(data?.data?.newHighlight);
          }
        }
        return data;
      } catch (err) {
        notifyError(err?.response?.data?.message || err.message);
      }
    },

    async deleteHighlights(payload) {
      dispatch.feed.setError(null);

      try {
        const { data } = await FeedApi.deleteHighlight(payload);

        if (data.status === 1) {
          notifySuccess(data?.message);
          await dispatch.feed.fetchHighlights();
        }
        return data;
      } catch (err) {
        dispatch.feed.setError(err?.response?.data?.message || err.message);
        notifyError(err?.response?.data?.message || err.message);
      }
    },

    async reportHighlights(payload) {
      dispatch.feed.setError(null);

      try {
        const { data } = await FeedApi.reportHighlight(payload);

        if (data.status === 1) {
          notifySuccess(data?.message);
          await dispatch.feed.fetchHighlights();
        }
        return data;
      } catch (err) {
        dispatch.feed.setError(err?.response?.data?.message || err.message);
        notifyError(err?.response?.data?.message || err.message);
      }
    },

    async fetchHighlight(payload) {
      dispatch.feed.setError(null);

      try {
        const { data } = await FeedApi.fetchHighlight(payload);

        return data;
      } catch (err) {
        dispatch.feed.setError(err?.response?.data?.message || err.message);
        notifyError(err?.response?.data?.message || err.message);
      }
    },

    async editHighlights(payload, rootState) {
      const { noNotification, isEmojiUpdate, ...others } = payload;
      dispatch.feed.setError(null);

      if (isEmojiUpdate) {
        const highlights = rootState?.feed?.highlights;
        const newHighlights = highlights.map((item) => {
          if (item.id === payload.id) {
            return {
              ...item,
              reactedByUsers: payload.reactedByUsers
            };
          }
          return item;
        });
        dispatch.feed.setState({
          highlights: newHighlights
        });
      }

      try {
        const { data } = await FeedApi.editHighlight(others);

        if (data.status === 1) {
          !noNotification && notifySuccess(data?.message);
        }
        if (data.status !== 1 && isEmojiUpdate) {
          dispatch.highlights.setState({
            ...rootState?.highlights
          });
        }
        return data;
      } catch (err) {
        dispatch.feed.setError(err?.response?.data?.message || err.message);
        !noNotification &&
          notifyError(err?.response?.data?.message || err.message);

        if (isEmojiUpdate) {
          dispatch.highlights.setState({
            ...rootState?.highlights
          });
        }
      }
    },

    //GET TEAM HIGHLIGHTS BY ID
    async getTeamHighlights(payload) {
      dispatch.feed.setError(null);

      try {
        const res = await FeedApi.getTeamHighlights(payload);

        if (res.status === 200) {
          dispatch.feed.setState({
            teamHighlights: res.data.data
          });
        }
      } catch (err) {
        notifyError(err?.response?.data?.message || err.message);
      }
    },

    async reset() {
      await Promise.all([dispatch.feed.setState(initialState)]);
    }
  })
});
