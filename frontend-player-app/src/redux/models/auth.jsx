/* eslint-disable import/no-cycle */
import LiveLike from "@livelike/engagementsdk";
import { createModel } from "@rematch/core";
import { AuthApi } from "../../services";
import { notifyError, notifySuccess } from "../../utils/helpers";
import { reducerActions } from "../reducer";

import { TRACKING_EVENTS } from "../../utils/constants";
import Analytics from "../../utils/google-analytics";
import { initializeLiveLike } from "../../utils/LiveLike";
import { dispatch as globalDispatch } from "../store";

const initialState = {
  confirmSignUpModal: { visible: false, email: "" },
  authUser: {
    token: null,
    userInfo: {},
    authenticated: false,
    refreshToken: null,
    expiresIn: null,
  },
};

export const auth = createModel()({
  state: initialState,
  reducers: reducerActions,
  effects: (dispatch) => ({
    async userLogin(payload) {
      dispatch.auth.setError(null);
      const { navigate, values, from } = payload;

      try {
        const { data } = await AuthApi.login(values);

        const receivedData = data;

        if (receivedData.status === 0) {
          notifyError(receivedData.message);
          dispatch.auth.logout();
        }

        if (data?.data?.id) {
          Analytics.setUserProperties({
            user_id: data?.data?.id,
          });

          Analytics.trackEvent({
            name: TRACKING_EVENTS.LOGIN,
            metadata: {
              method: "email",
              email: values.email,
              userId: data?.data?.id,
            },
          });
        }

        dispatch.auth.setState({
          authUser: {
            token: receivedData?.data.tokens.access,
            userInfo: data.data,
            authenticated: true,
            refreshToken: receivedData?.data.tokens.refresh,
            expiresIn: receivedData?.data.tokens?.expiresIn,
          },
        });

        await initializeLiveLike();

        // Gets all clubs on login and stores in state
        dispatch.club.getAllClubs();
        // Gets all Teams
        dispatch.team.getAllTeams();

        if (receivedData?.data?.userType !== "NON_PLAYER") {
          if (receivedData?.data.position.length < 1) {
            navigate("/create-profile");
          } else if (receivedData?.data.cludId.length < 1) {
            navigate("/club-select");
          } else {
            navigate(from);
          }
        } else {
          if (receivedData?.data.cludId.length < 1) {
            navigate("/create-profile");
            return;
          }

          const playerVerificationId = window.localStorage.getItem(
            "verificationPlayerId"
          );

          if (Boolean(playerVerificationId)) {
            navigate(`/profile?id=${playerVerificationId}&verify=true`);
            return;
          } else {
            navigate("/");
          }
        }
      } catch (err) {
        if (err?.message?.includes("Method not allowed")) {
          dispatch.auth.logout();
          notifyError("Please retry loggin");
          // navigate("/");
        }

        if (err.response) {
          if (err.response.data.data?.name === "UserNotConfirmedException") {
            notifyError("Please confirm your Account");
            dispatch.auth.confirmSignUpModalBox({
              visible: true,
              email: values.email,
            });
          } else {
            notifyError(err.response.data.message);
          }
        }
        return null;
      }
    },

    async refreshToken(_, rootState) {
      try {
        const authUser = rootState.auth.authUser;

        if (!authUser?.refreshToken)
          throw new Error("No refresh token available.");

        const { data } = await AuthApi.refreshToken(authUser?.refreshToken);

        dispatch.auth.setState({
          authUser: {
            ...authUser,
            token: data.data.access,
            expiresIn: data.data.expiresIn,
          },
        });

        return data.data.access;
      } catch (error) {
        notifyError("Unkown Error! Please log in again.");
        dispatch.auth.logout();
      }
    },

    async userSignup(payload) {
      dispatch.auth.setError(false);
      try {
        const { playerId, ...payloadData } = payload.user;
        const { data } = await AuthApi.signup(payload.user);

        if (data.status !== 1) {
          notifyError(data.message);
        }
        if (data.status === 1) {
          if (playerId) {
            await window.localStorage.setItem("verificationPlayerId", playerId);
          }
          payload.navigate("/login");
          notifySuccess(data.message);
          // To be restored when we stop verifying from backend
          // dispatch.auth.confirmSignUpModalBox({
          //   visible: true,
          //   email: payload.email
          // });
        }
      } catch (err) {
        notifyError(err?.response?.data?.message || err.message);
      }
    },

    async confirmSignUpModalBox(payload) {
      dispatch.auth.setError(null);
      dispatch.auth.setState({
        confirmSignUpModal: { visible: payload.visible, email: payload.email },
      });
    },

    async confirmSignUp(payload) {
      dispatch.auth.setError(null);
      const { email, code, navigate } = payload;
      const confirmDetails = {
        email,
        verificationCode: code,
      };
      try {
        const { data } = await AuthApi.verifyAccount(confirmDetails);
        const { message, status } = data;
        if (status !== 1) {
          notifyError(message);
        }

        if (status === 1) {
          notifySuccess(message);
          dispatch.auth.confirmSignUpModalBox({ visible: false, email: null });
          navigate("/login");
        }
      } catch (error) {
        notifyError(error);
      }
    },

    async resendVerifyCode(payload) {
      dispatch.auth.setError(null);
      try {
        const { data } = await AuthApi.resendVerifyAccount(payload);
        const { status, message } = data;
        if (status === 0) {
          notifyError(message);
        }
        if (status === 1) {
          notifySuccess("Confirmation Code Sent");
        }
      } catch (err) {
        notifyError(
          err?.response?.data?.message || err.message || "An error occured"
        );
      }
    },

    async forgotPasswordModalBox(payload) {
      dispatch.auth.setError(null);
      dispatch.auth.setState({
        forgotPasswordModal: { visible: payload.visible },
      });
    },

    async forgotPassword(payload) {
      dispatch.auth.setError(null);
      try {
        const { data } = await AuthApi.forgotPassword(payload);
        const { status, message } = data;
        if (status === 0) {
          notifyError(message);
        }
        if (status === 1) {
          notifySuccess(message);
        }
        return status;
      } catch (err) {
        notifyError(err?.response?.data?.message || err.message);
      }
    },

    async resetPassword(payload) {
      dispatch.auth.setError(null);
      try {
        const { data } = await AuthApi.resetPassword(payload);
        const { status, message } = data;
        if (status === 0) {
          notifyError(message);
        }
        if (status === 1) {
          notifySuccess(message);
        }
        return status;
      } catch (err) {
        notifyError(err?.response?.data?.message || err.message);
      }
    },

    async changePassword(payload) {
      dispatch.auth.setError(null);
      try {
        const { data } = await AuthApi.changePassword(payload);
        const { status, message } = data;
        if (status === 0) {
          notifyError(message);
        }
        if (status === 1) {
          notifySuccess(message);
        }
        return status;
      } catch (err) {
        notifyError(err?.response?.data?.message || err.message);
      }
    },

    async reset() {
      await Promise.all([dispatch.auth.setState(initialState)]);
    },
    async delAccount(payload) {
      try {
        const { data } = await AuthApi.deleteAccount(payload);
        console.log("m", data.message);
        notifySuccess(data.message);
        return data.message;
      } catch (error) {
        notifyError(error.response.data.message || error.message);
      }
    },
    async logout() {
      try {
        Analytics.trackEvent({
          name: TRACKING_EVENTS.LOGOUT,
          metadata: {},
        });

        Analytics.setUserProperties({
          user_id: null,
        });

        LiveLike._$$.accessToken = "";
        LiveLike.userProfile.access_token = "";

        await Promise.all([
          globalDispatch.auth.reset(),
          globalDispatch.fixtures.reset(),
          globalDispatch.seasons.reset(),
          globalDispatch.leagues.reset(),
          globalDispatch.team.reset(),
          globalDispatch.user.reset(),
          globalDispatch.club.reset(),
          globalDispatch.feed.reset(),
          globalDispatch.matches.reset(),
          globalDispatch.seasons.reset(),
          globalDispatch.chat.reset(),
        ]);
        const allLocalItems = window.localStorage;
        for (const key in allLocalItems) {
          if (Object.hasOwnProperty.call(allLocalItems, key)) {
            window.localStorage.removeItem(key);
          }
        }
        localStorage.clear();
      } catch (error) {
        notifyError(error.message);
      }
    },
  }),
});
