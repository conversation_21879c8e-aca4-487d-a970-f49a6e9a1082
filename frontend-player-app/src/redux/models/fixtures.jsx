/* eslint-disable import/no-cycle */
import { createModel } from "@rematch/core";
import moment from "moment";
import { FixturesApi } from "../../services";
import { notifyError, notifySuccess } from "../../utils/helpers";
import { reducerActions } from "../reducer";

import "react-toastify/dist/ReactToastify.css";

const initialState = {
  fixtures: { loaded: false, data: [] }
};

export const fixtures = createModel()({
  state: initialState,
  reducers: reducerActions,
  effects: (dispatch) => ({
    async getFixtures({ teamId }) {
      try {
        const res = await FixturesApi.getFixtures({ teamId });

        if (res.status === 200) {
          dispatch.fixtures.setState({
            fixtures: { loaded: true, data: res.data.data }
          });
        }
      } catch (e) {
        console.log("matches Error", e);
      }
    },

    async reset() {
      await Promise.all([dispatch.fixtures.setState(initialState)]);
    }
  })
});
