/* eslint-disable import/no-cycle */
import { createModel } from "@rematch/core";
import moment from "moment";
import { User<PERSON><PERSON>, Auth<PERSON><PERSON> } from "../../services";
import {
  getSingleModel,
  notifyError,
  notifySuccess
} from "../../utils/helpers";
import { reducerActions } from "../reducer";
import _groupBy from "lodash.groupby";

import "react-toastify/dist/ReactToastify.css";

const now = moment().unix();

const initialState = {
  loaded: false,
  data: null,
  guest: null,
  usersByProjection: []
};
export const user = createModel()({
  state: initialState,
  reducers: reducerActions,
  effects: (dispatch, rootState) => ({
    async updateUser(payload) {
      dispatch.user.setError(null);
      const { shouldNotShowNotification, ...others } = payload;
      try {
        const { data } = await UserApi.update(others);

        const auth = getSingleModel("auth");
        const receivedData = data;

        if (data.status === 1) {
          if (data.data.id === auth.authUser.userInfo.id) {
            await dispatch.user.getUserProfile({ id: data.data.id });
            await dispatch.auth.setState({
              authUser: {
                token: auth.authUser.token,
                userInfo: { ...auth.authUser.userInfo, ...receivedData.data },
                authenticated: auth.authUser.authenticated,
                refreshToken: auth.authUser.refreshToken
              }
            });
          }
          if (!shouldNotShowNotification) {
            notifySuccess(receivedData.message);
          }
        } else {
          notifyError(receivedData.message);
        }
        return data.status;
      } catch (err) {
        console.log(err);
        const isArray = Array.isArray(err?.response?.data?.message);
        const message = isArray
          ? err.response.data.message.join(" - ")
          : err?.response?.data?.message;
        notifyError(message || err.message);
      }
    },

    async fetchUserDetails(payload) {
      try {
        const { data } = await UserApi.getUserProfile(payload);

        return data.data;
      } catch (error) {
        notifyError(error.response.data.message || error.message);
      }
    },
    async fetchUserBlockedUsers() {
      try {
        const { data } = await UserApi.fetchBlockedUsers();
        return data.data;
      } catch (error) {
        notifyError(error.response.data.message || error.message);
      }
    },

    async blockUser(payload) {
      try {
        const { data } = await UserApi.blockUser({
          blockedUserId: payload
        });
        notifySuccess(data.message);
        return data.message;
      } catch (error) {
        notifyError(error.response.data.message || error.message);
      }
    },

    async unBlockUser(payload) {
      try {
        const { data } = await UserApi.unBlockUser({
          blockedUserId: payload
        });
        notifySuccess(data.message);
        return data.message;
      } catch (error) {
        notifyError(error.response.data.message || error.message);
      }
    },

    async getUserProfile(payload) {
      try {
        const { data } = await UserApi.getUserProfile(payload.id);
        const sortedSExperience = data?.data?.experiences?.sort(
          (a, b) =>
            Number(a.seasonName.split("/")[1]) -
            Number(b.seasonName.split("/")[1])
        );

        if (!data?.data?.id) {
          dispatch.auth.logout();
        }

        const groupedData = _groupBy(
          sortedSExperience,
          (item) => item.teamName
        );

        await dispatch.user.setState({
          loaded: true,
          data: { ...data.data, experiences: groupedData }
        });
        return { ...data.data, experiences: groupedData };
      } catch (error) {
        notifyError(error.response.data.message || error.message);
      }
    },

    // This Method is used to get the user profile for guest. don't confuse it with the getUserProfile method
    async getUserProfileForGuest(payload) {
      try {
        const { data } = await UserApi.getGuestUserProfile(payload.id);
        const sortedSExperience = data?.data?.experiences?.sort(
          (a, b) =>
            Number(a.seasonName.split("/")[1]) -
            Number(b.seasonName.split("/")[1])
        );

        const groupedData = _groupBy(
          sortedSExperience,
          (item) => item.teamName
        );

        await dispatch.user.setState({
          loaded: true,
          guest: { ...data.data, experiences: groupedData }
        });

        return { ...data.data, experiences: groupedData };
      } catch (error) {
        notifyError(error.response.data.message || error.message);
      }
    },

    async userSearch(payload) {
      try {
        const { data } = await UserApi.userSearch(payload);
        return data;
      } catch (error) {
        notifyError(error.response.data.message || error.message);
      }
    },

    async userSearchByProjection() {
      try {
        const { data } = await UserApi.userSearchByProjection();
        await dispatch.user.setState({
          usersByProjection: data.data.Items
        });
        return data.data;
      } catch (error) {
        notifyError(error.response.data.message || error.message);
      }
    },

    async reset() {
      await Promise.all([dispatch.user.setState(initialState)]);
    }
  })
});
