/* eslint-disable import/no-cycle */
import { createModel } from "@rematch/core";
import moment from "moment";
import { Announcement } from "../../services";
import { reducerActions } from "../reducer";
import _groupBy from "lodash.groupby";

import "react-toastify/dist/ReactToastify.css";
import { getSingleModel } from "../../utils/helpers";

const now = moment().unix();

const initialState = {
  announcements: [],
  activeAnnouncements: []
};
export const announcement = createModel()({
  state: initialState,
  reducers: reducerActions,
  effects: (dispatch, rootState) => ({
    async getAllAnnouncements() {
      dispatch.announcement.setError(null);
      try {
        const { data } = await Announcement.getAnnouncements({ limit: 1000 });

        if (data.status === 1) {
          const activeAnnouncements = data.data.Items.filter(
            (item) => item.isActive === true && item.expired === false
          );
          dispatch.announcement.setState({
            announcements: data.data.Items
          });
          dispatch.announcement.setState({
            activeAnnouncements: activeAnnouncements
          });
        } else {
          console.log(data.message);
        }
        return data.data.Items;
      } catch (err) {
        console.log("ERROR", err);
      }
    },

    async getOneAnnouncement(payload) {
      dispatch.announcement.setError(null);
      try {
        const { data } = await Announcement.getOneAnnouncement(payload);
        return data.data.Item;
      } catch (err) {
        console.log("ERROR", err);
      }
    },

    async updateAnnouncement(payload, rootState) {
      dispatch.announcement.setError(null);
      // update the state immediately
      const activeAnnouncements = rootState?.announcement?.activeAnnouncements;
      const newActiveAnnouncements = activeAnnouncements.map((item) => {
        if (item.id === payload.id) {
          return {
            ...item,
            ...(payload?.reactedByUsers && { reactedByUsers: payload.reactedByUsers })
          };
        }
        return item;
      });
      const newAnnouncements = rootState.announcement.announcements.map((item) => {
        if (item.id === payload.id) {
          return {
            ...item,
            ...(payload?.reactedByUsers && { reactedByUsers: payload.reactedByUsers })
          };
        }
        return item;
      });
      dispatch.announcement.setState({
        activeAnnouncements: newActiveAnnouncements,
        announcements: newAnnouncements
      });

      try {
        // then make the API call
        const { data } = await Announcement.updateAnnouncement(payload);

        if (data.status !== 1) {
          // set the state to be what it was before
          dispatch.announcement.setState({
            ...rootState?.announcement
          });
        }
        return data.data.Attributes;
      } catch (err) {
        // set the state to be what it was before
        dispatch.announcement.setState({
          ...rootState?.announcement
        });
        console.log("ERROR", err);
      }
    }
  })
});
