/* eslint-disable import/no-cycle */
import { createModel } from "@rematch/core";
import { LeaguesApi } from "../../services";
import { notifyError, notifySuccess } from "../../utils/helpers";
import { reducerActions } from "../reducer";

import "react-toastify/dist/ReactToastify.css";
import { PUBLISHSTATUS } from "../../utils/constants";

const initialState = {
  allLeaguesAndStats: [],
  selectedLeagueData: {}
};

export const leagues = createModel()({
  state: initialState,
  reducers: reducerActions,
  effects: (dispatch) => ({
    async getAllLeaguesAndStats({ currentTeamId }, rootState) {
      dispatch.user.setError(null);

      try {
        const { data, status } = await LeaguesApi.getAllLeaguesAndStats();

        if (status === 200 && data?.data?.Items?.length > 0) {
          const leagues = data?.data?.Items?.filter(({ leagueTable }) =>
            leagueTable.some(({ teamId }) => teamId === currentTeamId)
          );

          const publishedLeagues = !(rootState?.user?.data?.isManager &&
            rootState?.user?.data?.teamId === currentTeamId) 
            ? leagues?.filter(
              (items) =>
                items?.publishStatus === PUBLISHSTATUS.PUBLISHED
                
            ) : leagues;

            publishedLeagues.sort(
              (a, b) =>
                Number(b?.seasonTitle?.split("/")[1]) -
                Number(a?.seasonTitle?.split("/")[1])
            );

          dispatch.leagues.setState({
            allLeaguesAndStats: publishedLeagues
          });
        }
      } catch (err) {
        notifyError(err.response.data.message);
      }
    },

    async setSelectedLeagueData({ data }) {
      await dispatch.leagues.setState({
        selectedLeagueData: data
      });
    },

    async reset() {
      await Promise.all([dispatch.leagues.setState(initialState)]);
    }
  })
});
