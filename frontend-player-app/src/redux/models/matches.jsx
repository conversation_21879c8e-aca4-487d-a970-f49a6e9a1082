import { createModel } from "@rematch/core";
import { MtchesA<PERSON> } from "../../services";
import { reducerActions } from "../reducer";

import "react-toastify/dist/ReactToastify.css";
import { notifyError, notifySuccess } from "../../utils/helpers";

const initialState = {
  teamDashboard: { loaded: false, data: [] },
};

export const matches = createModel()({
  state: initialState,
  reducers: reducerActions,

  effects: (dispatch) => ({
    async getMatchesbyTeamAndSeason({ teamId }) {
      try {
        const res = await MtchesApi.getMatchesbyTeamAndSeason({ teamId });

        if (res.status === 200) {
          dispatch.matches.setState({
            teamDashboard: { loaded: true, data: res.data.data },
          });
        }
      } catch (e) {
        console.log("Matches Error", e);
      }
    },

    async publishResult(data, rootState) {
      const results = rootState?.matches?.teamDashboard.data;

      const newresults = results.map((item) => {
        if (item.id === data.matchId) {
          return {
            ...item,
            publishStatus: data.publishStatus,
          };
        }
        return item;
      });
      dispatch.matches.setState({
        teamDashboard: { loaded: false, data: newresults },
      });

      // Update Backend
      try {
        const res = await MtchesApi.publishResult(data);
        if (res.data.status === 1) {
          notifySuccess(res.data.message);
        }
        return res;
      } catch (error) {
        notifyError(error.response.data.message);
      }
    },

    async reset() {
      await Promise.all([dispatch.matches.setState(initialState)]);
    },
  }),
});
