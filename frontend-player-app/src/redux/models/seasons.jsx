import { createModel } from "@rematch/core";
import { SeasonsApi } from "../../services";
import { reducerActions } from "../reducer";

import "react-toastify/dist/ReactToastify.css";
import { notifyError, notifySuccess } from "../../utils/helpers";

const initialState = {
  selectedSeasonData: {},
};

export const seasons = createModel()({
  state: initialState,
  reducers: reducerActions,
  effects: (dispatch) => ({
    async getTeam(payload) {
      try {
        const res = await SeasonsApi.getAllSeasons(payload.id);
      } catch (e) {
        console.log(e);
      }
    },

    setSelectedSeasonData({ data }) {
      dispatch.seasons.setState({
        selectedSeasonData: data,
      });
    },

    async reset() {
      await Promise.all([dispatch.seasons.setState(initialState)]);
    },
  }),
});
