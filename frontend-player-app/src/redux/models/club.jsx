/* eslint-disable import/no-cycle */
import { createModel } from "@rematch/core";
import moment from "moment";
import { Club<PERSON><PERSON> } from "../../services";
import { notifyError, notifySuccess } from "../../utils/helpers";
import { reducerActions } from "../reducer";

import "react-toastify/dist/ReactToastify.css";

const now = moment().unix();

const initialState = {
  allClubs: []
};

export const club = createModel()({
  state: initialState,
  reducers: reducerActions,
  effects: (dispatch) => ({
    async getAllClubs(payload, rootState) {
      dispatch.user.setError(null);
      const { auth, user } = rootState;
      const isPrivate =
        auth.authUser.userInfo?.allowPrivateClubsTeams ||
        user.data?.allowPrivateClubsTeams;
        
      const userType =
        auth.authUser.userInfo?.userType ||
        user.data?.userType;
        
      try {
        const { data } = await ClubApi.allClubs(isPrivate, userType);

        dispatch.club.setState({
          allClubs: data.data.Items
        });
        return data.data.Items;
      } catch (err) {
        notifyError(err.response.data.message);
      }
    },

    async reset() {
      await Promise.all([dispatch.club.setState(initialState)]);
    }
  })
});
