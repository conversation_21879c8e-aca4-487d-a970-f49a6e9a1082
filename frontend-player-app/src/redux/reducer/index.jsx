export const reducerActions = {
  setState(state, payloadOrUpdater) {
    if (typeof payloadOrUpdater === "function") {
      const nextState = payloadOrUpdater(state);
      return {
        ...state,
        ...nextState,
        isServerError: false,
      };
    }

    return {
      ...state,
      ...payloadOrUpdater,
      isServerError: false,
    };
  },
  setError(state, payload) {
    return {
      ...state,
      isServerError: payload,
    };
  },
  clear(state = {}, payload = {}) {
    return {
      ...state,
      ...payload,
    };
  },
};
