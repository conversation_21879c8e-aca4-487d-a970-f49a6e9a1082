type ObjectCannedACL =
  | "private"
  | "authenticated-read"
  | "aws-exec-read"
  | "bucket-owner-full-control"
  | "bucket-owner-read"
  | "public-read"
  | "public-read-write";
export interface IUploaderFile {
  bucket: string;
  key: string;
  contentType?: string;
  metaData?: Record<string, string>;
  isMultiPart: boolean;
  partNumber?: number;
  uploadId?: string;
  contentLength?: number;
  completedParts?: { ETag: string; PartNumber: number }[];
  totalParts?: number;
}
