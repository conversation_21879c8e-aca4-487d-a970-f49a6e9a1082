import LiveLike from "@livelike/engagementsdk";
import { getSingleModel } from "./helpers";
import { liveLikeClientId, currentEnv } from "./constants";

export async function initializeLiveLike() {
  try {
    const userInfo = getSingleModel("auth")?.authUser.userInfo;

    await LiveLike.init({
      accessToken: userInfo.liveLikeProfileToken,
      clientId: liveLikeClientId,
      logger: currentEnv === "dev",
    });
  } catch (error) {
    console.log("Error initialising LiveLike: ", error);
  }
}
