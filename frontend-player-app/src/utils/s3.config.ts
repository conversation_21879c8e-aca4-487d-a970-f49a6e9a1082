import Compressor from "compressorjs";
import * as Sentry from "@sentry/react";
import { photoBucket, uploadedStreamsBaseUrl } from "./constants";
import { UploaderApiHandler } from "../services";
import { IUploaderFile } from "./s3.interfaces";
import { uploadPartWithProgress } from "./trackUpload";

enum MediaTypes {
  PHOTO = "photo",
  VIDEO = "video"
}
const currentEnv = import.meta.env.VITE_APP_ENV;

class S3BucketManager {
  private async getUrlAndUpload(params, file: File | Blob) {
    const {
      data: { data }
    } = await UploaderApiHandler.getUploadUrl(params);
    return await fetch(data.url, {
      method: "PUT",
      body: file
    });
  }

  private async uploadMultipart(
    file: File,
    bucket: string,
    key: string,
    timeStamp: number,
    videoData?: { width: any; height: any; duration: any },
    onProgress?: (progress: number) => void
  ): Promise<string> {
    const multipartUploadParams: IUploaderFile = {
      bucket,
      key,
      contentType: file.type,
      metaData: {
        width: String(videoData?.width),
        height: String(videoData?.height),
        duration: String(videoData?.duration),
        fileSizeInBits: String(file.size),
        timeStamp: String(timeStamp)
      },
      isMultiPart: true
    };

    const {
      data: { data }
    } = await UploaderApiHandler.getUploadUrl(multipartUploadParams);

    const uploadId = data.uploadId;
    if (!uploadId) throw new Error("Failed to initiate multipart upload.");

    const partSize = 5 * 1024 * 1024; // 5MB
    const parts: any = [];
    const totalParts = Math.ceil(file.size / partSize);

    try {
      for (
        let partNumber = 1, start = 0;
        start < file.size;
        start += partSize, partNumber++
      ) {
        const end = Math.min(start + partSize, file.size);
        const part = file.slice(start, end);

        const uploadPartParams: IUploaderFile = {
          bucket,
          key,
          uploadId,
          partNumber,
          isMultiPart: true,
          totalParts,
          contentLength: part.size,
        };

        // const upload = await this.getUrlAndUpload(uploadPartParams, part);
        const eTag = await uploadPartWithProgress(
          uploadPartParams,
          part,
          onProgress,
          { partNumber, totalParts }
        );

        // const eTag = upload.headers.get("ETag")?.replace(/"/g, "");
        parts.push({ ETag: eTag, PartNumber: partNumber });
      }

      const completeMultipart: IUploaderFile = {
        bucket,
        key,
        uploadId,
        completedParts: parts,
        isMultiPart: true,
        totalParts,
      };

      const {
        data: { data }
      } = await UploaderApiHandler.getUploadUrl(completeMultipart);

      return data?.location || `${bucket}/${key}`;
    } catch (error) {
      const {
        data: { data }
      } = await UploaderApiHandler.abortUpload({ bucket, key, uploadId });
      throw new Error(`Multipart upload failed: ${error.message}`);
    }
  }

  private compressAndUpload(
    file: File,
    bucket: string,
    key: string
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      const getUrlAndUpload = this.getUrlAndUpload;

      new Compressor(file, {
        quality: 0.6,
        async success(result) {
          try {
            const uploadParams: IUploaderFile = {
              bucket,
              key,
              contentType: file.type,
              contentLength: result.size,
              isMultiPart: false,
              metaData: {
                originalName: file.name,
                size: String(file.size),
                type: file.type
              }
            };
            await getUrlAndUpload(uploadParams, result);

            resolve(`${import.meta.env.VITE_S3_BASE_PHOTO_URL}/${key}`);
          } catch (error) {
            console.log(error);

            reject(error);
          }
        },
        error(err) {
          reject(err);
        }
      });
    });
  }

  async uploadAssets(
    file: File,
    bucketName: string,
    keyName: string,
    type: MediaTypes,
    videoData?: { width: any; height: any; duration: any },
    onProgress?: (progress: number) => void
  ): Promise<{ assetUrl: string; streamUrls?: any }> {
    try {
      let assetUrl;
      let streamUrls;
      const ext = file.name.split(".")[file.name.split(".").length - 1];
      const key = `${Date.now()}.${ext}`;

      if (file.size < 4) {
        throw new Error("Not a valid file");
      }

      if (type === MediaTypes.PHOTO) {
        assetUrl = await this.compressAndUpload(file, photoBucket, key);
      } else {
        const videoBucket = import.meta.env.VITE_S3_VIDEO_UPLOAD_BASE_BUCKET;
        const timeStamp = Date.now();
        const videoKey = `videosOnly/--${timeStamp}--${key}`;

        // if file size is greater than 20mb, upload using multipart upload
        if (file.size > 20 * 1024 * 1024) {
          assetUrl = await this.uploadMultipart(
            file,
            videoBucket,
            videoKey,
            timeStamp,
            videoData,
            onProgress
          );
        } else {
          const uploadParams: IUploaderFile = {
            bucket: videoBucket,
            key: videoKey,
            isMultiPart: false,
            contentType: file.type,
            metaData: {
              width: String(videoData?.width),
              height: String(videoData?.height),
              duration: String(videoData?.duration),
              fileSizeInBits: String(file.size),
              timeStamp: String(timeStamp)
            }
          };

          await uploadPartWithProgress(uploadParams, file, onProgress, {
            partNumber: 1,
            totalParts: 1
          });
          assetUrl = `${
            import.meta.env.VITE_S3_VIDEO_UPLOAD_BASE_URL
          }/${videoKey}`;
        }

        streamUrls = {
          key: videoKey,
          baseUrl: uploadedStreamsBaseUrl
        };
      }

      return { assetUrl, streamUrls };
    } catch (error) {
      Sentry.captureException(error);
      throw new Error(error.message || "Upload failed.");
    }
  }
}

export default S3BucketManager;
