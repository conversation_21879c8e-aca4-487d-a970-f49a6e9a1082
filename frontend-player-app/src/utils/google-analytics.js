// google-analytics.js
import ReactGA from "react-ga4";

export const Analytics = {
    initialize: () => {
        const measurementId = `${import.meta.env.VITE_GA_MEASUREMENT_ID}`
        if (measurementId) {
            ReactGA.initialize(measurementId);
        }
    },

    pageView: (path) => {
        if (window.location.hostname !== "localhost") {
            ReactGA.send({ hitType: "pageview", page: path });
        }
    },

    trackEvent: ({ name, metadata = {} }) => {
        if (window.location.hostname !== "localhost") {
            ReactGA.event(name, {
                ...metadata,
                timestamp: new Date().toISOString()
            });
        }
    },

    setUserProperties: (properties) => {
        ReactGA.set(properties);
    }
};

export default Analytics;