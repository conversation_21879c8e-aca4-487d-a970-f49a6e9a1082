export const BASE_URL = import.meta.env.VITE_BASE_URL;
export const isMvp = true;
export const liveLikeClientId = import.meta.env.VITE_CLIENTID;
export const liveLikeReactionPackId = import.meta.env.VITE_REACTION_PACK_ID;
export const feedsReactionTargetGroupId = import.meta.env
  .VITE_FEEDS_REACTIONS_TARGET_GROUP_ID;
export const reactionSpaceId = import.meta.env.VITE_REACTIONSPACE_ID;
export const currentEnv = import.meta.env.VITE_APP_ENV;
export const clientId = import.meta.env.VITE_CLIENTID;
export const photoBucket = import.meta.env.VITE_BUCKETNAME;

export const uploadedStreamsBaseUrl = import.meta.env
  .VITE_S3_VIDEO_OUTPUT_BASE_URL;
export const videoOriginBaseUrl = import.meta.env.VITE_S3_VIDEO_ORIGIN_BASE_URL;

export const VIDEO_STATUS = {
  PROCESSING: "PROCESSING",
  SUCCESSFUL: "SUCCESSFUL",
  FAILED: "FAILED"
};

export const LiveLikeEvents = {
  DELETED: "messagedeleted",
  RECEIVED: "messagereceived",
  UPDATED: "messageupdated",
  SENT: "messagesent",
  FAILED: "messagefailed",
  ADDED: "reactionadded",
  REMOVED: "reactionremoved",
  MESSAGES: "messagehistory",
  ROOMENTER: "roomentered",
  ROOMEXIT: "roomexited"
};
const LiveLikeReactionPacksByEnv = {
  dev: {
    id: "5f5591e5-1f0d-4eb6-b8b3-c1413f6b7627",
    url: "https://cf-blast.livelikecdn.com/api/v1/reaction-packs/5f5591e5-1f0d-4eb6-b8b3-c1413f6b7627/",
    name: "LiveLike Default Reactions",
    emojis: [
      {
        id: "f895b9ba-8d86-455f-9a7d-ce87d7504e0b",
        name: "Cry",
        file: "https://cf-blast-storage.livelikecdn.com/assets/902513b1-3f16-4d96-a36f-b9832c31e4f4.png",
        mimetype: "image/png"
      },
      {
        id: "3eb68561-4ab3-4d9d-a5c8-c8b26230867c",
        name: "Lol",
        file: "https://cf-blast-storage.livelikecdn.com/assets/7c26355b-e012-48cc-8f45-3fac124c9b12.png",
        mimetype: "image/png"
      },
      {
        id: "1ea8f088-0a2e-4287-926e-3de10464b9c9",
        name: "Love",
        file: "https://cf-blast-storage.livelikecdn.com/assets/7599e585-9ba4-4066-97b7-108d1281d3fb.png",
        mimetype: "image/png"
      },
      {
        id: "dad55363-f65f-45ba-b998-2e1ad57f2870",
        name: "Shock",
        file: "https://cf-blast-storage.livelikecdn.com/assets/4a3a0f42-ae8c-4d2d-ae75-0876d48ad081.png",
        mimetype: "image/png"
      },
      {
        id: "e1963cee-4fc4-4060-8cd2-3e681b6b568f",
        name: "Wow",
        file: "https://cf-blast-storage.livelikecdn.com/assets/7a407ebd-ba69-4271-8f16-7beffd4b16a6.png",
        mimetype: "image/png"
      }
    ]
  },
  production: {
    id: "c6a72dd4-639e-4c77-b19b-61d14104a0c4",
    url: "https://cf-blast.livelikecdn.com/api/v1/reaction-packs/c6a72dd4-639e-4c77-b19b-61d14104a0c4/",
    name: "PlayerApp Reactions Pack",
    emojis: [
      {
        id: "238e9911-5ad7-4f87-bb11-2e6e15e7eb71",
        name: "Heart",
        file: "https://cf-blast-storage.livelikecdn.com/assets/277cdf14-32cd-47a7-bd6e-94fd1160ac4a.png",
        mimetype: "image/png"
      },
      {
        id: "d7e0acd1-f39f-46e6-b473-306f5c9e2802",
        name: "ThumbsUp",
        file: "https://cf-blast-storage.livelikecdn.com/assets/9bf6af11-e854-4de8-a7fc-d7c711a38e86.png",
        mimetype: "image/png"
      },
      {
        id: "f9cbd29b-f584-4cb9-8ad9-4af2d1042610",
        name: "Laughing",
        file: "https://cf-blast-storage.livelikecdn.com/assets/4a5a2457-06d5-4d34-9d96-75272ba7f667.png",
        mimetype: "image/png"
      },
      {
        id: "fab8d673-b795-4902-ab1b-0ee7e307b5d7",
        name: "Gasp",
        file: "https://cf-blast-storage.livelikecdn.com/assets/9784859c-4914-4e03-8a44-c7586f4b4e6e.png",
        mimetype: "image/png"
      },
      {
        id: "7ee35aef-d17a-4af3-b325-3aeb09b5efa8",
        name: "Clap",
        file: "https://cf-blast-storage.livelikecdn.com/assets/d24b633e-e4eb-46ae-84c6-f1fdee6107fe.png",
        mimetype: "image/png"
      },
      {
        id: "e35244ba-72ac-43a3-a6c9-f686863f2ace",
        name: "Fire",
        file: "https://cf-blast-storage.livelikecdn.com/assets/fad5577b-88d6-44e5-9c43-664b41ea64d6.png",
        mimetype: "image/png"
      }
    ]
  }
};

export const PUBLISHSTATUS = {
  DRAFT: "DRAFT",
  PUBLISHED: "PUBLISHED",
  PREVIEW: "PREVIEW"
};

export const TRACKING_EVENTS = {
  PAGE_VIEW: 'page_view',
  LOGIN: 'user_login',
  LOGOUT: 'user_logout',
  BUTTON_CLICK: 'button_click',
  COMMENT_ADDED: 'comment_added',
  REACTION_ADDED: 'reaction_added',
  REACTION_REMOVED: 'reaction_removed',
  CONTENT_SHARED: 'content_shared',
  SEARCH_PERFORMED: 'search_performed',
  FORGOT_PASSWORD_CLICKED: 'forgot_password_clicked'
};

export const TRACKING_CATEGORY = {
  USER: 'user',
  HIGHLIGHT: 'highlight',
  ANNOUNCEMENT: 'announcement',
};

export const LiveLikeReactionPacks = LiveLikeReactionPacksByEnv[currentEnv];

