import axios from "axios";
import { Uploader<PERSON>piHandler } from "../services";

const axiosInstance2 = axios.create({});

export async function uploadPartWithProgress(
  params,
  blob,
  onProgress,
  { partNumber, totalParts }
) {
  const {
    data: { data }
  } = await UploaderApiHandler.getUploadUrl(params);
  const response = await axiosInstance2.put(data.url, blob, {
    headers: {
      "Content-Type": blob.type
    },
    onUploadProgress: (progressEvent) => {
      const percentCompleted = progressEvent.loaded / progressEvent.total;

      const partsBefore = partNumber - 1;

      const overallProgress =
        ((partsBefore + percentCompleted) / totalParts) * 100;

      onProgress(Math.round(overallProgress));
    }
  });

  const eTag = response.headers.etag?.replace(/"/g, "");
  return eTag;
}
