/* eslint-disable import/no-cycle */
import { toast } from "react-toastify";

import moment from "moment";
import store from "../redux/store";

export const getAllModels = () => {
  return store.getState();
};

export const getSingleModel = (model) => {
  const currentState = store.getState();
  return currentState?.[model];
};

export const reduxDispatch = () => store.dispatch;

export const formatCurrency = (value) => {
  return new Intl.NumberFormat("en-US", { minimumFractionDigits: 2 }).format(
    value
  );
};

export const notifySuccess = (message) =>
  toast.success(message, {
    position: "top-right",
    autoClose: 2000,
    hideProgressBar: true,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
    progress: undefined,
  });

export const notifyError = (message) =>
  toast.error(message, {
    position: "top-right",
    autoClose: 2000,
    hideProgressBar: true,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
    progress: undefined,
  });

export const notifyWarn = (message) =>
  toast.warn(message, {
    position: "top-right",
    autoClose: 2000,
    hideProgressBar: true,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
    progress: undefined,
  });

export const calculateAge = (dateOfBirth) => {
  if (!dateOfBirth) {
    return "";
  }
  const age = Math.floor(
    moment(new Date()).diff(
      moment(dateOfBirth, [
        "DD/MM/YYYY",
        "D/M/YYYY",
        "DD.MM.YYYY",
        "D.M.YYYY",
        "DD. MM. YYYY",
        "D. M. YYYY",
        "YYYY-MM-DD",
        "DD-MM-YYYY",
      ]),
      "years",
      true
    )
  );
  return age < 0 ? 0 : age;
};

export const cleanDate = (dateOfBirth = new Date()) => {
  // const dob = dateOfBirth || "";
  // const splitDate = dob.split("/");
  // const birthdateTimeStamp = new Date(
  //   splitDate[2],
  //   splitDate[1] - 1,
  //   splitDate[0]
  // );

  return moment(dateOfBirth, [
    "DD/MM/YYYY",
    "D/M/YYYY",
    "DD.MM.YYYY",
    "D.M.YYYY",
    "DD. MM. YYYY",
    "D. M. YYYY",
    "YYYY-MM-DD",
    "DD-MM-YYYY",
  ]).format("YYYY-MM-DD");
};

export const PlayerPostion = {
  GOAL_KEEPERS: "Goalkeeper",
  CENTRE_BACK: "Centre Back",
  LEFT_BACK: "Left Back",
  RIGHT_BACK: "Right Back",
  WING_BACK: "Wing Back",
  DEFENSIVE_MIDFIELD: "Defensive Midfield",
  CENTRAL_MIDFIELD: "Central Midfield",
  ATTACKING_MIDFIELD: "Attacking Midfield",
  WINGER: "Winger",
  FORWARD: "Forward",
  STRIKER: "Striker",
};

export const PreferredFoot = {
  RIGHT_FOOTED: "Right Footed",
  LEFT_FOOTED: "Left Footed",
  BOTH_FOOTED: "Both Footed",
};

export const readablePosition = (position) => {
  if (PlayerPostion[position]) {
    return PlayerPostion[position];
  }
  return position?.split("_").join(" ").toLowerCase();
};

export const trim150 = (str, length = 150) => {
  return str?.length > length ? str.substring(0, length) + "..." : str;
};

export const trim = (str, length) => {
  return str?.length > length ? str.substring(0, length) + "..." : str;
};

export const trim80 = (str, highlightId, userId) => {
  return str?.length > 80 ? (
    <div>
      {str.substring(0, 80)}...{" "}
      <a
        className="text-gray-500"
        href={`/user/comment?highlightId=${highlightId}&userId=${userId}`}
      >
        more
      </a>
    </div>
  ) : (
    str
  );
};

export const removeEmpty = (obj) => {
  return Object.fromEntries(
    Object.entries(obj).filter(
      ([_, v]) => v != "" || v != null || v != undefined
    )
  );
};

export function validateURL(textval) {  
  var urlregex = /^(https?|ftp):\/\/[^\s/$.?#].[^\s]*$/i  
  return urlregex.test(textval);
}

/**
 * Long numbers to appear in a shorter, and more eaisier to understand way
 * @param {number} num the number to be shortened
 * @param {number} decimalToDisplay is the number of decimal place to display in the abbreviation, not the input number
 * @returns
 */
export function getLikeShortNumberRound(num, decimdalToDisplay) {
  let x = ("" + num).length;
  let p = Math.pow;
  decimdalToDisplay = p(10, decimdalToDisplay);
  x -= x % 3;
  return (
    Math.round((num * decimdalToDisplay) / p(10, x)) / decimdalToDisplay +
    " kMGTPE"[x / 3]
  );
}

export function getTotalLikes(reactions) {
  const totalLikes = reactions?.reduce((prev, curr) => {
    return { count: (prev.count || 0) + (curr.count || 0) };
  }, [])?.count;
  const totalReactions = getLikeShortNumberRound(totalLikes, 1);
  return totalLikes === 0 ? "" : totalReactions;
}

export const removeDuplicates = (array, property) => {
  const uniqueIds = [];

  const unique = array.filter((element) => {
    const isDuplicate = uniqueIds.includes(element[property]);

    if (!isDuplicate) {
      uniqueIds.push(element[property]);

      return true;
    }

    return false;
  });

  return unique;
};

// const ffmpeg = createFFmpeg({ log: currentEnv !== "production" });

// export const loadFFmpeg = async () => {
//   if (!ffmpeg.isLoaded()) await ffmpeg.load();
// };

// export const processVideo = async (file) => {
//   const inputName = `input-${Date.now()}-${file.name}`;
//   const outputName = `${Date.now()}-${file.name}`;
//   ffmpeg.FS("writeFile", inputName, await fetchFile(file));
//   await ffmpeg.run(
//     "-i",
//     inputName,
//     "-c:v",
//     "libx264",
//     "-crf",
//     "24",
//     outputName
//   );
//   const outputData = ffmpeg.FS("readFile", outputName);
//   return outputData.buffer;
// };

export const getVideoDuration = async (file) => {
  const video = document.createElement("video");
  video.preload = "metadata";

  video.onloadedmetadata = () => {
    window.URL.revokeObjectURL(video.src);
    const duration = video.duration;
    return duration;
  };

  video.src = URL.createObjectURL(file);
};

/**
 * @description This function is used to search an array of objects or make a call to the API
 * @param {object} param
 * @param {Array} param.array
 * @param {string} param.searchTerm
 * @param {[Func]} param.makeSearchCall
 * @returns {Array<Record<string, string>> | Array<Promise> }
 */
export async function searchArrayOrMakeCallToAPI({
  array = [],
  searchTerm = "",
  makeSearchCall,
}) {
  if (!Boolean(searchTerm)) {
    return [];
  }

  const searchResults = array.filter((item) => {
    for (const key in item) {
      if (
        typeof item[key] === "string" &&
        item[key].toLowerCase().includes(searchTerm.toLowerCase())
      ) {
        return true;
      }
    }
    return false;
  });

  if (searchResults.length > 0) {
    return searchResults;
  } else {
    try {
      const searchPromises = makeSearchCall.map((funcs) => funcs(searchTerm));
      const searchResponses = await Promise.all(searchPromises);

      const mergedData = [
        ...searchResponses[0]?.data,
        ...searchResponses[1]?.data,
      ]?.map((data) => {
        return {
          ...data,
          type: data?.logoUrl === undefined ? "user" : "team",
        };
      });
      return mergedData;
    } catch (error) {
      console.error("Error fetching data from API:", error);
      return [];
    }
  }
}

export const findMatchingObject = (array1, array2) => {
  for (let obj1 of array1) {
    for (let obj2 of array2) {
      if (obj1.id === obj2.id) {
        return obj1;
      }
    }
  }
  return null;
};

export const isSafari = /^((?!chrome|android).)*safari/i.test(
  navigator.userAgent
);

export function isSafariFn() {
  const userAgent = navigator.userAgent;
  const vendor = navigator.vendor;

  return (
    vendor &&
    vendor.includes("Apple") &&
    userAgent &&
    !userAgent.includes("CriOS") &&
    !userAgent.includes("FxiOS")
  );
}

export const momentGetTimeAgo = (timestamp) => {
  const diffInMinutes = moment().diff(timestamp, "minutes");
  const diffInHours = moment().diff(timestamp, "hours");
  const diffInDays = moment().diff(timestamp, "days");
  const diffInWeeks = moment().diff(timestamp, "weeks");
  const diffInMonths = moment().diff(timestamp, "months");
  const diffInYears = moment().diff(timestamp, "years");

  if (diffInMinutes < 1) {
    return "Just now";
  } else if (diffInMinutes < 60) {
    return `${diffInMinutes}mins`;
  } else if (diffInHours < 24) {
    return `${diffInHours}h`;
  } else if (diffInDays < 7) {
    return `${diffInDays}d`;
  } else if (diffInWeeks < 4) {
    return `${diffInWeeks}w`;
  } else if (diffInMonths < 12) {
    return `${diffInMonths}m`;
  } else {
    return `${diffInYears}y`;
  }
};

export const getTimeAgo = (timestamp) => {
  const currentTime = new Date();
  const targetTime = new Date(timestamp);

  const timeDiff = currentTime.getTime() - targetTime.getTime();
  const seconds = Math.floor(timeDiff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  const months = Math.floor(days / 30);
  const years = Math.floor(days / 365);

  if (seconds < 60) {
    return "just now";
  } else if (minutes < 60) {
    return `${minutes} ${pluralize("minute", minutes)} ago`;
  } else if (hours < 24) {
    return `${hours} ${pluralize("hour", hours)} ago`;
  } else if (days < 30) {
    return `${days} ${pluralize("day", days)} ago`;
  } else if (days < 365) {
    return `${months} ${pluralize("month", months)} ago`;
  } else {
    return `${years} ${pluralize("year", years)} ago`;
  }
};

const pluralize = (word, count) => {
  return count === 1 ? word : `${word}s`;
};

export const handleCopyToClipBoard = (text) => {
  navigator.clipboard.writeText(text);
  notifySuccess("URL has been copied to your clipboard");
};

export function linkify(text) {
  const urlRegex =
    /(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/gi;

  const newTextWithLink = text.replace(urlRegex, function (url) {
    return `<a href='${url}' target="_blank">
        ${url}
      </a>`;
  });
  return <span dangerouslySetInnerHTML={{ __html: newTextWithLink }} />;
}

export function getUniqueEmojis(arr) {
  const array = Array.isArray(arr) ? arr : [];

  const uniqueEmojisSet = new Set();

  array.forEach((item) => {
    if (item.others && item.others.emoji) {
      uniqueEmojisSet.add(item.others.emoji);
    }
  });

  const uniqueEmojisArray = Array.from(uniqueEmojisSet);
  return uniqueEmojisArray;
}
/**
 *
 * @param {*} followers followers of the current profile and not necessarily the logged in user
 * @param {*} userId the id of the logged in user
 * @returns
 */
export const checkIfUserFollowsProfile = (followers, userId) => {
  for (let object of followers) {
    const follow = JSON.parse(object.from_profile.custom_data);
    if (follow.userId === userId) {
      return true;
    }
  }
  return false;
};

export const fetchRelationshipObject = (array, userId) => {
  const obj = array.find((item) => {
    const parsedData = JSON.parse(item.from_profile.custom_data);
    return parsedData.userId === userId;
  });
  return obj || null;
};

export const formatDateToWords = (dateString) => {
  function getDaySuffix(day) {
    if (day >= 11 && day <= 13) {
      return "TH";
    }
    switch (day % 10) {
      case 1:
        return "ST";
      case 2:
        return "ND";
      case 3:
        return "RD";
      default:
        return "TH";
    }
  }
  const months = [
    "JANUARY",
    "FEBRUARY",
    "MARCH",
    "APRIL",
    "MAY",
    "JUNE",
    "JULY",
    "AUGUST",
    "SEPTEMBER",
    "OCTOBER",
    "NOVEMBER",
    "DECEMBER",
  ];

  const dateParts = dateString.split("/");
  const day = parseInt(dateParts[0], 10);
  const month = parseInt(dateParts[1], 10);
  const year = parseInt(dateParts[2], 10);

  const formattedDate = `${day}${getDaySuffix(day)} ${
    months[month - 1]
  } ${year}`;

  return formattedDate;
};

export const shuffleArray = (array) => {
  const shuffledArray = [...array];
  for (let i = shuffledArray.length - 1; i > 0; i--) {
    const randomIndex = Math.floor(Math.random() * (i + 1));
    // Swap elements
    [shuffledArray[i], shuffledArray[randomIndex]] = [
      shuffledArray[randomIndex],
      shuffledArray[i],
    ];
  }
  return shuffledArray;
};

/**
 * Generates video details and thumbnail
 * @param {File} videofile
 * @param {number} seekTo
 * @param {boolean} videoPropsOnly
 * @returns {Promise<{duration: number, thumbnail: string, orientation: string}}
 */
export const generateVideoDetailsAndThumbnail = (
  videofile,
  seekTo = 0.0,
  videoPropsOnly
) => {
  return new Promise((resolve, reject) => {
    const videoPlayer = document.createElement("video");
    const fileReader = new FileReader();

    fileReader.onload = () => {
      videoPlayer.src = fileReader.result; // Use data URL as source
      videoPlayer.crossOrigin = "anonymous"; // Enable cross-origin compatibility if needed
      videoPlayer.load();

      videoPlayer.addEventListener("error", (ex) => {
        reject(`Error when loading video file: ${ex.message}`);
      });

      videoPlayer.addEventListener("loadedmetadata", () => {
        if (videoPlayer.duration < seekTo) {
          reject("Video is too short.");
          return;
        }
        videoPlayer.currentTime = seekTo;
        console.log(videoPropsOnly);

        if (videoPropsOnly) {
          console.log({
            orientation:
              videoPlayer.videoWidth > videoPlayer.videoHeight
                ? "landscape"
                : "portrait",
            duration: videoPlayer.duration,
          });

          return resolve({
            orientation:
              videoPlayer.videoWidth > videoPlayer.videoHeight
                ? "landscape"
                : "portrait",
            duration: videoPlayer.duration,
          });
        }

        videoPlayer.addEventListener("seeked", () => {
          try {
            const canvas = document.createElement("canvas");
            canvas.width = videoPlayer.videoWidth;
            canvas.height = videoPlayer.videoHeight;
            const ctx = canvas.getContext("2d");
            ctx.drawImage(videoPlayer, 0, 0, canvas.width, canvas.height);

            const orientation =
              videoPlayer.videoWidth > videoPlayer.videoHeight
                ? "landscape"
                : "portrait";

            ctx.canvas.toBlob(
              (blob) => {
                if (blob)
                  resolve({
                    thumbnail: blob,
                    duration: videoPlayer.duration,
                    orientation,
                  });
                else reject("Failed to create thumbnail.");
              },
              "image/jpeg",
              0.75
            );
          } catch (error) {
            reject(`Error during thumbnail generation: ${error.message}`);
          }
        });
      });
    };

    fileReader.onerror = () => {
      reject("Failed to read video file.");
    };

    fileReader.readAsDataURL(videofile);
  });
};

// allows the show of waiting icon if the highlight is less than 1 hour old and not processed
export const shouldShowWaitIcon = (
  highlight,
  unitOfTime = "hours",
  waitTime = 1
) => {
  const diffInTime = moment().diff(moment(highlight.createdAt), unitOfTime);
  if (["PROCESSING", "FAILED"].includes(highlight.videoStatus)) {
    return true;
  } else if (
    (!highlight.queueProcessed && diffInTime >= waitTime) ||
    highlight.queueProcessed
  ) {
    return false;
  }
  return true;
};
// allows the show of waiting icon if the highlight is less than 1 hour old and not processed
export const getVideoStatus = (
  highlight,
  unitOfTime = "hours",
  waitTime = 1
) => {
  const diffInTime = moment().diff(moment(highlight.createdAt), unitOfTime);
  if (highlight.videoStatus === "PROCESSING") {
    return "PROCESSING";
  } else if (
    (!highlight.queueProcessed && diffInTime >= waitTime * 2) ||
    highlight.queueProcessed ||
    highlight.videoStatus === "SUCCESSFUL"
  ) {
    return "SUCCESSFUL";
  } else if (highlight.videoStatus === "FAILED") {
    return "FAILED";
  }
  return "PROCESSING";
};

export const getUploadColor = (progress) => {
  if (progress <= 20) {
    return "text-red-400";
  } else if (progress <= 70) {
    return "text-yellow-500";
  } else if (progress > 70) {
    return "text-green-500";
  }
  return "text-black";
};
