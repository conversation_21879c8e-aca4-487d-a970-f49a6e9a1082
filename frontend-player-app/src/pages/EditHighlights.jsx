import { Link, useNavigate } from "react-router-dom";
import BackButton from "../components/reusable/buttons/BackButton";
import BigBlack from "../components/reusable/buttons/BigBlack";
import Header from "../components/reusable/Header";
import Logo from "../components/reusable/Logo";
import PageTitle from "../components/reusable/PageTitle";
import VideoUI from "../components/reusable/VideoUI";
import { useLongPress } from "../hooks";

const EditHighlights = () => {
  const navigate = useNavigate();

  const highlightsMockData = [
    {
      type: "video",
      url: "/images/footballVideo.mp4",
    },
    {
      type: "photo",
      url: "https://media.istockphoto.com/id/1347044133/photo/confident-female-soccer-player-practicing-skills-at-court.jpg?b=1&s=170667a&w=0&k=20&c=YRdCI457Dt6rVGQm0vySxin81VAZYXQQ_5SQs0vcN-U=",
    },
    {
      type: "photo",
      url: "https://media.istockphoto.com/id/1347044133/photo/confident-female-soccer-player-practicing-skills-at-court.jpg?b=1&s=170667a&w=0&k=20&c=YRdCI457Dt6rVGQm0vySxin81VAZYXQQ_5SQs0vcN-U=",
    },
    {
      type: "video",
      url: "/images/footballVideo.mp4",
    },
    {
      type: "video",
      url: "/images/footballVideo.mp4",
    },
    {
      type: "photo",
      url: "https://media.istockphoto.com/id/1347044133/photo/confident-female-soccer-player-practicing-skills-at-court.jpg?b=1&s=170667a&w=0&k=20&c=YRdCI457Dt6rVGQm0vySxin81VAZYXQQ_5SQs0vcN-U=",
    },
    {
      type: "photo",
      url: "https://media.istockphoto.com/id/1347044133/photo/confident-female-soccer-player-practicing-skills-at-court.jpg?b=1&s=170667a&w=0&k=20&c=YRdCI457Dt6rVGQm0vySxin81VAZYXQQ_5SQs0vcN-U=",
    },
    {
      type: "video",
      url: "/images/footballVideo.mp4",
    },
    {
      type: "video",
      url: "/images/footballVideo.mp4",
    },
  ];

  const onLongPress = () => {
    navigate("/team-dashboard/result-details/edit-highlight");
  };

  const defaultOptions = {
    shouldPreventDefault: true,
    delay: 500,
  };
  const longPressEvent = useLongPress(onLongPress, () => {}, defaultOptions);

  return (
    <div className="pb-[70px] page">
      <Header>
        <BackButton from="" />
        <Logo />
        <div />
      </Header>
      <PageTitle center={true}>
        <div className="mb-4">HIGHLIGHTS</div>
      </PageTitle>
      <div className="grid grid-cols-2 my-3 gap-2">
        {highlightsMockData.map(({ type, url }, i) => (
          <div
            key={i}
            className="w-full aspect-[4/7] overflow-hidden rounded-[32px]"
          >
            {type === "video" ? (
               <VideoUI videoUrl={url} />
            ) : (
              <img 
                {...longPressEvent}
                src="https://media.istockphoto.com/id/1347044133/photo/confident-female-soccer-player-practicing-skills-at-court.jpg?b=1&s=170667a&w=0&k=20&c=YRdCI457Dt6rVGQm0vySxin81VAZYXQQ_5SQs0vcN-U="
                alt="Photo"
                className="w-full h-full object-cover"
              />
            )}
          </div>
        ))}
      </div>
      <Link to="/upload-feed" className="sticky bottom-[10px] cursor-pointer">
        <BigBlack label="upload video" />
      </Link>
    </div>
  );
};

export default EditHighlights;
