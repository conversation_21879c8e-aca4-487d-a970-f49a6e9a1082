import BackButton from "../components/reusable/buttons/BackButton";
import Header from "../components/reusable/Header";
import PageTitle from "../components/reusable/PageTitle";
import { useFormik } from "formik";
import { changePasswordScheme } from "../../src/utils/formSchema";
import { BigButtons } from "../components/reusable/buttons/Buttons";
import { useNavigate } from "react-router-dom";
import { dispatch } from "../redux/store";
import { useSelector } from "react-redux";

const SecuritySetting = () => {
  const navigate = useNavigate();
  const submitPasswordChange = async (values) => {
    const cleanedValues = {
      oldPassword: values.oldPassword,
      newPassword: values.newPassword,
    };
    const res = await dispatch.auth.changePassword(cleanedValues);
    if (res === 1) navigate("/profile");
  };

  const initialValues = {
    oldPassword: "",
    newPassword: "",
    confirmPassword: "",
  };

  const updatingPassword = useSelector(
    ({ loading }) => loading.effects.auth.changePassword
  );

  const { values, errors, touched, handleBlur, handleChange, handleSubmit } =
    useFormik({
      initialValues: initialValues,
      onSubmit: submitPasswordChange,
      validationSchema: changePasswordScheme,
    });

  return (
    <div className="page md:max-w-[700px] mx-auto">
      <Header>
        <BackButton from="" />
      </Header>
      <PageTitle>
        <div>Security</div>
      </PageTitle>
      <form
        onSubmit={handleSubmit}
        className="flex flex-col justify-between min-h-[85vh]"
      >
        <div>
          <div className="relative z-0 w-full mb-6 mt-[36px] group">
            <input
              onBlur={handleBlur}
              value={values.oldPassword}
              onChange={handleChange}
              type="password"
              name="oldPassword"
              id="oldPassword"
              className={` block py-2.5 px-0 w-full text-sm text-gray-900 bg-transparent border-0 border-b-2 border-gray-300 appearance-none dark:text-white dark:border-gray-600 dark:focus:border-black-800 focus:outline-none focus:ring-0 focus:border-gray-600 peer`}
              placeholder="******"
            />
            <label
              htmlFor="oldPassword"
              className="peer-focus:font-medium absolute text-sm text-gray-500 dark:text-gray-400 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-gray-600 peer-focus:dark:text-gray-100 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6"
            >
              Current Password
            </label>
            <p className="text-red-500 text-xs italic py-1">
              {errors.oldPassword && touched.oldPassword
                ? errors.oldPassword
                : ""}
            </p>
          </div>
          <div className="relative z-0 w-full mb-6 group">
            <input
              onBlur={handleBlur}
              value={values.newPassword}
              onChange={handleChange}
              type="password"
              name="newPassword"
              id="newPassword"
              className={` block py-2.5 px-0 w-full text-sm text-gray-900 bg-transparent border-0 border-b-2 border-gray-300 appearance-none dark:text-white dark:border-gray-600 dark:focus:border-black-800 focus:outline-none focus:ring-0 focus:border-gray-600 peer`}
            />
            <label
              htmlFor="newPassword"
              className="peer-focus:font-medium absolute text-sm text-gray-500 dark:text-gray-400 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-gray-600 peer-focus:dark:text-gray-100 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6"
            >
              New Password
            </label>
            <p className="text-red-500 text-xs italic py-1">
              {errors.newPassword && touched.newPassword
                ? errors.newPassword
                : ""}
            </p>
          </div>
          <div className="relative z-0 w-full mb-6 group">
            <input
              onBlur={handleBlur}
              value={values.confirmPassword}
              onChange={handleChange}
              type="password"
              name="confirmPassword"
              id="confirmPassword"
              className={` block py-2.5 px-0 w-full text-sm text-gray-900 bg-transparent border-0 border-b-2 border-gray-300 appearance-none dark:text-white dark:border-gray-600 dark:focus:border-black-800 focus:outline-none focus:ring-0 focus:border-gray-600 peer`}
            />
            <label
              htmlFor="confirmPassword"
              className="peer-focus:font-medium absolute text-sm text-gray-500 dark:text-gray-400 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-gray-600 peer-focus:dark:text-gray-100 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6"
            >
              Confirm New Password
            </label>
            <p className="text-red-500 text-xs italic py-1">
              {errors.confirmPassword && touched.confirmPassword
                ? errors.confirmPassword
                : ""}
            </p>
          </div>
        </div>
        <BigButtons isLoading={updatingPassword} black label={"DONE"} />
      </form>
    </div>
  );
};
export default SecuritySetting;
