import { useEffect, useState } from "react";
import Tabs from "../components/reusable/Tabs";
import ReferenceCard from "../components/pageComponents/Reference/ReferenceCard";
import { useSearchParams, useNavigate, useParams } from "react-router-dom";
import Header from "../components/reusable/Header";
import BackButton from "../components/reusable/buttons/BackButton";
import { useSelector, useDispatch } from "react-redux";
import { ReferenceApi } from "../services";
import moment from "moment";
import BottomNavigation from "../components/reusable/BottomNav";
import PageTitle from "../components/reusable/PageTitle";
import { BigButtons } from "../components/reusable/buttons/Buttons";

const References = () => {
  const currentTab = useSearchParams()[0].get("tab");
  const [selected, setSelected] = useState(currentTab || "Received");
  const dispatch = useDispatch();
  const [references, setReferences] = useState({
    given: [],
    received: []
  });
  const { userInfo } = useSelector((state) => state.auth.authUser);
  const { toUserId } = useParams();
  const [toUserIdDetails, setToUserIdDetails] = useState({});

  //Fetching the user profile been visited
  const gettoUserDetails = async () => {
    const details = await dispatch.user.getUserProfileForGuest({
      id: toUserId
    });
    setToUserIdDetails(details);
  };

  const navigate = useNavigate();

  const handleSelection = (tab) => {
    setSelected(tab);
    // Update the search params to set the selected tab
    navigate(`.?tab=${tab}`);
  };

  const getUserReferences = async () => {
    try {
      const {
        data: { data }
      } = await ReferenceApi.getReferences(toUserId);
      setReferences(data);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getUserReferences();
  }, []);

  useEffect(() => {
    gettoUserDetails();
  }, []);

  return (
    <div className="px-5 pb-20 relative">
      <div className="h-[7vh] mb-5 w-full flex items-center">
        <BackButton
          onClick={() =>
            navigate(toUserId ? `/profile?id=${toUserId}` : "/profile")
          }
        />
        <div />
        <PageTitle center>REFERENCES</PageTitle>
      </div>
      {/* Display tabs if either the logged-in user or the profile being viewed is a NON_PLAYER */}
      {toUserIdDetails.userType === "NON_PLAYER" && (
        <Tabs
          tabs={["Received", "Given"]}
          selected={selected}
          setSelected={handleSelection}
          color="black"
        />
      )}

      {selected === "Received" &&
        references.received.length > 0 &&
        references.received.map((item, index) => (
          <ReferenceCard
            key={index}
            name={item.fromProfileName}
            relationship={item.relationship}
            title={item?.fromRole.join(",")}
            date={moment(item.createdAt).format("MMMM, DD YYYY")}
            message={item.message}
            photo={item?.fromProfilePhoto}
          />
        ))}

      {selected === "Given" &&
        references.given.length > 0 &&
        references.given.map((item, index) => (
          <ReferenceCard
            key={index}
            relationship={item.relationship}
            name={item?.toProfileName}
            title={item?.toRole.join(",")}
            date={moment(item.createdAt).format("MMMM, DD YYYY")}
            message={item.message}
            photo={item?.fromProfilePhoto}
          />
        ))}

      {userInfo.userType === "NON_PLAYER" && (
        <div className="fixed bottom-20 w-full bg-white outline-none border-none left-0 right-0 px-5">
          <BigButtons
            onClick={() => navigate(`/reference/form/${toUserId}`)}
            black
            label="Write Reference"
          />
        </div>
      )}

      <BottomNavigation />
    </div>
  );
};

export default References;
