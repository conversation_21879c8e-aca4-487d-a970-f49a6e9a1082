import React, { useState } from "react";
import { BigButtons } from "../components/reusable/buttons/Buttons";
import Header from "../components/reusable/Header";
import PageTitle from "../components/reusable/PageTitle";
import { useNavigate } from "react-router-dom";
import { notifyError } from "../utils/helpers";
import { dispatch } from "../redux/store";
import { LoginForm } from "../components/pageComponents/Login";
import ConfirmAccount from "../components/pageComponents/Signup/ConfirmAccount";
import PlayerLogo from "../components/svgIcons/PlayerLogo";
import Logo from "../components/reusable/Logo";

const Login = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigate = useNavigate();

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!email || !password) {
      return notifyError("Please Provide Required Inputs");
    }

    setIsSubmitting(true);
    const data = await dispatch.auth.userLogin({
      values: { email, password },
      navigate
    });

    if (data?.status === 1) {
      setEmail("");
      setPassword("");
      setIsSubmitting(false);
    }
  };

  return (
    <div className="page">
      <Header>
        <div></div>
        <div></div>
        {/* <div onClick={() => navigate("/")}>Skip</div> */}
      </Header>
      <div className="mb-[50px]">
        <Logo />
      </div>
      <PageTitle>
        <div>LOG IN</div>
      </PageTitle>
      <div className="border-b mt-4 text-sm text-gray-700 w-max mb-2 border-gray-300">
        Don’t have an account?{" "}
        <a
          className="text-gray-700 font-extrabold"
          target="_blank"
          href="https://join.playerapp.co/"
        >
          Join Waitlist
        </a>
      </div>
      <LoginForm />
      <ConfirmAccount />
    </div>
  );
};

export default Login;
