import React, { useEffect, useState } from "react";
import Fixtures from "../components/pageComponents/MyTeam/Fixtures";
import Results from "../components/pageComponents/MyTeam/Results";
import BottomNavigation from "../components/reusable/BottomNav";
import { BigButtons } from "../components/reusable/buttons/Buttons";
import Tabs from "../components/reusable/Tabs";
import { useNavigate } from "react-router-dom";
import { dispatch } from "../redux/store";
import { useSelector } from "react-redux";
import Loader from "../components/reusable/loading/Loader";
import Popup from "../components/pageComponents/MyTeam/Popup";
import SwitchButton from "../components/pageComponents/Custom/Switch";

const MyTeam = () => {
  const [selected, setSelected] = useState("results");
  const [loading, setLoading] = useState(true);
  const queryParams = new URLSearchParams(window.location.search);
  const newClubId = queryParams.get("clubId");
  const newTeamId = queryParams.get("teamId");

  const navigate = useNavigate();

  const {
    auth: { authUser },
    matches: { teamDashboard },
    fixtures: { fixtures },
    allTeams: {
      currentTeamId: loggedInUserCurrentTeamId,
      currentClubId: loggedInUserCurrentClubId,
      ...otherTeamsData
    },
    clubData,
    singleTeam
  } = useSelector(
    ({
      auth,
      matches,
      fixtures,
      team: { allTeams, singleTeam, clubData }
    }) => ({
      auth,
      matches,
      fixtures,
      allTeams,
      singleTeam,
      clubData
    })
  );

  const [currentTeamId, setCurrentTeamId] = useState("");
  const [currentClubId, setCurrentClubId] = useState("");

  const { isMatchLoading, isFixtures } = useSelector(({ loading }) => ({
    isMatchLoading: loading.effects.matches.getMatchesbyTeamAndSeason,
    isFixtures: loading.effects.fixtures.getFixtures
  }));

  const userInfo = useSelector((state) => state.user.data);
  const getData = async () => {
    if (selected === "fixtures" && !isFixtures) {
      await dispatch.fixtures.getFixtures({
        teamId: currentTeamId
      });
    }

    if (selected === "results" && !isMatchLoading) {
      await dispatch.matches.getMatchesbyTeamAndSeason({
        teamId: currentTeamId
      });
    }
  };

  const getSingleTeam = async () => {
    await dispatch.team.getSingleTeam({
      teamId: currentTeamId,
      clubId: currentClubId
    });
  };

  const handleSelectTeamFromParams = async (teamId, clubId) => {
    await dispatch.team.setState({
      allTeams: {
        ...otherTeamsData,
        currentTeamId: teamId,
        currentClubId: clubId
      }
    });
  };

  useEffect(() => {
    if (!loading) {
      getSingleTeam();
      getData();
    }

    if (
      !(currentClubId || currentTeamId) &&
      !(loggedInUserCurrentClubId || loggedInUserCurrentTeamId) &&
      !loading
    ) {
      navigate("/team-select");
    }
  }, [
    currentTeamId,
    currentClubId,
    selected,
    loggedInUserCurrentClubId,
    loggedInUserCurrentClubId
  ]);

  useEffect(() => {
    if (newClubId && newTeamId) {
      setCurrentClubId(newClubId);
      setCurrentTeamId(newTeamId);
      handleSelectTeamFromParams(newTeamId, newClubId);
      setLoading(false);
    } else {
      setCurrentClubId(loggedInUserCurrentClubId);
      setCurrentTeamId(loggedInUserCurrentTeamId);
      setLoading(false);
    }
  }, [
    newClubId,
    newTeamId,
    loggedInUserCurrentTeamId,
    loggedInUserCurrentClubId
  ]);

  // used to disable teams page. once we are ready. we will remove this one
  return (
    <Popup
      textComp={
        <>
          <p>COMING SOON.</p>
          <p>THE CLUB & TEAM FEATURE IS NOT AVAILABLE YET. </p>
          <p>SET TO LAUNCH IN 2026.</p>
        </>
      }
      shouldShowTextComp={true}
      shouldShowEmail={false}
    />
  );

  if (isMatchLoading || isFixtures) {
    return <Loader />;
  }

  return singleTeam?.isActive === undefined || singleTeam?.isActive ? (
    <div className="page mt-7 pb-[70px]">
      <div className="flex gap-2 items-center sticky top-0 bg-[#fff]/25 backdrop-blur-md h-[100px]">
        <div
          onClick={() =>
            navigate("/club-teams", {
              state: {
                clubData
              }
            })
          }
        >
          <img
            className="object-cover rounded-full w-[70px] h-[70px] border-[2px]"
            src={clubData?.clubLogoUrl}
            alt=""
          />
        </div>
        <div className="w-8/12">
          <a href="/club-teams">
            <p className="uppercase text-[18px] font-favela-bold font-bold text-black">
              {clubData?.clubName}
            </p>
          </a>
          <div className="font-mono mt-2">
            {singleTeam?.abr || singleTeam?.teamName}
          </div>
        </div>
      </div>

      <div className="flex flex-col gap-5 mt-7">
        <BigButtons
          onClick={() => navigate("/players")}
          black
          label="pLAYERS AND STATS"
        />

        <BigButtons
          onClick={() => navigate("/league-and-stats")}
          black
          label="LEAGUE AND STATS"
        />
        <BigButtons
          onClick={
            authUser.userInfo.id === "ADMIN"
              ? () => navigate("/team-dashboard/result-details/edit-highlights")
              : () => navigate("/team-dashboard/result-details/highlights")
          }
          green
          label={
            authUser.userInfo.id === "ADMIN" ? "Edit Highlights" : "HIGHLIGHTS"
          }
        />
        <div>
          <Tabs
            tabs={["results", "fixtures"]}
            selected={selected}
            setSelected={setSelected}
            color="black"
          />
          {teamDashboard.data.length > 0 && (
            <Results
              selected={selected}
              data={teamDashboard.data}
              currentTeamId={currentTeamId}
            />
          )}
          {fixtures.data.length > 0 && (
            <Fixtures selected={selected} data={fixtures.data} />
          )}
        </div>
      </div>
      <BottomNavigation />
    </div>
  ) : (
    <div>
      <Popup />
    </div>
  );
};

export default MyTeam;
