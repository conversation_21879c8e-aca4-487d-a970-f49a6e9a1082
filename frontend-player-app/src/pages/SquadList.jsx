import React from "react";
import { useNavigate } from "react-router-dom";
import { PlayerPostion } from "../utils/helpers";

const SquadList = ({ data }) => {
  const navigate = useNavigate();
  return (
    <>
      {Object.entries(PlayerPostion).map(([key, value], idx) => {
        return (
          <div className="mt-[20px]" key={idx + Math.random() * 10000}>
            {data[key] ? (
              <p className="text-[16px] font-medium">{value}</p>
            ) : (
              ""
            )}
            {data[key]?.map(({ firstName, lastName, photoUrl, id }, i) => (
              <div
                onClick={() => navigate(`/profile?id=${id}`)}
                key={i}
                className="flex items-center gap-2 mt-3"
              >
                <img
                  src={photoUrl ? photoUrl : "/images/profile.png"}
                  alt="profile pix"
                  className="w-[30px] h-[30px] rounded-full"
                />
                <p className="text-[14px] font-light">
                  {firstName} {lastName}
                </p>
              </div>
            ))}
          </div>
        );
      })}
    </>
  );
};

export default SquadList;
