import PageTitle from "../components/reusable/PageTitle";
import { useNavigate } from "react-router-dom";

const Onboarding = () => {
  const navigate = useNavigate();

  return (
    <div className="page flex flex-col items-center justify-center h-screen bg-white">
      <PageTitle>
        <div className="mb-20 mt-20">LETS GET STARTED</div>
      </PageTitle>

      {/* Steps */}
      <div className="w-full space-y-10 mb-8">
        <div className="">
          <p className="font-favela-bold font-[700] text-[15px] leading-[18px] mb-[40px]">1. CREATE YOUR PROFILE</p>
          <hr className="border-t-1 border-black mb-10" />
        </div>
        <div className="">
          <p className="font-favela-bold font-[700] text-[15px] leading-[18px] mb-[40px]">2. SHOWCASE YOUR TALENT</p>
          <hr className="border-t-1 border-black mb-10" />
        </div>
        <div className="">
          <p className="font-favela-bold font-[700] text-[15px] leading-[18px] mb-[40px]">3. ELEVATE YOUR GAME</p>
          <hr className="border-t-1 border-black mb-20" />
        </div>
      </div>

      {/* Buttons */}
      <div className="flex flex-col align-center justify-center space-y-5">
        <button
          className="font-favela-bold bg-black text-white font-bold rounded-full w-[327px] h-[72px]"
          onClick={() => navigate('/login')}
        >
          LOG IN
        </button>
        <button
          className="font-favela-bold bg-black text-white font-bold rounded-full w-[327px] h-[72px]"
          onClick={() => navigate('/signup1')}
        >
          SIGN UP
        </button>
      </div>
    </div>
  );
};

export default Onboarding;