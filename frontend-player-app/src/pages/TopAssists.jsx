import React from "react";
import { useSelector } from "react-redux";
import PlayerStatsHeader from "../components/reusable/PlayerStatsHeader";

const TopAssists = () => {
  const { selectedSeasonData } = useSelector(
    ({ seasons: { selectedSeasonData } }) => ({
      selectedSeasonData,
    })
  );

  return (
    <div>
      <PlayerStatsHeader from={"/players"} />
      <div className="px-[15px] mt-[25px]">
        <p className="uppercase text-[16px] font-favela-bold font-bold">
          toP assists
        </p>

        <table className="w-full">
          <tbody>
            {selectedSeasonData?.playerStats?.topAssists
              ?.sort(
                (a, b) => parseFloat(b.noOfassists) - parseFloat(a.noOfassists)
              )
              ?.map((data, i) => (
                <tr key={i} className="border-b-[1px] border-[#0000001A]">
                  <td className="py-[10px]">{i + 1}</td>
                  <td className="py-[10px]">
                    {data?.name} {data.position}
                  </td>
                  <td className="py-[10px] float-right">{data.noOfassists}</td>
                </tr>
              ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default TopAssists;
