import React, { useEffect, useRef, useState, useCallback } from "react";
import {
  getUniqueEmojis,
  momentGetTime<PERSON>go,
  notifyError,
  notify<PERSON><PERSON><PERSON>,
  notify<PERSON>arn,
  shouldShowWaitIcon
} from "../utils/helpers";
import LiveLike from "@livelike/engagementsdk";
import BackButton from "../components/reusable/buttons/BackButton";
import { useInView } from "react-intersection-observer";

import Loader from "../components/reusable/loading/Loader";
import { useNavigate } from "react-router-dom";
import Send from "../components/svgIcons/Send";
import { useSelector } from "react-redux";
import { dispatch } from "../redux/store";
import Spinner from "../components/reusable/spinner/Spinner";
import {
  LiveLikeReactionPacks,
  TRACKING_EVENTS,
  currentEnv,
  liveLikeClientId
} from "../utils/constants";
import ReactionListCard from "../components/reusable/ReactionListCard";
import Menu from "../components/svgIcons/Menu";
import Edit from "../components/svgIcons/Edit";
import RedDelete from "../components/svgIcons/RedDelete";
import ConfirmDelete from "../components/pageComponents/Profile/ConfirmDelete";
import EditFeed from "../components/pageComponents/Feeds/EditFeed";
import ResultHighlights from "../components/pageComponents/Feeds/ResultHighlights";
import ReactPlayerVideo from "../components/reusable/ReactPlayerVideo";
import { ThreeDotsIcon } from "../components/svgIcons";
import { CommentSort } from "@livelike/javascript";
import StackedReactions from "../components/pageComponents/Feeds/StackedReactions";
import ReactCommentShare from "../components/pageComponents/Feeds/ReactCommentShare";
import LoadingFeedVideo from "../components/pageComponents/Feeds/LoadingFeedVideo";
import { useDisableScroll } from "../hooks";
import Analytics from "../utils/google-analytics";

/**
 * Manages the feed details page
 * Note that this component was used to manage the announcement details page previous but no longer used. Hence, some of the code is not used.
 * There is need to remove the announcement part of the codes not working
 * @returns
 */
const Comments = () => {
  const [fetchedComments, setFetchedComments] = useState([]);
  const [commentsCount, setCommentsCount] = useState();
  const [commentsLoading, setCommentsLoading] = useState(false);
  const [sendLoading, setSendLoading] = useState(false);
  const [comment, setComment] = useState("");
  const [highlight, setHighlight] = useState(null);
  const [isPanelOpen, setIsPanelOpen] = useState(false);
  const [editModal, setEditModal] = useState(false);
  const [editFeedModal, setEditFeedModal] = useState(false);
  const [shouldFetchMore, setNextCusor] = useState(false);
  const [currentCommentItem, setCurrentCommentItem] = useState(null);

  const [shouldShowEmojiPanel, setShouldShowEmojiPanel] = useState({
    visible: false,
    highlightId: ""
  });

  const [reportModal, setReportModal] = useState(false);
  const [reportText, setReportText] = useState("");

  useDisableScroll(editFeedModal);

  const handleCloseModal = () => {
    setReportModal(false);
    setCurrentCommentItem(null);
  };

  const reporting = useSelector(
    ({ loading }) => loading.effects.feed.reportHighlights
  );

  const queryParams = new URLSearchParams(window.location.search);

  const type = queryParams.get("type");
  const [selected, setSelected] = useState(type);

  const highlightId = queryParams.get("highlightId");
  const navigatedFrom = queryParams.get("from") || "";
  const userId = queryParams.get("userId");
  const announcementId = queryParams.get("announcementId");

  const navigate = useNavigate();
  const emojiPanelRef = useRef(null);

  const commentInputRef = useRef(null);

  const { userInfo } = useSelector((state) => state.auth.authUser);

  // const { userList, teamsList } = useSelector(({ user, team }) => ({
  //   userList: user.usersByProjection,
  //   teamsList: team.teamsByProjection
  // }));

  const { loggedUserId, liveLikeProfileToken } = useSelector(
    ({ auth: { authUser }, user }) => ({
      loggedUserId: authUser?.userInfo?.id || "",
      liveLikeProfileToken: user?.data?.liveLikeProfileToken || "",
      authUser
    })
  );

  const isSameUser = loggedUserId === highlight?.userId;

  const togglePanel = () => {
    setIsPanelOpen(!isPanelOpen);
  };

  const handleButtonClick = (e) => {
    e.stopPropagation();
    togglePanel();
  };

  const handleClickOutside = (event) => {
    if (
      emojiPanelRef.current &&
      !emojiPanelRef.current.contains(event.target)
    ) {
      setIsPanelOpen(false);
    }
  };

  async function addUserReaction(highlight, reaction) {
    if (!loggedUserId) {
      notifyWarn("Please login to react");
      return;
    }

    setIsPanelOpen(false);
    function filterById(reactedBy, userId) {
      if (reactedBy) {
        const filteredArray = reactedBy.filter(
          (item) => item.userId !== userId
        );
        return filteredArray;
      }
    }

    try {
      if (highlight) {
        const reactedByUsers = highlight.reactedByUsers;
        const filteredReactions = filterById(reactedByUsers, userInfo.id) || [];
        const reactedByUsersWithNewReaction = [
          ...filteredReactions,
          {
            userId: userInfo.id,
            others: {
              user: {
                firstName: userInfo.firstName,
                lastName: userInfo.lastName,
                photoUrl: userInfo.photoUrl,
                cludId: userInfo.cludId,
                gender: userInfo.gender
              },
              emoji: reaction.file,
              emojiName: reaction.name
            }
          }
        ];

        // Update the state immediately
        setHighlight((prevHighlight) => ({
          ...prevHighlight,
          reactedByUsers: reactedByUsersWithNewReaction
        }));

        // Make the API call to update the reaction
        if (highlight && userId) {
          dispatch.feed.editHighlights({
            userId: highlight.userId,
            id: highlight?.id,
            reactedByUsers: reactedByUsersWithNewReaction,
            noNotification: true,
            isEmojiUpdate: true
          });
        }
        // if (highlight && !userId) {
        //   dispatch.announcement.updateAnnouncement({
        //     id: highlight?.id,
        //     reactedByUsers: reactedByUsersWithNewReaction,
        //     noNotification: true,
        //     isEmojiUpdate: true
        //   });
        // }

        Analytics.trackEvent({
          name: TRACKING_EVENTS.REACTION_ADDED,
          metadata: {
            reaction_type: reaction?.name,
            content_id: highlight?.id,
            content_type: "highlight",
          }
        })
      }
    } catch (error) {
      console.log("AN ERROR OCCURED", error);
      setHighlight({
        ...highlight,
        reactedByUsers: reactedByUsers
      });
    }
  }

  const getHighlight = async () => {
    if (highlightId && userId) {
      const res = await dispatch.feed.fetchHighlight({ highlightId, userId });
      setHighlight(res.data);
    }

    if (highlight && !userId) {
      const res = await dispatch.announcement.getOneAnnouncement({
        highlightId
      });
      setHighlight(res.data);
    }
    if (announcementId) {
      const res = await dispatch.announcement.getOneAnnouncement(
        announcementId
      );
      setHighlight(res);
    }
  };

  const sendCommentsCount = async (commentBoardId, count) => {
    if (commentBoardId) {
      if (highlightId) {
        try {
          dispatch.feed.editHighlights({
            noNotification: true,
            id: highlight?.id,
            userId: highlight.user.id,
            totalCommentCount: commentsCount
          });
        } catch (error) {
          console.log(error);
        }
      }

      if (announcementId) {
        try {
          const comment = await LiveLike.getComments({
            commentBoardId
          });
          dispatch.announcement.updateAnnouncement({
            id: highlight?.id,
            totalCommentCount: comment.count
          });
        } catch (error) {
          console.log(error);
        }
      }
    }
  };

  const createCommentBoard = async () => {
    try {
      const commentBoard = await LiveLike.createCommentBoard({
        title: highlight?.comment || highlight?.id,
        customId: highlight?.id,
        repliesDepth: 2,
        allowComments: true,
        customData: `created by ${highlight?.user?.id || highlight?.title
          } on ${Date.now()}`
      });
      if (highlightId && highlight) {
        dispatch.feed.editHighlights({
          noNotification: true,
          commentBoardId: commentBoard.id,
          id: highlight?.id,
          userId: highlight.user.id,
          totalCommentCount: 0
        });
      }
      if (announcementId) {
        dispatch.announcement.updateAnnouncement({
          id: highlight?.id,
          commentBoardId: commentBoard.id,
          totalCommentCount: 0
        });
      }
      return commentBoard;
    } catch (error) {
      console.log(error);
    }
  };

  // ==========================================================================//
  // HANDLES SCROLL VIEW AND SET LAST ITEM TO FETCH NEXT COMMENTS
  const ref = useRef();
  const { ref: inViewRef } = useInView({
    onChange: (inView) => {
      if (inView && shouldFetchMore) {
        fetchComments(shouldFetchMore);
      }
    }
  });

  // Use `useCallback` so we don't recreate the function on each render
  const setRefs = useCallback(
    (node) => {
      // Ref's from useRef needs to have the node assigned to `current`
      ref.current = node;
      // Callback refs, like the one from `useInView`, is a function that takes the node as an argument
      inViewRef(node);
    },
    [inViewRef]
  );
  // ==========================================================================//

  const fetchComments = async (cursor) => {
    if (loggedUserId) {
      setCommentsLoading(true);

      let commentBoardId = "";

      try {
        if (!highlight?.commentBoardId) {
          const getCommentBoardDetails = await LiveLike.getCommentBoardDetails({
            customId: highlightId
          });

          if (getCommentBoardDetails.id) {
            commentBoardId = getCommentBoardDetails.id;
            // if (highlightId) {
            //   dispatch.announcement.updateAnnouncement({
            //     id: highlightId,
            //     commentBoardId: getCommentBoardDetails.id
            //   });
            // }
          }
        } else {
          commentBoardId = highlight?.commentBoardId || "";
        }
      } catch (error) {
        setCommentsLoading(false);
        if (error === "Resource not found") {
          createCommentBoard();
        }
      }

      // Check if the comment board id has been created or fetched
      if (commentBoardId) {
        try {
          const getCommentPayload = {
            commentBoardId,
            sorting: CommentSort.NEWEST
          };

          let commentResponse;

          if (cursor && !cursor.done) {
            commentResponse = await Promise.resolve(cursor.next());
            setFetchedComments([
              ...fetchedComments,
              ...commentResponse?.value?.results
            ]);

            // Only reset the cursor if there are no more comments to fetch
            if (commentResponse.done) {
              setNextCusor(!commentResponse.done);
            }
          } else {
            commentResponse = await LiveLike.getComments(getCommentPayload);
            setFetchedComments(commentResponse.results);

            // Crazy thing is to save the whole response and call it again with the next
            if (!commentResponse.done) {
              setNextCusor(commentResponse);
            }
          }

          setCommentsCount(commentResponse.count);
          setCommentsLoading(false);
        } catch (error) {
          console.log(error, "Error");

          console.log(error.message, "Error");
          setCommentsLoading(false);
        }
      }
    }
  };

  const parseAuthorData = (commentItem) => {
    let author;

    if (commentItem?.author?.custom_data) {
      try {
        author = JSON.parse(commentItem.author.custom_data);
      } catch (error) {
        console.error("Invalid JSON:", error);
        author = {}; // Fallback to an empty object if parsing fails. It returns null initially
      }
    } else {
      author = getAuthorDataFromCommentCustomData(
        commentItem.custom_data
      ); // Fallback to an empty object if custom_data is undefined
    }

    return author;
  }

  const openReportModal = (item) => {
    setCurrentCommentItem(item)
    setReportModal(true)
  }

  const handleReportSubmit = async () => {
    if (!currentCommentItem) return;

    const author = parseAuthorData(currentCommentItem)

    const data = {
      reporterUserId: loggedUserId,
      reportedUserId: author?.userId,
      reporterFullName: `${userInfo?.firstName} ${userInfo?.lastName}`,
      reportedFullName: `${author?.firstName} ${author?.lastName}`,
      reason: reportText,
      reportedContent: currentCommentItem?.text,
      reportedPhotoUrl:
        author?.photoUrl,
      reporterPhotoUrl:
        userInfo?.photoUrl,
      reportType: "COMMENT",
      contendId: currentCommentItem?.id,
      metadata: {
        highlightId,
        userId,
      }
    }
    if (reportText === "") {
      notifyError("A reason is required");
    } else {
      const res = await dispatch.feed.reportHighlights(data);

      if (res.status === 1) {
        handleCloseModal(true);
        setReportText("");
        setCurrentCommentItem(null)
      } else {
        notifyError("Something went wrong");
      }
    }
  };

  const addUserComment = async () => {
    if (!highlight?.id) {
      return;
    }
    setSendLoading(true);

    const newComment = {
      id: userInfo.id,
      firstName: userInfo.firstName,
      lastName: userInfo.lastName,
      clubName: userInfo.clubName || "",
      teamName: userInfo.teamName || "",
      photoUrl: userInfo.photoUrl || "/images/profile.png"
    };

    const currentComments = [...fetchedComments];
    const currentCount = commentsCount || 0;

    // Update the local state with the new comment
    setFetchedComments([
      { custom_data: JSON.stringify(newComment), text: comment },
      ...currentComments
    ]);
    setCommentsCount(currentCount + 1); // Increment the comment count locally

    let commentBoardId = "";

    const fetchCommentBoardDetails = async () => {
      try {
        return await LiveLike.getCommentBoardDetails({
          customId: highlight?.id
        });
      } catch (error) {
        console.error(error);
        return await createCommentBoard();
      }
    };

    try {
      if (!highlight.commentBoardId) {
        const getCommentBoardDetails = await fetchCommentBoardDetails();
        if (getCommentBoardDetails.id) {
          commentBoardId = getCommentBoardDetails.id;
        }
      } else {
        commentBoardId = highlight.commentBoardId || "";
      }

      if (commentBoardId) {
        const addComment = await LiveLike.addComment({
          text: comment,
          customData: JSON.stringify(newComment),
          commentBoardId
        });
        if (addComment) {
          Analytics.trackEvent({
            name: TRACKING_EVENTS.COMMENT_ADDED,
            metadata: {
              content_id: highlight?.id,
              content_type: "highlight",
              comment_length: comment?.length
            }
          })

          notifySuccess("Comment Added");
          setSendLoading(false);
          sendCommentsCount(commentBoardId, currentCount + 1);
          commentInputRef.current.value = "";
          setComment("");


        }
      }
    } catch (error) {
      setSendLoading(false);
      console.log(error);
      setFetchedComments(currentComments);
      setCommentsCount(currentCount);
      sendCommentsCount(commentBoardId, currentCount);
    }
  };

  const handleSearch = async (data) => {
    const splitName = data.split(" ");
    const filteredNames = splitName.filter((name) => name !== "");
    const checkThirdName = filteredNames[2]
      ? !filteredNames[2].includes("(")
      : false;
    const fullName = `${filteredNames[0]} ${filteredNames[1]}${checkThirdName ? ` ${filteredNames[2]}` : ""
      }`;

    const searchResult = await handleSearch(fullName);
    if (searchResult.length > 0) {
      handleOpenProfile(searchResult[0].id);
    } else {
      notifyError("User profile not found");
    }
  };

  const parseString = (data, purpose = "userName") => {
    const rawData = data.split("/");
    const output = purpose === "userName" ? rawData[0] : rawData[1];
    return output.replace(/\s/g, "");
  };

  const getAuthorDataFromCommentCustomData = (customData) => {
    try {
      // handle new case of the customData for comments
      return JSON.parse(customData);
    } catch (error) {
      // Handle old case of the customData for comments
      console.error("Invalid JSON:", error);
      const firstName = parseString(customData, "userName");
      const id = parseString(customData, "id");
      return { firstName, id };
    }
  };

  const handleOpenProfile = (id) => {
    if (id) {
      navigate({ pathname: "/profile", search: `?id=${id}` });
    } else {
      navigate("/login");
    }
  };

  useEffect(() => {
    getHighlight();
  }, []);

  useEffect(() => {
    fetchComments(false);
  }, []);

  useEffect(() => {
    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, []);

  const Avatar = ({ name, id }) => {
    // Get the first letter of the name or use a default placeholder
    const initial = name ? name.charAt(0).toUpperCase() : "?";

    return (
      <div
        onClick={() => handleOpenProfile(id)}
        className="bg-gray-300 text-white rounded-full flex items-center justify-center"
        style={{ width: "45px", height: "45px" }}
      >
        <span className="text-lg font-bold text-[#000000]">{initial}</span>
      </div>
    );
  };

  return (
    <div className="pb-[70px] px-0">
      <div className="flex justify-between w-full h-[10vh] px-2">
        <BackButton from={`/${navigatedFrom}`} />
        {/* <div className="font-favela-bold md:w-8/12 text-[20px] mx-auto bg-white  flex items-center"></div> */}
        {userInfo?.id === highlight?.userId && highlight?.type !== "RESULT" && (
          <div className="flex items-center">
            <div className="dropdown dropdown-left dropdown-bottom">
              <label tabIndex={1} className="flex items-center">
                <Menu />
              </label>
              <div
                tabIndex={1}
                className="dropdown-content card card-compact w-56 shadow-md bg-white text-primary-content"
              >
                <div className="p-4 text-black ">
                  <div className="mt-[10px] flex flex-col gap-5 w-full">
                    <div
                      onClick={() => setEditFeedModal(true)}
                      className="flex gap-3 items-center"
                    >
                      <Edit />
                      <p className="text-[20px] font-normal">Edit</p>
                    </div>
                    <div
                      onClick={() => setEditModal(true)}
                      className="text-red-600 flex gap-3 items-center"
                    >
                      <RedDelete />
                      <p className="text-[20px] font-normal">Delete</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
      {editModal ? (
        <ConfirmDelete
          handleCloseModal={setEditModal}
          shouldShowModal={editModal}
          data={highlight}
        />
      ) : (
        ""
      )}
      {editFeedModal ? (
        <EditFeed close={setEditFeedModal} highlightObject={highlight} />
      ) : (
        ""
      )}
      <div>
        {highlight ? (
          <div key={highlight?.id}>
            <div
              className={`${highlight.type === "RESULT" ? "hidden" : ""
                } flex gap-2 items-center ml-2`}
            >
              <img
                src={`${highlight?.user?.photoUrl?.length > 5
                  ? highlight?.user?.photoUrl
                  : highlight.logoUrl
                    ? highlight.logoUrl
                    : "/images/profile.png"
                  }`}
                alt="profile pix"
                className="w-[58px] h-[58px] object-cover rounded-full"
              />
              <div onClick={() => handleOpenProfile(highlight?.user?.id, "id")}>
                <p className="text-[14px] font-medium">
                  {highlight?.user?.firstName
                    ? `@${highlight?.user?.firstName}`
                    : highlight?.title}
                </p>
                <p className="text-[14px] font-medium opacity-50">
                  {(highlightId &&
                    highlight?.user?.teamName !== "N/A" &&
                    highlight?.user?.teamName) ||
                    highlight?.user?.clubName}
                  {announcementId && highlight?.subtitle}
                </p>
                {/* <p className="font-medium opacity-50">{highlight?.comment}</p> */}
              </div>
            </div>
            {highlight.type === "PHOTO" && (
              <div className="w-full rounded-[0px] mt-6 overflow-hidden aspect-[4/5]">
                <img
                  src={highlightId ? highlight.url : highlight.assetUrl}
                  alt="feed"
                  className="w-full h-full object-cover"
                />
              </div>
            )}

            {highlight.type === "RESULT" && (
              <div className="w-full rounded-[0px] mt-6 overflow-hidden">
                <ResultHighlights highlight={highlight} />
              </div>
            )}

            {/* WAIT FOR 20 SECS AFTER UPLOAD AND TURN OFF WAIT ICON */}
            {highlight.type === "VIDEO" &&
              shouldShowWaitIcon(highlight, "seconds", 10) && (
                <LoadingFeedVideo shouldReload reloadTime={10000} />
              )}

            {/* CASE WHERE THE VIDEO IS PROCESSED AND READY TO PLAY IN HLS OR DIRECTLY FROM ORIGIN IF ITS 20secs AFTER UPLOAD */}
            {highlight.type === "VIDEO" &&
              !shouldShowWaitIcon(highlight, "seconds", 10) &&
              !highlight?.videoProcessingFailed && (
                <ReactPlayerVideo
                  highlight={highlight}
                  highlightId={highlightId}
                  shouldAllowOriginUrlPlay
                />
              )}

            {/* CASE WHERE THE VIDEO IS NOT PROCESSED */}
            {highlight.type === "VIDEO" &&
              !shouldShowWaitIcon(highlight) &&
              highlight?.videoProcessingFailed &&
              isSameUser && (
                <LoadingFeedVideo
                  iconSrc="/images/Green-Uploadfailedicons.png"
                  content1="Unfortunately, there was an error "
                  content2="uploading this highlight. Please try again."
                />
              )}

            <div
              className="relative ml-4 mt-10"
              onClick={(e) =>
                selected !== "reactions"
                  ? setSelected("reactions")
                  : handleButtonClick(e)
              }
            >
              <StackedReactions
                highlight={highlight}
                uniqueEmojis={getUniqueEmojis(highlight?.reactedByUsers)}
              />
            </div>
            {Boolean(loggedUserId && liveLikeProfileToken) && (
              <div className="flex items-center gap-x-3 absolute h-[48px] w-[90%] rounded-[10px] mx-auto mr-10 ml-[12px]">
                {/* REACTION OPENER, COMMENT ICON AND SHARE ICON */}
                <ReactCommentShare
                  onClickReact={() =>
                    setShouldShowEmojiPanel({
                      visible:
                        shouldShowEmojiPanel.visible &&
                          highlight?.id === shouldShowEmojiPanel?.highlightId
                          ? false
                          : true,
                      highlightId: highlight?.id
                    })
                  }
                  onClickComment={() =>
                    selected !== "comments" && setSelected("comments")
                  }
                  onClickShare={() =>
                    navigate("/feed/share", {
                      state: {
                        highlight: highlight,
                        type: "highlight"
                      }
                    })
                  }
                />

                {/* PANEL THAT SHOWS THE REACTIONS TO BE ADDED, NEEDS TO BE MOVED TO A NEW COMPOENENT */}
                {shouldShowEmojiPanel.visible &&
                  shouldShowEmojiPanel.highlightId === highlight?.id && (
                    <div className="absolute left-10 bg-[#F1F1F1] rounded-[10px] max-[300px]:w-[87%]">
                      {shouldShowEmojiPanel.visible &&
                        shouldShowEmojiPanel.highlightId === highlight?.id
                        ? LiveLikeReactionPacks?.emojis.map((item, idx) => {
                          return (
                            <button
                              onBlur={() =>
                                setShouldShowEmojiPanel({
                                  visible: false,
                                  highlightId: ""
                                })
                              }
                              key={idx}
                              onClick={() => {
                                addUserReaction(highlight, item);
                              }}
                              className={`max-[300px]:-mx-[5px] -mx-1 focus-visible:bg-transparent hover:outline-none bg-transparent focus-within:bg-transparent focus:bg-transparent focus:border-none focus-within:border-none border-none focus-visible:outline-none hover:bg-transparent`}
                            >
                              <img
                                src={item.file}
                                className="h-[20px] max-[300px]:w-[20px] max-[300px]:h-[20px] w-[20px]"
                              />
                            </button>
                          );
                        })
                        : ""}
                    </div>
                  )}
              </div>
            )}
            <div className="font-poppins  mt-[90px] mx-4 text-[12px] leading-[18px]">
              {/* Texts from the given hightlight */}
              <p className="w-full">
                <span className="font-bold">{`${highlight?.user?.firstName}${highlight?.user?.lastName} `}</span>
                {highlight?.comment}
              </p>
            </div>
            {selected === "reactions" ? (
              <div className="flex flex-col mt-5 mb-10 mx-4">
                {highlight?.reactedByUsers?.length > 0 &&
                  highlight.reactedByUsers.map((user, idx) => (
                    <div key={idx}>
                      <ReactionListCard
                        reaction={user.others}
                        userId={user.userId}
                      />
                    </div>
                  ))}
              </div>
            ) : (
              <div className="flex flex-col mt-5 mb-10 mx-4">
                {fetchedComments.length > 0 &&
                  fetchedComments.map((item, idx) => {
                    // Check if custom_data exists and is a valid string before parsing
                    const parsedData = parseAuthorData(item)

                    const commentUserID = parsedData?.userId || parsedData?.id;

                    return (
                      <div
                        key={idx}
                        ref={
                          fetchedComments.length - 1 === idx ? setRefs : null
                        }
                        className={`${idx !== fetchedComments.length - 1 ? "border-b" : ""
                          } border-gray-300 pb-2`}
                      >
                        <div className="flex justify-between items-start mt-5">
                          <div className="flex space-x-5 ">
                            {item?.author?.custom_data ||
                              parsedData.photoUrl ? (
                              <img
                                src={parsedData?.photoUrl}
                                alt=""
                                className="w-[45px] h-[45px] rounded-full border border-1 border-[#000000]"
                                onClick={() => {
                                  handleOpenProfile(commentUserID);
                                }}
                              />
                            ) : (
                              <Avatar
                                id={commentUserID}
                                name={parsedData?.firstName}
                              />
                            )}

                            <div>
                              <div className="flex space-x-6">
                                <p className="text-[#000000] text-[12px] font-[600]">
                                  {parsedData?.firstName}
                                </p>
                                <p className="text-[#808080] text-[12px] font-[600]">
                                  {momentGetTimeAgo(item.created_at)}
                                </p>
                              </div>

                              {(parsedData?.teamName ||
                                parsedData?.clubName) && (
                                  <p className="text-[#808080] text-[12px] font-[600]">
                                    {parsedData?.teamName || parsedData?.clubName}
                                  </p>
                                )}

                              <p className="text-[12px] text-[#000000] mt-1">
                                {item.text}
                              </p>
                            </div>
                          </div>

                          {loggedUserId && (
                            <div className="flex items-center">
                              <div className="dropdown dropdown-left dropdown-bottom">
                                <label
                                  tabIndex={1}
                                  className="flex items-center"
                                >
                                  <ThreeDotsIcon color={"#A5A5A5"} />
                                </label>
                                {userInfo?.id !== commentUserID && (
                                  <div
                                    tabIndex={1}
                                    className="dropdown-content card card-compact w-56 shadow-md bg-white text-primary-content"
                                  >
                                    <div className="p-4 text-black ">
                                      <div className="mt-[10px] flex flex-col gap-5 w-full">
                                        <div
                                          onClick={() => {
                                            openReportModal(item);
                                          }}
                                          className="text-red-600 flex gap-3 items-center"
                                        >
                                          Report comment
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                )}
                                {/* Modal for reporting comment */}
                                {reportModal && (
                                  <div className="fixed inset-0 bg-gray-800 bg-opacity-50 flex justify-center items-center">
                                    <div className="bg-white p-6 rounded shadow-lg w-[400px]">
                                      <h2 className="text-lg font-bold mb-4">
                                        Reason for reporting this comment
                                      </h2>
                                      <textarea
                                        className="w-full h-24 p-2 border border-gray-300 rounded mb-4 bg-[#ffffff]"
                                        placeholder="Enter your reason for reporting"
                                        value={reportText}
                                        onChange={(e) =>
                                          setReportText(e.target.value)
                                        }
                                      />
                                      <div className="flex justify-end gap-3">
                                        <button
                                          className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-500"
                                          onClick={() =>
                                            handleReportSubmit(idx)
                                          }
                                        >
                                          {reporting ? "Submiting" : "Submit"}
                                        </button>
                                        <button
                                          className="px-4 py-2 bg-gray-300 rounded hover:bg-gray-400"
                                          onClick={handleCloseModal}
                                        >
                                          Close
                                        </button>
                                      </div>
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                          )}
                        </div>
                        <p className="text-center">
                          {commentsLoading ? "Fetching more comments" : ""}
                        </p>
                      </div>
                    );
                  })}
              </div>
            )}
            {loggedUserId ? (
              <div className="fixed bottom-0 bg-white backdrop-blur-3xl py-2 px-[15px] w-full -ml-2">
                <div className="relative w-full flex items-center justify-between">
                  <textarea
                    type="text"
                    name="comment"
                    ref={commentInputRef}
                    onChange={(e) => setComment(e.target.value)}
                    id="comment"
                    className="border-b-2 border-gray-600 block pt-3 pb-1 px-0 pr-[60px] w-full text-gray-900 bg-transparent appearance-none focus:outline-none focus:ring-0 focus:border-gray-600 peer"
                    placeholder={`Add a comment for ${highlight.user?.firstName}`}
                  />
                  <div
                    onClick={() => addUserComment()}
                    className={`absolute w-[43.88px] h-[44.5px] flex justify-center items-center right-2 mb-6 ${sendLoading ? "" : "bg-[#14FF00]"
                      } rounded-xl`}
                  >
                    {sendLoading ? <Spinner /> : <Send />}
                  </div>
                </div>
              </div>
            ) : (
              <div className="w-full flex items-center justify-center">
                <a href="/login">Login to view and make comments</a>
              </div>
            )}
          </div>
        ) : (
          <Loader />
        )}
      </div>
    </div>
  );
};
export default Comments;
