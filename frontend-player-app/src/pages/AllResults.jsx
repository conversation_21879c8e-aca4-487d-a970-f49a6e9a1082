import React, { useEffect } from "react";
import { useSelector } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import BackButton from "../components/reusable/buttons/BackButton";
import Header from "../components/reusable/Header";
import PageTitle from "../components/reusable/PageTitle";
import _groupBy from "lodash.groupby";
import { useState } from "react";

const AllResults = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [seasonsData, setSeasonsData] = useState({});

  const { clubData, singleTeam, teamDashboard } = useSelector(
    ({ team: { clubData, singleTeam }, matches: { teamDashboard } }) => ({
      clubData,
      singleTeam,
      teamDashboard
    })
  );

  const handleSeasonSelect = (season) => {
    navigate("/all-results/season", {
      state: { season }
    });
  };

  useEffect(() => {
    const groupedBySeason = _groupBy(
      teamDashboard.data,
      (season) => season.seasonTitle
    );
    setSeasonsData(groupedBySeason);
  }, [singleTeam]);

  return (
    <div className="page">
      <Header>
        <BackButton />
        <div />
        <div />
      </Header>
      <PageTitle center>
        <img
          className="object-cover rounded-full w-[70px] h-[70px]"
          src={singleTeam?.logoUrl}
          alt=""
        />
        <div className="w-8/12 h-full">
          <div>{clubData?.clubName}</div>{" "}
          <div className="font-mono mt-2">{singleTeam.teamName}</div>
        </div>
      </PageTitle>
      <PageTitle>
        <div className="mb-4 mt-7">ALL RESULTS</div>
      </PageTitle>
      <div className="flex flex-col gap-3 justify-center">
        {Object.keys(seasonsData)
          .sort((a, b) => Number(b.split("/")[1]) - Number(a.split("/")[1]))
          .map((seasonTitle, key) => (
            <div
              onClick={() => {
                handleSeasonSelect(seasonTitle);
              }}
              key={key}
              className="border-b last:border-transparent py-2 flex justify-between"
            >
              <div>Season {seasonTitle}</div>
              <div>
                <img src="/images/right.png" alt="" />
              </div>
            </div>
          ))}
      </div>
    </div>
  );
};

export default AllResults;
