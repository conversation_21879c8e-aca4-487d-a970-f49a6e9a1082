import React, { useState, useEffect } from "react";
import { useLocation, useSearchParams } from "react-router-dom";
import Header from "../components/reusable/Header";
import BackButton from "../components/reusable/buttons/BackButton";
import BottomNavigation from "../components/reusable/BottomNav";
import Tabs from "../components/reusable/Tabs";
import ConnectionCards from "../components/pageComponents/Connections/ConnectionCards";
import { useSelector } from "react-redux";
import { currentEnv, liveLikeClientId } from "../utils/constants";
import { notifyError, notifySuccess } from "../utils/helpers";
import { dispatch } from "../redux/store";

const Connections = () => {
  const type = useSearchParams()[0].get("type");

  const [selected, setSelected] = useState(type);
  const [followers, setFollowers] = useState([]);
  const [followersCount, setFollowersCount] = useState(0);
  const [followings, setFollowing] = useState([]);
  const [followingsCount, setFollowingCount] = useState(0);

  const location = useLocation();

  const data = location.state.data;

  const userInfo = useSelector((state) => state.user.data);

  const { loggedUserId, liveLikeProfileToken, existingUsers } = useSelector(
    ({ auth: { authUser }, user }) => ({
      loggedUserId: authUser?.userInfo?.id || "",
      liveLikeProfileToken: user?.data?.liveLikeProfileToken || "",
      authUser,
      existingUsers: user.usersByProjection
    })
  );

  async function loadExistingProfile() {
    await dispatch.user.userSearchByProjection();
  }

  const follow = async (followerObject, nickname) => {
    try {
      const follow = await LiveLike.createProfileRelationship({
        relationshipTypeKey: "follow",
        toProfileId: followerObject.liveLikeProfileId,
        fromProfileId: userInfo.liveLikeProfileId
      });

      setFollowing([...followings, follow]);
      setFollowingCount(prev => prev + 1)

      notifySuccess(`${nickname} is now a connection!`);

      await dispatch.user.updateUser({
        id: userInfo.id,
        followingsIDs: [
          ...(userInfo?.followingsIDs || []),
          { userId: followerObject.userId, relationshipId: follow.id }
        ],
        shouldNotShowNotification: true
      });
    } catch (error) {
      // notifyError("An error occured!");
      console.log(error);
    }
  };

  const unfollow = async (relationshipId, followerObject, nickname) => {
    try {
      await LiveLike.deleteProfileRelationship({
        relationshipId: relationshipId
      });

      const filteredFollowinsIDs = userInfo.followingsIDs.filter(
        (userId) => userId.userId !== followerObject.userId
      );

      setFollowing((prev) => {
        const filterResult = prev.filter((item) => {
          return (
            JSON.parse(item.from_profile.custom_data || {})?.userId !==
            userInfo.id
          );
        });
        return filterResult;
      });

      setFollowingCount(prev => prev - 1)

      notifySuccess(`You unfollowed ${nickname}`);
      await dispatch.user.updateUser({
        id: userInfo.id,
        followingsIDs: filteredFollowinsIDs,
        shouldNotShowNotification: true
      });
    } catch (error) {
      console.log(error);
      // notifyError("An error occured!");
    }
  };

  useEffect(() => {
    if (data?.following) {
      setFollowing(data.following);
      setFollowingCount(data.followingCount)
    }

    if (data?.followers) {
      setFollowers(data.followers);
      setFollowersCount(data.followersCount)
    }
  }, [data]);

  useEffect(() => {
    if (Boolean(loggedUserId)) {
      loadExistingProfile();
    }
  }, [loggedUserId]);

  return (
    <div className="page text-white bg-black min-h-[100vh]">
      <Header bgColor="black">
        <div className="w-full flex items-center justify-between gap-7 mt-10">
          <BackButton invert from="" />
          <div className="capitalize text-[14px]">"{data?.fullName}"</div>
          <div></div>
        </div>
      </Header>
      <div className="mt-14">
        <Tabs
          isConnections={true}
          tabs={[
            { connectionCount: followersCount, title: "followers" },
            { connectionCount: followingsCount, title: "following" }
          ]}
          selected={selected}
          setSelected={setSelected}
          color="white"
        />
        <div
          className={`font-medium mt-[20px] md:grid md:grid-cols-2 gap-10 ${
            selected.includes("highlights")
              ? "md:grid-rows-none md:grid-cols-none"
              : ""
          }`}
        >
          {selected === "followers" ? (
            <div className="flex flex-col gap-2">
              {followers.length > 0 ? (
                followers?.map((follower) => {
                  const isProfileFollowingLoggedUser =
                    JSON.parse(follower.to_profile.custom_data || null)
                      ?.userId === userInfo.id;
                  const isLoggedUserFollowingProfile =
                    userInfo.followingsIDs?.find(
                      (following) =>
                        following.userId ===
                        JSON.parse(follower.from_profile.custom_data || null)
                          ?.userId
                    );
                  return (
                    <div key={follower.id}>
                      <ConnectionCards
                        relationship={follower?.from_profile}
                        connection={follower.from_profile.custom_data}
                        nickname={follower.from_profile.nickname}
                        follow={follow}
                        isProfileFollowingLoggedUser={Boolean(
                          isProfileFollowingLoggedUser
                        )}
                        isLoggedUserFollowingProfile={
                          isLoggedUserFollowingProfile
                        }
                        unfollow={unfollow}
                      />
                    </div>
                  );
                })
              ) : (
                <div className="w-full text-center">
                  {" "}
                  No one is following
                  {data?.fullName}
                </div>
              )}
            </div>
          ) : (
            <div className="flex flex-col gap-2">
              {followings?.length > 0 ? (
                followings.map((following) => {
                  const isLoggedUserFollowingProfile =
                    userInfo.followingsIDs?.find(
                      (user) =>
                        user.userId ===
                        JSON.parse(following?.to_profile?.custom_data || null)
                          ?.userId
                    );

                  return (
                    <div key={following.id}>
                      <ConnectionCards
                        relationship={following?.to_profile}
                        connection={following?.to_profile.custom_data}
                        nickname={following.to_profile.nickname}
                        follow={follow}
                        isProfileFollowingLoggedUser={
                          JSON.parse(following?.to_profile?.custom_data || null)
                            ?.userId === userInfo.id
                        }
                        isLoggedUserFollowingProfile={
                          isLoggedUserFollowingProfile ||
                          existingUsers?.some(
                            (user) =>
                              user.liveLikeProfileId === following.to_profile.id
                          )
                            ? { relationshipId: following?.id }
                            : null
                        }
                        unfollow={unfollow}
                      />
                    </div>
                  );
                })
              ) : (
                <div className="w-full text-center">
                  {data?.fullName} is not following anyone
                </div>
              )}
            </div>
          )}
        </div>
      </div>
      <BottomNavigation />
    </div>
  );
};

export default Connections;
