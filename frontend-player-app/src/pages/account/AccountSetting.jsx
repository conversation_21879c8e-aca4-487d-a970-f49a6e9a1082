import { useState } from 'react'
import { useNavigate } from "react-router-dom";
import BackButton from "../../components/reusable/buttons/BackButton";
import Header from "../../components/reusable/Header";
import PageTitle from "../../components/reusable/PageTitle";
import DeleteBasketIcon from "../../components/svgIcons/DeleteBasket.jsx";
import { dispatch } from "../../redux/store";
import DeleteModal from "../../components/pageComponents/AccountModals/DeleteUser.jsx";
import UnblockModal from "../../components/pageComponents/AccountModals/UnBlockUser.jsx";
import { useSelector } from "react-redux";

const AccountSettings = () => {
    const navigate = useNavigate();
    const settings = [
        {
            title: "CHANGE PASSWORD",
            linkPath: "/settings/security",
        },
        {
            title: "BLOCKED USERS",
            linkPath: "/settings/account/blocked-users",
        }
    ];
    //Prompt block modal
    const [isModalOpen, setIsModalOpen] = useState(false);
    const userInfo = useSelector((state) => state.user.data);

    async function handleLogout() {
        await dispatch.auth.logout();
        navigate("/");
    }

    const deletingUser = useSelector(
        ({ loading }) => loading.effects.auth?.delAccount
    );

    const handleDeleteAccount = async () => {
        await dispatch.auth.delAccount({
            requestFrom: userInfo?.userType,
            email: userInfo?.email,
            userId: userInfo?.id
        })
        await handleLogout()
    };

    return (
        <div className="page md:max-w-[700px] mx-auto flex flex-col min-h-screen">
            <Header>
                <BackButton from="" />
            </Header>
            <PageTitle>
                <div className="text-[20px] uppercase">Account Settings</div>
            </PageTitle>
            <DeleteModal
                isOpen={isModalOpen}
                onClose={() => setIsModalOpen(false)}
                onUnblock={handleDeleteAccount}
                loading={deletingUser}
            />
            <div className="mt-[65px] flex-grow">
                {settings.map(({ linkPath, title }, i) => (
                    <div
                        onClick={() => navigate(linkPath)}
                        key={i}
                        className="flex justify-between items-center py-[20px] cursor-pointer"
                    >
                        <p className="uppercase text-[15px] font-favela-bold font-bold">
                            {title}
                        </p>
                        <img src="/images/arrowRight.svg" alt="" />
                    </div>
                ))}
            </div>
            <div className="flex justify-center mt-auto mb-4">
                <button
                    onClick={() => {
                        setIsModalOpen(true);
                        // setId(user.id);
                    }}
                    className="h-[54px] w-[170px] px-4 py-4 flex items-center justify-center gap-2 rounded-xl border-2 border-[#FF5B5B] text-[#FF5B5B] bg-transparent focus:outline-none focus:ring-0 focus:border-none"
                >
                    <DeleteBasketIcon />
                    <span className="text-[12px]">Delete my account</span>
                </button>
            </div>
            {/*<BottomNavigation />*/}
        </div>
    );
};

export default AccountSettings;



