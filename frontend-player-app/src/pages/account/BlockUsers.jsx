import BackButton from "../../components/reusable/buttons/BackButton";
import Header from "../../components/reusable/Header";
import PageTitle from "../../components/reusable/PageTitle";
import { useEffect, useState } from "react";
import { dispatch } from "../../redux/store.jsx";
import { useSelector } from "react-redux";
import Loader from "../../components/reusable/loading/Loader.jsx";
import UnblockModal from "../../components/pageComponents/AccountModals/UnBlockUser.jsx";

export default function BlockUsers() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [data, setData] = useState([]);
  const [id, setId] = useState(""); //for blocked user id
  const fetchingBlocked = useSelector(
    ({ loading }) => loading.effects.user.fetchUserBlockedUsers
  );
  const unBlock = useSelector(
    ({ loading }) => loading.effects.user.fetchUserBlockedUsers
  );

  async function getBlockedUsers() {
    const data = await dispatch.user.fetchUserBlockedUsers();
    console.log(data);
    setData(data);
    return data;
  }
  useEffect(() => {
    getBlockedUsers();
  }, []);

  const handleUnblock = async () => {
    await dispatch.user.unBlockUser(id);
    await getBlockedUsers();
    setIsModalOpen(false);
  };

  if (fetchingBlocked || unBlock) {
    return <Loader />;
  }

  return (
    <div className="page md:max-w-[700px] mx-auto flex flex-col min-h-screen">
      <Header>
        <BackButton from="" />
      </Header>
      <PageTitle>
        <div className="text-[20px] uppercase">BLOCKED USERS</div>
      </PageTitle>
      <UnblockModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onUnblock={handleUnblock}
      />
      <div className="pt-10">
        {data.length === 0 ? (
          <div className="flex justify-center items-center">
            <div className="h-[212px] w-[310px] text-center">
              <p className="text-lg mb-2">You haven’t blocked any users.</p>
              <p className="text-gray-500">
                If you do block a user in the future, you can always come back
                here and unblock them again.
              </p>
            </div>
          </div>
        ) : (
          <div className="">
            {data.map((user) => (
              <div
                key={user.id}
                className="flex items-center justify-between p-2"
              >
                <div className="flex items-center space-x-4">
                  {user.photoUrl === "" ? (
                    <div className="w-[52px] h-[52px] rounded-full bg-[#A5A5A5]" />
                  ) : (
                    <img
                      className="w-[52px] h-[52px] rounded-full"
                      src={user.photoUrl}
                      alt={user.lastName}
                    />
                  )}
                  <span className="font-semibold">{`@ ${user.lastName}`}</span>
                </div>

                <button
                  className="btn btn-sm bg-[#000000] text-[#FFFFFF] h-[32px] w-[88px] border-1 ms-center justify-center rounded-xl"
                  onClick={() => {
                    setIsModalOpen(true);
                    setId(user.id);
                  }}
                >
                  Unblock
                </button>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
