import React, { useEffect, useState } from "react";
import BackButton from "../components/reusable/buttons/BackButton";
import Loader from "../components/reusable/loading/Loader";
import { useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import { dispatch } from "../redux/store";
import { formatDateToWords } from "../utils/helpers";
import BottomNavigation from "../components/reusable/BottomNav";

const Result = () => {
  const [highlight, setHighlight] = useState(null);

  const queryParams = new URLSearchParams(window.location.search);

  const highlightId = queryParams.get("highlightId");
  const userId = queryParams.get("userId");

  const navigate = useNavigate();

  const fetchingHighlight = useSelector(
    ({ loading }) => loading.effects.feed.fetchHighlight
  );

  const getHighlight = async () => {
    if (highlightId && userId) {
      const res = await dispatch.feed.fetchHighlight({ highlightId, userId });
      setHighlight(res.data);
    }
  };

  const handleOpenProfile = (id) => {
    if (id) {
      navigate({ pathname: "/profile", search: `?id=${id}` });
    } else {
      navigate("/login");
    }
  };

  useEffect(() => {
    getHighlight();
  }, []);

  return fetchingHighlight ? (
    <Loader />
  ) : (
    highlight && (
      <div className="pb-[70px] page">
        <div className="flex justify-between w-full h-[10vh]">
          <BackButton from="" />
        </div>
        <div className="w-full overflow-hidden h-full">
          <div className="w-full h-full relative">
            <img
              src="/images/green.jpg"
              className="w-full min-h-[70vh] rounded-[20px]"
            ></img>
            <div className="absolute top-0 p-5 text-white bg-gradient-to-tl from-[#0000006e] rounded-[20px] from-45% right-0 bottom-0 left-0 flex flex-col gap-5 justify-center items-center">
              <div className="font-favela-bold">
                {formatDateToWords(highlight.result.dateTimePlayed)}
              </div>
              <div className="grid grid-cols-3 gap-3">
                <div className="text-center">
                  {highlight.result.homeTeam.data.clubName}
                </div>
                <div className="text-black flex flex-nowrap gap-3">
                  <div className="bg-[#52FF00] flex justify-center items-center w-10 h-10 rounded-[10px] text-2xl font-favela-bold">
                    {highlight.result.homeTeam.score}
                  </div>
                  <div className="text-3xl font-black text-white"> - </div>
                  <div className="bg-[#52FF00] flex justify-center items-center w-10 h-10 rounded-[10px] text-2xl font-favela-bold">
                    {highlight.result.awayTeam.score}
                  </div>
                </div>
                <div className="text-center">
                  {highlight.result.awayTeam.data.clubName}
                </div>
              </div>
              <div className="flex flex-nowrap w-full gap-3">
                <div className="flex flex-col gap-3 items-start w-[50%]">
                  {highlight.result.homeTeam.goalScorers.map(
                    (goalScorer, idx) => (
                      <p key={idx}>{goalScorer}</p>
                    )
                  )}
                </div>
                <div className="flex flex-col gap-3 items-end w-[50%]">
                  {highlight.result.awayTeam.goalScorers.map(
                    (goalScorer, idx) => (
                      <p key={idx}>{goalScorer}</p>
                    )
                  )}
                </div>
              </div>
              {/* Assists */}
              <div className="w-full">
                <p className="w-full text-center font-semibold text-lg my-4">
                  Assists
                </p>
                <div className="flex flex-nowrap w-full gap-3">
                  <div className="flex flex-col gap-3 items-start w-[50%]">
                    {highlight.result.homeTeam.assists.map((assist, idx) => (
                      <p key={idx}>{assist}</p>
                    ))}
                  </div>
                  <div className="flex flex-col gap-3 items-end w-[50%]">
                    {highlight.result.awayTeam.assists.map((assist) => (
                      <p>{assist}</p>
                    ))}
                  </div>
                </div>
              </div>
              {/* Player of the match */}
              <div className="w-full">
                <p className="w-full text-center font-semibold text-lg my-4">
                  Player of the match
                </p>
                <div className="flex flex-nowrap w-full gap-3">
                  <div className="flex flex-col gap-3 items-start w-[50%]">
                    {highlight.result.homeTeam.playersOfTheMatch.map(
                      (pom, idx) => (
                        <p key={idx}>{pom}</p>
                      )
                    )}
                  </div>
                  <div className="flex flex-col gap-3 items-end w-[50%]">
                    {highlight.result.awayTeam.playersOfTheMatch.map(
                      (pom, idx) => (
                        <p key={idx}>{pom}</p>
                      )
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-x-0 absolute mt-[32px]  h-[48px] w-[90%] rounded-[10px] mx-auto mr-10"></div>
        </div>
        <BottomNavigation />
      </div>
    )
  );
};
export default Result;
