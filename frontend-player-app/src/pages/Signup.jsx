import React from "react";
import Header from "../components/reusable/Header";
import PageTitle from "../components/reusable/PageTitle";
import { Link, useNavigate } from "react-router-dom";
import SignupForm from "../components/pageComponents/Signup/SignupForm";
import ConfirmAccount from "../components/pageComponents/Signup/ConfirmAccount";

const Signup = () => {
  const navigate = useNavigate();
  return (
    <div className="page">
      <Header>
        <div></div>
        <div></div>
        {/* <div onClick={() => navigate("/")}>Skip</div> */}
      </Header>
      <PageTitle>
        <div>SIGN UP</div>
      </PageTitle>
      <div className="border-b mt-4 text-sm text-gray-700 w-max mb-2 border-gray-300">
        Already a member?{" "}
        <Link className="text-gray-700 font-extrabold" to={"/login"}>
          Log in
        </Link>
      </div>
      <SignupForm />
      <ConfirmAccount />
    </div>
  );
};

export default Signup;
