import React, { useEffect, useRef, useState } from "react";
import { useSelector } from "react-redux";
import _uniqWith from "lodash.uniqwith";
import _iseEqual from "lodash.isequal";
import {
  Link,
  useLocation,
  useNavigate,
  useSearchParams
} from "react-router-dom";

import ExperienceCard from "../components/pageComponents/Profile/ExperienceCard";
import BottomNavigation from "../components/reusable/BottomNav";
import Loader from "../components/reusable/loading/Loader";
import Logo from "../components/reusable/Logo";
import Spinner from "../components/reusable/spinner/Spinner";
import Tabs from "../components/reusable/Tabs";
import { dispatch } from "../redux/store";
import {
  PreferredFoot,
  calculateAge,
  checkIfUserFollowsProfile,
  fetchRelationshipObject,
  notifyError,
  notifySuccess,
  notifyWarn,
  readablePosition,
  trim150,
  trim,
  PlayerPostion
} from "../utils/helpers";
import ExperienceModal from "../components/pageComponents/Profile/ExperienceModal";
import WhiteEditIcon from "../components/svgIcons/WhiteEdit";
import S3BucketManger from "../utils/s3.config";
import { useInView } from "react-cool-inview";
import Highlights from "../components/pageComponents/Profile/Highlights";
import LiveLike from "@livelike/engagementsdk";
import { currentEnv, liveLikeClientId } from "../utils/constants";
import Line from "../components/svgIcons/Line";
import PlusSign from "../components/svgIcons/PlusSign";
import PhysicalData from "../components/pageComponents/Profile/PhysicalData";
import VerificationRequest from "../components/pageComponents/Profile/VerificationRequest";
import ShareProfile from "../components/pageComponents/Profile/ShareProfile";
import SwitchButton from "../components/pageComponents/Custom/Switch";
import SettingsIcon from "../components/svgIcons/SettingsIcon";
import ThreeDotsIcon from "../components/svgIcons/ThreeDots.jsx";
import { CautionIcon } from "../components/svgIcons/CautionIcon.jsx";
import BlockModal from "../components/pageComponents/AccountModals/BlockUser.jsx";
import moment from "moment";
import NewSettingsIcon from "../components/svgIcons/newSettingsIcon.jsx";

const Profile = () => {
  const [searchParams] = useSearchParams();
  const userId = searchParams.get("id");

  const location = useLocation();
  const verifyPlayerQuery = searchParams.get("verify");

  const [selected, setSelected] = useState("experience");
  const [isLoading, setLoading] = useState(true);

  const [userDetails, setUserDetails] = useState();

  const [uploadingBanner, setUploadingBanner] = useState(false);
  const [isFollowing, setIsFollowing] = useState(false);
  const [followers, setFollowers] = useState();
  const [following, setFollowing] = useState();
  const [relationshipObject, setRelationshipObject] = useState(null);
  const [followersCount, setFollowersCount] = useState(0);
  const [followingCount, setFollowingCount] = useState(0);

  //Prompt block modal
  const [isModalOpen, setIsModalOpen] = useState(false);

  const navigate = useNavigate();
  const profileImageRef = useRef(null);
  const bannerRef = useRef(null);

  //userInfo is same as loggedInUser
  const userInfo = useSelector((state) => state.user.data);
  const { loggedInUser, guestUser } = useSelector(({ user }) => ({
    loggedInUser: user.data,
    guestUser: user.guest
  }));

  const { liveLikeProfileId } = useSelector(({ auth: { authUser }, user }) => ({
    loggedUserId: authUser?.userInfo?.id || "",
    liveLikeProfileId: user?.data?.liveLikeProfileId || "",
    liveLikeProfileToken: user?.data?.liveLikeProfileToken || "",
    authUser,
  }));

  const currentProfileAlreadyFollowsTheLoggedInUser = Boolean(
    following?.find(
      (follow) =>
        JSON.parse(follow.to_profile.custom_data || null)?.userId ===
        userInfo?.id
    )
  );

  const fetchingProfileGuest = useSelector(
    ({ loading }) => loading.effects.user.getUserProfileForGuest
  );
  const fetchingProfile = useSelector(
    ({ loading }) => loading.effects.user.getUserProfile
  );

  const profileUpdating = useSelector(
    ({ loading }) => loading.effects.user.updateUser
  );

  const block = useSelector(({ loading }) => loading.effects.user.blockUser);

  const initialSelectionTabViaUrl =
    searchParams.get("selected") || "experience";

  // check if we should show verification switch
  const shouldShowVerifySwitch =
    (Boolean(verifyPlayerQuery) &&
      localStorage.getItem("verificationPlayerId") === userId) ||
    userInfo?.invitedBy === userId;

  const onVerifyPlayer = async (event) => {
    event.preventDefault();

    await dispatch.user.updateUser({
      id: userId,
      verified: true,
      verifiedById: userInfo.id,
      shouldNotShowNotification: true
    });

    localStorage.removeItem("verificationPlayerId");
  };

  const getProfile = async () => {
    if (userId && !guestUser) {
      const data = await dispatch.user.getUserProfileForGuest({
        id: userId
      });
      setUserDetails(data);
      return;
    }

    if (!userId && !loggedInUser && userInfo.id) {
      await dispatch.user.getUserProfile({
        id: userInfo?.id
      });
      return;
    } else {
      setUserDetails(loggedInUser);
    }
  };

  const handleBannerUpload = async (event) => {
    setUploadingBanner(true);

    const s3BucketManger = new S3BucketManger();
    try {
      const s3 = await s3BucketManger.uploadAssets(
        event.target.files[0],
        "",
        "",
        "photo"
      );
      await dispatch.user.updateUser({
        banner: s3.assetUrl,
        id: userInfo?.id,
        shouldNotShowNotification: true
      });
      setUploadingBanner(false);
    } catch (error) {
      setUploadingBanner(false);
      notifyError("Upload Failed");
    }
  };

  // const getAllTeams = async () => {
  //   dispatch.team.getAllTeams();
  // };
  const getAllClubs = async () => {
    await dispatch.club.getAllClubs();
  };

  const getFollowers = async () => {
    const followers = await LiveLike.getProfileRelationships({
      relationshipTypeKey: "follow",
      toProfileId: userDetails.liveLikeProfileId
    });
    setFollowersCount(followers.count);
    setFollowers(followers.results);
  };

  const getFollowing = async () => {
    const following = await LiveLike.getProfileRelationships({
      relationshipTypeKey: "follow",
      fromProfileId: userDetails.liveLikeProfileId
    });
    setFollowingCount(following.count);
    setFollowing(following.results);
  };

  const follow = async (userDetails) => {
    try {
      const follow = await LiveLike.createProfileRelationship({
        relationshipTypeKey: "follow",
        toProfileId: userDetails.liveLikeProfileId,
        fromProfileId: liveLikeProfileId
      });
      getFollowers();
      notifySuccess(`${userDetails.firstName} is now a connection!`);

      await dispatch.user.updateUser({
        id: userInfo.id,
        followingsIDs: [
          ...userInfo.followingsIDs,
          { userId: userDetails.id, relationshipId: follow.id }
        ],
        shouldNotShowNotification: true
      });
    } catch (error) {
      // notifyError("An error occured!");
    }
  };

  const unfollow = async () => {
    try {
      if (relationshipObject.id) {
        await LiveLike.deleteProfileRelationship({
          relationshipId: relationshipObject.id
        });
        getFollowers();

        const filteredFollowinsIDs = userInfo.followingsIDs.filter(
          (ids) => ids.userId !== userDetails.id
        );

        dispatch.user.updateUser({
          id: userInfo.id,
          followingsIDs: filteredFollowinsIDs,
          shouldNotShowNotification: true
        });
      }
    } catch (error) {
      // notifyError("An error occured!");
    }
  };

  const checkForFollow = async () => {
    setIsFollowing(checkIfUserFollowsProfile(followers, userInfo?.id));
  };
  const findRelationshipObject = async () => {
    setRelationshipObject(fetchRelationshipObject(followers, userInfo?.id));
  };

  async function loadExistingProfile() {
    await LiveLike.init({
      accessToken: liveLikeProfileToken,
      clientId: liveLikeClientId,
      logger: currentEnv === "dev"
    });
  }

  useEffect(() => {
    if (!(Object.keys(userInfo || {}).length > 0) && !userId) {
      navigate("/login");
      return;
    }

    if (userId && guestUser) {
      setUserDetails(guestUser);
    }

    if (!userId && loggedInUser) {
      setUserDetails(loggedInUser);
    }

    if (!guestUser && !loggedInUser) {
      getProfile();
    }

    // getUserHighlights();
  }, [userId, loggedInUser, guestUser]);

  useEffect(() => {
    if (fetchingProfile || fetchingProfileGuest || profileUpdating) {
      setLoading(true);
    } else {
      setLoading(false);
    }
  }, [fetchingProfile, fetchingProfileGuest, profileUpdating]);

  useEffect(() => {
    getAllClubs();
  }, []);

  useEffect(() => {
    setSelected(initialSelectionTabViaUrl);
  }, [initialSelectionTabViaUrl]);

  useEffect(() => {
    if (
      Boolean(userDetails) &&
      userDetails.liveLikeProfileId &&
      userDetails.liveLikeProfileToken
    ) {
      getFollowers();
      getFollowing();
    }
  }, [userDetails]);

  useEffect(() => {
    if (Boolean(followers)) {
      checkForFollow();
      findRelationshipObject();
    }
  }, [followers, userInfo]);

  const profileHeader = `Share ${
    userDetails?.id === userInfo?.id ? "your" : `${userDetails?.firstName}'s`
  } profile`;

  const profileTitle = `Check out this profile on PLAYER. \n \n`;
  const profileTitleinbox = `Check this out. \n \n`;

  const profileUrl = `${
    userDetails?.id === userInfo?.id
      ? `${window.document.location.href}?id=${userDetails?.id}`
      : window.document.location.href
  }`;

  if (isLoading || block) {
    return <Loader />;
  }

  const bgColorAndText =
    userDetails.userType !== "NON_PLAYER" ? "bg-black" : "bg-white";
  const textColor =
    userDetails.userType !== "NON_PLAYER" ? "text-white" : "text-black";
  const textColorInverted =
    userDetails.userType !== "NON_PLAYER" ? "text-black" : "text-white";
  const bgTextBtn =
    userDetails.userType !== "NON_PLAYER"
      ? "text-black bg-white"
      : "text-white bg-black";

  const handleBlock = async () => {
    await dispatch.user.blockUser(userDetails.id);
    setIsModalOpen(false);
  };

  return (
    <div
      className={`${bgColorAndText + " " + textColor} relative min-h-[100vh]`}
    >
      <div className="flex flex-col justify-center items-center gap-5">
        <BlockModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          onUnblock={handleBlock}
        />
        <div className="h-[7vh] w-full grid grid-cols-3 justify-items-center">
          <div className="self-center col-start-2 min-[420px]:col-start-1 min-[420px]:ml-4">
            <Logo light={userDetails?.userType !== "NON_PLAYER"} />
          </div>
          {/* <div> */}
          <div className="flex justify-end items-center col-start-3 ">
            <ShareProfile
              header={profileHeader}
              title={profileTitle}
              inboxProfileTitle={profileTitleinbox}
              color={textColor.split("-")[1]}
              url={profileUrl}
              profile={userDetails}
              id="profile"
            />
            {userDetails?.id === userInfo?.id && (
              <div
                onClick={() => navigate("/settings")}
                className="cursor-pointer"
              >
                <NewSettingsIcon
                  fill={
                    userDetails?.userType !== "NON_PLAYER" ? "#fff" : "#000"
                  }
                  stroke={
                    userDetails?.userType !== "NON_PLAYER" ? "#fff" : "#000"
                  }
                />
              </div>
            )}
            {/*put tripe dot here*/}
            {userDetails?.id !== userInfo?.id && (
              <div className="relative dropdown dropdown-end">
                <div className="bg-transparent" tabIndex={0} role="button">
                  <ThreeDotsIcon
                    color={
                      userDetails?.userType !== "NON_PLAYER"
                        ? "#A5A5A5"
                        : "#000"
                    }
                  />
                </div>
                <ul
                  tabIndex={0}
                  className=" mr-3 mt-3 dropdown-content menu bg-base-100 z-[1000] w-[182px] h-[56] p-2 shadow rounded-tl-[20px] rounded-br-[20px] rounded-bl-[20px]"
                >
                  <li>
                    <div
                      className="flex items-center text-[#FF5B5B] text-[20px] font-normal"
                      onClick={() => setIsModalOpen(true)}
                    >
                      <CautionIcon />
                      Block user
                    </div>
                  </li>
                </ul>
              </div>
            )}
          </div>

          {/* </div> */}
        </div>
      </div>
      <div className="w-full box-border">
        <div className="w-full flex flex-col md:max-w-[80%] m-auto min-[820px]:border-x border-gray-500 pb-[70px] box-border">
          <div className="w-full relative z-10 box-border flex">
            <div className="w-full max-h-fit grid grid-cols-6 grid-rows-1 justify-items-start">
              <div className="col-span-full row-start-1 row-span-2 w-full max-h-[200px] sm:max-h-[300px]">
                <img
                  alt=""
                  src={userDetails?.banner || "/images/blankImage.jpeg"}
                  className="object-cover h-[100px] md:h-[200px] w-full"
                />
                <div className="absolute top-4 right-8">
                  {uploadingBanner ? (
                    <div className="">
                      <Spinner />
                    </div>
                  ) : (
                    ""
                  )}

                  {userDetails?.id === userInfo?.id && (
                    <div
                      onClick={() => {
                        bannerRef.current.click();
                      }}
                    >
                      <WhiteEditIcon />
                    </div>
                  )}
                </div>
                <input
                  ref={bannerRef}
                  accept="image/*"
                  type="file"
                  style={{ display: "none" }}
                  onChange={(event) => handleBannerUpload(event)}
                />
              </div>
              {/* The button to open modal for image*/}
              <div className="flex gap-2 max-[350px]:gap-1 max-[350px]:px-2 w-[100%] z-10 max-h-[115px] mt-24 sm:max-h-[130px] col-span-full row-start-2 row-span-2">
                <div
                  className={`${
                    bgColorAndText + " " + textColor
                  } w-[100px] p-[2px] z-10 h-[100px] max-w-[40%] sm:w-[130px] sm:h-[130px] rounded-full ml-4 min-[420px]:ml-6 object-cover col-start-1 col-span-full row-start-2 row-span-2 self-center min-[820px]:ml-[60px]`}
                >
                  <img
                    onClick={() => {
                      profileImageRef.current.click();
                    }}
                    src={
                      userDetails?.photoUrl
                        ? userDetails?.photoUrl
                        : "/images/profile.png"
                    }
                    alt="pix"
                    className="w-[100px] h-full rounded-full object-cover"
                  />
                </div>
                <div className="flex text-[12px] w-[60%]">
                  <div className="w-[55%] flex flex-col justify-end items-center">
                    <div
                      onClick={() =>
                        navigate("/user/connections/?type=followers", {
                          state: {
                            data: {
                              followers,
                              following,
                              followersCount,
                              followingCount,
                              // getFollowers,
                              // getFollowing,
                              fullName: `${userDetails.firstName} ${userDetails.lastName}`
                            }
                          }
                        })
                      }
                      className="mb-2 flex flex-col items-center justify-center cursor-pointer select-none"
                    >
                      <div
                        className={`${textColor} text-xl font-semibold -mb-1`}
                      >
                        {followersCount}
                      </div>
                      <div className={`${textColor}`}>Followers</div>
                    </div>
                    {userDetails?.id === userInfo?.id ? (
                      <div
                        onClick={() => {
                          navigate("/user/message-list");
                        }}
                        className="flex w-[80%] -mt-1 items-center justify-center"
                      >
                        <div
                          className={`${bgTextBtn} flex items-center justify-center w-full  py-1 rounded-[20px]`}
                        >
                          Inbox
                        </div>
                      </div>
                    ) : isFollowing ? (
                      <div className="flex w-[80%] -mt-1 items-center justify-center">
                        <div
                          className={`dropdown dropdown-hover dropdown-bottom w-full`}
                        >
                          <label tabIndex={0} className="">
                            <div
                              className={`select-none cursor-pointer ${
                                userDetails.userType === "NON_PLAYER"
                                  ? "text-white bg-[#000000]"
                                  : "text-black bg-[#fff]"
                              } font-medium flex items-center justify-center w-full  py-1 rounded-[20px]`}
                            >
                              Following
                              <img
                                className="ml-2"
                                src={
                                  userDetails.userType === "NON_PLAYER"
                                    ? "/images/arrowDown.svg"
                                    : "/images/arrowDownBlack.svg"
                                }
                                alt="arrow-drop"
                              />
                            </div>
                          </label>
                          <div
                            tabIndex={0}
                            className={`dropdown-content w-[100%] shadow bg-[${
                              userDetails.userType === "NON_PLAYER"
                                ? "#000"
                                : "#fff"
                            }]`}
                          >
                            <div
                              onClick={() => {
                                unfollow();
                              }}
                              className={`absolute h-[43px] mt-1 font-medium flex items-center justify-center w-full ${
                                userDetails.userType === "NON_PLAYER"
                                  ? "bg-[#000] text-white"
                                  : "bg-[#fff] text-black"
                              } rounded-[10px]`}
                            >
                              Unfollow
                            </div>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div
                        onClick={() => {
                          if (loggedInUser) {
                            follow(userDetails);
                          } else {
                            notifyWarn("Please login to follow");
                          }
                        }}
                        className="flex w-[80%] -mt-1 items-center justify-center cursor-pointer select-none"
                      >
                        <div className="text-black font-medium flex items-center justify-center w-full bg-[#52FF00] py-1 rounded-[20px]">
                          {/* if the current profile user is one of the current loggedIn profile follower, then have a follow back*/}
                          {currentProfileAlreadyFollowsTheLoggedInUser
                            ? "Follow Back"
                            : "Follow"}
                        </div>
                      </div>
                    )}
                  </div>
                  <div className="flex w-[45%] flex-col justify-end items-center">
                    <div
                      onClick={() =>
                        navigate("/user/connections/?type=following", {
                          state: {
                            data: {
                              followers,
                              following,
                              followersCount,
                              followingCount,
                              // getFollowers,
                              // getFollowing,
                              fullName: `${userDetails.firstName} ${userDetails.lastName}`
                            }
                          }
                        })
                      }
                      className="mb-2 flex flex-col items-center justify-center cursor-pointer select-none"
                    >
                      <div
                        className={`${textColor} text-xl font-semibold -mb-1`}
                      >
                        {followingCount}
                      </div>
                      <div className={`${textColor}`}>Following</div>
                    </div>
                    <div className="flex w-full items-center justify-center">
                      {userDetails?.id === userInfo?.id ? (
                        <a
                          href="edit-profile"
                          className="flex -mt-1 items-center justify-center w-full"
                        >
                          <div
                            className={`${bgTextBtn} flex items-center justify-center w-full py-1 rounded-[20px]`}
                          >
                            {" "}
                            Edit Profile
                          </div>
                        </a>
                      ) : (
                        <div
                          onClick={() => {
                            navigate(`/user/message?recipientId=${userId}`);
                          }}
                          className="flex items-center justify-center w-full -mt-1"
                        >
                          <div
                            className={`${bgTextBtn} flex items-center justify-center w-full py-1 rounded-[20px]`}
                          >
                            {" "}
                            Message
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="flex mt-2 w-full gap-7 flex-col relative z-10 top-4 md:top-[76px]">
            <div className="smooth-scroll-container" />
            {/* Modal for image */}
            {userDetails?.photoUrl ? (
              <>
                <input
                  ref={profileImageRef}
                  type="checkbox"
                  id="my-modal-9"
                  className="modal-toggle"
                />
                <div className="modal">
                  <div className="modal-box relative">
                    <label
                      htmlFor="my-modal-9"
                      className="btn btn-sm btn-circle absolute right-2 top-2"
                    >
                      ✕
                    </label>
                    <img
                      src={
                        userDetails?.photoUrl
                          ? userDetails?.photoUrl
                          : "/images/profile.png"
                      }
                      alt="pix"
                      className="w-full object-cover"
                    />
                    <p
                      className={`${
                        userDetails.userType !== "NON_PLAYER"
                          ? "dark:text-black"
                          : "dark:text-white"
                      } inline text-center pt-3 italic`}
                    >
                      {userDetails?.bio}
                    </p>
                  </div>
                </div>{" "}
              </>
            ) : (
              ""
            )}
            {/* End Modal for image */}
          </div>

          <div className="">
            {/*to remodel page classname*/}
            <div>
              <div className="flex flex-col gap-2 text-[14px] mx-6 mt-5 text font-light justify-items-center min-[420px]:items-start min-[420px]:justify-items-start min-[420px]:border-b min-[420px]:pb-4 border-gray-500 min-[820px]:mx-0 min-[820px]:rounded-xl min-[820px]:px-[60px]">
                <p
                  className={`text-[16px] font-semibold capitalize ${textColor}`}
                >
                  <span
                    className={`${userDetails?.verified ? "inline" : "flex"}`}
                  >
                    {
                      <p className="w-full leading-10 inline">
                        {userDetails?.firstName} {userDetails?.lastName}
                        {""}
                        {userDetails.userType !== "NON_PLAYER" &&
                          `, ${calculateAge(userDetails?.birthday)}`}
                      </p>
                    }
                    {userDetails?.verified ? (
                      <img
                        title="verified player"
                        width={18}
                        height={18}
                        src="./images/CheckSquare.svg"
                        className="inline ml-5"
                      />
                    ) : !Boolean(shouldShowVerifySwitch) &&
                      userInfo.id === userDetails.id &&
                      userInfo?.userType !== "NON_PLAYER" ? (
                      <VerificationRequest
                        userDetails={userDetails}
                        userInfo={userInfo}
                        enableVerifyBtn={false}
                      />
                    ) : (
                      ""
                    )}
                    {Boolean(shouldShowVerifySwitch) &&
                      !userDetails?.verified && (
                        <SwitchButton
                          small
                          isLoading={isLoading}
                          handleCheckboxChange={onVerifyPlayer}
                          textFont={"font-poppins"}
                          uppercaseText={false}
                          text="Verify player"
                          labelPosition="left"
                          activeBgColor=":bg-[#52FF00]"
                          inActiveBgColor="bg-gray-300"
                        />
                      )}
                  </span>
                </p>
                <p className={`${textColor}`}>
                  {userDetails?.teamName || userDetails?.clubName}
                </p>
                {userDetails?.userType !== "NON_PLAYER" && (
                  <p className={`${textColor}`}>
                    Contract Expires: {userDetails?.contractExpiry || "n/a"}
                  </p>
                )}
                {userDetails?.userType !== "NON_PLAYER" && (
                  <p className={`${textColor} capitalize`}>
                    {PlayerPostion[userDetails?.position] ||
                      userDetails?.position}
                    ,{" "}
                    <span className="">
                      {PreferredFoot[userDetails?.preferredFoot] ||
                        userDetails?.preferredFoot}
                    </span>
                  </p>
                )}
                {userDetails?.userType === "NON_PLAYER" && (
                  <p className={`${textColor} capitalize`}>
                    {userDetails?.nonPlayerRole?.map((item, idx) => (
                      <span key={idx}>
                        {item}
                        {userDetails?.nonPlayerRole?.length - 1 !== idx
                          ? ","
                          : ""}{" "}
                      </span>
                    ))}
                  </p>
                )}

                {userDetails.userType !== "NON_PLAYER" && (
                  <p className={`${textColor}`}>{userDetails?.height}</p>
                )}

                {userDetails.userType !== "NON_PLAYER" && (
                  <p className={`${textColor}`}>
                    DOB:{" "}
                    {`${moment(userDetails?.birthday).format("DD.MM.YYYY")}`}
                  </p>
                )}

                <p className={`${textColor}`}>{userDetails?.location}</p>
                {userDetails.userType !== "NON_PLAYER" && (
                  <p className={`${textColor}`}>
                    Represented by: {userDetails?.representedBy || "n/a"}
                  </p>
                )}
              </div>
            </div>
            {userDetails?.bio && (
              <>
                <p
                  className={`${textColor} pt-4 mx-6 text-[14px] text-center min-[420px]:text-left min-[420px]:border-b min-[420px]:pb-4 border-gray-500 min-[820px]:mx-0 min-[820px]:rounded-xl min-[820px]:px-[60px]`}
                >
                  {trim(userDetails?.bio, 28)}
                  {userDetails?.bio?.length > 28 && (
                    <span
                      className="underline font-bold cursor-pointer"
                      onClick={() => {
                        const modal = document.getElementById("bio");
                        if (modal) modal.showModal();
                      }}
                    >
                      Read more
                    </span>
                  )}
                </p>
                <dialog
                  id="bio"
                  className="modal"
                  onClick={(e) => {
                    // Close the modal when clicking outside the modal box
                    if (e.target.id === "bio") {
                      e.target.close();
                    }
                  }}
                >
                  <div className="modal-box bg-[#0b0b0b] text-white">
                    <h3 className="font-bold text-lg">
                      {userDetails?.firstName}'s Bio
                    </h3>
                    <p className="py-4">{userDetails?.bio}</p>
                    <span
                      className="underline font-bold cursor-pointer"
                      onClick={() => {
                        const modal = document.getElementById("bio");
                        if (modal) modal.close();
                      }}
                    >
                      See less
                    </span>
                    {/* <div className="modal-action">
                      <form method="dialog">
                        
                      </form>
                    </div> */}
                  </div>
                </dialog>
              </>
            )}

            <div className="min-[420px]:border-b mx-6 min-[420px]:pb-4 border-gray-500 min-[820px]:mx-0 min-[820px]:rounded-xl min-[820px]:px-[60px] mb-16">
              <Tabs
                font="font-favela-bold uppercase"
                tabs={["experience", "highlights"]}
                selected={selected}
                setSelected={(selection) => {
                  searchParams.delete("selected", initialSelectionTabViaUrl);
                  searchParams.set("selected", selection);
                  navigate(`${location.pathname}?${searchParams.toString()}`);
                }}
                color={
                  userDetails?.userType !== "NON_PLAYER" ? "white" : "black"
                }
              />
              <div
                className={`font-medium mt-[20px] md:grid md:grid-cols-2 gap-10 ${
                  selected === "highlights"
                    ? "md:grid-rows-none md:grid-cols-none"
                    : ""
                }`}
              >
                {selected === "highlights" ? (
                  <Highlights
                    userId={userId || userDetails?.id}
                    isNonPlayer={userDetails?.userType === "NON_PLAYER"}
                    textColor={textColor}
                    ownProfile={userDetails?.id === userInfo?.id}
                  />
                ) : (
                  <>
                    {Object.keys(userDetails?.experiences).length > 0 ? (
                      <ExperienceCard
                        data={userDetails?.experiences}
                        bgColorAndText={bgColorAndText}
                        textColor={textColor}
                        shoudEdit={userDetails?.id === userInfo?.id}
                        userType={userDetails?.userType}
                      />
                    ) : (
                      <div
                        className={`w-full h-32 flex items-center justify-center ${textColor}`}
                      >
                        {userDetails?.id === userInfo?.id ? (
                          <span className="text-gray-400 text-center">
                            You don’t have any experience yet. Please add new
                            experience by clicking{" "}
                            <span
                              className={`lowercase inline px-0 mx-0 ${textColor}`}
                            >
                              the + button
                            </span>
                            <div className="absolute right-[100px] block md:hidden">
                              <Line
                                color={
                                  userInfo === "NON_PLAYER"
                                    ? "#000000"
                                    : "#ffffff"
                                }
                              />
                            </div>
                          </span>
                        ) : (
                          "No club history & stats"
                        )}
                      </div>
                    )}
                  </>
                )}
              </div>
              {/* Add experience modal */}
              {!userId && (
                <ExperienceModal
                  textColor={textColor}
                  isNonPlayer={userDetails?.userType === "NON_PLAYER"}
                />
              )}

              {/* Physical data, TODO: Open once done with the current profile implementation */}
              {userDetails?.userType !== "NON_PLAYER" && (
                <div className="mt-10">
                  <p
                    className={`uppercase text-[16px] font-favela-bold font-bold ${textColor}`}
                  >
                    PHYSICAL DATA
                  </p>
                  <div className="my-4">
                    <PhysicalData
                      userInfo={userDetails}
                      textColor={textColorInverted}
                    />
                  </div>
                </div>
              )}

              {/* Physical data */}
              <div className="mt-10">
                <p
                  className={`uppercase text-[16px] font-favela-bold font-bold ${textColor}`}
                >
                  References
                </p>
                <div className="my-4">
                  <Link to={`/reference/${userDetails.id}`}>
                    {" "}
                    {/* <PlusSign /> */}
                    <button
                      className={`btn btn-outline rounded-[20px] ${
                        userDetails.userType === "NON_PLAYER"
                          ? "text-black"
                          : "text-white"
                      }`}
                    >
                      View References
                    </button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
          <BottomNavigation />
        </div>
      </div>
    </div>
  );
};

export default Profile;
