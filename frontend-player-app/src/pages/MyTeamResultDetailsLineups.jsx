import React, { useEffect } from "react";
import { useLocation } from "react-router-dom";
import BackButton from "../components/reusable/buttons/BackButton";
import { lineups } from "../components/reusable/data";
import Header from "../components/reusable/Header";
import Logo from "../components/reusable/Logo";
import PageTitle from "../components/reusable/PageTitle";

const MyTeamResultDetailsLineups = () => {
  const location = useLocation();

  const { details } = location.state;


  return (
    <div className="text-sm px-4">
      <Header>
        <BackButton from="" />
        <Logo />
        <div></div>
      </Header>
      <PageTitle>
        <div>LINE UPS</div>
      </PageTitle>
      <div className="mt-7 text-base flex gap-7 justify-center items-center mb-2">
        <div className="flex justify-center items-center gap-3">
          <img 
            src={details?.homeTeam?.data?.logo}
            alt=""
            className="min-w-[50px] min-h-[50px] border-[1px] rounded-full object-cover"
          />
          {details?.homeTeam?.data?.clubName},
          {details?.homeTeam?.data?.teamName}
        </div>
        <div className="flex justify-center items-center gap-3">
          <img 
            src={details?.awayTeam?.data?.logo}
            alt=""
            className="min-w-[50px] min-h-[50px] border-[1px] rounded-full object-cover"
          />
          {details?.awayTeam?.data?.clubName},
          {details?.awayTeam?.data?.teamName}
        </div>
      </div>
      <div className="flex justify-between items-center mt-5">
        <div className="w-1/2">
          {details?.homeTeam?.lineUPs?.players?.map((player, key) => (
            <div className="mb-3 flex" key={key}>
              <div className="">
                {player.position}. {player.name}
              </div>
              <div className="flex flex-nowrap gap-1 items-center ml-2">
                {player.firstYellow ? (
                  player.secondYellow ? (
                    <div className="flex flex-nowrap items-center">
                      <div className="w-3 h-4 bg-yellow-300"></div>
                      <span className="text-xs mb-2">x2</span>
                    </div>
                  ) : (
                    <div className="w-3 h-4 bg-yellow-300"></div>
                  )
                ) : (
                  ""
                )}
                {player.red && (
                  <div>
                    <div className="w-3 h-4 bg-red-500"></div>
                  </div>
                )}
              </div>
            </div>
          ))}
          <div className="font-semibold text-lg mt-10 mb-5">Substitutes</div>
          <div>
            {details?.homeTeam?.lineUPs?.substitutes?.map((player, index) => (
              <div key={index} className="mb-3 flex items-center">
                {player.position}. {player.name}
              </div>
            ))}
          </div>
        </div>

        <div className="w-1/2">
          {details?.awayTeam?.lineUPs?.players?.map((player, key) => (
            <div className="mb-3 flex" key={key}>
              <div className="">
                {player?.position}. {player?.name}
              </div>
              <div className="flex flex-nowrap gap-1 items-center ml-2">
                {player.firstYellow ? (
                  player.secondYellow ? (
                    <div className="flex flex-nowrap items-center">
                      <div className="w-3 h-4 bg-yellow-300"></div>
                      <span className="text-xs mb-2">x2</span>
                    </div>
                  ) : (
                    <div className="w-3 h-4 bg-yellow-300"></div>
                  )
                ) : (
                  ""
                )}
                {player.red && (
                  <div>
                    <div className="w-3 h-4 bg-red-500"></div>
                  </div>
                )}
              </div>
            </div>
          ))}
          <div className="font-semibold text-lg mt-10 mb-5">Substitutes</div>
          <div>
            {details?.homeTeam?.lineUPs?.substitutes?.map((player, index) => (
              <div key={index} className="mb-3 flex items-center">
                {player.position}. {player.name}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MyTeamResultDetailsLineups;
