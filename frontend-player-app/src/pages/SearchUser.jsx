import React, { useCallback, useEffect, useState } from "react";
import _debounce from "lodash/debounce";
import BottomNavigation from "../components/reusable/BottomNav";
import BackButton from "../components/reusable/buttons/BackButton";
import Header from "../components/reusable/Header";
import SearchCard from "../components/reusable/SearchCard";
import { SearchIcon2 } from "../components/svgIcons/SearchIcon";
import { dispatch } from "../redux/store";
import Spinner from "../components/reusable/spinner/Spinner";
import { useSelector } from "react-redux";
import { searchArrayOrMakeCallToAPI } from "../utils/helpers";
import Analytics from "../utils/google-analytics";
import { TRACKING_EVENTS } from "../utils/constants";

const SearchUser = () => {
  const [searchResult, setSearchResult] = useState(null);
  const [recentSearch, setRecentSearch] = useState([]);
  const [selectedItems, setSelectedItems] = useState([]);
  // const [userList, setUserList] = useState([]);
  // const [teamsList, setTeamsList] = useState([]);

  const userInfo = useSelector((state) => state.user.data);


  const gettingUser = useSelector(
    ({ loading }) => loading.effects.user.userSearch
  );

  const { userList, teamsList } = useSelector(({ user, team }) => ({
    userList: user.usersByProjection,
    teamsList: team.teamsByProjection
  }));

  const handleSearch = async (query) => {
    const searchResposne = await searchArrayOrMakeCallToAPI({
      searchTerm: query,
      array: [
        ...userList?.map((item) => ({
          ...item,
          type: "user",
          fullname: `${item?.firstName} ${item?.lastName}`
        })),
        // ...teamsList?.map((item) => ({ ...item, type: "team" }))
      ],
      makeSearchCall: [dispatch.user.userSearch, dispatch.team.teamSearch]
    });

    const searchResponse = searchResposne?.length > 0 ? searchResposne : null;
    setSearchResult(searchResponse?.sort((a, b) => {
      if (a.type === "user" && b.type === "team") {
        return 1;
      } else if (a.type === "team" && b.type === "user") {
        return -1;
      }
    }));

    Analytics.trackEvent({
      name: TRACKING_EVENTS.SEARCH_PERFORMED,
      metadata: {
        search_term: query,
        result_count: searchResponse?.length ?? 0
      }
    })
  };

  const clearAllRecentSearch = () => {
    window.localStorage.removeItem("searchResult");
    setRecentSearch([]);
  };

  const removeFromRecent = (id) => {
    const recent = JSON.parse(localStorage.getItem("searchResult"));
    const newRecent = recent.filter((item) => item.id !== id);
    localStorage.setItem("searchResult", JSON.stringify(newRecent));
    setRecentSearch(newRecent);
  };

  const fetchSearchData = useCallback(_debounce(handleSearch, 400), [userList, teamsList]);

  const fetchAllUserData = async () => {
    const res = await dispatch.user.userSearchByProjection();
    // setUserList(res?.Items);
  };

  const fetchAllTeamData = async () => {
    const res = await dispatch.team.getTeamsByProjection();
    // setTeamsList(res?.Items);
  };

  useEffect(() => {
    const savedSearch = window.localStorage.getItem("searchResult");

    if (savedSearch) {
      setRecentSearch(JSON.parse(savedSearch));
    }
  }, [searchResult]);

  useEffect(() => {
    if (!Boolean(userList?.length)) {
      fetchAllUserData();
    }
  }, [userList]);

  useEffect(() => {
    if (!Boolean(teamsList?.length)) {
      fetchAllTeamData();
    }
  }, [teamsList]);

  return (
    <div className="page pb-[70px]">
      <Header>
        <div className="flex items-center justify-between w-full gap-7 mt-10">
          <BackButton from="" />
          <div className="relative z-0 w-full">
            <input
              onChange={(e) => fetchSearchData(e.target.value)}
              type="text"
              name="text"
              id="text"
              className={`border-b-[1px] border-gray-600 block py-3 pl-8 w-full text-sm text-gray-900 bg-transparent appearance-none dark:border-gray-600 dark:focus:border-black-800 focus:outline-none focus:ring-0`}
              placeholder="Type a name…"
            />
            <div className="absolute scale-[0.85] top-1/2 -left-1 transform -translate-y-1/2">
              <SearchIcon2 />
            </div>
          </div>
        </div>
      </Header>
      <div className="mt-10">
        {!gettingUser && searchResult
          ? searchResult?.map((result) => (
            <SearchCard
              isRecent={false}
              removeFromRecent={removeFromRecent}
              key={result.id}
              data={result}
            />
          ))
          : ""}

        {!searchResult && !gettingUser ? (
          <div className="flex justify-between w-full">
            <div className="font-bold">Recents</div>
            <button
              className="font-bold text-gray-400 underline outline-none border-none bg-transparent my-0 py-0 focus:outline-none focus:bg-slate-100 focus:text-black"
              onClick={clearAllRecentSearch}
            >
              Clear All
            </button>
          </div>
        ) : (
          ""
        )}
        <div className="mt-4">
          {gettingUser ? <Spinner /> : ""}

          {!gettingUser &&
            !searchResult &&
            recentSearch?.map((result) => (
              <SearchCard
                removeFromRecent={removeFromRecent}
                key={result.id}
                data={result}
              />
            ))}
        </div>
      </div>
      <BottomNavigation />
    </div>
  );
};

export default SearchUser;
