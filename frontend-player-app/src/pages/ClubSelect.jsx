import { useFormik } from "formik";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import TeamSelect from "../components/pageComponents/ClubSelect/TeamSelect";
import BackButton from "../components/reusable/buttons/BackButton";
import { BigButtons } from "../components/reusable/buttons/Buttons";
import Header from "../components/reusable/Header";
import PageTitle from "../components/reusable/PageTitle";
import Spinner from "../components/reusable/spinner/Spinner";
import { dispatch } from "../redux/store";
import { radioButtons } from "../utils/formSchema";

function ClubSelect() {
  const navigate = useNavigate();
  const [listOfClubs, setListOfClubs] = useState([]);
  const [searchValue, setSearchValue] = useState("");
  const [searchData, setSearchData] = useState([]);
  const [teamVisible, setTeamVisible] = useState(false);
  const [clubVisible, setClubVisible] = useState(true);
  const [teams, setTeams] = useState([]);
  const [clubDetails, setClubDetails] = useState([]);
  const [selectedTeam, setSelectedTeam] = useState(null);
  const [haveAuthorityError, setHaveAuthorityError] = useState("");

  const clubModel = useSelector(({ club }) => club);

  const handleClubSelection = async (club) => {
    setClubDetails(club);
    const res = await dispatch.team.getTeamsByClubId(club.id);

    setTeams(res.data);
    setClubVisible(false);
    setTeamVisible(true);
  };

  const { userInfo } = useSelector((state) => state.auth.authUser);
  const updateUserLoading = useSelector(
    ({ loading }) => loading.effects.user.updateUser
  );
  const pickClubLoading = useSelector(
    ({ loading }) => loading.effects.team.getTeamsByClubId
  );

  const onSubmit = async (values) => {
    const submitValues = {
      cludId: clubDetails.id,
      teamName: selectedTeam?.teamName || "N/A",
      clubName: clubDetails?.clubName || "N/A",
      teamId: selectedTeam.id,
      id: userInfo.id,
      userType: "MANAGER",
    };
    if (values?.clubAdmin === false) {
      delete submitValues.userType;
    }
    if (values.clubAdmin === true && values.haveAuthority === false) {
      setHaveAuthorityError("You must have Authority");
    } else {
      const res = await dispatch.user.updateUser(submitValues);      
      if (res === 1) navigate("/account-setup");
    }
  };

  const initialValues = {
    haveAuthority: false,
    clubAdmin: false,
  };

  const { values, handleChange, handleSubmit } = useFormik({
    initialValues,
    onSubmit,
    validationSchema: radioButtons,
  });

  useEffect(() => {
    setListOfClubs(clubModel?.allClubs);
  }, [clubModel?.allClubs]);

  useEffect(() => {
    setSearchData(
      listOfClubs?.filter((club) =>
        club?.clubName?.toLowerCase().includes(searchValue?.toLowerCase())
      )
    );
    if (searchValue.length === 0) setSearchData([]);
  }, [searchValue]);

  return (
    <div className="page">
      <Header>
        <BackButton onClick={() => {
          setSearchData([])
          setClubVisible(true)
          setTeamVisible(false)
          setTeams([])
          setSelectedTeam([])
          setSearchValue("")
        }} />
      </Header>
      {clubVisible && (
        <>
          <PageTitle>
            <div>CHOOSE YOUR CLUB</div>
          </PageTitle>
          <div className="font-poppins">Your most recent, or current club</div>
        </>
      )}

      <div className="flex flex-col h-[70vh] justify-between">
        {clubVisible && (
          <div className="mt-12 h-max">
            <label className="text-gray-500">Club Name</label>
            <div className="flex items-center">
              <div className="appearance-none border-none w-full text-gray-700 py-1 leading-tight">
                <input
                  onChange={(event) => setSearchValue(event.target.value)}
                  className="appearance-none bg-transparent border-b border-black w-full text-gray-700 py-1 pl-2 leading-tight focus:outline-none"
                  type="text"
                  placeholder="Enter club name"
                  aria-label="Full name"
                />
                <ul className="overflow-y-scroll max-h-[34vh] mt-3 bg-white w-full flex flex-col gap-5 rounded-xl">
                  {searchData?.map((club) => (
                    <li
                      onClick={() => handleClubSelection(club)}
                      key={club.id}
                      className="pb-2 border-b-[2px] border-gray-200 flex justify-between items-center"
                    >
                      {club?.clubName}
                      {pickClubLoading && club.id === clubDetails.id ? (
                        <Spinner />
                      ) : (
                        <img
                          className="w-11 h-11"
                          src={club.clubLogoUrl}
                          alt=""
                        />
                      )}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        )}
        <TeamSelect
          teams={teams}
          clubDetails={clubDetails}
          visible={teamVisible}
          setSelectedTeam={setSelectedTeam}
          selectedTeam={selectedTeam}
        />
        {/* <div className="flex flex-col justify-center items-center"> */}
          {/* REQUESTED TO BE REMOVED. FOR NOW COMMENTED OUT */}
          {/* <div className="px-3 py-2">
            <div className="flex items-center mb-2">
              <input
                type="checkbox"
                name="clubAdmin"
                id="clubAdmin"
                value={values.clubAdmin}
                onChange={handleChange}
                className="appearance-none outline-none opacity-0 absolute h-4 w-4"
              />
              <div className="border-2 rounded-full border-gray-900 w-6 h-6 flex flex-shrink-0 justify-center items-center mr-4 focus-within:border-gray-500">
                <svg
                  className="hidden w-4 h-4 pointer-events-none"
                  version="1.1"
                  viewBox="0 0 17 12"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g fill="none" fillRule="evenodd">
                    <g
                      transform="translate(-9 -11)"
                      fill="white"
                      fillRule="nonzero"
                    >
                      <path d="m25.576 11.414c0.56558 0.55188 0.56558 1.4439 0 1.9961l-9.404 9.176c-0.28213 0.27529-0.65247 0.41385-1.0228 0.41385-0.37034 0-0.74068-0.13855-1.0228-0.41385l-4.7019-4.588c-0.56584-0.55188-0.56584-1.4442 0-1.9961 0.56558-0.55214 1.4798-0.55214 2.0456 0l3.679 3.5899 8.3812-8.1779c0.56558-0.55214 1.4798-0.55214 2.0456 0z" />
                    </g>
                  </g>
                </svg>
              </div>
              <label htmlFor="A3-yes" className="select-none">
                Are You Club Admin?
              </label>
            </div>
          </div> */}

          {/* <div className="px-3 py-2">
            <div className="flex flex-col items-center mb-2">
              <div className="flex items-center mb-2">
                <input
                  type="checkbox"
                  name="haveAuthority"
                  id="haveAuthority"
                  value={values.haveAuthority}
                  onChange={handleChange}
                  className="appearance-none outline-none opacity-0 absolute h-4 w-4"
                />
                <div className="border-2 rounded-full border-gray-900 w-6 h-6 flex flex-shrink-0 justify-center items-center mr-4 focus-within:border-gray-500">
                  <svg
                    className="hidden w-4 h-4 pointer-events-none"
                    version="1.1"
                    viewBox="0 0 17 12"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g fill="none" fillRule="evenodd">
                      <g
                        transform="translate(-9 -11)"
                        fill="white"
                        fillRule="nonzero"
                      >
                        <path d="m25.576 11.414c0.56558 0.55188 0.56558 1.4439 0 1.9961l-9.404 9.176c-0.28213 0.27529-0.65247 0.41385-1.0228 0.41385-0.37034 0-0.74068-0.13855-1.0228-0.41385l-4.7019-4.588c-0.56584-0.55188-0.56584-1.4442 0-1.9961 0.56558-0.55214 1.4798-0.55214 2.0456 0l3.679 3.5899 8.3812-8.1779c0.56558-0.55214 1.4798-0.55214 2.0456 0z" />
                      </g>
                    </g>
                  </svg>
                </div>
                <label htmlFor="A3-yes" className="select-none">
                  You confirm you have the authority to oversee your club's page
                </label>
              </div>
              <p className="text-red-500 text-xs italic py-1">
                {haveAuthorityError && haveAuthorityError}
              </p>
            </div>
          </div> */}
        {/* </div> */}
      </div>
      {teamVisible && (
        <div className="mt-10">
          <BigButtons
            isLoading={updateUserLoading}
            onClick={() => handleSubmit()}
            black
            label={"NEXT"}
          />
        </div>
      )}
    </div>
  );
}

export default ClubSelect;
