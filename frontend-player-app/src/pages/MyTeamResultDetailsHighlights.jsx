import React, { useEffect } from "react";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import Feeds from "../components/pageComponents/MyTeam/Feeds";
import BottomNavigation from "../components/reusable/BottomNav";
import BackButton from "../components/reusable/buttons/BackButton";
import Header from "../components/reusable/Header";
import Loader from "../components/reusable/loading/Loader";
import Logo from "../components/reusable/Logo";
import PageTitle from "../components/reusable/PageTitle";
import { dispatch } from "../redux/store";

const MyTeamResultDetailsHighlights = () => {
  const navigate = useNavigate();
  const {
    teamHighlights,
    allTeams: { currentTeamId },
  } = useSelector(({ team: { allTeams }, feed: { teamHighlights } }) => ({
    allTeams,
    teamHighlights,
  }));

  const { isHighlights } = useSelector(({ loading }) => ({
    isHighlights: loading.effects.matches.getTeamHighlights,
  }));

  const handleGetTeamHighlights = async () => {
    await dispatch.feed.getTeamHighlights(currentTeamId);
  };

  useEffect(() => {
    if (currentTeamId) {
      handleGetTeamHighlights();
    } else {
      navigate("/team-select");
    }
  }, []);

  if (isHighlights) {
    return <Loader />;
  }

  return (
    <div className="pb-[70px] page">
      <Header>
        <BackButton from="/team-dashboard" />
        <Logo />
        <div></div>
      </Header>
      <PageTitle center={true}>
        <div className="mb-4">HIGHLIGHTS</div>
      </PageTitle>
      <Feeds highlights={teamHighlights} />
      <BottomNavigation />
    </div>
  );
};

export default MyTeamResultDetailsHighlights;
