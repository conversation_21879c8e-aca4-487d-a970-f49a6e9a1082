import React from "react";
import { useSelector } from "react-redux";
import LeagueStatsHeader from "../components/reusable/LeagueStatsHeader";

const LeagueTopCleanSheets = () => {
  const { selectedLeagueData } = useSelector(
    ({ leagues: { selectedLeagueData } }) => ({
      selectedLeagueData,
    })
  );

  return (
    <div>
      <LeagueStatsHeader  from={'/league-and-stats'} />
      <div className="px-[15px] mt-[25px]">
        <p className="uppercase text-[16px] font-favela-bold font-bold">
          toP clean cheets
        </p>

        <table className="w-full">
          <tbody>
            {selectedLeagueData?.leagueStats?.topCleanSheet
              ?.sort(
                (a, b) =>
                  parseFloat(+b.noOfcleanSheet) - parseFloat(+a.noOfcleanSheet)
              )
              ?.map((data, i) => (
                <tr key={i} className="border-b-[1px] border-[#0000001A]">
                  <td className="py-[10px]">{i + 1}</td>
                  <td className="py-[10px]">
                    {data?.name}
                  </td>
                  <td className="py-[10px] float-right">
                    {data.noOfcleanSheet}
                  </td>
                </tr>
              ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default LeagueTopCleanSheets;
