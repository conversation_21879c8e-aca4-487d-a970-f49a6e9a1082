import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import BackButton from "../components/reusable/buttons/BackButton";
import Header from "../components/reusable/Header";
import PageTitle from "../components/reusable/PageTitle";
import moment from "moment";

const AllResultSeason = () => {
  const location = useLocation();
  const season = location.state.season;
  const navigate = useNavigate();

  const { clubData, singleTeam, teamDashboard } = useSelector(
    ({ team: { clubData, singleTeam }, matches: { teamDashboard } }) => ({
      clubData,
      singleTeam,
      teamDashboard
    })
  );

  const [seasonData, setSeasonData] = useState([]);

  const handleClickResult = (detail) => {
    navigate("/team-dashboard/result-details", {
      state: { data: detail }
    });
  };

  const filterSeasonData = () => {
    const seasonData = teamDashboard.data.filter(
      ({ seasonTitle }) => seasonTitle === season
    );

    setSeasonData(seasonData);
  };

  useEffect(() => {
    filterSeasonData();
  }, []);

  return (
    <div className="page">
      <Header>
        <BackButton />
        <div />
        <div />
      </Header>
      <PageTitle center>
        <img 
          className="object-cover rounded-full w-[70px] h-[70px]"
          src={singleTeam?.logoUrl}
          alt=""
        />
        <div className="w-8/12 h-full">
          <div>{clubData?.clubName}</div>{" "}
          <div className="font-mono mt-2">{singleTeam?.teamName}</div>
        </div>
      </PageTitle>
      <PageTitle>
        <div className="mb-4 mt-7">SEASON {season}</div>
      </PageTitle>
      <div className="mt-7 md:w-8/12 mx-auto md:text-center">
        {seasonData?.map((result, key) => (
          <div key={key} className="mb-7">
            <div className="font-favela-bold text-xs mb-3 md:text-left">
              {moment(result.dateTimePlayed, "DD/MM/YYYY").format(
                "Do MMM YYYY"
              )}
            </div>
            <div
              onClick={() => handleClickResult(result)}
              className="flex justify-center mb-4 gap-4 items-center"
            >
              <div className="w-5/12">
                <p title={`${result?.homeTeam?.data?.clubName}`}>
                  {result?.homeTeam?.data?.clubName.substr(0, 18)}
                  {result?.homeTeam?.data?.clubName?.length > 18 ? "..." : ""}
                </p>
              </div>
              <div className="flex w-2/12 gap-1 flex-nowrap font-favela-bold">
                <div>{result?.homeTeam?.score}</div>
                <div>&#8211;</div>
                <div>{result?.awayTeam?.score}</div>
              </div>
              <div className="w-5/12">
                <p title={`${result?.awayTeam?.data?.clubName}`}>
                  {result?.awayTeam?.data?.clubName.substr(0, 18)}
                  {result?.awayTeam?.data?.clubName?.length > 18 ? "..." : ""}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default AllResultSeason;
