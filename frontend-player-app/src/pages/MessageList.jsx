import React, { useEffect } from "react";
import { useSelector } from "react-redux";
import { Link } from "react-router-dom";
import BottomNavigation from "../components/reusable/BottomNav";
import BackButton from "../components/reusable/buttons/BackButton";
import Header from "../components/reusable/Header";
import MessageListCard from "../components/reusable/MessageListCard";
import Spinner from "../components/reusable/spinner/Spinner";
import NewMessageIcon from "../components/svgIcons/NewMessageIcon";
import { dispatch } from "../redux/store";

export const MESSAGE_REFRESH_INTERVAL = 15000; // 15 seconds

const MessageList = () => {
  const userInfo = useSelector((state) => state.user.data);
  const chats = useSelector((state) => state.chat.chats);

  const loadingUser = useSelector(
    ({ loading }) => loading.effects.user.fetchUserDetails
  );
  const loadingChats = useSelector(
    ({ loading }) => loading.effects.chat.getChats
  );
  const isLoading = (loadingUser || loadingChats) && !chats?.length;

  const fetchChats = async () => {
    await dispatch.chat.getChats();
  };

  useEffect(() => {
    if (!userInfo?.id) return;

    fetchChats();

    const intervalId = setInterval(() => {
      fetchChats();
    }, MESSAGE_REFRESH_INTERVAL);

    return () => {
      clearInterval(intervalId);
    };
  }, [userInfo?.id]);

  return (
    <div className="w-full h-screen page">
      <Header>
        <div className="flex items-center justify-between w-full mt-10 gap-7">
          <BackButton from="" />
          <div className="uppercase font-favela-bold text-[20px]">Messages</div>
          <Link to="/user/new-message" className="opacity-[0.6]">
            <NewMessageIcon />
          </Link>
        </div>
      </Header>
      <div className="mt-10">
        {chats?.length > 0 ? (
          chats.map((chat) => (
            <div key={chat.chatroomId}>
              <MessageListCard chat={chat} />
            </div>
          ))
        ) : (
          <div className="w-full h-full py-16 text-2xl font-bold text-center text-gray-400 font-favela-bold">
            {isLoading ? <Spinner /> : "No Messages"}
          </div>
        )}
      </div>
      <BottomNavigation />
    </div>
  );
};

export default MessageList;
