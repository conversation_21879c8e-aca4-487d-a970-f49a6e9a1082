import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import LeagueStatsHeader from "../components/reusable/LeagueStatsHeader";
import Loader from "../components/reusable/loading/Loader";
import Tabs from "../components/reusable/Tabs";
import { dispatch } from "../redux/store";
import Popup from "../components/pageComponents/MyTeam/Popup";
import { LeaguesApi } from "../services";
import { PUBLISHSTATUS } from "../utils/constants";
import { notifyError, notifySuccess } from "../utils/helpers";
import SwitchButton from "../components/pageComponents/Custom/Switch";

const playerStats = [
  {
    title: "toP SCORER",
    linkPath: "/league-top-scorer",
  },
  {
    title: "toP assists",
    linkPath: "/league-top-assists",
  },
  {
    title: "TOP clean sheets",
    linkPath: "/league-top-clean-cheets",
  },
  {
    title: "TOP APPEARANCES",
    linkPath: "/league-top-apperances",
  },
];

const tableHead = ["Pos", "Pl", "w", "d", "L", "GD", "PTS"];

const LeagueAndStats = () => {
  const [selected, setSelected] = useState("");
  const [sortedLeagueData, setSortedLeagueData] = useState([]);
  const [selectedLeagueData, setSelectedLeague] = useState({});
  const [leaguePublishStatus, setLeaguePublishStatus] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const prevData = location.state;

  const {
    allTeams: { currentTeamId, currentClubId },
  } = useSelector(
    ({
      team: { allTeams, allTeamPlayers },
      leagues: { allLeaguesAndStats },
    }) => ({
      allTeams,
      allTeamPlayers,
      allLeaguesAndStats,
    })
  );

  const { isLeaguesLoading, isSingleTeam, currentLoggedUser } = useSelector(
    ({ loading, user }) => ({
      isLeaguesLoading: loading.effects.leagues.getAllLeaguesAndStats,
      isSingleTeam: loading.effects.team.getSingleTeam,
      currentLoggedUser: user?.data,
    })
  );

  const getAllLeaguesAndStats = async () => {
    await dispatch.leagues.getAllLeaguesAndStats({ currentTeamId });
  };

  const handlePublishLeague = async (status) => {
    try {
      setLeaguePublishStatus(status === PUBLISHSTATUS.PUBLISHED)
      await LeaguesApi.publishLeagues({
        ...selectedLeagueData,
        publishStatus: status,
      });
      notifySuccess("Successful");
      getAllLeaguesAndStats();
    } catch (error) {
      setLeaguePublishStatus(selectedLeagueData?.publishStatus &&
        selectedLeagueData?.publishStatus === PUBLISHSTATUS.PUBLISHED)
      notifyError(error?.response?.data?.message);
    }
  };

  useEffect(() => {
    if (prevData?.data?.tab) {
      setSelected(prevData?.data?.tab);
    } else {
      setSelected("table");
    }

    if (currentTeamId && currentClubId) {
      getAllLeaguesAndStats();
    } else {
      navigate("/team-select");
    }
    setSortedLeagueData(
      selectedLeagueData?.leagueTable?.sort(
        (a, b) => Number(b?.totalPoints) - Number(a?.totalPoints)
      )
    );
  }, []);

  useEffect(() => {
    setSortedLeagueData(
      selectedLeagueData?.leagueTable?.sort(
        (a, b) => Number(b?.totalPoints) - Number(a?.totalPoints)
      )
    );
  }, [selectedLeagueData]);

  useEffect(() => {
    setLeaguePublishStatus(selectedLeagueData?.publishStatus &&
      selectedLeagueData?.publishStatus === PUBLISHSTATUS.PUBLISHED)
  }, [selectedLeagueData])

  if (isLeaguesLoading) {
    return <Loader />;
  }

  return (
    <div className="min-h-[100vh]">
      <LeagueStatsHeader
        setSelectedLeague={setSelectedLeague}
        from={`/team-dashboard?clubId=${currentClubId}&teamId=${currentTeamId}`}
      />
      {Boolean(sortedLeagueData?.length) && !isLeaguesLoading ? (
        ""
      ) : (
        <div>
          <Popup />
        </div>
      )}
      <div className="px-[15px]">
        {currentLoggedUser &&
          currentLoggedUser?.teamId === currentTeamId &&
          currentLoggedUser?.isManager && (
            <div className="mt-5">
              <SwitchButton
                handleCheckboxChange={() =>
                  handlePublishLeague(
                    selectedLeagueData?.publishStatus ===
                      PUBLISHSTATUS.PUBLISHED
                      ? PUBLISHSTATUS.DRAFT
                      : PUBLISHSTATUS.PUBLISHED
                  )
                }
                checked={leaguePublishStatus}
              />
            </div>
          )}

        <Tabs
          tabs={["table", "player stats"]}
          selected={selected}
          setSelected={setSelected}
          color="black"
        />
        <div>
          {selected === "table" ? (
            <div className="mt-[30px]">
              <table className="w-full">
                <thead>
                  <tr className="">
                    {tableHead.map((th, i) => (
                      <th key={i} className="">
                        <p className="text-[17px] font-normal uppercase">
                          {th}
                        </p>
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {sortedLeagueData?.map(
                    (
                      {
                        loss,
                        winnings,
                        teamName,
                        drawers,
                        numberOfMatchPlayed,
                        numberOfGoals,
                        totalPoints,
                      },
                      i
                    ) => (
                      <tr
                        key={i}
                        className="h-[60px] border-b-[2px] border-[#0000001A]"
                      >
                        <td className="text-left">
                          <p className="text-[11px] uppercase font-normal font-favela-bold">
                            {i + 1}. {teamName}
                          </p>
                        </td>
                        <td className="text-left">
                          <p className="text-[12px] font-normal">
                            {numberOfMatchPlayed}
                          </p>
                        </td>
                        <td className="text-left">
                          <p className="text-[12px] font-normal">{winnings}</p>
                        </td>
                        <td className="text-left">
                          <p className="text-[12px] font-normal">{drawers}</p>
                        </td>
                        <td className="text-left">
                          <p className="text-[12px] font-normal">{loss}</p>
                        </td>
                        <td className="text-left">
                          <p className="text-[12px] font-normal">
                            {numberOfGoals}
                          </p>
                        </td>
                        <td className="text-left">
                          <p className="text-[12px] font-normal">
                            {totalPoints}
                          </p>
                        </td>
                      </tr>
                    )
                  )}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="mt-[25px]">
              {playerStats.map(({ linkPath, title }, i) => (
                <div
                  onClick={() => navigate(linkPath)}
                  key={i}
                  className="flex justify-between items-center py-[20px] cursor-pointer"
                >
                  <p className="uppercase text-[15px] font-favela-bold font-bold">
                    {title}
                  </p>
                  <img src="/images/arrowRight.svg" alt="" />
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default LeagueAndStats;
