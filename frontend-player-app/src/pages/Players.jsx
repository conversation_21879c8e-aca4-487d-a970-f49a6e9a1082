import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import BackButton from "../components/reusable/buttons/BackButton";
import Header from "../components/reusable/Header";
import Loader from "../components/reusable/loading/Loader";
import Logo from "../components/reusable/Logo";
import ShareModal from "../components/reusable/ShareModal";
import Tabs from "../components/reusable/Tabs";
import { dispatch } from "../redux/store";
import SquadList from "./SquadList";

const playerStats = [
  {
    title: "toP SCORER",
    linkPath: "/top-scorer",
  },
  {
    title: "toP assists",
    linkPath: "/top-assists",
  },
  {
    title: "TOP clean sheets",
    linkPath: "/top-clean-sheets",
  },
  {
    title: "TOP APPEARANCES",
    linkPath: "/top-apperances",
  },
];

const Players = () => {
  const navigate = useNavigate();
  const [selected, setSelected] = useState("");
  const location = useLocation();
  const prevData = location.state;

  const { isAllTeamPlayers, isSingleTeam } = useSelector(({ loading }) => ({
    isAllTeamPlayers: loading.effects.team.getAllTeamsPlayers,
    isSingleTeam: loading.effects.team.getSingleTeam,
  }));

  const {
    allTeams: { currentTeamId, currentClubId },
    allTeamPlayers,
    clubData,
  } = useSelector(({ team: { allTeams, allTeamPlayers, clubData } }) => ({
    allTeams,
    allTeamPlayers,
    clubData,
  }));

  const getAllTeamPlayers = async () => {
    if (selected === "squad list" && !allTeamPlayers.length) {
      await dispatch.team.getAllTeamsPlayers(currentTeamId);
    }
  };

  const getSingleTeam = async () => {
    if (selected === "player stats") {
      await dispatch.team.getSingleTeam({
        teamId: currentTeamId,
        clubId: currentClubId,
      });
    }
  };

  useEffect(() => {
    if (currentTeamId && currentClubId) {
      getAllTeamPlayers();
      getSingleTeam();
    } else {
      navigate("/team-select");
    }
  }, [selected]);

  useEffect(() => {
    if (prevData?.data?.tab) {
      setSelected(prevData?.data?.tab);
    } else {
      setSelected("squad list");
    }
  }, []);

  if (isAllTeamPlayers && isSingleTeam) {
    return <Loader />;
  }
  return (
    <div className="pb-[70px] page">
      <Header>
        <BackButton
          onClick={() => navigate("/team-dashboard")}
          data={{ tab: selected }}
        />
        <Logo />
        <div />
      </Header>

      <div className="flex justify-between items-center">
        <p className="uppercase text-[20px] font-favela-bold font-bold">
          Players and stats
        </p>
        <label htmlFor="share-modal" className="">
          <img  src="/images/plus.svg" alt="" />
        </label>
      </div>
      <div className="flex gap-3 items-center mt-[20px]">
        <img 
          src={clubData?.clubLogoUrl}
          alt=""
          className="w-[40px] h-[40px] rounded-full object-cover"
        />
        <p className="text-[20px] font-medium">{clubData?.clubName}</p>
      </div>
      <Tabs
        tabs={["squad list", "player stats"]}
        selected={selected}
        setSelected={setSelected}
        color="black"
      />
      {selected === "squad list" ? <SquadList data={allTeamPlayers} /> : null}
      {selected === "player stats" ? (
        <div className="mt-[25px]">
          {playerStats.map(({ linkPath, title }, i) => (
            <div
              onClick={() => navigate(linkPath)}
              key={i}
              className="flex justify-between items-center py-[20px] cursor-pointer"
            >
              <p className="uppercase text-[16px] font-favela-bold font-bold">
                {title}
              </p>
              <img  src="/images/arrowRight.svg" alt="" />
            </div>
          ))}
        </div>
      ) : null}

      {/* SHARE MODAL */}
      <ShareModal />
    </div>
  );
};

export default Players;
