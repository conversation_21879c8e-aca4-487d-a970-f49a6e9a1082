import React, { useRef, useState } from "react";
import CreateProfileForm from "../components/pageComponents/CreateProfileForm";
import BackButton from "../components/reusable/buttons/BackButton";
import Header from "../components/reusable/Header";
import Logo from "../components/reusable/Logo";
import PageSubTitle from "../components/reusable/PageSubTitle";
import PageTitle from "../components/reusable/PageTitle";
import S3BucketManger from "../utils/s3.config";
import { useSelector } from "react-redux";
import { dispatch } from "../redux/store";
import { notifyError } from "../utils/helpers";
import { useNavigate } from "react-router-dom";
import Spinner from "../components/reusable/spinner/Spinner";

function CreateProfile() {
  const { userInfo } = useSelector((state) => state.auth.authUser);
  const hiddenUploader = useRef();
  const [s3Loading, setS3Loading] = useState(false);
  const [preview, setPreview] = useState("");
  const navigate = useNavigate();

  const handleUpload = async (event) => {
    setPreview(event.target.files[0]);
    const s3BucketManger = new S3BucketManger();
    try {
      setS3Loading(true);
      const s3 = await s3BucketManger.uploadAssets(
        event.target.files[0],
        "",
        '',
        "photo"
      );
      await dispatch.user.updateUser({
        photoUrl: s3.assetUrl,
        id: userInfo?.id,
      });
      setPreview("");
      setS3Loading(false);
    } catch (error) {
      setS3Loading(false);
      notifyError("Upload Failed");
    }

    if (preview) {
      URL.revokeObjectURL(preview);
    }
  };

  const handleProfileClick = () => {
    hiddenUploader.current.click();
  };
  return (
    <div className="page">
      <Header>
        <BackButton from="" />
        {/* <div onClick={() => navigate("/")}>Skip</div> */}
      </Header>
      <PageTitle>
        <div>YOUR PROFILE</div>
      </PageTitle>
      <PageSubTitle>
        {preview ? (
          <>
            <img 
              onClick={handleProfileClick}
              className="w-32 h-32 object-cover rounded-full opacity-50"
              src={`${URL.createObjectURL(preview)}`}
              alt=""
            />
            <div className="absolute top-[18%]">
              <Spinner />
            </div>
          </>
        ) : (
          <img 
            onClick={handleProfileClick}
            className="w-32 h-32 object-cover rounded-full"
            src={`${
              userInfo?.photoUrl?.length > 5
                ? userInfo?.photoUrl
                : "/images/profile.png"
            }`}
            alt=""
          />
        )}
        <div className="mt-5 text-gray-600 text-[14px] leading-[21px] font-[400]">UPLOAD YOUR PHOTO</div>
        <input
          ref={hiddenUploader}
          accept="image/*"
          type="file"
          style={{ display: "none" }}
          onChange={(event) => handleUpload(event)}
        />
      </PageSubTitle>
      <CreateProfileForm s3Loading={s3Loading} />
    </div>
  );
}

export default CreateProfile;
