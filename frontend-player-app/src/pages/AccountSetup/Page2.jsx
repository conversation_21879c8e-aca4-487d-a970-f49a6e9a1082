import React from "react";


export default function Page2({ goToNextPage, goToPrevPage }) {
  return (
    <div className="h-full">
      <div className="max-w-md mx-auto bg-white overflow-hidden">
        <div
          className="relative h-[173px] bg-no-repeat bg-cover bg-center"
          style={{ backgroundImage: "url(images/NeilHarris_Large_2nd.png)" }}
        >
          <div className="h-full pt-5 px-4">
            <img
              onClick={goToPrevPage}
              className="h-[20px] w-[20px] inline text-left cursor-pointer"
              src="images/white-back.svg"
            />
          </div>
        </div>

        <div className="p-6 space-y-6 mt-7">
          <div className="text-center">
            <h2 className="font-poppins font-[600] text-[16px] leading-[24px] text-[#00000099]">
              Claim your session in just <span className="font-[800]">2 easy steps!</span>
            </h2>

            <p className="text-[#00000099] mb-4 mt-7 font-[400] text-[13px] leading-[24px] text-center">
              <span className="font-[700]">1. Complete your profile</span> in full.
            </p>

            <p className="text-[#00000099] font-[400] text-[13px] leading-[24px] text-center">
              <span className="font-[700]">2. Refer 5 of your friends</span> (playing at a similar or higher level to you) to the app.
            </p>


            <p className="text-[#00000099] my-5 font-[400] text-[13px] italic leading-[22px] font-poppins text-center">
              Keep an eye on your DMs. Neil will be in touch with more information.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
} 
