import React from "react";
import { BigButtons } from "../../components/reusable/buttons/Buttons";

export default function Page1({ goToNextPage, goToPrevPage }) {
  return (
    <div className="h-full">
      <div className="max-w-md mx-auto bg-white overflow-hidden">
        <div
          className="relative h-[173px] bg-no-repeat bg-cover bg-center"
          style={{ backgroundImage: "url(images/NeilHarris_Large.png)" }}
        >
          {/* <div className="h-full pt-5 px-4">
            <img
              onClick={goToPrevPage}
              className="h-[20px] w-[20px] inline text-left cursor-pointer"
              src="images/white-back.svg"
            />
          </div> */}
        </div>

        <div className="p-6 space-y-4 mt-2">
          <div className="text-center">
            <h2 className="font-poppins font-[600] text-[16px] leading-[24px] text-[#00000099]">
              Claim your free 1:1 session with ex- <span className="font-[800]">Manchester United</span> coach, <PERSON>.
            </h2>

            <div className="text-[#00000099] font-poppins font-[400] text-[12px] leading-[22px] mb-4 mt-4">
              <p className="mb-2">{" "}</p>With 21 years of coaching experience at <p>{" "}</p>
              <span className="font-[500]">Manchester United</span>, Neil helped bring through
              the likes of:
            </div>

            <p className="text-[#00000099] text-center text-[13px] font-[700] flex flex-col">
              <span>Kobbie Mainoo</span>
              <span>Marcus Rashford</span>
              <span>Scott Mctominay</span>
              <span>Angel Gomes</span>
            </p>

            <p className="text-[#00000099] mt-4 font-poppins font-[400] text-[13px] leading-[22px]">
              Complete your PLAYER profile <span className="font-[800]">and refer 5 of your friends</span> to get a 1:1 football career session with Neil to talk through your development areas, your club prospects and go through any video footage you have.
            </p>
            <p
              className="text-[#00000099] font-poppins font-[400] my-3 text-[13px] leading-[22px]"
            >
              This is a unique opportunity to <span className="font-[700]">elevate your game!</span>
            </p>

          </div>
        </div>
      </div>
    </div>
  );
}
