import React from "react";
import BackButton from "../../components/reusable/buttons/BackButton";
import { useNavigate } from "react-router-dom";
import googlePlayImage from "../../assets/googleplayLarge_.png";
import appleStoreImage from "../../assets/appstoreLarge_.png";

export default function Page3({ goToNextPage, goToPrevPage, userType }) {
  const navigate = useNavigate();
  return (
    <div className="relative min-h-screen">
      <div className="h-full">
        <div className="max-w-md mx-auto bg-white overflow-hidden">
          <div className="inset-0  h-full pt-5 px-4">
            <div className="w-full inline-flex">
              {userType !== "NON_PLAYER" && (
                <img
                  onClick={goToPrevPage}
                  className="h-[20px] w-[20px] inline text-left cursor-pointer"
                  src="images/black-back.svg"
                />
              )}
              <h1 className="font-[700] w-full text-[20px] leading-[24px] inline text-black text-center font-favela-bold mx-10">
                DOWNLOAD THE APP
              </h1>
            </div>
          </div>

          <div className="flex flex-col items-center p-6 space-y-9 mt-7">
            <h2 className="text-center font-[700] text-[18px] font-poppins leading-[27px]">
              For the best experience, we recommend downloading the Official
              PLAYER app.
            </h2>

            <div
              onClick={() =>
                window.open(
                  "https://apps.apple.com/gb/app/player-the-footballers-app/id6503473876",
                  "_blank"
                )
              }
            >
              <img
                className="mx-auto mt-4 w-[262.72px] h-[76px]"
                src={appleStoreImage}
              />
            </div>

            <div
              onClick={() =>
                window.open(
                  "https://play.google.com/store/apps/details?id=com.playerapp.playerapp&pcampaignid=web_share",
                  "_blank"
                )
              }
            >
              <img
                className="mx-auto mt-4 w-[262.72px] h-[76px]"
                src={googlePlayImage}
              />
            </div>
            <div className="mb-5 w-[70%] text-center italic text-[#000000] text-opacity-60 font-[400] font-italic text-[12px] leading-[18px]">
              Or click skip below to continue on your browser.
            </div>
          </div>
        </div>
      </div>
      <div
        className="absolute bottom-20 w-full mb-10"
        onClick={() => navigate("/login")}
      >
        <p className="underline text-center text-[#000000] text-opacity-60 font-[400] font-italic text-[16px] leading-[24px]">
          Skip
        </p>
      </div>
    </div>
  );
}
