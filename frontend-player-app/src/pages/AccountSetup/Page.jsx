import React, { useState } from "react";
import Page1 from "./Page1";
import Page2 from "./Page2";
import Page3 from "./Page3";
import { BigButtons } from "../../components/reusable/buttons/Buttons";
import { useSelector } from "react-redux";

const AccountSetup = () => {
  const [stepIndex, setStepIndex] = useState(0);
  const userInfo = useSelector((state) => state.user.data);

  const goToNextPage = () => {
    setStepIndex((prevIndex) => (prevIndex + 1) % pages.length);
  };

  const goToPrevPage = () => {
    setStepIndex((prevIndex) => (prevIndex - 1 + pages.length) % pages.length);
  };

  const NonPlayerPages = [
    {
      content: (
        <Page3
          userType={userInfo.userType}
          goToNextPage={goToNextPage}
          goToPrevPage={goToPrevPage}
        />
      )
    }
  ];

  const PlayerPages = [
    {
      content: <Page1 goToNextPage={goToNextPage} goToPrevPage={goToPrevPage} />
    },
    {
      content: <Page2 goToNextPage={goToNextPage} goToPrevPage={goToPrevPage} />
    },
    {
      content: (
        <Page3
          userType={userInfo.userType}
          goToNextPage={goToNextPage}
          goToPrevPage={goToPrevPage}
        />
      )
    }
  ];

  const pages =
    userInfo.userType === "NON_PLAYER" ? NonPlayerPages : PlayerPages;

  return (
    <div className="h-full flex flex-col">
      {/* Indicators */}
      {userInfo.userType !== "NON_PLAYER" && (
        <div className="flex mb-6 space-x-2 items-center justify-center mt-5">
          {pages.map((_, index) => (
            <div
              key={index}
              className={`rounded-full ${
                stepIndex === index
                  ? "bg-[#52FF00] w-[10px] h-[10px]"
                  : "bg-[#52FF0066] w-[8px] h-[8px]"
              }`}
            />
          ))}
        </div>
      )}

      {/* Page Content */}
      <div className="bg-white w-full h-full">{pages[stepIndex].content}</div>

      {stepIndex !== 2 && userInfo.userType !== "NON_PLAYER" && (
        <div className="mt-[5%] mb-6 w-full mx-auto">
          <BigButtons black onClick={goToNextPage} label={"NEXT"} />
        </div>
      )}
    </div>
  );
};

export default AccountSetup;
