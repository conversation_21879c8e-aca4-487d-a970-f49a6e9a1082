import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import BackButton from "../components/reusable/buttons/BackButton";
import { BigButtons } from "../components/reusable/buttons/Buttons";
import Header from "../components/reusable/Header";
import PageTitle from "../components/reusable/PageTitle";
import { dispatch } from "../redux/store";

function TeamSelect() {
  const navigate = useNavigate();
  const { allTeams } = useSelector((state) => state.team);
  const [selectedTeam, setSelectedTeam] = useState("");
  const [selectedClub, setSelectedClub] = useState("");

  const getAllTeams = async () => {
    await dispatch.team.getAllTeams();
  };

  const handleSelectTeam = async (teamId, clubId) => {
    setSelectedTeam(teamId);
    setSelectedClub(clubId);
    await dispatch.team.setState({
      allTeams: { ...allTeams, currentTeamId: teamId, currentClubId: clubId },
    });
  };

  const handleNext = () => {
    navigate({
      pathname: "/team-dashboard",
      search: `?teamId=${selectedTeam}&clubId=${selectedClub}`,
    });
  };

  useEffect(() => {
    getAllTeams();
  }, []);

  if (!allTeams.loaded) {
    <h1>Loading...</h1>;
  }

  const sortedTeams = allTeams?.data?.Items?.sort((a, b) =>
    a.teamName.toLowerCase() < b.teamName.toLowerCase()
      ? -1
      : b.teamName.toLowerCase() > a.teamName.toLowerCase()
      ? 1
      : 0
  );
  return (
    <div className="page">
      <Header>
        <BackButton />
        <div onClick={() => navigate("/")}>Skip</div>
      </Header>
      <div className="w-[90%]">
        <PageTitle>
          {/* <div>HAMMERSMITH FC</div> */}
          {/* <img  src="/images/clubs/smith.png" alt="" /> */}
        </PageTitle>
      </div>
      <div className="mt-5 font-favela-bold">Choose Your Team:</div>
      <ul className="mt-5 overflow-y-auto h-[60vh] bg-white w-full flex flex-col gap-5 rounded-xl">
        {sortedTeams?.map(({ id, teamName, clubId }) => (
          <li
            key={id}
            onClick={() => handleSelectTeam(id, clubId)}
            className="pb-1 border-b-[2px] border-gray-200 flex justify-between items-center"
          >
            <div className="w-full flex justify-between">
              <p>{teamName}</p>
              {selectedTeam === id ? (
                <div className="border-2 bg-black rounded-full border-gray-900 w-6 h-6 flex flex-shrink-0 justify-center items-center mr-2 ">
                  <svg
                    className="w-4 h-4"
                    version="1.1"
                    viewBox="0 0 17 12"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g fill="none" fillRule="evenodd">
                      <g
                        transform="translate(-9 -11)"
                        fill="white"
                        fillRule="nonzero"
                      >
                        <path d="m25.576 11.414c0.56558 0.55188 0.56558 1.4439 0 1.9961l-9.404 9.176c-0.28213 0.27529-0.65247 0.41385-1.0228 0.41385-0.37034 0-0.74068-0.13855-1.0228-0.41385l-4.7019-4.588c-0.56584-0.55188-0.56584-1.4442 0-1.9961 0.56558-0.55214 1.4798-0.55214 2.0456 0l3.679 3.5899 8.3812-8.1779c0.56558-0.55214 1.4798-0.55214 2.0456 0z" />
                      </g>
                    </g>
                  </svg>
                </div>
              ) : null}
            </div>
          </li>
        ))}
      </ul>
      <div className="mb-14 mt-10">
        <BigButtons black label={"NEXT"} onClick={handleNext} />
      </div>
    </div>
  );
}

export default TeamSelect;
