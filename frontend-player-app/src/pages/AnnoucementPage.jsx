import React, { useCallback, useEffect, useRef, useState } from "react";
import {
  momentGetTimeAgo,
  notify<PERSON>rror,
  notify<PERSON>uc<PERSON>,
  notify<PERSON>ar<PERSON>
} from "../utils/helpers";
import LiveLike from "@livelike/engagementsdk";
import BackButton from "../components/reusable/buttons/BackButton";
import { useNavigate } from "react-router-dom";
import Send from "../components/svgIcons/Send";
import { useSelector } from "react-redux";
import { dispatch } from "../redux/store";
import Spinner from "../components/reusable/spinner/Spinner";
import {
  LiveLikeReactionPacks,
  TRACKING_EVENTS,
  currentEnv,
  liveLikeClientId
} from "../utils/constants";
import Competition from "../components/pageComponents/Home/Competition.jsx";
import { ThreeDotsIcon } from "../components/svgIcons";
import UserListCard from "../components/reusable/UserListCard.jsx";
import { MentionText } from "../components/pageComponents/Announcement/CommentMentionText.jsx";
import { CommentSort } from "@livelike/javascript";
import { useInView } from "react-intersection-observer";
import ReactionListCard from "../components/reusable/ReactionListCard.jsx";
import Analytics from "../utils/google-analytics.js";

const AnnouncementPage = () => {
  const [fetchedComments, setFetchedComments] = useState([]);
  const [commentsCount, setCommentsCount] = useState();
  const [commentsLoading, setCommentsLoading] = useState(false);
  const [sendLoading, setSendLoading] = useState(false);
  const [commentText, setCommentText] = useState([]);
  const [isPanelOpen, setIsPanelOpen] = useState(false);
  const [currentAnnouncement, setAnnouncement] = useState(null);
  const [shouldFetchMore, setNextCusor] = useState(false);

  //states for mentions or usertag
  const [isMentioning, setIsMentioning] = useState(false);
  const [mentionQuery, setMentionQuery] = useState("");

  const [reportModal, setReportModal] = useState(false);
  const [reportText, setReportText] = useState("");
  const [currentCommentItem, setCurrentCommentItem] = useState(null);

  // Detect "@" and trigger modal
  const handleInputChange = useCallback((e) => {
    const { value } = e.target;
    setCommentText(value);

    // Check for "@" followed by a character (e.g., "@f")
    const mentionStart = value.lastIndexOf("@");
    if (mentionStart !== -1 && value.length > mentionStart + 1) {
      setIsMentioning(true);
      setMentionQuery(value.slice(mentionStart + 1)); // Query after "@"
    } else {
      // Close the modal if the mention criteria is not met
      setIsMentioning(false);
    }
  }, []);

  // Handle adding the selected mention to the input
  const handleMentionSelect = (mention) => {
    const mentionStart = commentText.lastIndexOf("@");
    const newCommentText = `${commentText.slice(0, mentionStart + 1)}${mention.firstName
      }${mention.lastName} `;

    setCommentText(newCommentText);
    setIsMentioning(false);

    // Focus back to input after selecting a mention
    setTimeout(() => {
      commentInputRef.current.focus();
    }, 0);
  };

  const handleCloseModal = () => {
    setReportModal(false);
    setCurrentCommentItem(null);
  };

  const reporting = useSelector(
    ({ loading }) => loading.effects.feed.reportHighlights
  );

  const [shouldShowEmojiPanel, setShouldShowEmojiPanel] = useState({
    visible: false,
    highlightId: ""
  });

  const queryParams = new URLSearchParams(window.location.search);

  const type = queryParams.get("type");
  const [selected, setSelected] = useState(type);

  const announcementId = queryParams.get("announcementId");
  const navigatedFrom = queryParams.get("from") || "";
  const userId = queryParams.get("userId");

  const navigate = useNavigate();
  const emojiPanelRef = useRef(null);

  const commentInputRef = useRef(null);

  const { userInfo } = useSelector((state) => state.auth.authUser);

  const { allAnnouncements, activeAnnouncements } = useSelector(
    ({ announcement }) => ({
      allAnnouncements: announcement.announcements,
      activeAnnouncements: announcement.activeAnnouncements
    })
  );

  const gettingUsers = useSelector(
    ({ loading }) => loading.effects.user?.userSearch
  );

  // Fetch user list from redux state
  const { userList } = useSelector(({ user }) => ({
    userList: user?.usersByProjection || []
  }));

  const fetchAllUserData = async () => {
    await dispatch.user.userSearchByProjection();
  };

  const { loggedUserId, liveLikeProfileToken } = useSelector(
    ({ auth: { authUser }, user }) => ({
      loggedUserId: authUser?.userInfo?.id || "",
      liveLikeProfileToken: user?.data?.liveLikeProfileToken || "",
      authUser
    })
  );

  const togglePanel = () => {
    setIsPanelOpen(!isPanelOpen);
  };

  const handleButtonClick = (e) => {
    e.stopPropagation();
    togglePanel();
  };

  const onReactionClick = (type) => {
    setSelected(type);
  };

  const handleClickOutside = (event) => {
    if (
      emojiPanelRef.current &&
      !emojiPanelRef.current.contains(event.target)
    ) {
      setIsPanelOpen(false);
    }
  };

  // ==========================================================================//
  // HANDLES SCROLL VIEW AND SET LAST ITEM TO FETCH NEXT COMMENTS
  const ref = useRef();
  const { ref: inViewRef } = useInView({
    onChange: (inView) => {
      if (inView && shouldFetchMore) {
        fetchComments(shouldFetchMore);
      }
    }
  });

  // Use `useCallback` so we don't recreate the function on each render
  const setRefs = useCallback(
    (node) => {
      // Ref's from useRef needs to have the node assigned to `current`
      ref.current = node;
      // Callback refs, like the one from `useInView`, is a function that takes the node as an argument
      inViewRef(node);
    },
    [inViewRef]
  );
  // ==========================================================================//


  const getCurrentAnnoucement = () => {
    setAnnouncement(
      allAnnouncements.filter((item) => item.id === announcementId)[0]
    );
  };

  const sendCommentsCount = async (commentBoardId, count) => {
    if (commentBoardId) {
      if (announcementId) {
        try {
          const commentResponse = await LiveLike.getComments({
            commentBoardId
          });
          dispatch.announcement.updateAnnouncement({
            id: currentAnnouncement?.id,
            totalCommentCount: commentResponse.count
          });
        } catch (error) {
          console.log(error);
        }
      }
    }
  };

  const createCommentBoard = async () => {
    try {
      const commentBoard = await LiveLike.createCommentBoard({
        title: currentAnnouncement?.comment || currentAnnouncement?.id,
        customId: currentAnnouncement?.id || announcementId,
        repliesDepth: 2,
        allowComments: true,
        customData: `created by ${currentAnnouncement?.user?.id || currentAnnouncement?.title
          } on ${Date.now()}`
      });

      if (announcementId) {
        dispatch.announcement.updateAnnouncement({
          id: currentAnnouncement?.id || announcementId,
          commentBoardId: commentBoard.id,
          totalCommentCount: 0
        });
      }
      return commentBoard;
    } catch (error) {
      console.log(error);
    }
  };


  const fetchComments = async (cursor) => {

    if (loggedUserId) {
      setCommentsLoading(true);

      let commentBoardId = "";

      try {
        if (!currentAnnouncement?.commentBoardId) {
          const getCommentBoardDetails = await LiveLike.getCommentBoardDetails({
            customId: announcementId
          });


          if (getCommentBoardDetails.id && !currentAnnouncement?.commentBoardId) {
            commentBoardId = getCommentBoardDetails.id;
            if (announcementId) {
              dispatch.announcement.updateAnnouncement({
                id: announcementId,
                commentBoardId: getCommentBoardDetails.id
              });
            }
          }
        } else {
          commentBoardId = currentAnnouncement?.commentBoardId || "";
        }
      } catch (error) {
        setCommentsLoading(false);
        if (error === "Resource not found") {
          createCommentBoard();
        }
      }

      // Check if the comment board id has been created or fetched
      if (commentBoardId) {
        try {
          const getCommentPayload = {
            commentBoardId,
            sorting: CommentSort.NEWEST
          };

          let commentResponse;

          if (cursor && !cursor.done) {
            commentResponse = await Promise.resolve(cursor.next());
            setFetchedComments([
              ...fetchedComments,
              ...commentResponse?.value?.results
            ]);

            // Only reset the cursor if there are no more comments to fetch
            if (commentResponse.done) {
              setNextCusor(!commentResponse.done);
            }
          } else {
            commentResponse = await LiveLike.getComments(getCommentPayload);
            setFetchedComments(commentResponse.results);
            // Crazy thing is to save the whole response and call it again with the next
            setNextCusor(commentResponse);
          }

          setCommentsCount(commentResponse.count);
          setCommentsLoading(false);
        } catch (error) {
          console.log(error, "Error");

          console.log(error.message, "Error");
          setCommentsLoading(false);
        }
      }
    }
  };


  const parseAuthorData = (commentItem) => {
    let author;

    if (commentItem?.author?.custom_data) {
      try {
        author = JSON.parse(commentItem.author.custom_data);
      } catch (error) {
        console.error("Invalid JSON:", error);
        author = {}; // Fallback to an empty object if parsing fails. It returns null initially
      }
    } else {
      author = getAuthorDataFromCommentCustomData(
        commentItem.custom_data
      ); // Fallback to an empty object if custom_data is undefined
    }

    return author;
  }

  const openReportModal = (item) => {
    setCurrentCommentItem(item)
    setReportModal(true)
  }

  const handleReportSubmit = async () => {
    if (!currentCommentItem) return;

    const author = parseAuthorData(currentCommentItem)

    const data = {
      reporterUserId: loggedUserId,
      reportedUserId: author?.userId,
      reporterFullName: `${userInfo?.firstName} ${userInfo?.lastName}`,
      reportedFullName: `${author?.firstName} ${author?.lastName}`,
      reason: reportText,
      reportedContent: currentCommentItem?.text,
      reportedPhotoUrl:
        author?.photoUrl,
      reporterPhotoUrl:
        userInfo?.photoUrl,
      reportType: "COMMENT",
      contendId: currentCommentItem?.id,
      metadata: {
        announcementId
      }
    }
    if (reportText === "") {
      notifyError("A reason is required");
    } else {
      const res = await dispatch.feed.reportHighlights(data);

      if (res.status === 1) {
        handleCloseModal(true);
        setReportText("");
        setCurrentCommentItem(null)
      } else {
        notifyError("Something went wrong");
      }
    }
  };

  const addUserComment = async () => {
    if (!currentAnnouncement?.id) {
      return;
    }
    setSendLoading(true);

    const currentComments = [...fetchedComments];
    const currentCount = commentsCount || 0;

    let commentBoardId = "";

    try {
      if (!currentAnnouncement?.commentBoardId) {
        const getCommentBoardDetails = await LiveLike.getCommentBoardDetails({
          customId: currentAnnouncement?.id
        });
        if (getCommentBoardDetails.id) {
          commentBoardId = getCommentBoardDetails.id;
        }
      } else {
        commentBoardId = currentAnnouncement?.commentBoardId || "";
      }

      if (commentBoardId) {
        const addedComment = await LiveLike.addComment({
          text: commentText,
          customData: JSON.stringify({
            id: userInfo.id,
            firstName: userInfo.firstName,
            lastName: userInfo.lastName,
            photoUrl: userInfo.photoUrl || "/images/profile.png",
            teamName: userInfo?.teamName,
            clubName: userInfo?.clubName
          }),
          commentBoardId
        });

        if (addedComment) {
          Analytics.trackEvent({
            name: TRACKING_EVENTS.COMMENT_ADDED,
            metadata: {
              content_id: currentAnnouncement?.id,
              content_type: "announcement",
              comment_length: commentText?.length
            }
          })

          notifySuccess("Comment Added");
          setSendLoading(false);
          sendCommentsCount(commentBoardId, currentCount + 1);
          commentInputRef.current.value = "";
          setCommentText("");

          setFetchedComments((prev) => [addedComment, ...prev]);
          setCommentsCount((prev) => prev + 1);

        }
        // await fetchComments(false)
      }
    } catch (error) {
      setSendLoading(false);
      console.log(error);
      setFetchedComments(currentComments);
      setCommentsCount(currentCount);
      sendCommentsCount(commentBoardId, currentCount);
    }
  };

  const handleSearch = async (data) => {
    const splitName = data.split(" ");
    const filteredNames = splitName.filter((name) => name !== "");
    const checkThirdName = filteredNames[2]
      ? !filteredNames[2].includes("(")
      : false;
    const fullName = `${filteredNames[0]} ${filteredNames[1]}${checkThirdName ? ` ${filteredNames[2]}` : ""
      }`;

    const searchResult = await handleSearch(fullName);
    if (searchResult.length > 0) {
      handleOpenProfile(searchResult[0].id);
    } else {
      notifyError("User profile not found");
    }
  };

  const parseString = (data, purpose = "userName") => {
    const rawData = data.split("/");
    const output = purpose === "userName" ? rawData[0] : rawData[1];
    return output.replace(/\s/g, "");
  };

  const getAuthorDataFromCommentCustomData = (customData) => {
    try {
      // handle new case of the customData for comments
      return JSON.parse(customData);
    } catch (error) {
      // Handle old case of the customData for comments
      console.error("Invalid JSON:", error);
      const firstName = parseString(customData, "userName");
      const id = parseString(customData, "id");
      return { firstName, id };
    }
  };

  const handleOpenProfile = (id) => {
    if (id) {
      navigate({ pathname: "/profile", search: `?id=${id}` });
    } else {
      navigate("/login");
    }
  };

  useEffect(() => {
    getCurrentAnnoucement();
  }, [type]);

  useEffect(() => {
    if (currentAnnouncement) {
      fetchComments(false);
    }
  }, [currentAnnouncement]);

  useEffect(() => {
    if (!userList?.length) {
      fetchAllUserData();
    }
  }, []);

  useEffect(() => {
    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, []);

  const Avatar = ({ name, id }) => {
    // Get the first letter of the name or use a default placeholder
    const initial = name ? name.charAt(0).toUpperCase() : "?";

    return (
      <div
        onClick={() => handleOpenProfile(id)}
        className="bg-gray-300 text-white rounded-full flex items-center justify-center"
        style={{ width: "45px", height: "45px" }}
      >
        <span className="text-lg font-bold text-[#000000]">{initial}</span>
      </div>
    );
  };

  return (
    <div className="pb-[70px] px-0">
      <div className="flex justify-between w-full h-[7vh] px-2">
        <BackButton from={`/${navigatedFrom}`} />
      </div>

      <div>
        <Competition
          activeAnnouncements={[currentAnnouncement]}
          isDetailPage={true}
          liveLikeProfileToken={liveLikeProfileToken}
          loggedUserId={loggedUserId}
          reactionPacks={LiveLikeReactionPacks}
          onReactionClick={onReactionClick}
          setCurrentAnnouncement={setAnnouncement}
        />

        {currentAnnouncement && (
          <div key={currentAnnouncement?.id}>
            {currentAnnouncement?.announcementType === "VOTE" && (
              <div>
                {currentAnnouncement?.voteSubmittedAssets.map((item, index) => (
                  <div key={index} className="px-4 pb-2">
                    <div className="w-full">
                      <p
                        className="text-[#7582B2] text-[12px] font-[400] underline inline"
                        onClick={() => handleOpenProfile(item?.userId)}
                      >
                        {item?.orderIndex}. {item?.userName}
                      </p>
                      <span className="px-2" > - </span>
                      <p className="text-[#000000] text-[12px] font-[400] text-left inline">
                        {item?.text}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {currentAnnouncement?.announcementType === "WINNER" && (
              <div>
                {/* <p className="px-4">Special mentions to the SOTM runner ups:</p> */}
                {currentAnnouncement?.voteSubmittedAssets.map((item, index) => (
                  <div key={index} className="px-4 py-[1px]">
                    <div className="grid grid-cols-[45%_5%_50%] w-full">
                      <p
                        className="text-[#7582B2] text-[12px] font-[400] underline cursor-pointer"
                        onClick={() => handleOpenProfile(item?.userId)}
                      >
                        {item?.userName}
                      </p>
                      <span className="">-</span>
                      <p className="text-[#000000] text-[12px] font-[400] text-left">
                        {item?.text}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* <div className="w-full h-[2px] bg-[#A5A5A5] mt-10" /> */}

            {selected === "reactions" ? (
              <div className="flex flex-col mt-5 mb-10 mx-4">
                {currentAnnouncement?.reactedByUsers?.length > 0 &&
                  currentAnnouncement.reactedByUsers.map((user, idx) => (
                    <div key={idx}>
                      <ReactionListCard
                        reaction={user.others}
                        userId={user.userId}
                      />
                    </div>
                  ))}
              </div>
            ) : (
              <div className="flex flex-col mt-5 mb-10 mx-4">
                {fetchedComments.length > 0 &&
                  fetchedComments.map((item, idx) => {
                    // Check if custom_data exists and is a valid string before parsing
                    const parsedData = parseAuthorData(item)

                    const commentUserID = parsedData?.userId || parsedData?.id;

                    return (
                      <div
                        key={idx}
                        ref={
                          fetchedComments.length - 1 === idx ? setRefs : null
                        }
                        className={`${idx !== fetchedComments.length - 1 ? "border-b" : ""
                          } border-gray-300 pb-2`}
                      >
                        <div className="flex justify-between items-start mt-5">
                          <div className="flex space-x-5 ">
                            {item?.author?.custom_data ||
                              parsedData.photoUrl ? (
                              <img
                                src={
                                  parsedData?.photoUrl || "/images/profile.png"
                                }
                                alt=""
                                className="w-[45px] h-[45px] rounded-full border border-1 border-[#000000]"
                                onClick={() => {
                                  handleOpenProfile(commentUserID);
                                }}
                              />
                            ) : (
                              <Avatar
                                id={commentUserID}
                                name={parsedData?.firstName}
                              />
                            )}

                            <div>
                              <div className="flex space-x-6">
                                <p className="text-[#000000] text-[12px] font-[600]">
                                  {parsedData?.firstName}
                                </p>
                                <p className="text-[#808080] text-[12px] font-[600]">
                                  {momentGetTimeAgo(item.created_at)}
                                </p>
                              </div>

                              {(parsedData?.teamName ||
                                parsedData?.clubName) && (
                                  <p className="text-[#808080] text-[12px] font-[600]">
                                    {parsedData?.teamName || parsedData?.clubName}
                                  </p>
                                )}

                              <p className="text-[12px] text-[#000000] mt-1">
                                {item.text}
                              </p>
                            </div>
                          </div>

                          {loggedUserId && (
                            <div className="flex items-center">
                              <div className="dropdown dropdown-left dropdown-bottom">
                                <label
                                  tabIndex={1}
                                  className="flex items-center"
                                >
                                  <ThreeDotsIcon color={"#A5A5A5"} />
                                </label>
                                {userInfo?.id !== commentUserID && (
                                  <div
                                    tabIndex={1}
                                    className="dropdown-content card card-compact w-56 shadow-md bg-white text-primary-content"
                                  >
                                    <div className="p-4 text-black ">
                                      <div className="mt-[10px] flex flex-col gap-5 w-full">
                                        <div
                                          onClick={() => {
                                            openReportModal(item)
                                          }}
                                          className="text-red-600 flex gap-3 items-center"
                                        >
                                          Report comment
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                )}
                                {/* Modal for reporting comment */}
                                {reportModal && (
                                  <div className="fixed inset-0 bg-gray-800 bg-opacity-50 flex justify-center items-center">
                                    <div className="bg-white p-6 rounded shadow-lg w-[400px]">
                                      <h2 className="text-lg font-bold mb-4">
                                        Reason for reporting this comment
                                      </h2>
                                      <textarea
                                        className="w-full h-24 p-2 border border-gray-300 rounded mb-4 bg-[#ffffff]"
                                        placeholder="Enter your reason for reporting"
                                        value={reportText}
                                        onChange={(e) =>
                                          setReportText(e.target.value)
                                        }
                                      />
                                      <div className="flex justify-end gap-3">
                                        <button
                                          className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-500"
                                          onClick={() =>
                                            handleReportSubmit()
                                          }
                                        >
                                          {reporting ? "Submiting" : "Submit"}
                                        </button>
                                        <button
                                          className="px-4 py-2 bg-gray-300 rounded hover:bg-gray-400"
                                          onClick={handleCloseModal}
                                        >
                                          Close
                                        </button>
                                      </div>
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                          )}
                        </div>
                        <p className="text-center">
                          {commentsLoading ? "Fetching more comments" : ""}
                        </p>
                      </div>
                    );
                  })}
              </div>
            )}

            {loggedUserId ? (
              <div className="fixed bottom-0 bg-white backdrop-blur-3xl py-2 px-[15px] w-full -ml-2">
                <div className="relative w-full flex items-center justify-between">
                  <textarea
                    type="text"
                    name="comment"
                    ref={commentInputRef}
                    value={commentText}
                    onChange={handleInputChange} // Handle the input change for "@" detection
                    id="comment"
                    className="border-b-2 border-gray-600 block pt-3 pb-1 px-0 pr-[60px] w-full text-gray-900 bg-transparent appearance-none focus:outline-none focus:ring-0 focus:border-gray-600 peer"
                    placeholder={'comment here'}
                  // maxLength={500}
                  />
                  <div
                    onClick={() => addUserComment()}
                    className={`absolute w-[43.88px] h-[44.5px] mb-6 flex justify-center items-center right-2 ${sendLoading ? "" : "bg-[#14FF00]"
                      } rounded-xl`}
                  >
                    {sendLoading ? <Spinner /> : <Send />}
                  </div>
                </div>

                {/* Render the modal for mentions */}
                {isMentioning && (
                  <UserListCard
                    query={mentionQuery}
                    onSelect={handleMentionSelect}
                    closeModal={() => setIsMentioning(false)}
                    isMentioning={isMentioning}
                  />
                )}
              </div>
            ) : (
              <div className="w-full flex items-center justify-center">
                <a href="/login">Login to view and make comments</a>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};
export default AnnouncementPage;
