import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import BackButton from "../components/reusable/buttons/BackButton";
import BigBlack from "../components/reusable/buttons/BigBlack";
import Header from "../components/reusable/Header";
import { dispatch } from "../redux/store";

const ClubTeams = () => {
  const navigate = useNavigate();
  const { allTeams, clubData } = useSelector(
    ({ team: { allTeams, clubData } }) => ({
      allTeams,
      clubData,
    })
  );
  const [teams, setTeams] = useState([]);
  const [selectedTeam, setSelectedTeam] = useState("");
  const [selectedClub, setSelectedClub] = useState("");

  const getTeamsByClubId = async () => {
    const res = await dispatch.team.getTeamsByClubId(allTeams.currentClubId);
    if (res.status === 1) {
      setTeams(res.data);
    }
  };

  const handleSelectTeam = async (teamId, clubId) => {
    setSelectedTeam(teamId);
    setSelectedClub(clubId);
    await dispatch.team.setState({
      allTeams: { ...allTeams, currentTeamId: teamId, currentClubId: clubId },
    });
  };

  const handleNext = () => {
    navigate({
      pathname: "/team-dashboard",
      search: `?teamId=${selectedTeam}&clubId=${selectedClub}`,
    });
  };

  useEffect(() => {
    getTeamsByClubId();
  }, [allTeams]);

  const sortedTeams = teams?.sort((a, b) =>
    a.teamName.toLowerCase() < b.teamName.toLowerCase()
      ? -1
      : b.teamName.toLowerCase() > a.teamName.toLowerCase()
      ? 1
      : 0
  );
  return (
    <div className="page pb-[70px]">
      <Header bgColor={"#FFF"}>
        <BackButton from="" />
      </Header>
      <div className="flex gap-2 items-center">
        <img
          className="object-cover rounded-full w-[70px] h-[70px] border-[1px]"
          src={clubData?.clubLogoUrl}
          alt="logo"
        />
        <p className="uppercase text-[18px] font-favela-bold font-bold text-black">
          {clubData?.clubName}
        </p>
      </div>
      <div className="mt-[20px]">
        <p className="text-[14px] font-favela-bold font-bold text-black">
          Choose Your Team:
        </p>

        <ul className="mt-5 overflow-y-auto h-[60vh] bg-white w-full flex flex-col gap-5 rounded-xl">
          {sortedTeams?.map(({ id, teamName, clubId }) => (
            <li
              key={id}
              onClick={() => handleSelectTeam(id, clubId)}
              className="pb-1 border-b-[2px] border-gray-200 flex justify-between items-center"
            >
              <div className="w-full flex justify-between">
                <p>{teamName}</p>
                {selectedTeam === id ? (
                  <div className="border-2 bg-black rounded-full border-gray-900 w-6 h-6 flex flex-shrink-0 justify-center items-center mr-2 ">
                    <svg
                      className="w-4 h-4"
                      version="1.1"
                      viewBox="0 0 17 12"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g fill="none" fillRule="evenodd">
                        <g
                          transform="translate(-9 -11)"
                          fill="white"
                          fillRule="nonzero"
                        >
                          <path d="m25.576 11.414c0.56558 0.55188 0.56558 1.4439 0 1.9961l-9.404 9.176c-0.28213 0.27529-0.65247 0.41385-1.0228 0.41385-0.37034 0-0.74068-0.13855-1.0228-0.41385l-4.7019-4.588c-0.56584-0.55188-0.56584-1.4442 0-1.9961 0.56558-0.55214 1.4798-0.55214 2.0456 0l3.679 3.5899 8.3812-8.1779c0.56558-0.55214 1.4798-0.55214 2.0456 0z" />
                        </g>
                      </g>
                    </svg>
                  </div>
                ) : null}
              </div>
            </li>
          ))}
        </ul>
      </div>

      <div className="fixed bottom-[15px] right-0 left-0 cursor-pointer">
        <BigBlack label="Next" onClick={handleNext} />
      </div>
    </div>
  );
};

export default ClubTeams;
