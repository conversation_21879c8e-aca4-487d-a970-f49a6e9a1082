import React from "react";
import { useLocation, useNavigate } from "react-router-dom";
import BackButton from "../components/reusable/buttons/BackButton";
import { BigButtons } from "../components/reusable/buttons/Buttons";
import Header from "../components/reusable/Header";
import Logo from "../components/reusable/Logo";
import PageTitle from "../components/reusable/PageTitle";
import { notifyError, searchArrayOrMakeCallToAPI } from "../utils/helpers";
import Loader from "../components/reusable/loading/Loader";
import { useSelector } from "react-redux";
import { dispatch } from "../redux/store";

const MyTeamResultDetails = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const details = location.state.data;

  const gettingUser = useSelector(
    ({ loading }) => loading.effects.user.userSearch
  );
  const { userList, teamsList } = useSelector(({ user, team }) => ({
    userList: user.usersByProjection,
    teamsList: team.teamsByProjection,
  }));

  const handleOpenProfile = (id) => {
    if (id) {
      navigate({ pathname: "/profile", search: `?id=${id}` });
    } else {
      navigate("/login");
    }
  };

  const handleSearch = async (query) => {
    const searchResponse = searchArrayOrMakeCallToAPI({
      searchTerm: query,
      array: [
        ...userList?.map((item) => ({
          ...item,
          type: "user",
          fullname: `${item?.firstName} ${item?.lastName}`,
        })),
        ...teamsList?.map((item) => ({ ...item, type: "team" })),
      ],
      makeSearchCall: [dispatch.user.userSearch, dispatch.team.teamSearch],
    });

    return searchResponse || [];
  };

  const handleProfileOpen = async (data) => {
    const splitName = data.split(" ");
    const filteredNames = splitName.filter((name) => name !== "");
    const checkThirdName = filteredNames[2]
      ? !filteredNames[2].includes("(")
      : false;
    const fullName = `${filteredNames[0]} ${filteredNames[1]}${
      checkThirdName ? ` ${filteredNames[2]}` : ""
    }`;

    const searchResult = await handleSearch(fullName);
    if (searchResult[0].id) {
      handleOpenProfile(searchResult[0].id);
    } else {
      notifyError("User profile not found");
    }
  };

  return gettingUser ? (
    <Loader />
  ) : (
    <div className="page">
      <Header>
        <BackButton from="" />
        <Logo />
        <div></div>
      </Header>
      <PageTitle>
        <div>RESULTS</div>
      </PageTitle>
      <div className="flex justify-center mb-4 gap-4 items-center mt-4">
        <div className="w-5/12">{details?.homeTeam?.data?.clubName}</div>
        <div className="flex w-2/12 gap-1 flex-nowrap font-favela-bold">
          <div>{details?.homeTeam?.score}</div>
          <div>&#8211;</div>
          <div>{details?.awayTeam?.score}</div>
        </div>
        <div className="w-5/12">{details?.awayTeam?.data?.clubName}</div>
      </div>
      <div className="mt-5 flex justify-between">
        <div>
          {details?.homeTeam?.goalScorers?.map((scorer, key) => (
            <div
              onClick={() => handleProfileOpen(scorer)}
              className="border-b text-sm text-gray-600 w-max mb-2 border-gray-300"
              key={key}
            >
              {scorer}
            </div>
          ))}
        </div>
        <div>
          {details?.awayTeam?.goalScorers?.map((scorer, key) => (
            <div
              onClick={() => handleProfileOpen(scorer)}
              className="border-b w-max mb-2 text-sm text-gray-600 border-gray-300"
              key={key}
            >
              {scorer}
            </div>
          ))}
        </div>
      </div>
      <div className="mt-5 text-center mb-2">Assists</div>
      <div className="flex justify-between">
        <div>
          {details?.homeTeam?.assists?.map((assist, key) => (
            <div
              onClick={() => handleProfileOpen(assist)}
              className="border-b text-sm text-gray-600 w-max mb-2 border-gray-300"
              key={key}
            >
              {assist}
            </div>
          ))}
        </div>
        <div>
          {details?.awayTeam?.assists?.map((assist, key) => (
            <div
              onClick={() => handleProfileOpen(assist)}
              className="border-b text-sm text-gray-600 w-max mb-2 border-gray-300"
              key={key}
            >
              {assist}
            </div>
          ))}
        </div>
      </div>

      <div className="mt-5 text-center mb-2">Player of the match</div>
      <div className="border-b text-sm text-gray-600 w-max mb-2 border-gray-300">
        {details?.homeTeam?.playersOfTheMatch?.map((player, key) => (
          <p key={key}>{player}</p>
        ))}
        {details?.awayTeam?.playersOfTheMatch?.map((player, key) => (
          <p onClick={() => handleProfileOpen(player)} key={key}>{player}</p>
        ))}
      </div>
      <div className="flex flex-col gap-5 my-7">
        <BigButtons
          onClick={() => navigate("/team-dashboard/result-details/highlights")}
          green
          label="HIGHLIGHTS"
        />
        <BigButtons
          onClick={() =>
            navigate("/team-dashboard/result-details/lineups", {
              state: {
                details,
              },
            })
          }
          black
          label="LINEUPS"
        />
      </div>
    </div>
  );
};

export default MyTeamResultDetails;
