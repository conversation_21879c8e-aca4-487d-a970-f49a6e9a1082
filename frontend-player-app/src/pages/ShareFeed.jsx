import _debounce from "lodash/debounce";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { useSelector } from "react-redux";
import { useLocation } from "react-router-dom";
import ShareModalForFeed from "../components/pageComponents/ShareFeed/ShareModalForFeed";
import BackButton from "../components/reusable/buttons/BackButton";
import { BigButtons } from "../components/reusable/buttons/Buttons";
import Header from "../components/reusable/Header";
import PageTitle from "../components/reusable/PageTitle";
import SearchCardForShare from "../components/reusable/SearchCardForShare";
import Spinner from "../components/reusable/spinner/Spinner";
import { SearchIcon2 } from "../components/svgIcons/SearchIcon";
import { dispatch } from "../redux/store";
import { Chat } from "../services";
import { TRACKING_EVENTS } from "../utils/constants";
import Analytics from "../utils/google-analytics";
import {
  notifyError,
  notifySuccess,
  searchArrayOrMakeCallToAPI,
} from "../utils/helpers";

const ShareFeed = ({ setUserId }) => {
  const [searchResult, setSearchResult] = useState(null);
  const [user, setUser] = useState(null);
  const [selectedItems, setSelectedItems] = useState("");
  const [sending, setSending] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  const handleItemSelect = (itemId) => {
    setSelectedItems((prevItems) => {
      if (prevItems.includes(itemId)) {
        return prevItems.filter((id) => id !== itemId);
      } else {
        return [...prevItems, itemId];
      }
    });
  };

  const location = useLocation();

  const typeOfContent = location.state?.type;
  const profile = location.state?.profile;
  const highlight = location?.state?.highlight;

  const shareId = useMemo(() => {
    switch (typeOfContent) {
      case "announcement":
      case "highlight":
        return highlight.id;
      case "profile":
        return profile.id;

      default:
        break;
    }
  }, [typeOfContent]);

  const userInfo = useSelector((state) => state.user.data);

  const gettingUser = useSelector(
    ({ loading }) => loading.effects.user.userSearch
  );
  const userList = useSelector((state) => state.user.usersByProjection);

  const handleSearch = async (query) => {
    const results = await searchArrayOrMakeCallToAPI({
      searchTerm: query,
      array: userList,
      makeSearchCall: [dispatch.user.userSearch],
    });

    const sortResponse =
      results.filter((record) => {
        return record.id !== userInfo.id;
      }) ?? [];

    const searchResponse = sortResponse?.length > 0 ? sortResponse : null;
    setSearchResult(searchResponse);

    Analytics.trackEvent({
      name: TRACKING_EVENTS.SEARCH_PERFORMED,
      metadata: {
        search_term: query,
        result_count: searchResponse?.length ?? 0,
      },
    });
  };

  const fetchSearchData = useCallback(_debounce(handleSearch, 400), []);

  const fetchAllUserData = async () => {
    const res = await dispatch.user.userSearchByProjection();
  };

  const highlightText = `Check out this post on PLAYER. \n \n`;

  const highlightUrl = `${`${window.location.origin}/user/comment?highlightId=${highlight?.id}&userId=${highlight?.userId}`}`;

  const announcementText = `Check out this ${
    highlight?.announcementType === "COMPETITION" ? "competition" : "post"
  } on PLAYER: \n \n`;
  const announcementUrl = `${window.location.origin}/user/announcement?announcementId=${highlight?.id}`;

  const profileText = location?.state?.title;
  const profileUrl = location?.state?.url;

  const getShareContent = () => {
    switch (typeOfContent) {
      case "announcement":
        return { url: announcementUrl, title: announcementText };
      case "highlight":
        return { url: highlightUrl, title: highlightText };
      case "profile":
        return { url: profileUrl, title: profileText };

      default:
        break;
    }
  };

  const handleSend = async () => {
    setSending(true);
    try {
      await Chat.share({
        recipientIds: selectedItems,
        type: typeOfContent,
        id: shareId,
      });

      Analytics.trackEvent({
        name: TRACKING_EVENTS.CONTENT_SHARED,
        metadata: {
          share_method: "message",
          content_id: highlight?.id ?? profile?.id,
          content_type: typeOfContent,
        },
      });
      notifySuccess("Shared!");
    } catch (error) {
      console.log(error);
      notifyError("Error occurred:", error);
    }
    setSending(false);
  };

  useEffect(() => {
    if (!Boolean(userList?.length)) {
      fetchAllUserData();
    }
  }, []);

  useEffect(() => {
    if (user) {
      setSearchResult(null);
    }
  }, [user]);

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop =
        window.pageYOffset || document.documentElement.scrollTop;
      const isTop = scrollTop === 0;
      setIsScrolled(!isTop);
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return (
    <div className="w-full h-full page dark:bg-white">
      <Header>
        <div className="flex items-center justify-between w-full mt-10 gap-7">
          <BackButton from="" />
          <PageTitle>
            <div className="relative z-0 w-full">
              <input
                onChange={(e) => fetchSearchData(e.target.value)}
                type="text"
                name="text"
                id="text"
                className={`font-poppins border-b-[1px] border-gray-600 block py-3 pl-8 w-full text-sm text-gray-900 bg-transparent appearance-none dark:border-gray-600 dark:focus:border-black-800 focus:outline-none focus:ring-0`}
                placeholder="Type a name…"
              />
              <div className="absolute scale-[0.85] top-1/2 -left-1 transform -translate-y-1/2">
                <SearchIcon2 />
              </div>
            </div>
          </PageTitle>
        </div>
      </Header>
      <div>
        <div className="flex items-center mt-8 mb-3 gap-x-3"></div>
      </div>
      <div className="bg-white ">
        {!gettingUser && searchResult
          ? searchResult?.map((user) => (
              <SearchCardForShare
                key={user.id}
                user={user}
                setUserId={setUserId}
                setUser={setUser}
                handleItemSelect={handleItemSelect}
                selectedItems={selectedItems}
              />
            ))
          : ""}
      </div>
      {gettingUser ? <Spinner /> : ""}
      {selectedItems.length > 0 && (
        <div className="w-[90vw] fixed bottom-5 py-3 mx-auto bg-transparent ">
          <BigButtons
            isLoading={sending}
            onClick={handleSend}
            green
            label="SEND"
          />
        </div>
      )}
      {selectedItems.length > 0 || isScrolled ? (
        ""
      ) : (
        <ShareModalForFeed
          title={getShareContent(typeOfContent).title}
          url={getShareContent(typeOfContent).url}
        />
      )}
    </div>
  );
};

export default ShareFeed;
