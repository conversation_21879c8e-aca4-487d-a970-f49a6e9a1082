import { useSelector } from "react-redux";
import ReferenceForm from "../components/pageComponents/Reference/ReferenceForm";
import Header from "../components/reusable/Header";
import BackButton from "../components/reusable/buttons/BackButton";
import { useNavigate, useParams } from "react-router-dom";
import { useEffect, useState } from "react";
import { UserApi } from "../services";

const ReferencesFormPage = () => {
  const [toUserDetails, seTtoUserDetails] = useState(null);

  const { userInfo } = useSelector((state) => state.auth.authUser);
  
  const navigate = useNavigate();
  const { toUserId } = useParams(); 

  const isSameUser = userInfo.id === toUserId;

  const gettoUserinfo = async () => {
    try {
      const {
        data: { data }
      } = await UserApi.getGuestUserProfile(toUserId);
      seTtoUserDetails(data);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    gettoUserinfo();
  }, []);

  return (
    <div className="page">
      <Header>
        <BackButton onClick={() => navigate(-1)} />
        <div />
      </Header>
      {!isSameUser && <div>
        <h4 className="text-[16px] text-black font-poppins font-bold">
          Write reference to {toUserDetails?.firstName}
        </h4>
        <p className="text-[12px] text-black text-opacity-50">
          This reference will appear on {toUserDetails?.firstName}'s Profile
        </p>
        <div className="flex items-center my-5">
          <img
            src={toUserDetails?.photoUrl || '/images/profile.png'}
            className="w-[42px] h-[42px] rounded-full mr-5"
          />
          <h3 className="text-black font-poppins font-bold text-[14px]">
          {toUserDetails?.firstName} {toUserDetails?.lastName}
          </h3>
        </div>
      </div>}
      <div className="flex flex-col justify-between h-[60vh] ">
        <ReferenceForm isSameUser={isSameUser} toUserDetails={isSameUser ? null : toUserDetails} />
      </div>
    </div>
  );
};

export default ReferencesFormPage;
