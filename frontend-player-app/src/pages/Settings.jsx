import { useNavigate } from "react-router-dom";
import BackButton from "../components/reusable/buttons/BackButton";
import Header from "../components/reusable/Header";
import PageTitle from "../components/reusable/PageTitle";
import logout from "../assets/log-out.png";
import { dispatch } from "../redux/store";
import BottomNavigation from "../components/reusable/BottomNav";

const Settings = () => {
  const navigate = useNavigate();
  const settings = [
    {
      title: "ACCOUNT SETTINGS",
      linkPath: "/settings/account"
    },
    {
      title: "PRIVACY AND SECURITY",
      linkPath: "/settings/account/privacy-and-security"
    }
  ];

  async function handleLogout() {
    await dispatch.auth.logout();
    navigate("/");
  }
  return (
    <div className="page md:max-w-[700px] mx-auto">
      <Header>
        <BackButton from="" />
      </Header>
      <PageTitle>
        <div className="text-[20px] uppercase">Settings</div>
      </PageTitle>
      <div className="mt-[65px]">
        {settings.map(({ linkPath, title }, i) => (
          <div
            onClick={() => navigate(linkPath)}
            key={i}
            className="flex justify-between items-center py-[20px] cursor-pointer"
          >
            <p className="uppercase text-[15px] font-favela-bold font-bold">
              {title}
            </p>
            <img src="/images/arrowRight.svg" alt="" />
          </div>
        ))}
      </div>
      <div className="absolute bottom-10 left-0 text-center w-full">
        <p onClick={handleLogout} className="font-favela mt-5 underline">Log out</p>
      </div>
    </div>
  );
};

export default Settings;
