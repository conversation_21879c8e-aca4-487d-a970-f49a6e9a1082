import React, { useEffect, useState } from "react";
import { Feeds } from "../components/pageComponents/Home";
import BottomNavigation from "../components/reusable/BottomNav";
// import BigBlack from "../components/reusable/buttons/BigBlack";
// import Header from "../components/reusable/Header";
import Logo from "../components/reusable/Logo";
// import PageTitle from "../components/reusable/PageTitle";
import { useSelector } from "react-redux";
import { Link } from "react-router-dom";
import Applestore from "../assets/appstoreSmall_.png";
import Playstore from "../assets/googleplaySmall_.png";
import UnreadMessageCount from "../components/reusable/UnreadMessageCount";
import UploadViaAppBanner from "../components/reusable/UploadViaAppBanner";
import MessageBtn from "../components/svgIcons/MessageBtn";
import { dispatch } from "../redux/store";
import { MESSAGE_REFRESH_INTERVAL } from "./MessageList";

function App() {
  const [show, setShow] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);
  const [openAppBanner, setOpenAppBanner] = useState(false);
  const { userInfo: userDetails } = useSelector((state) => state.auth.authUser);
  const unreadMessageCount = useSelector(
    (state) => state.chat.unreadMessageCount
  );

  const controlNavbar = () => {
    if (window.scrollY > lastScrollY + 1) {
      // If scrolling down, hide the header
      setShow(false);
    } else {
      // If scrolling up, show the header
      setShow(true);
    }
    // Remember current scroll position for the next move
    setLastScrollY(window.scrollY);
  };

  const handleStartUpload = () => {
    // if (currentEnv === "dev") {
    //   navigate("/upload-feed");
    // } else {
    // Trigger Share Component
    setOpenAppBanner(true);
    // }
  };

  const fetchHighlights = async () => {
    await dispatch.feed.fetchHighlights();
  };

  const fetchAnnouncements = async () => {
    await dispatch.announcement.getAllAnnouncements();
  };

  const fetchUnreadMessageCount = async () => {
    await dispatch.chat.getUnreadMessageCount();
  };

  useEffect(() => {
    fetchAnnouncements();
    fetchHighlights();
  }, []);

  useEffect(() => {
    if (!userDetails?.id) return;

    fetchUnreadMessageCount();

    const intervalId = setInterval(() => {
      fetchUnreadMessageCount();
    }, MESSAGE_REFRESH_INTERVAL);

    return () => {
      clearInterval(intervalId);
    };
  }, [userDetails?.id]);

  useEffect(() => {
    window.addEventListener("scroll", controlNavbar);

    // Cleanup function
    return () => {
      window.removeEventListener("scroll", controlNavbar);
    };
  }, [lastScrollY]);

  return (
    <div className="pb-[70px]">
      <div className="">
        <div
          className={`px-[12px] grid grid-cols-4 h-[80px] w-full items-center fixed top-0 bg-white z-50`}
        >
          <div
            className="flex items-center justify-center col-span-2 col-start-2"
            onClick={() => scrollTo({ top: 0, behavior: "smooth" })}
          >
            <Logo light={false} />
          </div>
          {userDetails.id ? (
            <Link to="/user/message-list" className="relative justify-self-end">
              {unreadMessageCount > 0 && (
                <div className="absolute -top-2 -right-2">
                  <UnreadMessageCount count={unreadMessageCount} />
                </div>
              )}
              <MessageBtn />
            </Link>
          ) : null}
        </div>

        <div className="flex justify-center items-center w-full h-[80px] bg-[#52FF00] mt-20">
          <div
            onClick={() =>
              window.open(
                "https://apps.apple.com/gb/app/player-the-footballers-app/id6503473876",
                "_blank"
              )
            }
            className="flex items-center mx-1 mt-3 cursor-pointer"
          >
            <img src={Applestore} alt="apple logo" className="w-[153px]" />
          </div>
          <div
            onClick={() =>
              window.open(
                "https://play.google.com/store/apps/details?id=com.playerapp.playerapp&pcampaignid=web_share",
                "_blank"
              )
            }
            className="flex items-center mx-1 mt-3 cursor-pointer"
          >
            <img src={Playstore} alt="google logo" className="w-[153px]" />
          </div>
        </div>
      </div>
      <div className="mt-[-20px]">
        <Feeds />
      </div>
      <div
        className={`sticky flex items-center w-16 h-16 mx-auto rounded-full bottom-[80px] cursor-pointer`}
      >
        <span
          onClick={handleStartUpload}
          className="flex items-center justify-center mx-auto text-5xl font-medium text-black uppercase rounded-full bg-primary-green w-14 h-14"
        >
          +
        </span>
      </div>
      <div>
        <BottomNavigation />
      </div>
      {openAppBanner && (
        <UploadViaAppBanner onClose={() => setOpenAppBanner(false)} />
      )}
    </div>
  );
}

export default App;
