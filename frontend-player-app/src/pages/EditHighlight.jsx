import React from "react";
import BackButton from "../components/reusable/buttons/BackButton";
import BigGreen from "../components/reusable/buttons/BigGreen";
import Header from "../components/reusable/Header";

const EditHighlight = () => {
  return (
    <div className="mt-7 page">
      <Header>
        <BackButton from="" />
        {/* DELETE MODAL BUTTON */}
        <label htmlFor="delete-modal" className="">
          <img  src="/images/claritytrashline.svg" alt="delete" />
        </label>
      </Header>
      <div className="w-full rounded-[32px] mt-6 overflow-hidden aspect-[4/5]">
        <img 
          src={
            "https://images.unsplash.com/photo-1517466787929-bc90951d0974?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8Zm9vdGJhbGxlcnxlbnwwfHwwfHw%3D&auto=format&fit=crop&w=500&q=60"
          }
          alt="feed"
          className="w-full h-full object-cover"
        />
      </div>
      <div className="flex items-center border-b border-black py-1 mt-[20px]">
        <input
          className="appearance-none bg-transparent border-none w-full text-gray-700 mr-3 py-1 leading-tight focus:outline-none"
          type="select"
          placeholder="Enter Capture"
          aria-label="cature"
        />
      </div>
      <div onClick={() => {}} className="mt-20 cursor-pointer">
        <BigGreen label="Publish" />
      </div>

      {/* DELETE MODAL */}
      <input type="checkbox" id="delete-modal" className="modal-toggle" />
      <label htmlFor="" className="modal cursor-pointer">
        <div className="modal-box relative" htmlFor="">
          <h3 className="text-[22px] text-center font-normal">
            Delete Highlight?
          </h3>

          <div className="flex justify-around items-center mt-[20px]">
            <div className="w-[135px] h-[44px] text-[22px] font-normal rounded-[50px] text-white flex justify-center items-center bg-black">
              Delete
            </div>
            <label htmlFor="delete-modal">
              <div className="w-[135px] h-[44px] text-[22px] font-normal rounded-[50px] flex justify-center items-center">
                Close
              </div>
            </label>
          </div>
        </div>
      </label>
    </div>
  );
};

export default EditHighlight;
