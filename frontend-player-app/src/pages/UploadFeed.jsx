import React, { useEffect, useRef, useState } from "react";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import BackButton from "../components/reusable/buttons/BackButton";
import { BigButtons } from "../components/reusable/buttons/Buttons";
import Spinner from "../components/reusable/spinner/Spinner";
import { dispatch } from "../redux/store";
import S3BucketManger from "../utils/s3.config";
import * as Sentry from "@sentry/react";
import { getUploadColor, isSafariFn, notifyError } from "../utils/helpers";

const UploadFeed = () => {
  const navigate = useNavigate();
  const [uploaded, setUploaded] = useState(false);
  const [s3Loading, setS3Loading] = useState(false);
  const [uploadProgress, setProgress] = useState(0);
  const [url, setUrl] = useState("");
  const [streamUrlObject, setStreamUrlObject] = useState(null);
  const [uploadType, setUploadType] = useState("");
  const [comment, setComment] = useState("");
  const [disabledVideoUpload, setDisabledVideoUpload] = useState(false);
  const [file, setFile] = useState({ src: "", preview: "" });
  const [currentFile, setCurrentFile] = useState("");
  const [videoDuration, setVideoDuration] = useState(0);
  const [videoWidth, setVideoWidth] = useState(0);
  const [videoHeight, setVideoHeight] = useState(0);
  const [videoOrientation, setVideoOrientation] = useState("");
  const [commentError, setCommentError] = useState("");
  const [currentFileUrl, setCurrentObjectUrl] = useState("");

  const hiddenUploader = useRef();

  const { userInfo } = useSelector((state) => state.auth.authUser);

  const uploading = useSelector(
    ({ loading }) => loading.effects.feed.createHighlights
  );

  /**
   * Hanles the file change from the input file
   * @param {*} e
   */
  const handleFileChange = (e) => {
    e.preventDefault();
    const fileSelected = e.target.files[0];

    setCurrentFile(fileSelected);
  };

  const handleUploadClick = () => {
    hiddenUploader.current.click();
  };

  /**
   * Handles the upload of the file to s3 bucket
   * @returns {Promise}
   */
  const handleUpload = async () => {
    const s3BucketManger = new S3BucketManger();

    if (disabledVideoUpload || !file.src) {
      notifyError(
        "Please upload another video/image by clicking on any space below"
      );
      return;
    }
    try {
      setS3Loading(true);
      let uploadedDetails;
      if (uploadType === "VIDEO") {
        setProgress("uploading");
        uploadedDetails = await s3BucketManger.uploadAssets(
          file.src,
          "",
          `${Date.now()}-${file.src.name}`,
          "video",
          { width: videoWidth, height: videoHeight, duration: videoDuration },
          (progress) => {
            setProgress(progress);
          }
        );
      } else {
        uploadedDetails = await s3BucketManger.uploadAssets(
          file.src,
          "",
          "",
          "photo"
        );
      }
      setUrl(uploadedDetails.assetUrl);
      setStreamUrlObject({
        ...uploadedDetails.streamUrls,
        duration: videoDuration,
        orientation: videoOrientation
      });

      URL.revokeObjectURL(currentFile);

      setS3Loading(false);
      setUploaded(true);
    } catch (error) {
      console.log(error);
      setS3Loading(false);
      Sentry.captureException(error);
      notifyError(error.message || "Upload Failed");
    }
  };

  /**
   * Function to handle video upload for setting the meta of video
   */
  const handleVideoUpload = async () => {
    if (currentFileUrl) {
      URL.revokeObjectURL(currentFileUrl);
      setCurrentObjectUrl("");
    }

    const objectUrl = URL.createObjectURL(currentFile);
    setCurrentObjectUrl(objectUrl);

    const videoLoader = document.createElement("video");
    videoLoader.src = objectUrl;

    videoLoader.onloadedmetadata = () => {
      if (videoLoader?.duration > 180) {
        notifyError("Video cannot be more than 3 mins");
        setDisabledVideoUpload(true);
        return;
      }
      setVideoDuration(videoLoader.duration);
      setVideoWidth(videoLoader.videoWidth);
      setVideoHeight(videoLoader.videoHeight);
      setVideoOrientation(
        videoLoader.videoWidth > videoLoader.videoHeight
          ? "landscape"
          : "portrait"
      );
      setDisabledVideoUpload(false);
    };

    const previewSafari = (
      <img
        className="object-cover h-[400px] w-[350px] mx-auto"
        width={400}
        height={350}
        src={objectUrl}
      />
    );
    const previewOthers = (
      <video
        className="object-cover h-[400px] w-[350px] mx-auto"
        width={400}
        height={350}
        controls
        src={objectUrl}
      />
    );

    setUploadType("VIDEO");
    setFile({
      src: currentFile,
      preview: isSafariFn() ? previewSafari : previewOthers
    });
  };

  /**
   * Handle the submit of the feed
   */
  const handleSubmit = async () => {
    if (comment.length < 1) {
      setCommentError("Please add a Comment");
    } else {
      await dispatch.feed.createHighlights({
        userId: userInfo.id,
        type: uploadType,
        url: url,
        streamUrl: streamUrlObject,
        comment: comment,
        queueProcessed: false
      });
      navigate("/");
    }
  };

  // Validate if there is a user or not
  useEffect(() => {
    if (!userInfo.id) {
      navigate("/");
    }
  }, []);

  // Handle the files changes to determine if it is an image or video
  useEffect(() => {
    const imageReg = /[\/.](gif|jpg|jpeg|tiff|png|webp)$/i;
    const ivideoReg = /[\/.](mp4|mov|wmv|avi|mkv|mpeg|webm|flv|quicktime)$/i;
    setFile({ src: "", preview: "" });

    if (ivideoReg.test(currentFile?.type)) {
      handleVideoUpload();
      return;
    }

    if (imageReg.test(currentFile?.type)) {
      setDisabledVideoUpload(false);
      const preview = (
        <img
          src={URL.createObjectURL(currentFile)}
          alt="feed"
          className="w-full max-h-[520px] object-cover rounded-[32px] "
        />
      );
      setFile({ src: currentFile, preview });
      setUploadType("PHOTO");
      return;
    }

    if (
      currentFile &&
      (!ivideoReg.test(currentFile?.type) || !imageReg.test(currentFile?.type))
    ) {
      URL.revokeObjectURL(currentFile);
      notifyError("Invalid file provided");
    }
  }, [currentFile]);

  return (
    <div className="mt-7 page">
      <BackButton from="" />
      {uploaded ? (
        <>
          <div className="w-full flex items-center rounded-[32px] mt-6 overflow-hidden aspect-[4/5]">
            {uploadType === "PHOTO" ? (
              <img
                src={url}
                alt="feed"
                className="w-full h-full object-cover"
              />
            ) : (
              <video
                width="400"
                height="350"
                controls
                // poster="https://media.geeksforgeeks.org/wp-content/cdn-uploads/20190710102234/download3.png"
              >
                <source src={`${url}#t=0.001`} type="video/mp4" />
              </video>
            )}
          </div>
          <div className="flex flex-cols items-center border-b border-black py-1 mt-[20px]">
            <input
              onChange={(e) => setComment(e.target.value)}
              className="appearance-none bg-transparent border-none w-full text-gray-700 mr-3 py-1 leading-tight focus:outline-none"
              type="select"
              placeholder="Enter Caption"
              aria-label="cature"
            />
          </div>
          <p className="absolute flex mt-4 justify-center text-red-600 text-xs">
            {commentError ? commentError : ""}
          </p>
          <BigButtons
            isLoading={uploading}
            onClick={() => handleSubmit()}
            className="mt-20"
            green
            label="Publish"
          />
        </>
      ) : (
        <>
          <div
            onClick={handleUploadClick}
            className="w-full h-[500px] mt-[20px] bg-[#B5B5B51A] rounded-[32px] gap-4 cursor-pointer flex justify-center items-center flex-col aspect-[4/5]"
          >
            {s3Loading ? (
              <Spinner waitingText={`${uploadProgress}... \n please wait`} />
            ) : (
              <>
                {!file.src ? (
                  <>
                    <img src="/images/uploadIcon.svg" alt="upload" />
                    <p>Upload your hightlights</p>
                    <p>3 mins max</p>
                    <p>(3:4 for best fit)</p>
                  </>
                ) : (
                  <div className="w-full">{file.preview}</div>
                )}
              </>
            )}
          </div>
          <input
            ref={hiddenUploader}
            accept="video/*,image/*"
            type="file"
            multiple={false}
            style={{ display: "none" }}
            disabled={s3Loading}
            onChange={handleFileChange}
          />
          {currentFile && (
            <BigButtons
              onClick={() => handleUpload()}
              className={`mt-20 cursor-pointer sticky bottom-[10px] ${
                s3Loading ? "opacity-50" : ""
              }`}
              green
              isDisabled={s3Loading || disabledVideoUpload || !file.src}
              label={`Upload ${uploadType}`}
            />
          )}
        </>
      )}
      <div className="flex justify-center items-center mt-10">
        {Boolean(uploadProgress) && <span className={`text-2xl text-center font-bold ${getUploadColor(uploadProgress)}`}>{uploadProgress}%</span>}
      </div>
      <p className="text-center text-gray-400 mt-10">
        upload is only available on dev
      </p>
      <p className="text-center text-gray-400 mt-2">
        On prod, only mobile app allows upload
      </p>
    </div>
  );
};

export default UploadFeed;
