import _debounce from "lodash/debounce";
import React, { useCallback, useEffect, useState } from "react";
import { useSelector } from "react-redux";
import BottomNavigation from "../components/reusable/BottomNav";
import BackButton from "../components/reusable/buttons/BackButton";
import Header from "../components/reusable/Header";
import { Loader } from "../components/reusable/loading";
import PageTitle from "../components/reusable/PageTitle";
import SearchCardForMessage from "../components/reusable/SearchCardForMessage";
import Spinner from "../components/reusable/spinner/Spinner";
import Close from "../components/svgIcons/Close";
import { dispatch } from "../redux/store";
import { searchArrayOrMakeCallToAPI } from "../utils/helpers";

const NewMessage = ({ setUserId }) => {
  const [searchResult, setSearchResult] = useState(null);
  const [user, setUser] = useState(null);

  const userInfo = useSelector((state) => state.user.data);

  const loadingUser = useSelector(
    ({ loading }) => loading.effects.user.fetchUserDetails
  );

  const gettingUser = useSelector(
    ({ loading }) => loading.effects.user.userSearch
  );
  const userList = useSelector((state) => state.user.usersByProjection);

  const handleSearch = async (query) => {
    const searchResposne = await searchArrayOrMakeCallToAPI({
      searchTerm: query,
      array: userList,
      makeSearchCall: [dispatch.user.userSearch],
    });

    const sortResponse = searchResposne.filter((record) => {
      return record.id !== userInfo.id;
    });

    const searchResponse = sortResponse?.length > 0 ? sortResponse : null;
    setSearchResult(searchResponse);
  };

  const fetchSearchData = useCallback(_debounce(handleSearch, 400), []);

  const fetchAllUserData = async () => {
    const res = await dispatch.user.userSearchByProjection();
  };

  useEffect(() => {
    if (!Boolean(userList?.length)) {
      fetchAllUserData();
    }
  }, []);

  useEffect(() => {
    if (user) {
      setSearchResult(null);
    }
  }, [user]);

  return loadingUser ? (
    <Loader />
  ) : (
    <div className="w-full h-screen page">
      <Header>
        <div className="flex items-center justify-between w-full mt-10 gap-7">
          <BackButton from="" />
          <PageTitle>
            <div className="uppercase">Send A Message</div>
          </PageTitle>
        </div>
      </Header>
      <div>
        <div className="flex items-center mt-8 mb-3 gap-x-3">
          To:{" "}
          <div className="flex items-center gap-3 font-bold">
            <div>{user ? user.firstName : ""}</div>
            {user && (
              <div
                className="p-3"
                onClick={() => {
                  setUser(null), setUserId("");
                }}
              >
                <Close />
              </div>
            )}
          </div>
        </div>
        <input
          onChange={(e) => fetchSearchData(e.target.value)}
          type="text"
          className={`border-b-[2px] mb-7 border-gray-500 block py-3 w-full text-sm text-gray-900 bg-transparent appearance-none dark:border-gray-600 dark:focus:border-black-800 focus:outline-none focus:ring-0`}
          placeholder="Type name..."
        />
      </div>
      {!gettingUser && searchResult
        ? searchResult?.map((user) => (
            <SearchCardForMessage
              key={user.id}
              user={user}
              setUserId={setUserId}
              setUser={setUser}
            />
          ))
        : ""}
      {gettingUser ? <Spinner /> : ""}
      <BottomNavigation />
    </div>
  );
};

export default NewMessage;
