import React, { useState, useRef, useEffect, useCallback } from "react";
import { useSelector } from "react-redux";
import _debounce from "lodash/debounce";
import { dispatch } from "../redux/store";

import S3BucketManger from "../utils/s3.config";
import BackButton from "../components/reusable/buttons/BackButton";
import Header from "../components/reusable/Header";
import PageTitle from "../components/reusable/PageTitle";
import EditProfileForm from "../components/pageComponents/EditProfile/EditProfileForm";
import { notifyError } from "../utils/helpers";

const EditProfile = () => {
  const [fileUplod, setUploading] = useState({ uploading: false, file: null });
  const [uploadingBanner, setUploadingBanner] = useState(false);
  const bannerRef = useRef(null);
  

  const { userInfo } = useSelector((state) => state.auth.authUser);

  const hiddenUploader = useRef();

  const handleUpload = async (event) => {
    setUploading({ uploading: true, file: event.target.files[0] });

    const s3BucketManger = new S3BucketManger();
    try {
      const s3 = await s3BucketManger.uploadAssets(
        event.target.files[0],
        "",
        "",
        "photo"
      );
      await dispatch.user.updateUser({
        photoUrl: s3.assetUrl,
        id: userInfo?.id
      });
      setUploading({ uploading: false, file: null });
    } catch (error) {
      console.log(error);
      setUploading({ uploading: false, file: null });
      notifyError("Upload Failed");
    }
  };

  const handleBannerUpload = async (event) => {
    setUploadingBanner(true);

    const s3BucketManger = new S3BucketManger();
    try {
      const s3 = await s3BucketManger.uploadAssets(
        event.target.files[0],
        "",
        "",
        "photo"
      );
      await dispatch.user.updateUser({
        banner: s3.assetUrl,
        id: userInfo?.id,
        shouldNotShowNotification: true
      });
      setUploadingBanner(false);
    } catch (error) {
      setUploadingBanner(false);
      notifyError("Upload Failed");
    }
  };

  const handleProfileClick = () => {
    hiddenUploader.current.click();
  };
  const handleBannerClick = () => {
    bannerRef.current.click();
  }

  const getImageUrl = () => {
    if (fileUplod.uploading) {
      return URL.createObjectURL(fileUplod.file);
    }
    return userInfo?.photoUrl?.length > 5
      ? userInfo?.photoUrl
      : "/images/profile.png";
  };

  return (
    <div className="page md:max-w-[700px] mx-auto">
      <Header>
        <BackButton from="" />
        <div></div>
      </Header>
      <PageTitle>
        <div className="mb-10">EDIT PROFILE</div>
      </PageTitle>
      <div className="col-span-full row-start-1 row-span-2 w-full max-h-[200px] sm:max-h-[300px]">
        <img
          alt=""
          src={userInfo?.banner || "/images/blankImage.jpeg"}
          className="object-cover h-[74.63px] md:h-[200px] w-full rounded-[20px]"
          onClick={handleBannerClick}
        />

        <div 
          className="text-gray-600 text-xs underline leading-10 text-center"
          onClick={handleBannerClick}
        >
          {uploadingBanner ? (
             "Uploading"
          ) : (
            "Change Banner"
          )}
        </div>
        <input
          ref={bannerRef}
          accept="image/*"
          type="file"
          style={{ display: "none" }}
          onChange={(event) => handleBannerUpload(event)}
        />
      </div>
      <div className="flex flex-col justify-center items-center mt-7 mb-8 gap-2">
        <img 
          onClick={handleProfileClick}
          src={getImageUrl()}
          alt="pix"
          className="w-[96px] h-[96px] rounded-full object-cover"
        />

        <div
          className="text-gray-600 text-xs underline leading-10"
          onClick={handleProfileClick}
        >
          {fileUplod.uploading ? "uploading..." : "Change Photo"}
        </div>
        <input
          ref={hiddenUploader}
          accept="image/*"
          type="file"
          style={{ display: "none" }}
          onChange={(event) => handleUpload(event)}
        />
      </div>
      <EditProfileForm uploading={fileUplod.uploading } />
    </div>
  );
};

export default EditProfile;
