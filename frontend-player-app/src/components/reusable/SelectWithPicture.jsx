import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import Spinner from "./spinner/Spinner";

const SelectWithPicture = ({
  setSelectClub,
  selectClub,
  setTeams,
  setSelectTeam,
  getTeamsBasedOnSelectedClub,
  searchValue,
  setSearchValue,
  setNotFoundClubError,
  setTeamLogo,
  className = "",
  userInfo = {}
}) => {
  const [searchData, setSearchData] = useState([]);
  const [listOfClubs, setListOfClubs] = useState([]);
  const [showClubs, setShowClubs] = useState(false);

  const { clubModel } = useSelector(({ club }) => ({
    clubModel: club
  }));

  const pickClubLoading = useSelector(
    ({ loading }) => loading.effects.team.getTeamsByClubId
  );

  const handleClubChange = (selectedClub) => {
    if (setTeamLogo) {
      setTeamLogo(selectedClub?.clubLogoUrl);
    }

    // if the club exists in the state and have ID then get the teams within this club
    if (selectedClub?.id) {
      setSelectClub(selectedClub || {});
      getTeamsBasedOnSelectedClub !== null &&
        getTeamsBasedOnSelectedClub(selectedClub.id);
    }

    // reset selected teams
    setTeams && setTeams([]);
    setSelectTeam && setSelectTeam({});
  };

  useEffect(() => {
    if (searchValue.length > 0) {
      const result = listOfClubs?.filter((club) =>
        club?.clubName?.toLowerCase().includes(searchValue?.toLowerCase())
      );

      if (setNotFoundClubError) {
        setNotFoundClubError(result?.length === 0);
      }
      
      setShowClubs(result?.length > 0);

      setSearchData(result);
    }

    if (searchValue.length === 0) {
      setSearchData([]);
    }
  }, [searchValue]);

  useEffect(() => {
    setListOfClubs(clubModel?.allClubs);
  }, [clubModel?.allClubs]);

  return (
    <>
      <div className="">
        {selectClub?.clubLogoUrl && (
          <img
            src={selectClub.clubLogoUrl}
            alt="Club Logo"
            className="w-[28px] h-[28px] absolute left-4 top-1/2 transform -translate-y-1/2  pt-2"
          />
        )}
        <input
          onChange={(event) => {
            setSearchValue(event.target.value);
            setSelectClub({});
          }}
          defaultValue={userInfo?.clubName}
          className={`${
            selectClub?.clubLogoUrl ? "pl-12" : "pl-4"
          } appearance-none text-[14px] bg-transparent border-b border-gray-600 w-full text-[#000000] py-7 leading-[24px] focus:outline-none`}
          type="text"
          value={selectClub?.clubName || searchValue}
          aria-label="clubName"
          placeholder={userInfo?.clubName || "Search"}
        />
      </div>

      {showClubs && (
        <div
          className={`items=center overflow-y-scroll text-[12px] max-h-[34vh] mt-3 bg-white w-full flex flex-col gap-5 rounded-xl ${className} p-4 shadow-2xl`}
        >
          <ul className="">
            {searchData?.map((club) => (
              <li
                onClick={() => {
                  handleClubChange(club);
                  setShowClubs(false);
                }}
                key={club.id}
                className="pb-7 border-b-[2px] border-gray-200 flex my-7"
              >
                {pickClubLoading ? (
                  <Spinner />
                ) : (
                  <img
                    className="w-[28px] h-[28px] mr-5 object-contain"
                    src={club.clubLogoUrl}
                    alt=""
                  />
                )}
                <p className="text-[14px] font-[400] leading-[24px] font-poppins text-center">
                  {club?.clubName}
                </p>
              </li>
            ))}
          </ul>
        </div>
      )}
    </>
  );
};

export default React.memo(SelectWithPicture);
