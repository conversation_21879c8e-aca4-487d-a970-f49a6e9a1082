import CommentIcon from '../svgIcons/CommentIcon';
import { NewCommentIcon } from '../svgIcons';

const CommentButton = ({highlight}) => {
    return (
        <button
        onClick={() =>
          navigate(
            `/user/comment?highlightId=${highlight.id}&userId=${highlight.userId}`
          )
        }
        className="hover:border-none bg-transparent m-0 p-0 hover:outline-none focus:outline-none"
      >
        <CommentIcon />
      </button>
    );
};

export default CommentButton;