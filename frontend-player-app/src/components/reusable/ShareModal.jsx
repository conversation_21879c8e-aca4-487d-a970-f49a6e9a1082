import {
  EmailShareButton,
  TelegramShareButton,
  WhatsappShareButton,
  
} from "react-share";
import { handleCopyToClipBoard } from "../../utils/helpers";

const ShareModal = ({
  url = "https://www.playerapp.co/invite",
  header = "Invite your friends",
  title = "Join your team on PLAYER",
  closeIconPath = '/images/closeModalIcon.svg',
  id
}) => {
  const link = encodeURI(url);
  const styles = {
    outline: "none"
  };

  const clipText = `${title} \n ${link}`;

  return (
    <div className="share-container">
      <input type="checkbox" id={id} className="modal-toggle" />
      <label htmlFor="" className="modal cursor-pointer">
        <div className="modal-box relative bg-white" htmlFor="">
          <label
            htmlFor={id}
            className="btn btn-sm btn-circle absolute right-2 top-2 bg-white"
          >
            <img  src={closeIconPath} alt="" />
          </label>
          <p className="uppercase text-[18px] text-center font-favela-bold font-bold text-black">
            {header}
          </p>

          <div className="flex justify-around items-center mt-[20px]">
            <EmailShareButton url={link} subject={title} style={styles}>
              <div className="w-[70px] h-[70px] rounded-[20px] border-[1px] border-[#000000] flex justify-center items-center">
                <img  src="/images/gmailIcon.svg" alt="gmailIcon" />
              </div>
            </EmailShareButton>
            <WhatsappShareButton url={link} title={title} style={styles}>
              <div className="w-[70px] h-[70px] rounded-[20px] border-[1px] border-[#000000] flex justify-center items-center">
                <img  src="/images/whatsappIcon.svg" alt="whatsappIcon" />
              </div>
            </WhatsappShareButton>
            {/* <TelegramShareButton url={link} title={title} style={styles}>
              <div className="w-[70px] h-[70px] rounded-[20px] border-[1px] border-[#000000] flex justify-center items-center">
                <img  src="/images/telegramIcon.svg" alt="telegramIcon" />
              </div>
            </TelegramShareButton> */}
            <button onClick={() => handleCopyToClipBoard(clipText)} className="m-0 p-0">
              <div className="w-[70px] h-[70px] rounded-[20px] border-[1px] border-[#000000] flex justify-center items-center">
                <img  src="/images/copyIcon.svg" alt="copyIcon" />
              </div>
            </button>
            
          </div>
        </div>
      </label>
    </div>
  );
};

export default ShareModal;
