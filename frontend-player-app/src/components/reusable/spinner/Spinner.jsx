import React from "react";
import "./spinner.css";

const Spinner = ({ type, waitingText }) => {

  let loadingClassName = "loading loading-ring loading-lg";

  if (type === "ring") {
    loadingClassName = "loading loading-ring loading-lg";
  }

  if (type === "ball") {
    loadingClassName = "loading loading-ball loading-lg";
  }

  return (
    <div className={`${loadingClassName}`}>
      {waitingText ? (
        <p className="text-black text-center absolute w-[100px]">
          {" "}
          {waitingText}{" "}
        </p>
      ) : (
        ""
      )}
    </div>
  );
};
export default Spinner;
