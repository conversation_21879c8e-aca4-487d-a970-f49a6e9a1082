import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { DropDownIcon } from "../svgIcons";
import BackButton from "./buttons/BackButton";
import Header from "./Header";
import { dispatch } from "../../redux/store";

const LeagueStatsHeader = ({ from, setSelectedLeague = () => {} }) => {
  const [selectedInternalLeague, setSelectedInternalLeague] = useState({});
  const navigate = useNavigate();
  const {
    singleTeam,
    currentTeamId,
    currentClubId,
    allLeaguesAndStats,
    clubData,
  } = useSelector(
    ({
      team: {
        singleTeam,
        allTeams: { currentTeamId, currentClubId },
        clubData
      },
      leagues: { allLeaguesAndStats },
    }) => ({
      singleTeam,
      currentTeamId,
      currentClubId,
      allLeaguesAndStats,
      clubData,
    })
  );

  const [selectedSeasonOption, setSelectedSeason] = useState("");

  const handleFilter = async (seasonId, seasonTitle, id) => {
    const filteredSeason = await allLeaguesAndStats?.find(
      (data) => data.seasonId === seasonId && data.id === id
    );

    if (currentTeamId && currentClubId) {
      setSelectedLeague(filteredSeason);
      dispatch.leagues.setSelectedLeagueData({ data: filteredSeason });
      setSelectedInternalLeague(filteredSeason);
      setSelectedSeason(seasonTitle);
    } else {
      navigate("/team-select");
    }
  };

  useEffect(() => {
    if (currentTeamId && currentClubId) {
      dispatch.leagues.setSelectedLeagueData({ data: allLeaguesAndStats[0] });
      setSelectedInternalLeague(allLeaguesAndStats[0]);
      setSelectedLeague(allLeaguesAndStats[0]);
      setSelectedSeason(allLeaguesAndStats[0]?.seasonTitle);
    } else {
      navigate("/team-select");
    }
  }, [allLeaguesAndStats]);

  return (
    <div className="bg-[#F1F1F1] h-[30vh] px-[15px]">
      <Header bgColor={"#F1F1F1"}>
        {from ? (
          <BackButton from={from} data={{ tab: "player stats" }} />
        ) : (
          <BackButton onClick={() => navigate("/team-dashboard")} />
        )}
      </Header>
      <div>
        <div className="flex items-center justify-between">
          <p className="uppercase text-[18px] font-favela-bold font-bold">
            {selectedInternalLeague?.leagueName}
          </p>
          <img
            src={selectedInternalLeague?.logoUrl || clubData?.clubLogoUrl}
            alt="logo"
            className="w-[78px] h-[78px] rounded-full object-cover border-[2px]"
          />
        </div>
        <div className="dropdown">
          <label tabIndex={0} className="">
            <div className="flex items-center gap-4 mt-[20px]">
              <p className="text-[16px] font-normal justify-self-center">
                {selectedSeasonOption}
              </p>
              <DropDownIcon w={15} h={8} color={"black"} />
            </div>
          </label>
          <ul
            tabIndex={0}
            className="dropdown-content menu p-2 shadow bg-base-100 rounded-box w-52"
          >
            {allLeaguesAndStats?.map(({ seasonTitle, seasonId, id }, i) => (
              <li
                key={i}
                onClick={() => handleFilter(seasonId, seasonTitle, id)}
                className="active:bg-white focus:bg-white"
              >
                <a className="active:bg-white focus:bg-white">{seasonTitle}</a>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
};

export default LeagueStatsHeader;
