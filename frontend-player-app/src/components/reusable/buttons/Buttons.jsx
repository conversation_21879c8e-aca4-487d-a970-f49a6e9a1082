import Spinner from "../spinner/Spinner";

export const BigButtons = ({
  label,
  onClick,
  black,
  green,
  isLoading,
  className,
  isDisabled = false,
  ...others
}) => {
  return (
    <button
      {...others}
      type="submit"
      disabled={isLoading || isDisabled}
      onClick={onClick}
      className={`font-favela-bold flex gap-x-4 mx-auto cursor-pointer justify-center uppercase items-center ${
        black && "bg-black text-white"
      } ${
        green && "bg-[#52FF00] text-black"
      } w-10/12 md:w-8/12 min-h-[60px] rounded-full ${className}`}
    >
      {isLoading && <Spinner />}
      {label}
    </button>
  );
};

export const MediumButtons = ({
  label,
  onClick,
  black,
  green,
  isLoading,
  className,
  isDisabled = false
}) => {
  return (
    <button
      type="submit"
      disabled={isLoading || isDisabled}
      onClick={onClick}
      className={`${black && "bg-black text-white"} ${
        green && "bg-[#52FF00] text-black"
      } text-[22px] py-1 rounded-full ${className}`}
    >
      {isLoading && <Spinner />}
      {label}
    </button>
  );
};

export const SmallButtons = ({ label, onClick, black, green, isLoading }) => {
  return (
    <div
      onClick={onClick}
      className={`font-favela flex gap-x-4 mx-auto justify-center items-center ${
        black && "bg-black text-white"
      } ${
        green && "bg-[#52FF00] text-black"
      } w-20 md:w-24 py-1 px-3 rounded-full text-[16px]`}
    >
      {isLoading && <Spinner />}
      {label}
    </div>
  );
};
