import React from "react";
import { useNavigate } from "react-router-dom";

const BackButton = ({ onClick = null, invert, data = null, from = "" }) => {
  const navigate = useNavigate();

  return (
    <button
      onClick={
        onClick
          ? onClick
          : () => {
            navigate(from || -1, {
              state: {
                data,
              },
            });
          }
      }
      className="bg-transparent p-0 m-0 font-favela-bold text-white rounded-full focus:outline-none focus:bg-transparent hover:border-none focus:border-none"
    >
      <img
        className={`bg-white ${invert && "invert"} rounded-full`}
        src="/images/goback.png"
        alt=""
      />
    </button>
  );
};

export default BackButton;
