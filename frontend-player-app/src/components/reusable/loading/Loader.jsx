import { useState } from "react";
import "./loader.css";
import Spinner from "../spinner/Spinner";
import Logo from "./assets/logo.svg";

const Loader = ({ onBoarding }) => {
	const [transparent, setTransparent] = useState(false);

	const setDisplay = () => {
		setTimeout(() => {
			setTransparent(true);
		}, 2000);
	};

	setDisplay();

	return (
		<div
			className={`fixed  ${onBoarding ? "boarding" : "normal"} ${
				transparent && "trans"
			} top-0 bottom-0 right-0 left-0 z-10 flex items-center justify-center`}
		>
			<div className="flex flex-col gap-10 items-center">
				{onBoarding && (
					<div>
						<img  src={Logo} alt="logo" />
					</div>
				)}
				<div>
					<Spinner type={'ball'} />
				</div>
			</div>
		</div>
	);
};
export default Loader;
