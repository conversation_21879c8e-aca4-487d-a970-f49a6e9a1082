import ReactPlayer from "react-player/lazy";
import { PlayIcon } from "../svgIcons";
import React, { useCallback, useRef, useState } from "react";
import { useInView } from "react-intersection-observer";
import Spinner from "./spinner/Spinner";
import {
  uploadedStreamsBaseUrl,
  videoOriginBaseUrl
} from "../../utils/constants";
import styled from "@emotion/styled";
import { isSafari } from "../../utils/helpers";

const ReactPlayerVideo = ({
  highlightId,
  highlight,
  shouldAllowOriginUrlPlay = false,
  setErrorPlayingVideo = () => {}
}) => {
  const [isReady, setIsReady] = useState(false);
  const [volume, setVolume] = useState(false);
  const [playing, setPlaying] = useState(false);
  const [orientation, setOrientation] = useState(null);
  const [inview, setinview] = useState({
    isInviewPort: false,
    id: "",
    url: "",
    thumbnail: ""
  });

  const ref = useRef();

  const { ref: inViewRef } = useInView({
    onChange: (inView) => {
      if (inView) {
        const videoDetails = getVideoDetails();
        setinview({
          isInviewPort: true,
          id: highlightId,
          url: videoDetails.url,
          thumbnail: videoDetails.thumbnail
        });
        // stop auto playing in viewport if the browser is safari
        !isSafari && setPlaying(isReady);
      } else {
        setinview((prev) => ({ ...prev, isInviewPort: false }));
        setPlaying(false);
        setVolume(false);
      }
    }
  });

  const handleSetPlaying = useCallback(() => {
    setPlaying(!playing);
  }, [playing]);

  const handleError = (err) => {
    console.log(err);
    
    setErrorPlayingVideo(true);
  };

  // Use `useCallback` so we don't recreate the function on each render
  const setRefs = useCallback(
    (node) => {
      // Ref's from useRef needs to have the node assigned to `current`
      ref.current = node;
      // Callback refs, like the one from `useInView`, is a function that takes the node as an argument
      inViewRef(node);
    },
    [inViewRef]
  );

  const setVolumeLevel = useCallback(() => {
    setVolume(!volume);
  }, [volume]);

  const handleReady = () => {
    setTimeout(() => {
      setIsReady(inview.id === highlightId);
    }, 300);
  };

  //
  const getObjectFit = () => {
    if (highlightId) {
      if (highlight?.streamUrl?.baseUrl) {
        if (highlight?.streamUrl?.orientation === "landscape") {
          return "contain";
        } else if (highlight?.streamUrl?.orientation === "portrait") {
          return "cover";
        } else {
          return "contain";
        }
      }
    }
  };

  const getVideoDetails = () => {
    if (highlightId) {
      if (highlight?.streamUrl?.baseUrl && highlight?.queueProcessed) {
        return {
          // duration to be used when requested
          duration: ((highlight?.streamUrl?.duration || 60) / 60).toFixed(2),
          thumbnail: `${uploadedStreamsBaseUrl}/${
            highlight?.streamUrl?.key?.split("--")[1]
          }/thumbnail.png`,
          url: `${uploadedStreamsBaseUrl}/${
            highlight?.streamUrl?.key?.split("--")[1]
          }/index.m3u8`
        };
      }
      if (
        highlight?.streamUrl?.baseUrl &&
        !highlight?.queueProcessed &&
        shouldAllowOriginUrlPlay
      ) {
        return {
          // duration to be used when requested
          duration: ((highlight?.streamUrl?.duration || 60) / 60).toFixed(2),
          thumbnail: "/images/loadball.gif",
          url: `${videoOriginBaseUrl}/${highlight?.streamUrl?.key}`
        };
      }

      return highlightId ? highlight?.url : highlight?.assetUrl;
    }
    return "";
  };

  return (
    <div ref={setRefs} className="w-full h-full mt-3 relative">
      {!isReady && (
        <div className="z-50">
          <div className="absolute top-0 right-0 bottom-0 left-0 flex justify-center items-center z-40">
            <Spinner />
          </div>
          <img
            className="h-[520px] absolute w-full object-cover rounded-[0px]"
            src={inview.thumbnail}
          />
        </div>
      )}
      <div className="h-[520px] flex justify-center items-center rounded-[0px]">
        {inview.isInviewPort && inview.id === highlightId ? (
          <ReactPlayer
            onReady={handleReady}
            url={inview.url}
            onPlay={() => {
              setPlaying(true);
            }}
            onPause={() => setPlaying(false)}
            playing={playing}
            controls={false}
            onError={handleError}
            volume={Number(volume)}
            // wrapper={(props) => (
            //   <VideoWrapper {...props} objectFit={getObjectFit()} />
            // )}
            width="100%"
            height="100%"
            style={{ backgroundColor: "#000" }}
            poster={inview.thumbnail}
            className="react-player"
            fallback={
              <img
                className="h-[520px] absolute w-full object-cover rounded-[0px]"
                src={inview.thumbnail}
              />
            }
          />
        ) : (
          <img
            className="h-[520px] absolute w-full object-cover rounded-[0px]"
            src={inview.thumbnail}
          />
        )}
      </div>

      {/* Play video */}
      {playing && isReady ? (
        <div
          onClick={handleSetPlaying}
          className="absolute top-0 right-0 bottom-0 left-0 flex justify-center items-center z-40"
        />
      ) : (
        isReady && (
          <div
            onClick={handleSetPlaying}
            className="absolute top-0 right-0 bottom-0 left-0 flex justify-center items-center"
          >
            <div className="w-[50px] h-[50px] bg-gray-100 rounded-full flex justify-center items-center">
              <PlayIcon setVolumeLevel={setVolumeLevel} />
            </div>
          </div>
        )
      )}
      {highlightId == inview.id && (
        <VolumeIcon setVolumeLevel={setVolumeLevel} volume={volume} />
      )}
    </div>
  );
};

const VideoWrapper = React.memo(styled.div`
  width: 100%;
  height: 100%;
  position: absolute;
  background-color: #000000;
  object-fit: cover;
  /* border-radius: 10px; */
  /* background-image: url(${({ poster, playing }) => poster}); */
  background-size: cover;
  background-repeat: no-repeat;
  inset: 0;
  @media (max-width: 768px) {
    border-radius: 0px;
  }
  video {
    /* object-fit: ${({ objectFit }) => objectFit}; */
    /* object-fit: contain; */
  }
`);

const VolumeIcon = React.memo(({ setVolumeLevel, volume }) => {
  return (
    <button
      onClick={setVolumeLevel}
      className="absolute top-[90%] right-[10px] z-40 bg-transparent border-none outline-none focus:outline-none focus:border-none hover:outline-none hover:border-none"
    >
      <img
        src={!volume ? "/images/mute.svg" : "/images/volume.svg"}
        className="w-[30px] h-[30px] bg-gray-800 p-2 rounded-full shadow-2xl"
      />
    </button>
  );
});

export default ReactPlayerVideo;
