import React from "react";

const Tabs = ({ tabs, selected, setSelected, color, isConnections, font = 'font-poppins' }) => {
  return isConnections ? (
    <div className="">
      <div className="w-full grid grid-cols-2 min-[420px]:flex min-[420px]:gap-24 min-[420px] justify-center min-[420px]:justify-items-center items-center mt-7 text-[14px] min-[420px]:text-lg md:mt-16">
        {tabs.map((tab, i) => (
          <div
            key={i}
            onClick={() => setSelected(tab.title)}
            className={`${
              selected === tab.title
                ? `border-b-[2px] opacity-100 border-${color} box-border opacity-100`
                : "opacity-60"
            } pb-3 cursor-pointer text-${color} capitalize text-center`}
          >
            {tab.connectionCount} {tab.title}
          </div>
        ))}
      </div>
    </div>
  ) : (
    <div className="">
      <div className={`w-full grid grid-cols-2 min-[420px]:flex min-[420px]:gap-24 min-[420px] justify-center min-[420px]:justify-items-center items-center mt-7 text-[16px] min-[420px]:text-[16px] md:mt-16 font-[500] ${font}`}>
        {tabs.map((tab, i) => (
          <div
            key={i}
            onClick={() => setSelected(tab)}
            className={`${
              selected === tab && `border-b-2 border-${color} box-border`
            } pb-3 cursor-pointer text-${color} text-center`}
          >
            {tab}
          </div>
        ))}
      </div>
    </div>
  );
};

export default Tabs;
