/* VideoCarousel.css */

/* Custom left and right buttons */
.custom-prev-btn, .custom-next-btn {
    background-color: green;
    border: none;
    color: white;
    font-size: 24px;
    width: 50px;
    height: 50px;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
  }
  
  .custom-prev-btn {
    left: 15px; /* Adjust position */
  }
  
  .custom-next-btn {
    right: 15px; /* Adjust position */
  }
  
  /* Dots customization */
  .alice-carousel__dots-item {
    background-color: grey;
    border-radius: 50%;
    width: 10px;
    height: 10px;
    margin: 0 5px;
  }
  
  .alice-carousel__dots-item.__active {
    background-color: green; /* Active dot color */
  }
  