import React from "react";

const TextInput = ({ label, onChange, disabled, placeholder, value }) => {
  return (
    <div className="mt-7">
      <label className="font-favela-bold">{label}</label>
      <div className="flex items-center border-b border-black py-1">
        <input
          onChange={onChange}
          className="appearance-none bg-transparent border-none w-full text-gray-700 mr-3 py-1 leading-tight focus:outline-none"
          type="text"
          value={value}
          disabled={disabled}
          placeholder={placeholder}
          aria-label="Full name"
        />
      </div>
    </div>
  );
};

export default TextInput;
