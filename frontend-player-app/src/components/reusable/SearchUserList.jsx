import React, { useCallback, useEffect, useState } from "react";
import _debounce from "lodash/debounce";
import { useSelector } from "react-redux";
import Spinner from "./spinner/Spinner";
import { searchArrayOrMakeCallToAPI } from "../../utils/helpers";
import { dispatch } from "../../redux/store";

const SearchUserList = ({ selectedUser, onSelectUser }) => {
  const [searchResult, setSearchResult] = useState(null);
  const [inputValue, setInputValue] = useState("");

  const gettingUser = useSelector(
    ({ loading }) => loading.effects.user.userSearch
  );
  const { userList, teamsList } = useSelector(({ user, team }) => ({
    userList: user.usersByProjection,
    teamsList: team.teamsByProjection
  }));

  const handleSearch = async (query) => {
    const searchResposne = await searchArrayOrMakeCallToAPI({
      searchTerm: query,
      array: [
        ...userList?.map((item) => ({
          ...item,
          type: "user",
          fullname: `${item?.firstName} ${item?.lastName}`
        }))
      ],
      makeSearchCall: [dispatch.user.userSearch, dispatch.team.teamSearch]
    });

    const searchResponse = searchResposne?.length > 0 ? searchResposne : null;
    setSearchResult(
      searchResponse?.sort((a, b) => {
        if (a.type === "user" && b.type === "team") {
          return 1;
        } else if (a.type === "team" && b.type === "user") {
          return -1;
        }
      })
    );
  };

  const handleSearchResult = (user) => {
    onSelectUser(user);
    handleSearch("");
    setInputValue(`@${user?.firstName}${user?.lastName}`)
  };

  const fetchSearchData = useCallback(_debounce(handleSearch, 400), [
    userList,
    teamsList
  ]);

  const fetchAllUserData = async () => {
    await dispatch.user.userSearchByProjection();
  };

  useEffect(() => {
    if (!Boolean(userList?.length)) {
      fetchAllUserData();
    }
  }, [userList]);

  return (
    <div className="">
      <div className="w-full">
        <input
          onChange={(e) => {
            setInputValue(e.target.value);
            fetchSearchData(e.target.value)
        }}
          type="text"
          value={inputValue}
          name="text"
          id="text"
          className={`border-b-[1px] border-gray-600 block py-3 w-full text-sm text-gray-900 bg-transparent appearance-none dark:border-gray-600 dark:focus:border-black-800 focus:outline-none focus:ring-0`}
          placeholder={
            selectedUser
              ? `${selectedUser?.firstName || ""} ${
                  selectedUser?.lastName || ""
                }`
              : "Type name"
          }
        />
      </div>

      {searchResult && (
        <div className="-mt-5">
          <div className="h-[200px] overflow-y-scroll absolute left-3 z-10 bg-white w-[95%] rounded-b-2xl border  border-gray-900 mx-auto p-3">
            {!gettingUser && searchResult
              ? searchResult?.map((result, idx) => (
                  <div
                    key={idx}
                    onClick={() => handleSearchResult(result)}
                    className="flex items-center cursor-pointer text-[12px] font-poppins leading-[18px] my-2"
                  >
                    <img
                      className="w-[41px] h-[41px] rounded-full"
                      src={result?.photoUrl || "/images/profile.png"}
                      alt="profile image"
                    />
                    <div className="px-5">
                      <p className="text-black font-semibold">
                        @{result?.firstName}
                        {result?.lastName}
                      </p>
                      <p className="text-gray-600 ">
                        {result?.teamName || result?.clubName}
                      </p>
                    </div>
                  </div>
                ))
              : ""}
          </div>

          <div className="mt-4">{gettingUser ? <Spinner /> : ""}</div>
        </div>
      )}
    </div>
  );
};

export default SearchUserList;
