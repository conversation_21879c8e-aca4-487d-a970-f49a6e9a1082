import React from "react";

const SearchCardForShare = ({ user, handleItemSelect, selectedItems }) => {
  return (
    <div className="w-full flex justify-between items-center py-1 mb-3">
      <div
        className="flex gap-5 items-center"
        onClick={() => handleItemSelect(user.id)}
      >
        <img
          src={`${
            user?.photoUrl?.length > 5 ? user?.photoUrl : "/images/profile.png"
          }`}
          alt="profile pix"
          className="w-[46px] h-[46px] object-cover rounded-full"
        />
        <div>
          <p className="text-[14px] font-medium">
            @{user.firstName} {user.lastName}
          </p>
          <p className="text-[14px] font-medium opacity-50">
            {(user.teamName !== "N/A" && user.teamName) || user.clubName}
          </p>
        </div>
      </div>
      <input
        type="checkbox"
        checked={selectedItems.includes(user.id)}
        onChange={() => handleItemSelect(user.id)}
      />
    </div>
  );
};

export default SearchCardForShare;
