import React, { ErrorInfo, ReactElement } from "react";
import BackButton from "./buttons/BackButton";
import Header from "./Header";
import Logo from "./Logo";
import PageTitle from "./PageTitle";
interface ErrorBoundaryState {
  hasError: boolean;
  errorMessage: string;
}
interface ErrorboundaryProps {
  children: ReactElement;
}
export class ErrorBoundaries extends React.Component<
  ErrorboundaryProps,
  ErrorBoundaryState
> {
  constructor(props: ErrorboundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      errorMessage: "",
    };
  }
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({ hasError: true });
    this.setState({ errorMessage: error.message });
    //Do something with err and errorInfo
  }
  render(): React.ReactNode {
    if (this.state?.hasError) {
      return (
        <div className="page">
          <Header>
            <BackButton invert={false} from="/" />
            <Logo light />
            <div></div>
          </Header>
          <PageTitle center>
            <div>AN ERROR OCCURED</div>
          </PageTitle>
          <div className="mt-7">{this.state.errorMessage}</div>
        </div>
      );
    }
    return this.props.children;
  }
}
