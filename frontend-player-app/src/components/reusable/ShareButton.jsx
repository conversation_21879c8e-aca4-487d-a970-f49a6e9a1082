import Share from "../svgIcons/Share";

const ShareButton = ({highlight, type = "highlight"}) => {
    return (
        <button
        onClick={() =>
          navigate("/feed/share", {
            state: {
              highlight,
              type
            }
          })
        }
        className="hover:border-none bg-transparent m-0 p-0 hover:outline-none focus:outline-none"
      >
        <Share />
      </button>
    );
};

export default ShareButton;