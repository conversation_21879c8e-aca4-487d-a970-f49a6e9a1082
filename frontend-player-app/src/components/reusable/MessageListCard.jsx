import moment from "moment";
import React from "react";
import { useNavigate } from "react-router-dom";
import { trim150 } from "../../utils/helpers";
import UnreadMessageCount from "./UnreadMessageCount";

const MessageListCard = ({ chat }) => {
  const navigate = useNavigate();

  return (
    <div
      className="flex items-start justify-between w-full py-4 mb-3 border-b"
      onClick={() => {
        navigate(
          `/user/message?recipientId=${chat.otherUser?.id}&chatroomId=${chat.chatroomId}`
        );
      }}
    >
      <div className="flex max-w-[75%] gap-4 items-center">
        <img
          src={chat.otherUser?.photoUrl || "/images/profile.png"}
          alt="profile picture"
          className="w-[40px] h-[40px] object-cover rounded-full"
        />
        <div className="flex flex-col gap-1">
          <p className="font-bold">
            {`${chat.otherUser?.firstName || ""}${
              chat.otherUser?.lastName ? ` ${chat.otherUser.lastName}` : ""
            }` || "User"}
          </p>
          <p className="font-medium opacity-50 min-h-[1.3125rem]">
            {chat.lastMessage && trim150(chat.lastMessage, 50)}
          </p>
        </div>
      </div>
      <div className="flex flex-col items-end gap-1">
        <div className="text-gray-700">
          {chat.lastMessageSentAt && moment(chat.lastMessageSentAt).fromNow()}
        </div>
        {chat.unreadMessageCount > 0 && (
          <UnreadMessageCount count={chat.unreadMessageCount} />
        )}
      </div>
    </div>
  );
};

export default MessageListCard;
