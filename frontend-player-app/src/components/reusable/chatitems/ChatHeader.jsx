import BackButton from "../buttons/BackButton";

const ChatHeader = ({ userDetails }) => {
  return (
    <div className="flex fixed font-poppins top-0 right-0 left-0 px-[15px] justify-between items-center h-[63px] border-b border-black/10 bg-white">
      <div className="flex items-center gap-4">
        <BackButton />
        <div className="flex gap-3 items-center text-[13px] leading-[20px]">
          <div className="w-[42px] h-[42px] rounded-full">
            <img
              src={userDetails?.photoUrl || "/images/profile.png"}
              alt="profile picture"
              className="object-cover w-full h-full rounded-full"
            />
          </div>
          <div className="flex flex-col">
            <p className="font-bold">
              {userDetails?.firstName} {userDetails?.lastName}
            </p>
            <p>
              {userDetails?.teamName === "N/A"
                ? "No Team"
                : userDetails?.teamName}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
export default ChatHeader;
