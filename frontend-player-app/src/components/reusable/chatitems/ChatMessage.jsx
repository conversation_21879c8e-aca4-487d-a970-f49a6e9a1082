import React, { useEffect, useMemo, useRef } from "react";
import { useSelector } from "react-redux";
import { getTimeAgo, linkify } from "../../../utils/helpers";
import { addChatClass, getNextMessage } from "./constant";
import ShareMessageCard from "./ShareMessageCard";

const handleClass = addChatClass();

const ChatMessage = ({ chats }) => {
  const { userInfo } = useSelector((state) => state.auth.authUser);
  const chatBottomRef = useRef(null);

  const refs = useMemo(
    () => Array.from({ length: chats.length }).map(() => React.createRef(null)),
    [chats.length]
  );

  useEffect(() => {
    if (chatBottomRef.current) {
      chatBottomRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [chats]);

  return (
    <div className="font-normal text-[11px] leading-[16.5px] flex flex-col gap-4 font-poppins pt-[63px] mb-[79px]">
      {/* <p className="pt-4 text-center">Jun 6</p> */}
      <div className="font-normal text-[11px] leading-[16.5px] flex flex-col gap-[5px]">
        {chats.map((chat, index, arr) => {
          const {
            message,
            sender_id,
            id,
            created_at,
            sender_image_url,
            image_url,
            message_metadata = {},
          } = chat;
          return (
            <React.Fragment key={id}>
              <div className="flex mt-2 w-full gap-7 flex-col relative z-10 top-4 md:top-[76px]">
                {/* Modal for image */}
                {image_url ? (
                  <React.Fragment key={id}>
                    <input
                      ref={refs[index]}
                      type="checkbox"
                      id={`meessage-modal-${index}}`}
                      className="modal-toggle"
                    />
                    <div className="modal">
                      <div className="relative modal-box">
                        <label
                          htmlFor={`meessage-modal-${index}}`}
                          className="absolute btn btn-sm btn-circle right-2 top-2"
                        >
                          ✕
                        </label>
                        <img
                          src={image_url}
                          alt="pix"
                          className="object-cover w-full"
                        />
                      </div>
                    </div>
                  </React.Fragment>
                ) : (
                  ""
                )}
                {/* End Modal for image */}
              </div>

              <>
                {message_metadata?.sharingType ? (
                  <ShareMessageCard
                    message_metadata={message_metadata}
                    message={message}
                    isSenderView={sender_id === userInfo.liveLikeProfileId}
                  />
                ) : (
                  <>
                    {sender_id !== userInfo.liveLikeProfileId ? (
                      <div
                        className={`${handleClass(
                          sender_id === userInfo.liveLikeProfileId,
                          getNextMessage(sender_id, index, arr.length, arr)
                        )} flex items-end gap-2`}
                        key={id}
                      >
                        <div className="w-[32px] h-[32px] rounded-full guest-image">
                          <img
                            src={
                              sender_image_url
                                ? sender_image_url
                                : "/images/profile.png"
                            }
                            alt=""
                            className="hidden object-cover w-full h-full rounded-full"
                          />
                        </div>
                        <div>
                          {message ? (
                            linkify(message)
                          ) : (
                            <img
                              className="w-[60%]"
                              src={image_url}
                              alt=""
                              onClick={() => {
                                refs[index]?.current?.click();
                              }}
                            />
                          )}
                        </div>
                      </div>
                    ) : (
                      <p
                        className={`${handleClass(
                          sender_id === userInfo.liveLikeProfileId,
                          getNextMessage(sender_id, index, arr.length, arr)
                        )}`}
                        key={id}
                      >
                        {message ? (
                          linkify(message)
                        ) : (
                          <img
                            className="w-full"
                            src={image_url}
                            alt=""
                            onClick={() => {
                              refs[index]?.current?.click();
                            }}
                          />
                        )}
                      </p>
                    )}
                  </>
                )}
              </>

              <span
                ref={chats.length === index + 1 ? chatBottomRef : null}
                className="hidden text-black"
              >
                {getTimeAgo(created_at)}
              </span>
            </React.Fragment>
          );
        })}
      </div>
    </div>
  );
};

export default ChatMessage;
