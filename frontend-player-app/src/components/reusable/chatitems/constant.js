#!/usr/bin/node

export const selfId = Math.random().toString(36).substring(2, 9);
const guestId = Math.random().toString(36).substring(2, 9);

export const chats = [
  {
    chatId: Math.random().toString(36).substring(2, 9),
    message:
      "Vorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc vulputate libero et velit interdum, ac aliquet odio mattis.!!",
    timeStamp: new Date().toString(),
    sender_id: selfId,
  },
  {
    chatId: Math.random().toString(36).substring(2, 9),
    message: "Vorem ipsum dolor sit amet, consectetur adipiscing elit.",
    timeStamp: new Date().toString(),
    userId: selfId,
  },
  {
    chatId: Math.random().toString(36).substring(2, 9),
    message: "Vorem ipsum dolor sit amet, consectetur adipiscing elit. ",
    timeStamp: new Date().toString(),
    userId: guestId,
  },
  {
    chatId: Math.random().toString(36).substring(2, 9),
    message:
      " Nunc vulputate libero et velit interdum, ac aliquet odio mattis.",
    timeStamp: new Date().toString(),
    userId: guestId,
  },
  {
    chatId: Math.random().toString(36).substring(2, 9),
    message:
      "Vorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc vulputate libero et velit interdum, ac aliquet odio mattis.",
    timeStamp: new Date().toString(),
    userId: selfId,
  },
  {
    chatId: Math.random().toString(36).substring(2, 9),
    message: "Vorem ipsum dolor sit amet, consectetur elit. ",
    timeStamp: new Date().toString(),
    userId: selfId,
  },
  {
    chatId: Math.random().toString(36).substring(2, 9),
    message: "I will call you back!!",
    timeStamp: new Date().toString(),
    userId: selfId,
  },
  {
    chatId: Math.random().toString(36).substring(2, 9),
    message: "when!!",
    timeStamp: new Date().toString(),
    userId: guestId,
  },
  {
    chatId: Math.random().toString(36).substring(2, 9),
    message: "I will call you back!!",
    timeStamp: new Date().toString(),
    userId: selfId,
  },
  {
    chatId: Math.random().toString(36).substring(2, 9),
    message:
      "I want you to get the team in the right shape..., the last game is not great i must confess, get it right of face the board!!",
    timeStamp: new Date().toString(),
    userId: guestId,
  },
  {
    chatId: Math.random().toString(36).substring(2, 9),
    message: "this is unacceptable!!",
    timeStamp: new Date().toString(),
    userId: guestId,
  },
];

export const addChatClass = () => {
  let userMessageCount = 0;
  let guestMessageCount = 0;
  return (user, nextMessage) => {
    if (user) {
      guestMessageCount = 0;
      if (userMessageCount === 0 && nextMessage) {
        userMessageCount++;
        return "user-message user-first-message";
      } else if (userMessageCount === 0 && !nextMessage) {
        userMessageCount++;
        return "user-message user-lone-message";
      }
      if (userMessageCount > 0 && nextMessage) {
        userMessageCount++;
        return "user-message user-middle-message";
      } else {
        userMessageCount = 0;
        return "user-message user-last-message";
      }
    } else {
      userMessageCount = 0;
      if (guestMessageCount === 0 && nextMessage) {
        guestMessageCount++;
        return "guest-message guest-first-message";
      } else if (guestMessageCount === 0 && !nextMessage) {
        guestMessageCount++;
        return "guest-message guest-lone-message";
      }
      if (guestMessageCount > 0 && nextMessage) {
        guestMessageCount++;
        return "guest-message guest-middle-message";
      } else {
        guestMessageCount = 0;
        return "guest-message guest-last-message";
      }
    }
  };
};

export const getNextMessage = (id, ind, chatLen, arr) => {
  const index = ind + 1;

  if (index >= chatLen) {
    return false;
  }
  return id === arr[index]["sender_id"];
};
