import { useNavigate } from "react-router-dom";

const ShareMessageCard = ({
  isSenderView = false,
  message_metadata,
  message,
  sender_image_url,
}) => {
  const navigate = useNavigate();
  const {
    shareContentDetails: { text, mediaUrl, userId, id, teamName },
    sharingType,
  } = message_metadata;

  const getActionLabel = () => {
    const actionLabels = {
      SHARE_ANNOUNCEMENT: "View Announcement",
      PINNED_POST: "View Pinned Post",
      SHARE_PROFILE: "View Profile",
    };

    return actionLabels[sharingType] || "View Post";
  };

  const onViewClick = () => {
    const urlMap = {
      SHARE_ANNOUNCEMENT: `/user/announcement?announcementId=${id}`,
      PINNED_POST: `/user/announcement?announcementId=${id}`,
      SHARE_PROFILE: `/profile?id=${userId}`,
    };

    const url =
      urlMap[sharingType] || `/user/comment?highlightId=${id}&userId=${userId}`;

    navigate(url);
  };
  return (
    <div
      style={{
        display: "flex",
        justifyContent: isSenderView ? "end" : "start",
      }}
    >
      {isSenderView && (
        <div className="w-[32px] h-[32px] rounded-full guest-image">
          <img
            src={mediaUrl || "/images/profile.png"}
            alt=""
            className="hidden object-cover w-full h-full rounded-full"
          />
        </div>
      )}
      <div
        style={{
          borderRadius: isSenderView
            ? "20px 20px 0px 20px"
            : "20px 20px 20px 0px",
          background: isSenderView ? "#F1F1F1" : "#FFFFFF",
        }}
        className="w-[274px] border-[#F1F1F1] border p-3 flex flex-col gap-2"
      >
        {message && <h6 className="text-[11px] font-[500] mb-1">{message}</h6>}
        <div className="flex flex-row w-full gap-3">
          {(sharingType !== "SHARE_ANNOUNCEMENT" ||
            sharingType !== "PINNED_POST") && (
            <div className="w-[42px] h-[42px] rounded-full">
              <img
                src={mediaUrl || "/images/profile.png"}
                alt="Profile Image"
                className="object-cover w-full h-full rounded-full"
              />
            </div>
          )}
          <div className={`w-[calc(100%-54px)]`}>
            {sharingType === "SHARE_PROFILE" ? (
              <>
                <div className="flex flex-col gap-1 text-black text-[13px] font-poppins">
                  <h6 className="font-bold">{text}</h6>
                  <p className="font-[300]">{teamName}</p>
                </div>
              </>
            ) : (
              <>
                <div className="w-full">
                  <h6 className="text-[11px] font-poppins">{text}</h6>
                </div>
              </>
            )}
          </div>
        </div>

        <hr className="w-full text-[#797979]" />

        <div className="flex justify-center mt-2">
          <h6
            onClick={onViewClick}
            className="text-[#797979] underline cursor-pointer"
          >
            {getActionLabel()}
          </h6>
        </div>
      </div>
    </div>
  );
};

export default ShareMessageCard;
