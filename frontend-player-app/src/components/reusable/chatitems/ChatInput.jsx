import React, { useState, useRef, useEffect } from "react";
import Paperclip from "../../svgIcons/Paperclip";
import SendIcon from "../../../assets/send-icon.png";
import CameraIcon from "../../svgIcons/CameraIcon";
import { SmallButtons } from "../../reusable/buttons/Buttons";
import Spinner from "../spinner/Spinner";
import { notifyError } from "../../../utils/helpers";

const ChatInput = ({ sendTextMessage, sendImageMessage }) => {
  const [value, setValue] = useState("");
  const [captureLoading, setCaptureLoading] = useState(false);
  const [showCameraModal, setShowCameraModal] = useState(false);
  const [s3Loading, setS3Loading] = useState(false);
  const [file, setFile] = useState({ src: "", preview: "" });
  const [currentFile, setCurrentFile] = useState("");
  const [shouldShowUpload, setShouldShowUpload] = useState(false);

  const videoRef = useRef(null);
  const hiddenUploader = useRef();

  const handleSubmitTextMessage = (e) => {
    e.preventDefault();
    sendTextMessage(value);
    setValue("");
  };

  const handleCameraClick = () => {
    return //temporarily disabled camera
    // setShowCameraModal(true);
    // if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
    //   navigator.mediaDevices
    //     .getUserMedia({ video: true })
    //     .then((stream) => {
    //       const video = videoRef.current;
    //       video.srcObject = stream;
    //       video.play();
    //     })
    //     .catch((error) => {
    //       console.error("Error accessing camera:", error);
    //       setShowCameraModal(false);
    //     });
    // }
  };

  const handleCaptureClick = async () => {
    setCaptureLoading(true);

    const video = videoRef.current;
    if (video) {
      const canvas = document.createElement("canvas");
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      const ctx = canvas.getContext("2d");
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
      const capturedImage = canvas.toDataURL("image/jpeg");

      // Convert data URL to Blob
      const base64Data = capturedImage.split(",")[1];
      const mimeType = capturedImage.split(";")[0].split(":")[1];
      const byteCharacters = atob(base64Data);
      const byteArrays = [];
      for (let offset = 0; offset < byteCharacters.length; offset += 1024) {
        const slice = byteCharacters.slice(offset, offset + 1024);
        const byteNumbers = new Array(slice.length);
        for (let i = 0; i < slice.length; i++) {
          byteNumbers[i] = slice.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        byteArrays.push(byteArray);
      }
      const file = new File(byteArrays, "image.jpg", { type: mimeType });

      try {
        setShowCameraModal(false);

        await sendImageMessage(URL.createObjectURL(file));
        URL.revokeObjectURL(file);

      } catch (error) {
        console.error(error);
        setCaptureLoading(false);
      }

      const stream = video.srcObject;
      if (stream) {
        stream.getTracks().forEach((track) => track.stop());
      }
      video.srcObject = null;
    }

    setCaptureLoading(false);
  };

  const handleCloseModal = () => {
    const video = videoRef.current;
    if (video) {
      const stream = video.srcObject;
      if (stream) {
        stream.getTracks().forEach((track) => track.stop());
      }
      video.srcObject = null;
    }
    setShowCameraModal(false);
  };

  const handleCloseFileModal = () => {
    setFile({ src: "", preview: "" });
    setCurrentFile("");
  };

  const handleFileChange = (e) => {
    e.preventDefault();
    const fileSelected = e.target.files[0];
    setCurrentFile(fileSelected);
  };

  const handleUploadClick = () => {
    return //temporarily disabled upload
    // hiddenUploader.current.click();
  };

  const handleUpload = async () => {
    handleCloseFileModal();
    
    await sendImageMessage(URL.createObjectURL(file.src));

    URL.revokeObjectURL(file.src);
    setFile({ src: "", preview: "" });
  };

  useEffect(() => {
    const imageReg = /[\/.](gif|jpg|jpeg|tiff|png|webp)$/i;
    setFile({ src: "", preview: "" });

    if (imageReg.test(currentFile?.type)) {
      const preview = (
        <img
          src={URL.createObjectURL(currentFile)}
          alt="feed"
          className="w-full h-full object-cover rounded-[32px] "
        />
      );
      setFile({ src: currentFile, preview });
      return;
    }

    if (currentFile && !imageReg.test(currentFile?.type)) {
      notifyError("Invalid file provided");
    }
  }, [currentFile]);

  // useEffect(() => {
  //     setShouldShowUpload(value.length <= 0);
  // }, [value]);

  return (
    <div
      className={`${
        file.src || showCameraModal
          ? "relative"
          : "fixed bottom-[70px] pb-[9px] border-b border-black/40 bg-white backdrop-blur-3xl py-2 right-[15px] mx-auto left-[15px]"
      }`}
    >
      <div className="relative w-full flex items-center justify-between">
        <form onSubmit={handleSubmitTextMessage} className="flex-1">
          <input
            type="text"
            placeholder="Write a message..."
            value={value}
            onChange={(e) => setValue(e.target.value)}
            className="w-full py-1 px-2 text-gray-900 bg-transparent outline-none"
          />
        </form>
        <div className="flex items-center gap-4">
          {shouldShowUpload ? (
            <>
              <div className="w-6 h-6 opacity-50 cursor-not-allowed" onClick={handleUploadClick}>
                <Paperclip />
              </div>

              <input
                ref={hiddenUploader}
                accept="image/*"
                type="file"
                multiple={false}
                style={{ display: "none" }}
                disabled={s3Loading}
                onChange={handleFileChange}
              />
              <div
                className="w-6 h-6 opacity-50 cursor-not-allowed"
                onClick={handleCameraClick}
              >
                <CameraIcon />
              </div>
            </>
          ) : (
            ""
          )}
          <div>
            <img
              src={SendIcon}
              alt="send"
              className="w-6 h-6 cursor-pointer"
              onClick={handleSubmitTextMessage}
            />
          </div>
        </div>
      </div>
      {showCameraModal && (
        <div className="fixed inset-0 flex items-center p-5 justify-center bg-black bg-opacity-50">
          <div className="relative bg-white rounded-lg shadow-lg p-4 w-full max-w-[600px]">
            <div className="aspect-w-4 aspect-h-3">
              {captureLoading ? (
                <div className="object-cover rounded">
                  <Spinner />
                </div>
              ) : (
                <video
                  ref={videoRef}
                  className="object-cover rounded"
                  autoPlay
                />
              )}
            </div>
            <div className="mt-4 flex justify-center gap-4 px-2">
              <SmallButtons onClick={handleCloseModal} black label="Close" />
              <SmallButtons
                onClick={handleCaptureClick}
                green
                label="Capture"
              />
            </div>
          </div>
        </div>
      )}
      {file.src && (
        <div className="fixed inset-0 flex items-center p-5 justify-center bg-black bg-opacity-50">
          <div className="relative bg-white rounded-lg shadow-lg p-4 w-full h-[60%] max-h-[600px] max-w-[600px]">
            <div className="aspect-w-4 aspect-h-3">
              {s3Loading ? (
                <div className="flex items-center justify-center h-full">
                  <Spinner />
                </div>
              ) : (
                <div className="object-cover rounded">{file.preview}</div>
              )}
            </div>
            <div className="mt-4 flex justify-center gap-4">
              <SmallButtons
                onClick={handleCloseFileModal}
                black
                label="Close"
              />
              <SmallButtons onClick={() => handleUpload()} green label="Send" />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChatInput;
