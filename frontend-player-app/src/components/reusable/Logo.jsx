import React from "react";
import { Link } from "react-router-dom";

const Logo = ({ light, shouldNavigate = true }) => {
  return shouldNavigate ? (
    <Link to={"/"}>
      <img
        className={`h-7 md:h-10 bg-${light ? "black" : "white"}`}
        src={
          !light
            ? "/images/newlogo_black_small.png"
            : "/images/newlogowhite.png"
        }
        alt="logo"
      />
    </Link>
  ) : (
    <img
      className={`h-7 md:h-auto bg-${light ? "black" : "white"}`}
      src={
        !light ? "/images/newlogo_black_small.png" : "/images/newlogowhite.png"
      }
      alt="logo"
    />
  );
};

export default Logo;
