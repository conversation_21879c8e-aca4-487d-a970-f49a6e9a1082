import React, { useEffect, useRef, useState } from "react";
import { PlayIcon } from "../svgIcons";

const VideoUI = ({ videoUrl }) => {
  const videoRef = useRef(null);
  const [playing, setPlaying] = useState(false);

  const videoHandler = (control) => {
    if (control === "play") {
      videoRef.current.play();
      setPlaying(true);
    } else if (control === "pause") {
      videoRef.current.pause();
      setPlaying(false);
    }
  };

  useEffect(() => {
    if (!playing && videoRef.current) {
      videoRef.current.load();
    }

    videoRef.current.addEventListener("loadeddata", (e) => {
      videoRef.current?.play();
      setPlaying(true);
    });
  
    return () => {
      videoRef?.current?.removeEventListener("loadeddata",  () => {
        videoRef.current.pause();
        setPlaying(false)
      });
    };
  }, []);

  return (
    <div className="w-full h-full relative">
      <div className="h-[300px] w-full flex justify-center items-center bg-black rounded-[32px] p-3">
         <video 
          ref={videoRef}
          
          preload="none"
          poster="./images/loading.gif"
          className="absolute inset-0 object-cover w-full h-full rounded-[32px]"
        >
          <source src={`${videoUrl}#t=0.003`} type="video/mp4" />
        </video>
      </div>
      {playing ? (
        <div
          onClick={() => videoHandler("pause")}
          className="absolute top-0 right-0 bottom-0 left-0 flex justify-center items-center"
        />
      ) : (
        <div
          onClick={() => videoHandler("play")}
          className="absolute top-0 right-0 bottom-0 left-0 flex justify-center items-center"
        >
          <div className="w-[50px] h-[50px] bg-gray-100 rounded-full flex justify-center items-center">
            <PlayIcon />
          </div>
        </div>
      )}
    </div>
  );
};

export default VideoUI;
