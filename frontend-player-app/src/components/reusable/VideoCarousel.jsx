import Carousel from "react-material-ui-carousel";
import { ChevronLeft, ChevronRight } from "../svgIcons";
import AnnoucementPlayerVideo from "../pageComponents/Announcement/AnnoucementPlayerVideo";
import React, { useState } from "react";
import { VIDEO_STATUS } from "../../utils/constants";
import LoadingFeedVideo from "../pageComponents/Feeds/LoadingFeedVideo";

const VideoCarousel = ({ voteSubmittedAssets }) => {
  const [togglePlay, setTogglePlay] = useState(0);

  return (
    <div className="relative">
      <Carousel
        autoPlay={false}
        animation="slide"
        NextIcon={<ChevronRight />}
        PrevIcon={<ChevronLeft />}
        onChange={(index) => setTogglePlay(index)}
        fullHeightHover={false}
        navButtonsProps={{
          style: {
            backgroundColor: "transparent",
            marginTop: "40px",
            border: "none",
            outline: "none"
          }
        }}
        navButtonsAlwaysVisible
        indicators={true}
        indicatorIconButtonProps={{
          style: {
            padding: "2px",
            color: "rgba(82, 255, 0, 0.4)",
            border: "none",
            outline: "none",
            width: "10px",
            height: "10px",
            margin: "0 4px"
          }
        }}
        activeIndicatorIconButtonProps={{
          style: {
            backgroundColor: "#52FF00",
            padding: "2px",
            width: "10px",
            height: "10px",
            margin: "0 4px"
          }
        }}
        indicatorContainerProps={{
          className: "absolute bottom-4 left-0 right-0 z-10"
        }}
      >
        {[...voteSubmittedAssets]
          .sort((a, b) => a.orderIndex - b.orderIndex)
          .map((item, i) => {            
            if (
              item.assetType === "VIDEO" &&
              item.videoStatus === VIDEO_STATUS.SUCCESSFUL
            ) {
              return (
                <AnnoucementPlayerVideo
                  index={i}
                  currentViewIndex={togglePlay}
                  key={i}
                  streamUrl={item.streamUrl}
                  assetUrl={item.assetUrl}
                />
              );
            }

            if (
              item.assetType === "VIDEO" &&
              item.videoStatus === VIDEO_STATUS.PROCESSING
            ) {
              return <LoadingFeedVideo />;
            }

            if (
              item.assetType === "VIDEO" &&
              item.videoStatus === VIDEO_STATUS.FAILED
            ) {
              return (
                <LoadingFeedVideo
                  iconSrc="/images/Green-Uploadfailedicons.png"
                  content1="Unfortunately, there was an error "
                  content2="uploading this highlight. Please try again."
                />
              );
            }

            // HANDLE LEGACY VIDEOS
            if (item.assetType === "VIDEO" && !VIDEO_STATUS[item.videoStatus]) {
              return (
                <AnnoucementPlayerVideo
                index={i}
                currentViewIndex={togglePlay}
                key={i}
                streamUrl={item.streamUrl}
                assetUrl={item.assetUrl}
              />
              );
            }
          })}
      </Carousel>
    </div>
  );
};

export default React.memo(VideoCarousel);
