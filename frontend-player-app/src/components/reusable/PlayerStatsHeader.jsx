import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { Await, useNavigate } from "react-router-dom";
import { dispatch } from "../../redux/store";
import { DropDownIcon } from "../svgIcons";
import BackButton from "./buttons/BackButton";
import Header from "./Header";

const LeagueStatsHeader = ({ from }) => {
  const navigate = useNavigate();
  const { singleTeam, currentTeamId, currentClubId, clubData } = useSelector(
    ({
      team: {
        singleTeam,
        allTeams: { currentTeamId, currentClubId },
        clubData,
      },
    }) => ({
      singleTeam,
      currentTeamId,
      currentClubId,
      clubData,
    })
  );

  const [selectedSeasonOption, setSelectedSeason] = useState("");

  const handleFilter = async (seasonData) => {
    const filteredSeason = await singleTeam.seasonsStats.find(
      (data) => data.seasonId === seasonData.seasonId
    );

    if (currentTeamId && currentClubId) {
      dispatch.seasons.setSelectedSeasonData({
        data: filteredSeason,
      });
      setSelectedSeason(seasonData.season);
    } else {
      navigate("/team-select");
    }
  };

  useEffect(() => {
    if (currentTeamId && currentClubId) {
      dispatch.seasons.setSelectedSeasonData({
        data: singleTeam?.seasonsStats?.[0],
      });
      setSelectedSeason(singleTeam?.seasonsStats?.[0]?.season);
    } else {
      navigate("/team-select");
    }
  }, []);

  return (
    <div className="bg-[#F1F1F1] h-[30vh] px-[15px]">
      <Header bgColor={"#F1F1F1"}>
        <BackButton from={from} data={{ tab: "player stats" }} />
      </Header>
      <div>
        <div className="flex items-center justify-between">
          <p className="uppercase text-[16px] font-favela-bold font-bold">
            {clubData?.clubName}
          </p>
          <img 
            src={clubData?.clubLogoUrl}
            alt="logo"
            className="w-[78px] h-[78px] rounded-full object-cover border-[2px]"
          />
        </div>
        <div className="dropdown">
          <label tabIndex={0} className="">
            <div className="flex items-center gap-4 mt-[20px]">
              <p className="text-[16px] font-normal justify-self-center">
                {selectedSeasonOption}
              </p>
              <DropDownIcon w={15} h={8} color={"black"} />
            </div>
          </label>
          <ul
            tabIndex={0}
            className="dropdown-content menu p-2 shadow bg-base-100 rounded-box w-52"
          >
            {singleTeam?.seasonsStats?.map((data, i) => (
              <li
                key={i}
                onClick={() => handleFilter(data)}
                className="active:bg-white focus:bg-white"
              >
                <a className="active:bg-white focus:bg-white">{data.season}</a>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
};

export default LeagueStatsHeader;
