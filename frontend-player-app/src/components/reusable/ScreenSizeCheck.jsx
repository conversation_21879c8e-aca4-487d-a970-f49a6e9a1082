import React, { useState, useEffect } from "react";
import Header from "./Header";
import Logo from "./Logo";
import { useSelector } from "react-redux";
import { dispatch } from "../../redux/store";

const ScreenSizeCheck = ({ children }) => {
  const [width, setWidth] = useState(window.innerWidth);
  const [currentUserDetails, setCurrentUserDetails] = useState(null);
  const [currentPathname, setCurrentPath] = useState("");

  const urlSearchParams = new URLSearchParams(window.location.search);
  const allowedPaths = ['/profile', '/settings', '/settings/security', '/edit-profile', '/profile/physical']

  const handleWindowSizeChange = () => {
    setWidth(window.innerWidth);
  };

  const { userInfo } = useSelector((state) => state.auth.authUser);
  const { loggedInUser } = useSelector(({ user }) => ({
    loggedInUser: user.data
  }));

  const getProfile = async (userId) => {
    if (userId) {
      const data = await dispatch.user.getUserProfileForGuest({
        id: userId
      });
      setCurrentUserDetails(data);
      return;
    }

    if (!userId && !loggedInUser && userInfo.id) {
      await dispatch.user.getUserProfile({
        id: userInfo?.id
      });
      return;
    } else {
      setCurrentUserDetails(loggedInUser);
    }
  };

  useEffect(() => {
    window.addEventListener("resize", handleWindowSizeChange);

    return () => {
      window.removeEventListener("resize", handleWindowSizeChange);
    };
  }, []);

  useEffect(() => {
    const path = window.location.pathname;
    setCurrentPath(path);

    if (path === "/profile") {
      getProfile(urlSearchParams.get("id"));
    }
  }, [window.location.pathname, urlSearchParams.get("id")]);

  const isAllowedToView = width <= 768 || allowedPaths.includes(currentPathname);

  return isAllowedToView ? (
    <>{children}</>
  ) : (
    <div className="font-favela-bold h-[80vh] text-5xl leading-snug tracking-widest">
      <Header>
        <div />
        <Logo light={false} shouldNavigate={false} />
        <div />
      </Header>
      <div className="w-full h-full bg-gray-50 flex justify-center items-center">
        <div className="text-center max-w-xl">
          This Application is only available for Mobile Devices. Please visit
          from a Mobile Device.
        </div>
      </div>
    </div>
  );
};
export default ScreenSizeCheck;
