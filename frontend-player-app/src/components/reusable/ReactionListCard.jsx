import React from "react";
import { useNavigate } from "react-router-dom";

const ReactionListCard = ({ reaction, userId }) => {
  const navigate = useNavigate();

  const handleOpenProfile = (id) => {
    if (id) {
      navigate({ pathname: "/profile", search: `?id=${id}` });
    } else {
      navigate("/login");
    }
  };

  return (
    <div
      onClick={() => handleOpenProfile(userId)}
      className="flex mt-2 hover:bg-[#f4f3f3a1] items-center justify-between"
    >
      <div className="font-medium font-poppins">@{reaction.user.firstName}</div>
      <div className="p-[1px] rounded-full bg-white">
        <img
          className="w-7 h-7"
          src={reaction.emoji}
          alt={reaction.emojiName}
        />
      </div>
    </div>
  );
};

export default React.memo(ReactionListCard);
