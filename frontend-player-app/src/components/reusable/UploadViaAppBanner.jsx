import React, { useEffect } from "react";
import { BigButtons, MediumButtons, SmallButtons } from "./buttons/Buttons";
import { X } from "lucide-react";

const UploadViaAppBanner = ({ onClose }) => {

  const openIntent = () => {
    const userAgent = navigator.userAgent || navigator.vendor || window.opera;
    const link = "https://playerapp.co/Feed"; // Matches AndroidManifest.xml
    const fallbackUrl =
      "https://play.google.com/store/apps/details?id=com.playerapp.playerapp"; // Play Store

    if (/android/i.test(userAgent)) {
      window.location.href = link;

      // If app isn't installed, redirect to Play Store after a delay
      setTimeout(() => {
        window.location.href = fallbackUrl;
      }, 100);
    } else if (/iPad|iPhone|iPod/i.test(userAgent)) {
      window.location.href = link;

      const storeLink = "https://apps.apple.com/gb/app/player-the-footballers-app/id6503473876"
      setTimeout(() => {
        window.location.href = storeLink;
      }, 100);
    } else {
      window.location.href = "https://playerapp.co"; // Default to website
    }
  };

  const onDeepLinkClick = () => {
    // Open Mobile Application Deep Link URL
    openIntent("Feed");
  };

  return (
    <div
      onClick={onClose}
      className="h-screen w-screen z-[1000] fixed bottom-0 left-0 flex flex-col justify-end bg-[#00000066] overflow-hidden"
    >
      <div
        onClick={(e) => e.stopPropagation()}
        className="h-[92px] p-5 bg-primary-green flex flex-row gap-3 items-center"
      >
        <div className="flex flex-row gap-5 w-full items-center justify-center">
          <div onClick={onClose} className="h-8 w-8">
            <X height={28} width={28} className="bg-white text-black rounded-full p-1" />
          </div>
          <h6 className="font-semibold text-base h-[44px] text-black">
            Upload your video on the <span className="font-bold">PLAYER</span>{" "}
            app.
          </h6>
        </div>
        <MediumButtons
          onClick={onDeepLinkClick}
          black
          className={"text-white h-[44px] text-base w-[145px]"}
          label={"Open"}
        />
      </div>
    </div>
  );
};

export default UploadViaAppBanner;
