import { useEffect } from "react";

export default function ZoomControlWrapper({ children }) {
    const isIos = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;

    const disableZoom = () => {
        const metaTag = document.querySelector('meta[name="viewport"]');
        if (metaTag) {
            metaTag.setAttribute(
                "content",
                "width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
            );
        }
    };

    const enableZoom = () => {
        const metaTag = document.querySelector('meta[name="viewport"]');
        if (metaTag) {
            metaTag.setAttribute(
                "content",
                "width=device-width, initial-scale=1.0"
            );
        }
    };

    useEffect(() => {
        if (isIos) {
            disableZoom();
        }
        return () => {
            if (isIos) {
                enableZoom();
            }
        };
    }, []);

    return <>{children}</>;
}