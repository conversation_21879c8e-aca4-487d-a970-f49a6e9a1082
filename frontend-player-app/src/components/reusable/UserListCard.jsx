import { useSelector } from "react-redux";
import { dispatch } from "../../redux/store";
import React, { useEffect, useRef } from "react";

const UserListCard = ({ closeModal, query, onSelect, isMentioning }) => {
  const modalRef = useRef(null);

  // Manage loading state
  const gettingUser = useSelector(
    ({ loading }) => loading.effects.user?.userSearch
  );

  // Fetch user list from redux state
  const { userList } = useSelector(({ user }) => ({
    userList: user?.usersByProjection || []
  }));

  const fetchAllUserData = async () => {
    await dispatch.user.userSearchByProjection();
  };

  useEffect(() => {
    if (!userList?.length) {
      fetchAllUserData();
    }
  }, [userList]);

  // Handle click outside the modal to close it
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        closeModal();
      }
    };

    // Add event listener when the component mounts
    document.addEventListener("mousedown", handleClickOutside);

    // Cleanup the event listener when the component unmounts
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [closeModal]);

  // Filter the user list based on the query, if userList is not empty
  const filteredUserList = userList.length
    ? userList.filter(
        (mention) =>
          mention.firstName?.toLowerCase().includes(query.toLowerCase()) ||
          mention.lastName?.toLowerCase().includes(query.toLowerCase()) ||
          mention.username?.toLowerCase().includes(query.toLowerCase())
      )
    : [];

  return (
    isMentioning &&
    filteredUserList.length > 0 && (
      <div className="absolute -top-[300px] bg-gray-800 bg-opacity-50 flex justify-center items-center">
        <div
          ref={modalRef}
          className="bg-white p-6 rounded shadow-lg w-[300px] max-h-[300px] overflow-y-auto"
        >
          {/* Display loading state */}
          {gettingUser && (
            <p className="text-base font-semibold">Fetching users...</p>
          )}

          <ul className="space-y-3 p-2">
            {filteredUserList.map((mention, index) => (
              <li
                key={index}
                onClick={() => onSelect(mention)}
                className="flex items-center space-x-3 p-2 hover:bg-gray-700 rounded-lg cursor-pointer"
              >
                <img
                  src={mention.photoUrl || "/images/profile.png"}
                  alt={`${mention?.firstName} ${mention?.lastName}`}
                  className="w-10 h-10 rounded-full"
                />
                <div>
                  <p className="text-base font-semibold">
                    {mention?.firstName} {mention?.lastName}
                  </p>
                  <p className="text-sm text-gray-400">
                    @{mention.firstName}
                    {mention?.lastName}
                  </p>
                </div>
              </li>
            ))}
          </ul>
        </div>
      </div>
    )
  );
};
export default React.memo(UserListCard);
