import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import {useSelector} from "react-redux";
import dayjs from "dayjs";


const DatePickerComponent = ({setValue, showLabel = true}) => {
    const { userInfo } = useSelector((state) => state.auth.authUser);
    const handleDateChange = (newValue) => {
        setValue(newValue.$d);
    };

    return (
        <DatePicker
            label={"Date of Birth"}
            defaultValue={dayjs(userInfo?.birthday)}
            onChange={handleDateChange}
            slotProps={{
                textField: {
                    variant: "standard",
                     color: "black",
                    sx: {
                        my: -0.6,
                        "& .MuiInputBase-root": {
                            color: "black",
                            border: "none",
                            "&:after": {
                                border: "none"
                            },
                            "&:before": {
                                border: "none"
                            }
                        },
                        
                    },
                    InputLabelProps: {
                        variant: 'standard',
                        style: {
                            display: showLabel ? 'block' : 'none',
                            marginLeft: 0,
                        }
                    }
                },
            }}
            format="DD-MM-YYYY"
        />
    );
};

export default DatePickerComponent;