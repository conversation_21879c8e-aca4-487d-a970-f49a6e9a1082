import React from "react";
import { useSelector } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import { HomeIcon } from "../svgIcons";
import SearchIcon from "../svgIcons/SearchIcon";
import ProfileNavIcon from "../svgIcons/ProfileNav";

const userTypes = ["PLAYER", "ADMIN", "MANAGER"];

const BottomNavigation = () => {
  const location = useLocation();
  const navigate = useNavigate();

  const {
    auth: { authUser },
    user
  } = useSelector(({ auth, user }) => ({
    auth,
    user
  }));

  const navToMyTeams = async () => {
    if (!authUser?.userInfo?.id) {
      navigate("/login");
      return;
    }
    if (user?.data?.cludId?.length > 4 || user?.data?.teamId > 4) {
      navigate(
        "/team-dashboard?teamId=" +
          user.data.teamId +
          "&clubId=" +
          user.data.cludId
      );
    } else {
      navigate("/no-club-error");
    }
  };

  return (
    <div className="fixed md:hidden flex justify-around items-center h-[70px] bottom-0 right-0 left-0 bg-white z-50">
      <button
        onClick={() => navigate("/")}
        className="font-favela bg-transparent hover:border-none hover:outline-none focus:outline-none"
      >
        <HomeIcon opacity={location.pathname === "/" ? "1" : "0.5"} />
      </button>

      <button
        onClick={() => navigate("/user/search")}
        className={`cursor-pointer hover:border-none bg-transparent hover:outline-none focus:outline-none ${
          location.pathname === "/user/search" ? "opacity-100" : "opacity-50"
        }`}
      >
        <SearchIcon />
      </button>

      {/* <div
        onClick={navToMyTeams}
        className={`w-[24px] hover:border-none hover:outline-none focus:outline-none h-[24px] border-2 border-black rounded-full cursor-pointer overflow-hidden ${
          location.pathname === "/team-dashboard" ? "opacity-100" : "opacity-50"
        }`}
      >
        <img
          src="/images/teams.png"
          alt="profile picture"
          className="w-full h-full object-cover"
        />
      </div> */}

      <button
        onClick={() => navigate("/profile")}
        className="font-favela bg-transparent hover:outline-none hover:border-none focus:outline-none"
      >
        <div
          className={`w-[20px] h-[20px] border-2 border-black rounded-full cursor-pointer overflow-hidden ${
            location.pathname === "/profile" ? "opacity-100" : "opacity-50"
          }`}
        >
          <img
            src="/images/profile.png"
            alt="profile picture"
            className="w-full h-full object-cover"
          />
        </div>
      </button>
    </div>
  );
};

export default BottomNavigation;
