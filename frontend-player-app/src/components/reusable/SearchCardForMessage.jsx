import React from "react";
import { useNavigate } from "react-router-dom";

const SearchCardForMessage = ({ user, setUser }) => {
  const navigate = useNavigate();

  return (
    <div className="flex items-center justify-between w-full py-1 mb-3">
      <div
        className="flex items-center gap-5"
        onClick={() => {
          setUser(user);
          navigate(`/user/message?recipientId=${user.id}`);
        }}
      >
        <img
          src={`${
            user?.photoUrl?.length > 5 ? user?.photoUrl : "/images/profile.png"
          }`}
          alt="profile pix"
          className="w-[46px] h-[46px] object-cover rounded-full"
        />
        <div>
          <p className="text-[14px] font-medium">
            @{user.firstName} {user.lastName}
          </p>
          <p className="text-[14px] font-medium opacity-50">
            {(user.teamName !== "N/A" && user.teamName) || user.clubName}
          </p>
        </div>
      </div>
    </div>
  );
};

export default SearchCardForMessage;
