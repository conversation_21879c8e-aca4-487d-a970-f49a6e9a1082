import React, { useEffect, useRef, useState } from "react";

const Videos = ({ setCurrentVideo, className, url, ...others }) => {
  const videoRef = useRef(null);
// review this implementation when audio control is added
  // useEffect(() => {
  //   if (others.inView) {
  //     setCurrentVideo({ Url: url, playing: true });
  //     videoRef.current.play();
  //   } else {
  //     setCurrentVideo({ Url: url, playing: false });
  //     videoRef.current.pause();
  //   }
  // }, [others.inView]);

  return (
    <div ref={others.observe}>
       <video ref={videoRef} className={className}>
        <source src={`${url}#t=0.003`} type="video/mp4" />
      </video>
    </div>
  );
};

export default Videos;
