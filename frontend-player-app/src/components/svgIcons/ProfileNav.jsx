import React from "react";

const ProfileNavIcon = ({ opacity }) => {
  return (
    <svg
      width="24"
      height="18"
      viewBox="0 0 24 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g>
        <path
          opacity={opacity}
          id="Vector"
          d="M15.5206 13.2276C16.235 12.5281 16.7248 11.6317 16.9276 10.6527C17.1304 9.67376 17.0369 8.65656 16.6592 7.73089C16.2815 6.80523 15.6366 6.01306 14.8068 5.45543C13.977 4.8978 12.9999 4.59999 12.0001 4.59999C11.0004 4.59999 10.0232 4.8978 9.19344 5.45543C8.36363 6.01306 7.71874 6.80523 7.34102 7.73089C6.9633 8.65656 6.86987 9.67376 7.07266 10.6527C7.27545 11.6317 7.76526 12.5281 8.47961 13.2276C7.27012 13.8705 6.26455 14.8389 5.5765 16.0233C5.42915 16.2895 5.3922 16.603 5.47361 16.8962C5.55503 17.1894 5.74829 17.439 6.01182 17.5911C6.27534 17.7433 6.58808 17.786 6.88274 17.7099C7.17739 17.6339 7.43042 17.4452 7.58739 17.1845C8.04115 16.4178 8.6868 15.7826 9.46073 15.3413C10.2347 14.9 11.1102 14.668 12.0011 14.668C12.892 14.668 13.7675 14.9 14.5414 15.3413C15.3154 15.7826 15.961 16.4178 16.4148 17.1845C16.4895 17.3195 16.5903 17.4382 16.7114 17.5338C16.8326 17.6294 16.9715 17.6999 17.1202 17.7411C17.2688 17.7824 17.4242 17.7936 17.5773 17.7742C17.7303 17.7547 17.8779 17.705 18.0115 17.6278C18.1451 17.5507 18.262 17.4477 18.3554 17.3248C18.4487 17.202 18.5167 17.0618 18.5552 16.9124C18.5938 16.763 18.6022 16.6075 18.5799 16.4548C18.5577 16.3021 18.5052 16.1554 18.4257 16.0233C17.7371 14.8386 16.7308 13.8702 15.5206 13.2276ZM9.29055 9.63738C9.29055 9.10147 9.44946 8.57761 9.74719 8.13202C10.0449 7.68643 10.4681 7.33914 10.9632 7.13406C11.4583 6.92898 12.0031 6.87532 12.5287 6.97987C13.0543 7.08442 13.5371 7.34248 13.9161 7.72142C14.295 8.10036 14.5531 8.58316 14.6576 9.10877C14.7622 9.63437 14.7085 10.1792 14.5034 10.6743C14.2984 11.1694 13.9511 11.5926 13.5055 11.8903C13.0599 12.188 12.536 12.3469 12.0001 12.3469C11.2815 12.3469 10.5923 12.0615 10.0842 11.5533C9.57602 11.0452 9.29055 10.356 9.29055 9.63738ZM23.5361 10.5654C23.4141 10.6571 23.2753 10.7239 23.1275 10.7619C22.9797 10.7999 22.8259 10.8084 22.6748 10.7869C22.5238 10.7654 22.3784 10.7144 22.2471 10.6367C22.1157 10.559 22.001 10.4562 21.9094 10.3341C21.1033 9.26094 19.9885 8.43839 19.0711 8.24001C18.8272 8.18725 18.6067 8.05731 18.4425 7.86944C18.2782 7.68157 18.1788 7.44576 18.159 7.19697C18.1392 6.94818 18.2002 6.69964 18.3327 6.48819C18.4653 6.27673 18.6625 6.11361 18.895 6.023C19.1845 5.90999 19.4426 5.7289 19.6473 5.49507C19.8521 5.26124 19.9975 4.98154 20.0713 4.67962C20.1451 4.37771 20.1451 4.06246 20.0713 3.76055C19.9975 3.45864 19.8521 3.17893 19.6473 2.9451C19.4426 2.71127 19.1845 2.53019 18.895 2.41717C18.6055 2.30415 18.293 2.26253 17.984 2.29581C17.675 2.32909 17.3785 2.4363 17.1197 2.60838C16.8609 2.78046 16.6473 3.01233 16.497 3.2844C16.4258 3.42227 16.3276 3.54446 16.2083 3.64375C16.089 3.74304 15.9511 3.8174 15.8026 3.86246C15.654 3.90752 15.498 3.92234 15.3437 3.90607C15.1893 3.88979 15.0398 3.84275 14.9039 3.7677C14.7681 3.69266 14.6487 3.59115 14.5527 3.46917C14.4568 3.34719 14.3862 3.20721 14.3453 3.0575C14.3044 2.9078 14.2939 2.75141 14.3144 2.59758C14.3349 2.44374 14.3861 2.29559 14.4649 2.16186C14.7522 1.65377 15.141 1.21018 15.607 0.858661C16.073 0.507138 16.6063 0.255187 17.1738 0.118457C17.7412 -0.0182717 18.3308 -0.0368587 18.9057 0.0638514C19.4807 0.164561 20.0288 0.382417 20.516 0.703887C21.0033 1.02536 21.4192 1.44357 21.738 1.93256C22.0568 2.42154 22.2716 2.97085 22.3691 3.54636C22.4667 4.12187 22.4449 4.7113 22.305 5.27802C22.1652 5.84474 21.9103 6.37665 21.5562 6.84071C22.4082 7.40829 23.1562 8.11831 23.7674 8.93966C23.9523 9.18593 24.0318 9.49555 23.9884 9.80043C23.945 10.1053 23.7824 10.3805 23.5361 10.5654ZM4.9291 8.24292C4.01172 8.44129 2.89693 9.26384 2.09083 10.338C1.9054 10.5843 1.62974 10.7468 1.32449 10.7898C1.01924 10.8328 0.709407 10.7528 0.46315 10.5673C0.216893 10.3819 0.0543846 10.1062 0.0113739 9.801C-0.0316368 9.49575 0.0483737 9.18592 0.233804 8.93966C0.845236 8.11855 1.59318 7.40857 2.44501 6.84071C2.09093 6.37665 1.83605 5.84474 1.6962 5.27802C1.55635 4.7113 1.53452 4.12187 1.63206 3.54636C1.72961 2.97085 1.94444 2.42154 2.26323 1.93256C2.58201 1.44357 2.99793 1.02536 3.48516 0.703887C3.97238 0.382417 4.5205 0.164561 5.09547 0.0638514C5.67043 -0.0368587 6.25997 -0.0182717 6.82745 0.118457C7.39493 0.255187 7.92823 0.507138 8.39424 0.858661C8.86024 1.21018 9.24899 1.65377 9.53634 2.16186C9.61511 2.29559 9.66627 2.44374 9.68681 2.59758C9.70734 2.75141 9.69683 2.9078 9.6559 3.0575C9.61497 3.20721 9.54444 3.34719 9.44849 3.46917C9.35254 3.59115 9.2331 3.69266 9.09725 3.7677C8.9614 3.84275 8.81189 3.88979 8.65755 3.90607C8.50321 3.92234 8.34717 3.90752 8.19865 3.86246C8.05014 3.8174 7.91216 3.74304 7.79288 3.64375C7.67359 3.54446 7.57542 3.42227 7.50416 3.2844C7.3539 3.01233 7.14033 2.78046 6.88151 2.60838C6.62269 2.4363 6.32624 2.32909 6.01722 2.29581C5.70821 2.26253 5.39572 2.30415 5.1062 2.41717C4.81667 2.53019 4.55862 2.71127 4.35387 2.9451C4.14913 3.17893 4.00371 3.45864 3.92991 3.76055C3.85611 4.06246 3.85611 4.37771 3.92991 4.67962C4.0037 4.98154 4.14913 5.26124 4.35387 5.49507C4.55862 5.7289 4.81667 5.90999 5.10619 6.023C5.33874 6.11361 5.5359 6.27673 5.66847 6.48819C5.80104 6.69964 5.86195 6.94818 5.84219 7.19697C5.82243 7.44576 5.72303 7.68157 5.55874 7.86944C5.39445 8.05731 5.17401 8.18725 4.93007 8.24001L4.9291 8.24292Z"
          fill="black"
        />
      </g>
    </svg>
  );
};

export default ProfileNavIcon;
