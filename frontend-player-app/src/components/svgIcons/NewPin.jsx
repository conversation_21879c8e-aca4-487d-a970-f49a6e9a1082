export default function NewPin() {
  return (
    <svg
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M23.7312 1.67622L23.7312 1.67624L30.3238 8.27046C30.3238 8.27047 30.3238 8.27048 30.3238 8.27049C30.6892 8.63585 30.9595 9.08519 31.111 9.57919C31.2625 10.0732 31.2907 10.5968 31.193 11.1042C31.0953 11.6116 30.8747 12.0874 30.5506 12.4898C30.2264 12.8922 29.8086 13.2091 29.3336 13.4126L29.3336 13.4127L23.3339 15.983C23.5481 17.2337 23.4675 18.5182 23.0975 19.7339C22.7077 21.0143 22.0087 22.1791 21.0622 23.1254L21.0622 23.1254M23.7312 1.67622L20.8854 22.9486L21.0622 23.1254M23.7312 1.67622C23.3658 1.31082 22.9165 1.04053 22.4225 0.889002C21.9285 0.737475 21.4048 0.709332 20.8974 0.807035C20.39 0.904737 19.9143 1.12531 19.5119 1.44944C19.1094 1.77358 18.7926 2.1914 18.589 2.66636L16.017 8.66772C14.7664 8.45334 13.4821 8.53362 12.2664 8.90341C10.986 9.29289 9.82114 9.99156 8.87464 10.9378L8.87461 10.9378L6.02925 13.7831L6.02914 13.7833C5.74862 14.0641 5.59106 14.4449 5.59106 14.8418C5.59106 15.2388 5.74862 15.6195 6.02914 15.9004L6.02922 15.9005L10.0041 19.8769M23.7312 1.67622L10.0041 19.8769M21.0622 23.1254L18.2169 25.9707L21.0622 23.1254ZM16.2746 25.794L16.0978 25.9707C16.2369 26.1099 16.4021 26.2203 16.5839 26.2957C16.7657 26.371 16.9605 26.4098 17.1573 26.4098C17.3541 26.4098 17.549 26.371 17.7308 26.2957C17.9126 26.2203 18.0778 26.1099 18.2169 25.9707L16.6796 26.0647C16.5281 26.0019 16.3905 25.9099 16.2746 25.794ZM16.2746 25.794L16.0978 25.9707M16.2746 25.794L12.2998 21.8192L12.123 21.9959M16.0978 25.9707L10.3576 19.8769L1.3972 28.839C1.27459 28.9533 1.17625 29.091 1.10804 29.2441M16.0978 25.9707L12.123 21.9959M16.0978 25.9707L10.0041 19.8769M12.123 21.9959L11.9463 21.8192L3.16099 30.6028M12.123 21.9959L3.34071 30.7766M3.16099 30.6028C3.04674 30.7254 2.90897 30.8238 2.75588 30.892C2.6028 30.9602 2.43755 30.9969 2.26998 30.9998C2.10242 31.0028 1.93597 30.9719 1.78058 30.9092M3.16099 30.6028L3.33775 30.7796L3.34071 30.7766M3.16099 30.6028L3.3439 30.7732C3.34284 30.7744 3.34178 30.7755 3.34071 30.7766M1.78058 30.9092C1.62519 30.8464 1.48403 30.753 1.36552 30.6345C1.24702 30.516 1.15359 30.3748 1.09083 30.2194C1.02806 30.064 0.997238 29.8976 1.00019 29.73C1.00315 29.5625 1.03983 29.3972 1.10804 29.2441M1.78058 30.9092L1.68695 31.141C1.50043 31.0656 1.33099 30.9535 1.18874 30.8113C1.0465 30.669 0.934363 30.4996 0.859023 30.3131C0.783683 30.1265 0.746684 29.9267 0.750233 29.7256C0.753782 29.5245 0.797806 29.3261 0.879679 29.1424L1.10804 29.2441M1.78058 30.9092L1.68695 31.141C1.87347 31.2163 2.07326 31.2533 2.27439 31.2498C2.47553 31.2462 2.67388 31.2022 2.85763 31.1203C3.03996 31.0391 3.2042 30.9222 3.34071 30.7766M1.10804 29.2441L0.879679 29.1424C0.960927 28.96 1.0778 28.7958 1.2234 28.6593L10.0041 19.8769M21.3415 3.84668L21.3416 3.84662C21.3523 3.82165 21.3689 3.79968 21.3901 3.78264C21.4112 3.76561 21.4362 3.75403 21.4629 3.74891C21.4896 3.7438 21.5171 3.74531 21.5431 3.75332C21.569 3.76132 21.5926 3.77558 21.6118 3.79483L21.6122 3.79524L28.2065 10.3878L28.2065 10.3879C28.2258 10.4071 28.24 10.4308 28.2479 10.4567C28.2559 10.4827 28.2574 10.5103 28.2522 10.537L28.4976 10.5846L28.2522 10.537C28.247 10.5637 28.2353 10.5887 28.2182 10.6098L28.4125 10.7671L28.2182 10.6098C28.2011 10.6309 28.1791 10.6475 28.1541 10.6581L28.1533 10.6585L20.9733 13.7368L20.973 13.7369C20.6232 13.8874 20.3438 14.1655 20.1917 14.5146C20.0396 14.8637 20.0261 15.2577 20.1541 15.6164L20.1542 15.6167C20.4842 16.5374 20.5455 17.533 20.3311 18.4872C20.1167 19.4415 19.6354 20.3151 18.9433 21.0063L18.9432 21.0064L17.1565 22.7931L9.20693 14.8435L10.9936 13.0568L10.9937 13.0567C11.685 12.3649 12.5587 11.8839 13.5129 11.6698C14.4672 11.4557 15.4627 11.5173 16.3832 11.8474L16.3833 11.8474C16.7423 11.976 17.1367 11.9629 17.4863 11.8107C17.8359 11.6586 18.1143 11.3788 18.2648 11.0286L18.2649 11.0283L21.3415 3.84668Z"
        fill="#67FF5A"
        stroke="#67FF5A"
        strokeWidth="0.5"
      />
    </svg>
  );
}
