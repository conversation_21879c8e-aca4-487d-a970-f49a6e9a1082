import React from "react";

const DeleteIcon = ({fill1 = '#808080', fill2 = 'white', className='', width=20, height=20}) => {
  return (
    <svg
      width={width}
      height={width}
      viewBox="0 0 20 20"
      fill="none"
      className={className}
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_2495_10802)">
        <path
          d="M12.539 14.4446H7.46119C7.3543 14.4421 7.24895 14.4185 7.15117 14.3753C7.05338 14.332 6.96508 14.27 6.89131 14.1926C6.81753 14.1152 6.75972 14.024 6.72119 13.9243C6.68266 13.8245 6.66416 13.7182 6.66674 13.6113V8.11963H7.2223V13.6113C7.21965 13.6452 7.22373 13.6794 7.23432 13.7117C7.2449 13.7441 7.26178 13.774 7.28398 13.7999C7.30618 13.8257 7.33327 13.8468 7.36368 13.8622C7.39409 13.8775 7.42723 13.8866 7.46119 13.8891H12.539C12.5729 13.8866 12.6061 13.8775 12.6365 13.8622C12.6669 13.8468 12.694 13.8257 12.7162 13.7999C12.7384 13.774 12.7552 13.7441 12.7658 13.7117C12.7764 13.6794 12.7805 13.6452 12.7779 13.6113V8.11963H13.3334V13.6113C13.336 13.7182 13.3175 13.8245 13.279 13.9243C13.2404 14.024 13.1826 14.1152 13.1088 14.1926C13.0351 14.27 12.9468 14.332 12.849 14.3753C12.7512 14.4185 12.6459 14.4421 12.539 14.4446Z"
          fill={fill1}
        />
        <path
          d="M13.5502 7.49989H6.38911C6.31543 7.49989 6.24478 7.47063 6.19269 7.41853C6.14059 7.36644 6.11133 7.29578 6.11133 7.22211C6.11133 7.14844 6.14059 7.07779 6.19269 7.0257C6.24478 6.9736 6.31543 6.94434 6.38911 6.94434H13.5502C13.6239 6.94434 13.6945 6.9736 13.7466 7.0257C13.7987 7.07779 13.828 7.14844 13.828 7.22211C13.828 7.29578 13.7987 7.36644 13.7466 7.41853C13.6945 7.47063 13.6239 7.49989 13.5502 7.49989Z"
          fill={fill1}
        />
        <path
          d="M10.8335 8.61133H11.3891V12.778H10.8335V8.61133Z"
          fill={fill1}
        />
        <path
          d="M8.61133 8.61133H9.16688V12.778H8.61133V8.61133Z"
          fill={fill1}
        />
        <path
          d="M11.3891 6.62789H10.8613V6.11122H9.13911V6.62789H8.61133V6.11122C8.61115 5.96857 8.66585 5.83131 8.76411 5.72789C8.86236 5.62446 8.99663 5.5628 9.13911 5.55566H10.8613C11.0038 5.5628 11.1381 5.62446 11.2363 5.72789C11.3346 5.83131 11.3893 5.96857 11.3891 6.11122V6.62789Z"
          fill={fill1}
        />
      </g>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10 19.25C15.1086 19.25 19.25 15.1086 19.25 10C19.25 4.89137 15.1086 0.75 10 0.75C4.89137 0.75 0.75 4.89137 0.75 10C0.75 15.1086 4.89137 19.25 10 19.25ZM10 20C15.5228 20 20 15.5228 20 10C20 4.47715 15.5228 0 10 0C4.47715 0 0 4.47715 0 10C0 15.5228 4.47715 20 10 20Z"
        fill={fill2}
        fillOpacity="0.5"
      />
      <defs>
        <clipPath id="clip0_2495_10802">
          <rect
            width="10"
            height="10"
            fill={fill2}
            transform="translate(5 5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default DeleteIcon;
