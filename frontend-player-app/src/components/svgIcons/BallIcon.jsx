import React from "react";

const BallIcon = ({ opacity }) => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12 0.625C5.72789 0.625 0.625 5.72789 0.625 12C0.625 18.2721 5.72789 23.375 12 23.375C18.2721 23.375 23.375 18.2721 23.375 12C23.375 5.72789 18.2721 0.625 12 0.625ZM19.8203 17.25H17.3473C17.2707 17.2498 17.1955 17.2295 17.1292 17.1911C17.0628 17.1527 17.0078 17.0976 16.9695 17.0312L16.0868 15.5175C16.0287 15.4185 16.0118 15.3007 16.0398 15.1894L16.8524 11.9125C16.8687 11.8465 16.9002 11.7852 16.9443 11.7334C16.9885 11.6817 17.0441 11.6409 17.1067 11.6145L18.6434 10.9637C18.7185 10.9318 18.801 10.9216 18.8816 10.9342C18.9622 10.9467 19.0377 10.9816 19.0995 11.0348L21.3762 12.9937C21.4323 13.042 21.4752 13.1038 21.501 13.1732C21.5267 13.2426 21.5344 13.3175 21.5233 13.3907C21.3347 14.6842 20.8828 15.9253 20.1955 17.0373C20.1566 17.1021 20.1017 17.1558 20.0359 17.1931C19.9702 17.2304 19.8959 17.25 19.8203 17.25ZM5.35656 10.968L6.89328 11.6188C6.95592 11.6453 7.01151 11.6861 7.05566 11.7378C7.0998 11.7895 7.13127 11.8508 7.14758 11.9169L7.96023 15.1937C7.9882 15.3051 7.97131 15.4229 7.9132 15.5219L7.03055 17.0312C6.99223 17.0976 6.93716 17.1527 6.87084 17.1911C6.80452 17.2295 6.72928 17.2498 6.65266 17.25H4.17969C4.10489 17.25 4.03134 17.2309 3.96604 17.1944C3.90075 17.158 3.84589 17.1054 3.80672 17.0416C3.11937 15.9297 2.66745 14.6886 2.47891 13.3951C2.46782 13.3219 2.4755 13.247 2.50123 13.1776C2.52696 13.1081 2.5699 13.0464 2.62602 12.998L4.90266 11.0391C4.96428 10.9864 5.03937 10.9518 5.11953 10.9392C5.19968 10.9267 5.28175 10.9366 5.35656 10.968ZM19.408 6.19711L18.4198 9.00695C18.3999 9.06438 18.3683 9.11703 18.327 9.16157C18.2856 9.20611 18.2355 9.24154 18.1797 9.26562L16.562 9.95086C16.4878 9.98226 16.4063 9.99255 16.3266 9.98059C16.2469 9.96862 16.1721 9.93486 16.1103 9.88305L13.0336 7.30234C12.9844 7.26167 12.9448 7.21073 12.9174 7.1531C12.89 7.09548 12.8755 7.03256 12.875 6.96875V5.13234C12.875 5.06039 12.8928 4.98956 12.9268 4.92612C12.9608 4.86269 13.0098 4.80861 13.0697 4.76867L15.412 3.20789C15.4731 3.16709 15.5435 3.14235 15.6167 3.13594C15.6899 3.12954 15.7636 3.14168 15.8309 3.17125C17.1786 3.75907 18.3746 4.64633 19.3281 5.76562C19.3783 5.82414 19.412 5.89487 19.4261 5.97063C19.4401 6.0464 19.4338 6.12453 19.408 6.19711ZM8.5875 3.20789L10.9303 4.76867C10.9902 4.80861 11.0392 4.86269 11.0732 4.92612C11.1072 4.98956 11.125 5.06039 11.125 5.13234V6.96875C11.125 7.03266 11.111 7.09579 11.084 7.15371C11.057 7.21163 11.0176 7.26292 10.9686 7.30398L7.89188 9.88469C7.83013 9.93651 7.75528 9.97026 7.67557 9.98223C7.59586 9.99419 7.51439 9.98391 7.44016 9.9525L5.82031 9.26562C5.76418 9.24185 5.71363 9.20661 5.67191 9.16216C5.63019 9.11771 5.59822 9.06503 5.57805 9.0075L4.58984 6.19766C4.56416 6.12481 4.55823 6.04647 4.57263 5.97059C4.58704 5.8947 4.62128 5.82399 4.67188 5.76562C5.6259 4.6452 6.82293 3.75715 8.17188 3.16906C8.2389 3.14039 8.31203 3.12893 8.38462 3.13571C8.4572 3.14249 8.52694 3.1673 8.5875 3.20789ZM9.53414 21.1159L8.45516 18.3984C8.43048 18.3367 8.42022 18.2701 8.42515 18.2037C8.43008 18.1374 8.45006 18.0731 8.48359 18.0156L9.3132 16.5938C9.35152 16.5274 9.40659 16.4723 9.47291 16.4339C9.53923 16.3955 9.61447 16.3752 9.69109 16.375H14.3089C14.3855 16.3752 14.4608 16.3955 14.5271 16.4339C14.5934 16.4723 14.6485 16.5274 14.6868 16.5938L15.517 18.0156C15.5506 18.073 15.5707 18.1373 15.5757 18.2037C15.5807 18.27 15.5705 18.3366 15.5459 18.3984L14.4746 21.1148C14.4484 21.1812 14.4064 21.2402 14.3521 21.2866C14.2979 21.333 14.2331 21.3655 14.1634 21.3811C12.7418 21.7074 11.2647 21.7074 9.84312 21.3811C9.77398 21.3652 9.70972 21.3328 9.65589 21.2866C9.60207 21.2404 9.56028 21.1818 9.53414 21.1159Z"
        fill="black"
        fillOpacity={opacity}
      />
    </svg>
  );
};

export default BallIcon;
