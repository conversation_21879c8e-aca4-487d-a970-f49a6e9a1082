import React from "react";

const MessageBtn = () => {
  return (
    <svg
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle cx="16" cy="16" r="16" fill="#52FF00" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.68631 9.93063C7.64453 10.9715 7.64453 12.648 7.64453 16C7.64453 19.352 7.64453 21.0284 8.68631 22.0693C9.7272 23.1111 11.4036 23.1111 14.7556 23.1111H18.3112C21.6632 23.1111 23.3396 23.1111 24.3805 22.0693C25.4223 21.0284 25.4223 19.352 25.4223 16C25.4223 12.648 25.4223 10.9715 24.3805 9.93063C23.3396 8.88885 21.6632 8.88885 18.3112 8.88885H14.7556C11.4036 8.88885 9.7272 8.88885 8.68631 9.93063ZM22.3788 12.0177C22.4919 12.1536 22.5464 12.3287 22.5304 12.5047C22.5144 12.6808 22.4292 12.8432 22.2934 12.9564L20.3414 14.5831C19.553 15.2409 18.9148 15.7724 18.3503 16.1351C17.7636 16.5129 17.1921 16.7511 16.5334 16.7511C15.8748 16.7511 15.3032 16.512 14.7156 16.1351C14.1521 15.7724 13.5139 15.24 12.7254 14.584L10.7734 12.9573C10.6375 12.8441 10.5521 12.6816 10.536 12.5055C10.5199 12.3294 10.5745 12.1541 10.6876 12.0182C10.8008 11.8823 10.9633 11.7969 11.1394 11.7808C11.3156 11.7647 11.4908 11.8193 11.6268 11.9324L13.5468 13.5315C14.3761 14.2222 14.9512 14.7004 15.4383 15.0133C15.9085 15.3155 16.2276 15.4177 16.5343 15.4177C16.841 15.4177 17.1601 15.3164 17.6303 15.0133C18.1165 14.7004 18.6925 14.2222 19.5219 13.5315L21.441 11.9315C21.5769 11.8185 21.7521 11.7641 21.9282 11.7803C22.1042 11.7965 22.2657 11.8819 22.3788 12.0177Z"
        fill="black"
      />
    </svg>
  );
};

export default MessageBtn;
