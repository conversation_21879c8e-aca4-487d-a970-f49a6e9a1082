const ShareIcon = ({ fill = "white" }) => {
  return (
    <svg
      width="26"
      height="26"
      viewBox="0 0 26 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g opacity="0.6">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M13 24.1C19.1304 24.1 24.1 19.1304 24.1 13C24.1 6.86964 19.1304 1.9 13 1.9C6.86964 1.9 1.9 6.86964 1.9 13C1.9 19.1304 6.86964 24.1 13 24.1ZM13 25C19.6274 25 25 19.6274 25 13C25 6.37258 19.6274 1 13 1C6.37258 1 1 6.37258 1 13C1 19.6274 6.37258 25 13 25Z"
          fill={fill}
          stroke={fill}
        />
        <path
          d="M15.1341 15.6475C14.6612 15.6475 14.2381 15.8403 13.9146 16.1423L9.47811 13.4756C9.50923 13.3278 9.53411 13.18 9.53411 13.0258C9.53411 12.8716 9.50923 12.7238 9.47811 12.576L13.8648 9.93504C14.2008 10.2563 14.6426 10.4555 15.1341 10.4555C16.167 10.4555 17.0008 9.59448 17.0008 8.52781C17.0008 7.46114 16.167 6.6001 15.1341 6.6001C14.1012 6.6001 13.2674 7.46114 13.2674 8.52781C13.2674 8.68203 13.2923 8.82982 13.3234 8.97761L8.93678 11.6186C8.60078 11.2973 8.159 11.0981 7.66745 11.0981C6.63456 11.0981 5.80078 11.9591 5.80078 13.0258C5.80078 14.0925 6.63456 14.9535 7.66745 14.9535C8.159 14.9535 8.60078 14.7543 8.93678 14.433L13.367 17.1061C13.3359 17.2411 13.3172 17.3824 13.3172 17.5238C13.3172 18.5583 14.1323 19.4001 15.1341 19.4001C16.1359 19.4001 16.951 18.5583 16.951 17.5238C16.951 16.4893 16.1359 15.6475 15.1341 15.6475Z"
          fill={fill}
          stroke={fill}
          strokeWidth="0.5"
        />
      </g>
    </svg>
  );
};

export default ShareIcon;
