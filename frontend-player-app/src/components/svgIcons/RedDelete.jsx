import React from "react";

const RedDelete = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.17297 0H11.827C12.0616 0 12.2659 1.18445e-07 12.4584 0.0287181C12.8332 0.0856853 13.1888 0.225089 13.4966 0.435776C13.8045 0.646463 14.0561 0.922585 14.2314 1.24205C14.3222 1.40615 14.3859 1.58974 14.4605 1.8L14.5805 2.14359C14.6633 2.42273 14.8434 2.667 15.0914 2.83651C15.3394 3.00603 15.6406 3.09078 15.9459 3.07692H19.1892C19.4042 3.07692 19.6105 3.15797 19.7625 3.30223C19.9146 3.44648 20 3.64214 20 3.84615C20 4.05017 19.9146 4.24582 19.7625 4.39008C19.6105 4.53434 19.4042 4.61538 19.1892 4.61538H0.810811C0.59577 4.61538 0.389537 4.53434 0.237481 4.39008C0.0854244 4.24582 0 4.05017 0 3.84615C0 3.64214 0.0854244 3.44648 0.237481 3.30223C0.389537 3.15797 0.59577 3.07692 0.810811 3.07692H4.15135C4.44011 3.07035 4.71905 2.97618 4.94727 2.80821C5.17549 2.64024 5.34098 2.40731 5.41946 2.14359L5.54054 1.8C5.61405 1.58974 5.67784 1.40615 5.76757 1.24205C5.94291 0.92246 6.19469 0.646259 6.50276 0.435564C6.81082 0.22487 7.1666 0.0855355 7.54162 0.0287181C7.73405 1.18445e-07 7.93838 0 8.17189 0H8.17297ZM6.76541 3.07692C6.84131 2.93588 6.9046 2.78904 6.95459 2.63795L7.0627 2.33026C7.16108 2.05026 7.18378 1.99385 7.20649 1.95282C7.26485 1.84616 7.34874 1.75396 7.45143 1.68361C7.55412 1.61325 7.67276 1.5667 7.79784 1.54769C7.93877 1.5364 8.08029 1.53332 8.22162 1.53846H11.7784C12.0897 1.53846 12.1546 1.54051 12.2022 1.54872C12.3271 1.56762 12.4457 1.61401 12.5484 1.68418C12.6511 1.75435 12.735 1.84635 12.7935 1.95282C12.8162 1.99385 12.8389 2.05026 12.9373 2.33128L13.0454 2.63897L13.0876 2.75385C13.1297 2.86667 13.1795 2.97333 13.2346 3.07692H6.76541Z"
        fill="#FF5B5B"
      />
      <path
        d="M3.42194 6.35875C3.4076 6.15514 3.3086 5.96528 3.14671 5.83093C2.98482 5.69657 2.77331 5.62874 2.5587 5.64234C2.34409 5.65594 2.14396 5.74986 2.00235 5.90345C1.86073 6.05704 1.78923 6.25771 1.80356 6.46131L2.30518 13.5916C2.39708 14.9064 2.47167 15.969 2.64681 16.8039C2.82951 17.6705 3.1387 18.3946 3.7787 18.9618C4.41762 19.53 5.20032 19.7752 6.12464 19.889C7.01329 19.9998 8.13545 19.9998 9.52572 19.9998H10.476C11.8652 19.9998 12.9884 19.9998 13.8771 19.889C14.8003 19.7752 15.583 19.53 16.223 18.9618C16.8619 18.3946 17.1711 17.6695 17.3538 16.8039C17.529 15.97 17.6025 14.9064 17.6955 13.5916L18.1971 6.46131C18.2114 6.25771 18.1399 6.05704 17.9983 5.90345C17.8567 5.74986 17.6565 5.65594 17.4419 5.64234C17.2273 5.62874 17.0158 5.69657 16.8539 5.83093C16.692 5.96528 16.593 6.15514 16.5787 6.35875L16.0814 13.4357C15.9841 14.8172 15.9149 15.7793 15.7636 16.5023C15.6155 17.2049 15.41 17.5762 15.1149 17.8387C14.8187 18.1013 14.4144 18.2716 13.6663 18.3639C12.8955 18.4593 11.8792 18.4613 10.4187 18.4613H9.58194C8.12248 18.4613 7.10626 18.4593 6.33437 18.3639C5.58626 18.2716 5.18194 18.1013 4.88572 17.8387C4.59059 17.5762 4.38518 17.2049 4.23708 16.5034C4.08572 15.7793 4.01654 14.8172 3.91924 13.4346L3.42194 6.35875Z"
        fill="#FF5B5B"
      />
      <path
        d="M7.21578 8.20891C7.42966 8.18858 7.6433 8.24965 7.80973 8.3787C7.97616 8.50775 8.08177 8.69422 8.10335 8.89712L8.64389 14.0253C8.65972 14.2254 8.59252 14.4234 8.45662 14.5772C8.32073 14.7309 8.12689 14.8282 7.91646 14.8483C7.70602 14.8684 7.49565 14.8096 7.33022 14.6846C7.16478 14.5596 7.05738 14.3782 7.03091 14.1792L6.49037 9.05097C6.46894 8.84805 6.53331 8.64537 6.66934 8.48747C6.80536 8.32957 7.00191 8.22938 7.21578 8.20891ZM12.7833 8.20891C12.997 8.22939 13.1934 8.32943 13.3294 8.48711C13.4654 8.64478 13.5299 8.8472 13.5088 9.04994L12.9682 14.1781C12.9414 14.3768 12.834 14.5578 12.6687 14.6825C12.5035 14.8072 12.2935 14.8658 12.0834 14.8459C11.8733 14.826 11.6797 14.7291 11.5437 14.5759C11.4077 14.4226 11.3401 14.2251 11.3552 14.0253L11.8958 8.89712C11.9174 8.69442 12.0228 8.50811 12.189 8.37909C12.3552 8.25007 12.5696 8.18887 12.7833 8.20891Z"
        fill="#FF5B5B"
      />
    </svg>
  );
};

export default RedDelete;
