import { useEffect } from "react";
import Analytics from "../../utils/google-analytics";
import { TRACKING_EVENTS } from "../../utils/constants";

const GlobalClickTracker = () => {
    useEffect(() => {
        const handleButtonClick = (event) => {
            const target = event.target;

            // Check if the clicked element is a button or has role="button"
            if (target.tagName === "BUTTON" || target.getAttribute("role") === "button") {
                const buttonText = target.innerText || "Unnamed Button";

                Analytics.trackEvent({
                    name: TRACKING_EVENTS.BUTTON_CLICK,
                    metadata: {
                        button_name: buttonText,
                        page: window?.location?.pathname
                    }
                })
            }
        };

        document.addEventListener("click", handleButtonClick);

        return () => {
            document.removeEventListener("click", handleButtonClick);
        };
    }, []);

    return null;
};

export default GlobalClickTracker;
