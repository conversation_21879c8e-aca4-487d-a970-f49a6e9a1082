const LoadingCards = ({width, height}) => {
  return (
    <div className="flex flex-col gap-2 w-full">
      {/* Rows */}
      {Array.from({ length: 2 }).map((_, rowIndex) => (
        <div key={rowIndex} className="flex justify-center gap-1">
          {/* Columns */}
          {Array.from({ length: 3 }).map((_, colIndex) => (
            <div
              key={colIndex}
              className={"w-[33%] h-[110px] bg-gray-200 animate-pulse rounded-lg"}
            ></div>
          ))}
        </div>
      ))}
    </div>
  );
};

export default LoadingCards;
