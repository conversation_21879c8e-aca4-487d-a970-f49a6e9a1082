import { TRACKING_EVENTS } from "../../utils/constants";
import { useEffect } from "react";
import Analytics from "../../utils/google-analytics";

const PageViewTracker = ({ router }) => {
    useEffect(() => {
        const unsubscribe = router.subscribe((state) => {
            if (state.location) {
                Analytics.pageView(state.location.pathname);
            }
        });

        return () => unsubscribe(); // Cleanup on unmount
    }, [router]);

    return null;
};

export default PageViewTracker;