import React from "react";
import { useNavigate } from "react-router-dom";

const StackedReactions = ({
  highlight,
  uniqueEmojis,
  className = "-top-[35px]",
  route,
  onReactionsClick
}) => {
  const navigate = useNavigate();

  let defaultRoute = route
    ? route
    : `/user/comment?highlightId=${highlight.id}&userId=${
        highlight.userId
      }&type=${"reactions"}`;

  return (
    <div
      className={`absolute text-[#808080]  w-full left-0 flex justify-between pl-0 ${className}`}
    >
      {uniqueEmojis.length > 0 ? (
        <div
          onClick={() => {
            if (onReactionsClick) {
              onReactionsClick();
            }
            navigate(defaultRoute);
          }}
          className={`flex w-[${
            uniqueEmojis.length === 1 ? 45 : uniqueEmojis.length * 30
          }px] py-1 items-center justify-between`}
        >
          {/* Show the reacted emojis */}
          {uniqueEmojis.map((item, idx) => {
            return (
              idx < 3 && (
                <div
                  key={idx}
                  className="rounded-full flex justify-center items-center bg-[#D9D9D9] h-[30px] w-[30px] -mr-4"
                >
                  <img src={item} className="h-[20px] w-[20px]" />
                </div>
              )
            );
          })}
          <p
            onClick={() => {
              if (onReactionsClick) {
                onReactionsClick();
              }
              navigate(defaultRoute);
            }}
            className="text-base text-[#808080] ml-6 self-center text-center"
          >
            {highlight?.reactedByUsers
              ? highlight?.reactedByUsers?.length
              : "0 reactions"}
          </p>
        </div>
      ) : (
        // This is the case when the user has not yet reacted to the highlight, this is removed for now
        <p
          onClick={() =>
            navigate(
              `/user/comment?highlightId=${highlight.id}&userId=${
                highlight.userId
              }&type=${"reactions"}`
            )
          }
          className="text-base text-[#808080] ml-6 self-center text-center"
        >
          {""}
        </p>
      )}
    </div>
  );
};

export default React.memo(StackedReactions);
