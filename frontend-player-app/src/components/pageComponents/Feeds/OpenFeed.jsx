import React, { useState } from "react";
import { useSelector } from "react-redux";
import BackButton from "../../reusable/buttons/BackButton";
import Edit from "../../svgIcons/Edit";
import Menu from "../../svgIcons/Menu";
import RedDelete from "../../svgIcons/RedDelete";
import ConfirmDelete from "../Profile/ConfirmDelete";
import EditFeed from "./EditFeed";

const OpenFeed = ({ children, highlight, closeModal, isAnnouncement }) => {
  const [editModal, setEditModal] = useState(false);
  const [editFeedModal, setEditFeedModal] = useState(false);
  const { userInfo } = useSelector((state) => state.auth.authUser);

  return (
    <>
      <div className="fixed openFeed flex justify-center items-center z-40 top-0 bg-black bg-opacity-20 h-[100vh] w-full md:w-[65%]">
        <div
          className={`${
            highlight?.type === "PHOTO"
              ? isAnnouncement
                ? "h-[80%]"
                : "h-[90%]"
              : "h-[70%]"
          } bg-white flex flex-col px-5 z-40 overflow-y-auto w-full rounded-[32px]`}
        >
          <div className="relative mt-5 mb-4 flex justify-between">
            <div
              className="btn btn-sm btn-circle absolute right-5 bg-white"
              onClick={closeModal}
            >
              <img src="/images/closeModalIcon.svg" alt="close" />
            </div>
            {userInfo?.id === highlight?.userId && (
              <div className="">
                <div className="dropdown dropdown-right dropdown-bottom">
                  <label tabIndex={1} className="flex items-center">
                    <Menu />
                  </label>
                  <div
                    tabIndex={1}
                    className="dropdown-content card card-compact w-56 shadow-md bg-white text-primary-content"
                  >
                    <div className="p-4 text-black ">
                      <div className="mt-[10px] flex flex-col gap-5 w-full">
                        <div
                          onClick={() => setEditFeedModal(true)}
                          className="flex gap-3 items-center"
                        >
                          <Edit />
                          <p className="text-[20px] font-normal">Edit</p>
                        </div>
                        <div
                          onClick={() => setEditModal(true)}
                          className="text-red-600 flex gap-3 items-center"
                        >
                          <RedDelete />
                          <p className="text-[20px] font-normal">Delete</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
          <div className="flex flex-col justify-center h-full">
            <div className="">{children}</div>
            {!isAnnouncement && (
              <div className="flex my-4 gap-2 items-center">
                <img
                  src={`${
                    highlight?.user?.photoUrl?.length > 5
                      ? highlight?.user?.photoUrl
                      : "/images/profile.png"
                  }`}
                  alt="profile pix"
                  className="w-[35px] h-[35px] object-cover rounded-full"
                />
                <div>
                  <p className="text-[16px] font-medium">
                    {highlight?.user?.firstName}
                  </p>
                </div>
              </div>
            )}
            {editModal ? (
              <ConfirmDelete
                handleCloseModal={setEditModal}
                shouldShowModal={editModal}
                data={highlight}
              />
            ) : (
              ""
            )}
            {editFeedModal ? (
              <EditFeed close={setEditFeedModal} highlightObject={highlight} />
            ) : (
              ""
            )}
            <div className="text-gray-600">{highlight?.comment}</div>
          </div>
        </div>
      </div>
    </>
  );
};

export default OpenFeed;
