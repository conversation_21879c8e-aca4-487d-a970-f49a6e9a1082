import React from "react";
import NewSmile from "../../svgIcons/NewSmile";
import { NewCommentIcon, NewShareIcon } from "../../svgIcons";

const ReactCommentShare = ({ onClickReact, onClickComment, onClickShare }) => {
  return (
    <div className="flex gap-x-2">
      <button
        onClick={onClickReact}
        className="hover:border-none bg-[#A0A0A0] m-0 p-0 hover:outline-none focus:outline-none w-[32px] h-[32px] rounded-full flex justify-center items-center"
      >
        <div className="w-[20px] h-[20px] flex justify-center items-center">
          <NewSmile />
        </div>
      </button>

      <button
        onClick={onClickComment}
        className="hover:border-none bg-[#A0A0A0] m-0 p-0 hover:outline-none focus:outline-none w-[32px] h-[32px] rounded-full flex justify-center items-center"
      >
        <div className="w-[20px] h-[20px] flex justify-center items-center">
          <NewCommentIcon />
        </div>
      </button>
      <button
        onClick={onClickShare}
        className="hover:border-none bg-[#A0A0A0] m-0 p-0 hover:outline-none focus:outline-none w-[32px] h-[32px] rounded-full flex justify-center items-center"
      >
        <div className="w-[20px] h-[20px] flex justify-center items-center">
          <NewShareIcon />
        </div>
      </button>
    </div>
  );
};

export default React.memo(ReactCommentShare);
