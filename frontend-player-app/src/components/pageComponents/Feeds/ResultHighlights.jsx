import React, { useEffect } from "react";
import {
  formatDateToWords,
  notifyError,
  searchArrayOrMakeCallToAPI
} from "../../../utils/helpers";
import { useSelector } from "react-redux";
import { dispatch } from "../../../redux/store";
import { useNavigate } from "react-router-dom";

const ResultHighlights = ({ highlight }) => {
  const navigate = useNavigate();

  const { userList, teamsList } = useSelector(({ user, team }) => ({
    userList: user.usersByProjection,
    teamsList: team.teamsByProjection
  }));

  const handleOpenProfile = (id) => {
    if (id) {
      navigate({ pathname: "/profile", search: `?id=${id}` });
    } else {
      navigate("/login");
    }
  };

  const handleSearch = async (query) => {
    const searchResponse = searchArrayOrMakeCallToAPI({
      searchTerm: query,
      array: [
        ...userList?.map((item) => ({
          ...item,
          type: "user",
          fullname: `${item?.firstName} ${item?.lastName}`
        })),
        ...teamsList?.map((item) => ({ ...item, type: "team" }))
      ],
      makeSearchCall: [dispatch.user.userSearch, dispatch.team.teamSearch]
    });

    return searchResponse || [];
  };

  const handleProfileOpen = async (data) => {
    const splitName = data.split(" ");
    const filteredNames = splitName.filter((name) => name !== "");
    const checkThirdName = filteredNames[2]
      ? !filteredNames[2].includes("(")
      : false;
    const fullName = `${filteredNames[0]} ${filteredNames[1]}${
      checkThirdName ? ` ${filteredNames[2]}` : ""
    }`;

    const searchResult = await handleSearch(fullName);
    if (searchResult.length > 0) {
      handleOpenProfile(searchResult[0].id);
    } else {
      notifyError("User profile not found");
    }
  };

  useEffect(() => {
    if (!userList?.length) {
      dispatch.user.userSearchByProjection();
    }
  }, []);

  return (
    <div className="w-full h-full relative">
      <img
        src="/images/green.jpg"
        className="w-full min-h-[70vh] rounded-[20px]"
      ></img>
      <div className="absolute top-0 p-5 text-white bg-gradient-to-tl from-[#0000006e] rounded-[20px] from-45% right-0 bottom-0 left-0 flex flex-col gap-5 justify-center items-center">
        <div className="font-favela-bold">
          {formatDateToWords(highlight.result.dateTimePlayed)}
        </div>
        <div className="grid grid-cols-3 gap-3">
          <div className="text-center">
            {highlight.result.homeTeam.data.clubName}
          </div>
          <div className="text-black flex flex-nowrap gap-3">
            <div className="bg-[#52FF00] flex justify-center items-center w-10 h-10 rounded-[10px] text-2xl font-favela-bold">
              {highlight.result.homeTeam.score}
            </div>
            <div className="text-3xl font-black text-white"> - </div>
            <div className="bg-[#52FF00] flex justify-center items-center w-10 h-10 rounded-[10px] text-2xl font-favela-bold">
              {highlight.result.awayTeam.score}
            </div>
          </div>
          <div className="text-center">
            {highlight.result.awayTeam.data.clubName}
          </div>
        </div>
        <div className="flex flex-nowrap w-full gap-3">
          <div className="flex flex-col gap-3 items-start w-[50%]">
            {highlight.result.homeTeam.goalScorers.map((goalScorer, idx) => (
              <p
                className="underline"
                key={idx}
                onClick={() => handleProfileOpen(goalScorer)}
              >
                {goalScorer}
              </p>
            ))}
          </div>
          <div className="flex flex-col gap-3 items-end w-[50%]">
            {highlight.result.awayTeam.goalScorers.map((goalScorer, idx) => (
              <p
                className="underline"
                key={idx}
                onClick={() => handleProfileOpen(goalScorer)}
              >
                {goalScorer}
              </p>
            ))}
          </div>
        </div>
        {/* Assists */}
        <div className="w-full">
          <p className="w-full text-center font-semibold text-lg my-4">
            Assists
          </p>
          <div className="flex flex-nowrap w-full gap-3">
            <div className="flex flex-col gap-3 items-start w-[50%]">
              {highlight.result.homeTeam.assists.map((assist, idx) => (
                <p
                  className="underline"
                  key={idx}
                  onClick={() => handleProfileOpen(assist)}
                >
                  {assist}
                </p>
              ))}
            </div>
            <div className="flex flex-col gap-3 items-end w-[50%]">
              {highlight.result.awayTeam.assists.map((assist, idx) => (
                <p
                  className="underline"
                  key={idx}
                  onClick={() => handleProfileOpen(assist)}
                >
                  {assist}
                </p>
              ))}
            </div>
          </div>
        </div>
        {/* Player of the match */}
        <div className="w-full">
          <p className="w-full text-center font-semibold text-lg my-4">
            Player of the match
          </p>
          <div className="flex flex-nowrap w-full gap-3">
            <div className="flex flex-col gap-3 items-start w-[50%]">
              {highlight.result.homeTeam.playersOfTheMatch.map((pom, idx) => (
                <p
                  className="underline"
                  key={idx}
                  onClick={() => handleProfileOpen(pom)}
                >
                  {pom}
                </p>
              ))}
            </div>
            <div className="flex flex-col gap-3 items-end w-[50%]">
              {highlight.result.awayTeam.playersOfTheMatch.map((pom, idx) => (
                <p
                  className="underline"
                  key={idx}
                  onClick={() => handleProfileOpen(pom)}
                >
                  {pom}
                </p>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResultHighlights;
