import React, { useState } from "react";
import { useSelector } from "react-redux";
import { dispatch } from "../../../redux/store";
import BackButton from "../../reusable/buttons/BackButton";
import { BigButtons } from "../../reusable/buttons/Buttons";

const EditFeed = ({ highlightObject, close }) => {
  const [comment, setComment] = useState(highlightObject.comment);
  const [commentError, setCommentError] = useState("");

  const editing = useSelector(
    ({ loading }) => loading.effects.feed.editHighlights
  );

  const handleSubmit = async () => {
    if (comment.length < 1) {
      setCommentError("Please add a Comment");
    } else {
      const res = await dispatch.feed.editHighlights({
        userId: highlightObject.userId,
        id: highlightObject.id,
        comment: comment,
      });
      if (res.status === 1) close(false);
    }
  };
  return (
    <div className="absolute z-50 top-0 left-0 bottom-0 overflow-y-scroll pt-7 w-full h-[100vh] bg-white px-[15px]">
      <BackButton onClick={() => close(false)} />
      <div className="w-full flex items-center rounded-[32px] mt-6 overflow-hidden aspect-[4/5]">
        {highlightObject?.type === "PHOTO" ? (
          <img
            src={highlightObject.url}
            alt="feed"
            className="w-full h-full object-cover"
          />
        ) : (
          <video width="400" height="350" controls>
            <source src={`${highlightObject.url}#t=0.001`} type="video/mp4" />
          </video>
        )}
      </div>
      <div className="flex flex-cols items-center border-b border-black py-1 mt-[20px]">
        <input
          onChange={(e) => setComment(e.target.value)}
          className="appearance-none bg-transparent border-none w-full text-gray-700 mr-3 py-1 leading-tight focus:outline-none"
          type="select"
          value={comment}
          placeholder="Enter Caption"
          aria-label="cature"
        />
      </div>
      <p className="absolute flex mt-4 justify-center text-red-600 text-xs">
        {commentError ? commentError : ""}
      </p>
      <BigButtons
        isLoading={editing}
        onClick={() => handleSubmit()}
        className="mt-7"
        green
        label="Publish"
      />
    </div>
  );
};

export default EditFeed;
