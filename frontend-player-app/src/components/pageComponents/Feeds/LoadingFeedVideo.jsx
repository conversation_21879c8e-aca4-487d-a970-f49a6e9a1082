import { useEffect } from "react";
import hourglassIcon from "../../../assets/hourglass.svg";

const LoadingFeedVideo = ({
  content1 = "Please wait while we prepare",
  content2 = "this highlight, or check back soon...",
  iconSrc = hourglassIcon,
  reloadTime = 3000,
  shouldReload = false
}) => {
  useEffect(() => {
    const timer = setTimeout(() => {
      if (shouldReload) {
        window.location.reload();
      }
    }, reloadTime);

    return () => {
      clearTimeout(timer);
    };
  }, []);

  return (
    <div className="flex flex-col w-full h-[520px] bg-[#F8F8F8] items-center justify-center">
      <img src={iconSrc} alt="hourglass" />
      <p className=" px-10 py-5 max-w-[340px]  text-center text-[12px] font-[400] leading-[18px] text-black font-poppins">
        {content1} {"    "}
        {content2}
      </p>
    </div>
  );
};

export default LoadingFeedVideo;
