import React, { useState, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import _debounce from "lodash/debounce";

import { useFormik } from "formik";
import { editProfile } from "../../../utils/formSchema";
import { BigButtons } from "../../reusable/buttons/Buttons";
import { PreferredFoot, cleanDate, notifyError, notify<PERSON>arn, removeEmpty } from "../../../utils/helpers";
import { useSelector } from "react-redux";
import { dispatch } from "../../../redux/store";
import SelectWithPicture from "../../reusable/SelectWithPicture";
import DropdownWithCheckBox from "../Custom/DropdownWithCheckBox";
import moment from "moment";

const EditProfileForm = ({ uploading }) => {
  const [teams, setTeams] = useState([]);
  const [searchValue, setSearchValue] = useState("");
  const [selectTeam, setSelectTeam] = useState({});
  const [selectClub, setSelectClub] = useState({});
  const [isFetchingTeams, setIsFetchingTeams] = useState(false);
  const [errorMessage, setNotFoundClubError] = useState(false);

  const { userInfo } = useSelector((state) => state.auth.authUser);

  const loadingUserUpdate = useSelector(
    ({ loading }) => loading.effects.user.updateUser
  );
  const navigate = useNavigate();

  var dateThreshold = moment().subtract(5, "years").format("YYYY-MM-DD");

  const { teamsModel } = useSelector(({ team }) => ({
    teamsModel: team.allTeams
  }));

  const handleTeamChange = ({ target }) => {
    setSelectTeam(
      teams.find((item) =>
        item?.teamName?.toLowerCase()?.includes(target.value?.toLowerCase())
      ) || {}
    );
  };

  const handleClubSelection = async (clubId) => {
    setIsFetchingTeams(true);
    const teamsInGivenClub = [];
    // get the teams from the state

    const res = await dispatch.team.getTeamsByClubId(clubId);
    teamsInGivenClub.push(...res?.data);
    // if only one team, select the team for the player
    if (teamsInGivenClub?.length === 1) {
      setSelectTeam(teamsInGivenClub[0]);
    }
    setTeams(teamsInGivenClub);
    setIsFetchingTeams(false);
  };

  const getTeamsBasedOnSelectedClub = useCallback(
    _debounce(handleClubSelection, 100),
    []
  );

  const onSubmit = async (values) => {
    const mergedValues = {
      ...values,
      // birthday: (value && value?.toLocaleDateString()) || "",
      id: userInfo.id,
      teamId: selectTeam?.id || values.teamId,
      cludId: selectClub?.id || values.cludId,
      clubName: selectClub?.clubName || values?.clubName,
      teamName: selectTeam?.teamName || values.teamName
    };

    const cleanData = removeEmpty(mergedValues);

    if (teams?.length && !selectTeam?.id) {
      notifyError("Please select a team");
      return;
    }

    if (!mergedValues?.clubName || !mergedValues?.cludId) {
      notifyError("Please reselect the org or Club");
      return;
    }

    if (errorMessage && searchValue.length > 2) {
      notifyWarn("The club or org you entered is not found");
      return;
    }

    const res = await dispatch.user.updateUser(cleanData);

    if (res === 1) {
      await Promise.all([
        await dispatch.user.getUserProfile({
          id: userInfo?.id
        }),
        await dispatch.user.userSearchByProjection()
      ])
      navigate(-1);
    }
  };

  const initialValues = {
    firstName: userInfo?.firstName || "",
    birthday: userInfo?.birthday || "",
    lastName: userInfo?.lastName || "",
    preferredFoot: userInfo?.preferredFoot || "",
    height: userInfo?.height || "",
    clubName: userInfo?.clubName || "",
    teamName: userInfo?.teamName || "",
    location: userInfo?.location || "",
    bio: userInfo?.bio || "",
    position: userInfo?.position || "",
    cludId: userInfo?.cludId || "-",
    nonPlayerRole: userInfo?.nonPlayerRole || [],
    teamId: userInfo?.teamId || "-",
    contractExpiry: userInfo?.contractExpiry || "",
    representedBy: userInfo?.representedBy || ""
  };

  const {
    values,
    errors,
    touched,
    handleBlur,
    handleChange,
    handleSubmit,
    setFieldValue
  } = useFormik({
    initialValues,
    onSubmit,
    validationSchema: editProfile
  });

  const handleRoleChange = (roles) => {
    setFieldValue("nonPlayerRole", roles);
  };

  return (
    <form onSubmit={handleSubmit}>
      <div className="grid grid-cols-2 gap-6">
        <div className="relative z-0 w-full mb-7 group">
          <input
            type="text"
            value={values?.firstName}
            onChange={handleChange}
            onBlur={handleBlur}
            name="firstName"
            id="firstName"
            className="border-b-2 border-gray-600 block pt-3 pb-1 px-0 w-full text-sm text-gray-900 bg-transparent appearance-none  focus:outline-none focus:ring-0 focus:border-gray-600 peer"
            placeholder=" "
          />
          <label
            htmlFor="firstName"
            className="peer-focus:font-medium absolute text-sm text-gray-500 dark:text-gray-400 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-gray-600 peer-focus:dark:text-gray-100 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6"
          >
            First Name
          </label>
          <p className="absolute flex justify-center text-red-600 text-xs">
            {errors.firstName && touched.firstName ? errors.firstName : ""}
          </p>
        </div>
        <div className="relative z-0 w-full mb-7 group">
          <input
            type="text"
            value={values?.lastName}
            onChange={handleChange}
            onBlur={handleBlur}
            name="lastName"
            id="lastName"
            className="border-b-2 border-gray-600 block pt-3 pb-1 px-0 w-full text-sm text-gray-900 bg-transparent appearance-none  focus:outline-none focus:ring-0 focus:border-gray-600 peer"
            placeholder=" "
          />
          <label
            htmlFor="lastName"
            className="peer-focus:font-medium absolute text-sm text-gray-500 dark:text-gray-400 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-gray-600 peer-focus:dark:text-gray-100 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6"
          >
            Last Name
          </label>
          <p className="absolute flex justify-center text-red-600 text-xs">
            {errors.lastName && touched.lastName ? errors.lastName : ""}
          </p>
        </div>
      </div>

      {userInfo.userType !== "NON_PLAYER" && (
        <div className="grid grid-cols-2 gap-6">
          <div className="relative z-0 w-full mb-7 group">
            <select
              type="text"
              value={values?.preferredFoot}
              onChange={handleChange}
              onBlur={handleBlur}
              name="preferredFoot"
              id="preferredFoot"
              className="border-b-2 border-gray-600 block pt-3 pb-1 px-0 w-full text-sm text-gray-900 bg-transparent appearance-none  focus:outline-none focus:ring-0 focus:border-gray-600 peer placeholder:text-center "
              placeholder=""
            >
              <option key={"--"} disabled={true} value="">
                - -
              </option>
              {Object.keys(PreferredFoot).map((key) => {
                return (
                  <option key={key} value={key}>
                    {PreferredFoot[key]}
                  </option>
                );
              })}
            </select>
            <label
              htmlFor="preferredFoot"
              className="peer-focus:font-medium absolute text-sm text-gray-500 dark:text-gray-400 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-gray-600 peer-focus:dark:text-gray-100 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6"
            >
              Preferred Foot
            </label>
            <p className="absolute flex justify-center text-red-600 text-xs">
              {errors.preferredFoot && touched.preferredFoot
                ? errors.preferredFoot
                : ""}
            </p>
          </div>
          <div className="relative z-0 w-full mb-7 group">
            <input
              type="text"
              value={values?.height}
              onChange={handleChange}
              onBlur={handleBlur}
              name="height"
              id="height"
              className="border-b-2 border-gray-600 block pt-3 pb-1 px-0 w-full text-sm text-gray-900 bg-transparent appearance-none  focus:outline-none focus:ring-0 focus:border-gray-600 peer"
              placeholder="Meters"
            />
            <label
              htmlFor="height"
              className="peer-focus:font-medium absolute text-sm text-gray-500 dark:text-gray-400 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-gray-600 peer-focus:dark:text-gray-100 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6"
            >
              Height
            </label>
            <p className="absolute flex justify-center text-red-600 text-xs">
              {errors.height && touched.height ? errors.height : ""}
            </p>
          </div>
        </div>
      )}

      <div className="grid grid-cols-2 gap-6">
        <div className="relative z-0 w-full mb-7 group">
          {userInfo.userType !== "NON_PLAYER" ? (
            <div className="border-b-2 border-gray-600 block pt-3 pb-[2px] px-0 w-full text-sm text-gray-900 bg-transparent appearance-none  focus:outline-none focus:ring-0 focus:border-gray-600 peer">
              <input
                type="date"
                onChange={(e) => {
                  setFieldValue("birthday", cleanDate(e.target.value));
                }}
                value={cleanDate(values.birthday)}
                max={dateThreshold}
              />
            </div>
          ) : (
            <div
              title={userInfo.email}
              className="border-b-2 border-gray-600 block pt-3 pb-[2px] px-0 w-full text-sm text-gray-500 bg-transparent appearance-none  focus:outline-none focus:ring-0 focus:border-gray-600 peer"
            >
              <label className="peer-focus:font-medium absolute text-sm text-gray-500 dark:text-gray-400 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-gray-600 peer-focus:dark:text-gray-100 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6">
                email
              </label>
              <input disabled type="email" value={userInfo.email} />
            </div>
          )}
        </div>
        <div className="relative z-0 w-full mb-7 group">
          <input
            type="text"
            value={values?.location}
            onChange={handleChange}
            onBlur={handleBlur}
            name="location"
            id="location"
            className="border-b-2 border-gray-600 block pt-3 pb-1 px-0 w-full text-sm text-gray-900 bg-transparent appearance-none  focus:outline-none focus:ring-0 focus:border-gray-600 peer"
            placeholder=" "
          />
          <label
            htmlFor="location"
            className="peer-focus:font-medium absolute text-sm text-gray-500 dark:text-gray-400 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-gray-600 peer-focus:dark:text-gray-100 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6"
          >
            Location
          </label>
        </div>
      </div>
      <div className="relative z-0 w-full mb-7 group">
        <div className="flex items-center">
          <div className="appearance-none border-none w-full text-gray-700 py-1 leading-tight">
            <SelectWithPicture
              userInfo={userInfo}
              setSelectClub={setSelectClub}
              selectClub={selectClub}
              setTeams={setTeams}
              setSelectTeam={setSelectTeam}
              setNotFoundClubError={setNotFoundClubError}
              searchValue={searchValue}
              setSearchValue={setSearchValue}
              getTeamsBasedOnSelectedClub={getTeamsBasedOnSelectedClub}
            />
          </div>
        </div>
        <label
          htmlFor="clubs"
          className="peer-focus:font-medium absolute text-sm text-gray-500 dark:text-gray-400 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-gray-600 peer-focus:dark:text-gray-100 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6"
        >
          {userInfo.userType !== "NON_PLAYER"
            ? "Football Club"
            : "Organisation Name"}
        </label>
      </div>
      {teams?.length ? (
        <div className="relative z-0 w-full mb-7 group">
          <div className="flex items-center">
            <div className="appearance-none border-none w-full text-gray-700 py-1 leading-tight">
              <select
                onChange={handleTeamChange}
                className="appearance-none bg-transparent border-b-2 border-gray-600 w-full text-gray-700 py-1 pl-2 leading-tight focus:outline-none"
              >
                <option value="">Select team</option>
                {teams
                  .sort((a, b) => b.index - a.index)
                  .map((team, idx) => (
                    <option
                      defaultValue={selectTeam?.teamName}
                      key={idx}
                      value={team?.teamName}
                    >
                      {team?.teamName}
                    </option>
                  ))}
              </select>
            </div>
          </div>
          <label
            htmlFor="teamName"
            className="peer-focus:font-medium absolute text-sm text-gray-500 dark:text-gray-400 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-gray-600 peer-focus:dark:text-gray-100 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6"
          >
            Team
          </label>
        </div>
      ) : (
        <>
          {errorMessage && searchValue.length > 2 && (
            <div className="text-red-600 font-poppins -mt-[20px] text-xs mb-5">
              <p>Team not in database.</p>
              <p>
                Please contact{" "}
                <a
                  className="text-red-600"
                  href={`mailto:<EMAIL>?subject=Add team to database&body=Hello PLAYER, \n Please add '${searchValue}' to your system`}
                >
                  <EMAIL>{" "}
                </a>
                to get it added.
              </p>
            </div>
          )}
        </>
      )}
      {userInfo.userType !== "NON_PLAYER" && (
        <div className="relative z-0 w-full mb-7 group">
          <label
            htmlFor="bio"
            className="text-[16px] peer-focus:font-medium absolute text-sm text-gray-500 dark:text-gray-400 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-gray-600 peer-focus:dark:text-gray-100 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6"
          >
            Contract Expiry Date
          </label>
          <input
            type="text"
            value={values?.contractExpiry}
            onChange={handleChange}
            onBlur={handleBlur}
            name="contractExpiry"
            id="contractExpiry"
            className="border-b-2 border-gray-600 block pt-3 pb-1 px-0 w-full text-sm text-gray-900 bg-transparent appearance-none  focus:outline-none focus:ring-0 focus:border-gray-600 peer"
            placeholder="eg. July 2025 (Leave blank if not applicable)"
          />
          <p className="absolute mt-1 flex justify-center text-red-600 text-xs">
            {errors.contractExpiry && touched.contractExpiry
              ? errors.contractExpiry
              : ""}
          </p>
        </div>
      )}
      {userInfo.userType !== "NON_PLAYER" && (
        <div className="relative z-0 w-full mb-7 group">
          <label
            htmlFor="bio"
            className="text-[20px] peer-focus:font-medium absolute text-sm text-gray-500 dark:text-gray-400 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-gray-600 peer-focus:dark:text-gray-100 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6"
          >
            Represented by
          </label>
          <input
            type="text"
            value={values?.representedBy}
            onChange={handleChange}
            onBlur={handleBlur}
            name="representedBy"
            id="representedBy"
            className="border-b-2 border-gray-600 block pt-3 pb-1 px-0 w-full text-sm text-gray-900 bg-transparent appearance-none  focus:outline-none focus:ring-0 focus:border-gray-600 peer"
            placeholder="eg. Manager/Agent (Leave blank if not applicable)"
          />
          <p className="absolute mt-1 flex justify-center text-red-600 text-xs">
            {errors.representedBy && touched.representedBy
              ? errors.representedBy
              : ""}
          </p>
        </div>
      )}
      {userInfo.userType !== "NON_PLAYER" && (
        <div className="relative z-0 w-full mb-7 group">
          <select
            type="text"
            value={values?.position}
            onChange={handleChange}
            onBlur={handleBlur}
            name="position"
            id="position"
            className="border-b-2 border-gray-600 block pt-3 pb-1 px-0 w-full text-sm text-gray-900 bg-transparent appearance-none  focus:outline-none focus:ring-0 focus:border-gray-600 peer placeholder:text-center "
            placeholder="(150 characters)"
          >
            <option disabled={true} value="">
              - -
            </option>
            <option value="GOAL_KEEPERS">Goalkeeper</option>
            <option value="CENTRE_BACK">Centre Back</option>
            <option value="RIGHT_BACK">Right Back</option>
            <option value="LEFT_BACK">Left Back</option>
            <option value="WING_BACK">Wing Back</option>
            <option value="WINGER">Winger</option>
            <option value="DEFENSIVE_MIDFIELD">Defensive Midfield</option>
            <option value="CENTRAL_MIDFIELD">Central Midfield</option>
            <option value="ATTACKING_MIDFIELD">Attacking Midfield</option>
            <option value="FORWARD">Forward</option>
            <option value="STRIKER">Striker</option>
          </select>
          <label
            htmlFor="position"
            className="peer-focus:font-medium absolute text-sm text-gray-500 dark:text-gray-400 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-gray-600 peer-focus:dark:text-gray-100 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6"
          >
            Position
          </label>
          <p className="absolute mt-1 flex justify-center text-red-600 text-xs">
            {errors.position && touched.position ? errors.position : ""}
          </p>
        </div>
      )}
      <div className="relative z-0 w-full mb-7 group">
        <textarea
          type="text"
          value={values?.bio}
          onChange={handleChange}
          onBlur={handleBlur}
          name="bio"
          id="bio"
          className="border-b-2 border-gray-600 block pt-3 pb-1 px-0 w-full text-sm text-gray-900 bg-transparent appearance-none  focus:outline-none focus:ring-0 focus:border-gray-600 peer placeholder:text-center "
          placeholder="write about yourself"
        />
        <label
          htmlFor="bio"
          className="peer-focus:font-medium absolute text-sm text-gray-500 dark:text-gray-400 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-gray-600 peer-focus:dark:text-gray-100 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6"
        >
          Bio
        </label>
        <p className="absolute mt-1 flex justify-center text-red-600 text-xs">
          {errors.bio && touched.bio ? errors.bio : ""}
        </p>
      </div>

      {userInfo.userType === "NON_PLAYER" && (
        <div className="relative z-0 w-full mb-7 group">
          <label
            htmlFor="bio"
            className="peer-focus:font-medium absolute text-sm text-gray-500 dark:text-gray-400 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-gray-600 peer-focus:dark:text-gray-100 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6"
          >
            Role
          </label>
          <div className="border-b-2 border-black ">
            <DropdownWithCheckBox
              defaultOptions={userInfo.nonPlayerRole}
              onChange={handleRoleChange}
            />
          </div>
        </div>
      )}
      <div className="mb-14 mt-7">
        <BigButtons
          isDisabled={uploading || isFetchingTeams}
          black
          label={uploading ? "Uploading" : "SAVE"}
          isLoading={loadingUserUpdate}
        />
      </div>
    </form>
  );
};

export default EditProfileForm;
