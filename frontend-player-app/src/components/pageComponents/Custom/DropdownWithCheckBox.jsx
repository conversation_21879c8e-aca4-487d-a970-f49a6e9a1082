import React, { useCallback, useEffect } from "react";
import { DropDownIcon } from "../../svgIcons";

const nonPlayers = [
  "Agent",
  "Coach",
  "Scout",
  "Academy Manager / Director",
  "Chief Scout",
  "Sporting Director",
  "Player Care"
];

const DropdownWithCheckBox = ({
  onChange,
  listOfOptions = nonPlayers,
  defaultOptions = [],
  icon
}) => {
  const [selectedOption, setSelectedOption] = React.useState(defaultOptions);

  const handleChange = (item) => {
    const isChecked = selectedOption.includes(item);

    if (isChecked) {
      setSelectedOption(selectedOption.filter((option) => option !== item));
    } else {
      setSelectedOption([...selectedOption, item]);
    }
  };

  const handleChangeWithCallback = useCallback(
    (item) => handleChange(item),
    [listOfOptions, selectedOption, handleChange]
  );

  useEffect(() => {
    onChange(selectedOption);
  }, [selectedOption]);

    return (
    <div className="dropdown">
      <div tabIndex={0} role="button" className="m-1 w-full  min-w-[258px]">
        {icon || (
          <span className="flex items-center font-[400] font-poppins">
            - -
            <DropDownIcon w={15} h={8} color={"black"} />
            <div className="flex max-w-[250px] scroll-smooth overflow-x-scroll flex-nowrap ml-2">
              {selectedOption.map((item, idx) => (
                <span
                  className="pr-2 text-ellipsis whitespace-nowrap"
                  key={item}
                >
                  {item} {selectedOption.length-1 !== idx ? "," : ""}
                </span>
              ))}
            </div>
          </span>
        )}
      </div>
      <ul
        tabIndex={0}
        className="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-[200px] "
      >
        {listOfOptions.map((role, key) => (
          <li key={key}>
            <label className="cursor-pointer pr-0 active:bg-gray-950">
              <input
                type="checkbox"
                checked={selectedOption.includes(role)}
                className="checkbox checkbox-md"
                onChange={() => handleChangeWithCallback(role)}
              />
              <span className="">{role}</span>
            </label>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default DropdownWithCheckBox;
