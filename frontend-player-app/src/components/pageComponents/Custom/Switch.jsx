import React from "react";

const SwitchButton = ({
  checked,
  handleCheckboxChange,
  small,
  isLoading,
  inActiveBgColor = 'bg-gray-400',
  activeBgColor =':bg-gray-800',
  labelPosition = "right",
  text = "publish",
  uppercaseText = true,
  textFont = "font-favela-bold",
}) => {
  return (
    <div className={`${uppercaseText ? 'uppercase' : ''} flex justify-center items-center gap-4 ${textFont} w-full text-center`}>
      {labelPosition === "left" && (
        <div className={`${small ? "text-sm" : "text-[16px]"}`}>{text}</div>
      )}
      <label className="relative inline-flex items-center cursor-pointer">
        <input
          type="checkbox"
          value=""
          disabled={isLoading}
          className="sr-only peer"
          checked={checked}
          onChange={handleCheckboxChange}
        />
        <div
          className={`${
            small
              ? "w-9 h-5 after:top-[2px] after:left-[2px] after:h-4 after:w-4"
              : "w-14 h-7 after:top-0.5 after:left-[4px] after:h-6 after:w-6"
          } ${
            isLoading ? "opacity-50" : ""
          }  ${inActiveBgColor} peer-focus:outline-none rounded-full peer dark${activeBgColor} peer-checked:after:translate-x-full after:content-[''] after:absolute after:bg-white after:rounded-full after:transition-all peer-checked${activeBgColor}`}
        ></div>
      </label>
      {labelPosition === "right" && (
        <div className={`${small ? "" : "text-[16px]"}`}>{text}</div>
      )}
    </div>
  );
};

export default SwitchButton;
