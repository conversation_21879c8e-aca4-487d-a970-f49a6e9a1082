import { useEffect, useState } from "react";
import { useFormik } from "formik";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";

import { referenceFormSchema } from "../../../utils/formSchema";
import DropdownWithCheckBox from "../Custom/DropdownWithCheckBox";
import { ReferenceApi } from "../../../services";
import { notifyError, notifySuccess } from "../../../utils/helpers";
import { BigButtons } from "../../reusable/buttons/Buttons";
import SearchUserList from "../../reusable/SearchUserList";

const ReferenceForm = ({ toUserDetails, isSameUser }) => {
  const [submitting, setSubmitting] = useState(false);
  const [toSelectedUser, setToSelectedUser] = useState(toUserDetails);

  const navigate = useNavigate();
  const { userInfo } = useSelector((state) => state.auth.authUser);

  const onSubmit = async (values) => {    
    const reference = {
      fromProfileName: `${userInfo.firstName} ${userInfo.lastName}`,
      fromProfilePhoto: userInfo.photoUrl || "",
      fromProfileId: userInfo.id,
      toProfileName: `${toSelectedUser?.firstName} ${toSelectedUser?.lastName}`,
      toProfileId: toSelectedUser?.id,
      toProfilePhoto: toSelectedUser.photoUrl || "",
      message: values.message,
      toRole: toSelectedUser?.nonPlayerRole || [],
      fromRole: values.positionAtTime,
      relationship: values.relationship
    };    
    try {
      setSubmitting(true);
      await ReferenceApi.postReferences(reference);
      notifySuccess("Submitted");
      navigate(-1);
    } catch (error) {
      notifyError(error.message);
    } finally {
      setSubmitting(false);
    }
  };

  const initialValues = {
    relationship: "",
    positionAtTime: "",
    message: "",
    toUserId: ""
  };

  const {
    values,
    errors,
    touched,
    handleBlur,
    handleChange,
    handleSubmit,
    setFieldValue
  } = useFormik({
    initialValues,
    onSubmit,
    validationSchema: referenceFormSchema
  });

  const handleRoleChange = (positionAtTime) => {
    setFieldValue("positionAtTime", positionAtTime);
  };
  const onSelectUser = (userData) => {
    setToSelectedUser(userData);
    setFieldValue("toUserId", userData.id);
  };

  useEffect(() => {
    if (!isSameUser) {
      setToSelectedUser(toUserDetails);
      setFieldValue("toUserId", toUserDetails?.id);
    }
  }, [toUserDetails, isSameUser])

  return (
    <div>
      {isSameUser && (
        <div>
          <h4 className="text-[16px] text-black font-poppins font-bold">
            Write a reference for someone
          </h4>
          <p className="text-[12px] text-black text-opacity-50">
            This reference will appear on their Profile
          </p>
        </div>
      )}

      <form
        onSubmit={handleSubmit}
        className="w-full flex flex-col justify-between h-[60vh]"
      >
        <div>
          {isSameUser && (
            <div className="mt-7">
              <label className="font-poppins text-black text-opacity-50 text-[14px]">
                To:
              </label>
              <SearchUserList
                selectedUser={toSelectedUser}
                onSelectUser={onSelectUser}
              />
              <p className="absolute flex justify-center text-red-600 text-xs">
                {errors.toUserId && touched.toUserId ? errors.toUserId : ""}
              </p>
            </div>
          )}
          <div className="mt-7">
            <label className="font-poppins text-black text-opacity-50 text-[14px]">
              Relationship*
            </label>
            <div className="flex items-center border-b border-black py-4">
              <input
                className="appearance-none bg-transparent border-none w-full text-gray-700 mr-3 py-1 leading-tight focus:outline-none"
                type="text"
                placeholder="E.g. Coach"
                value={values.relationship}
                onChange={handleChange}
                onBlur={handleBlur}
                name="relationship"
                id="relationship"
              />
            </div>
            <p className="absolute flex justify-center text-red-600 text-xs">
              {errors.relationship && touched.relationship
                ? errors.relationship
                : ""}
            </p>
          </div>

          <div className="mt-7">
            <label className="font-poppins text-black text-opacity-50 text-[14px] ">
              Position at the time*
            </label>
            <div className="flex items-center border-b border-black py-4">
              <div className="appearance-none bg-transparent border-none w-full text-gray-700 mr-3 py-1 leading-tight focus:outline-none">
                <DropdownWithCheckBox onChange={handleRoleChange} />
              </div>
            </div>

            <p className="absolute flex justify-center text-red-600 text-xs">
              {errors.positionAtTime && touched.positionAtTime
                ? errors.positionAtTime
                : ""}
            </p>
          </div>

          <div className="mt-7">
            <label className="font-poppins text-black text-opacity-50 text-[14px]">
              Add reference*
            </label>
            <div className="flex items-center border-b border-black py-4">
              <input
                className="appearance-none bg-transparent border-none w-full text-gray-700 mr-3 py-1 leading-tight focus:outline-none"
                type="text"
                placeholder="Write your reference here..."
                value={values.message}
                onChange={handleChange}
                onBlur={handleBlur}
                name="message"
                id="message"
              />
            </div>
            <p className="absolute flex justify-center text-red-600 text-xs">
              {errors.message && touched.message ? errors.message : ""}
            </p>
          </div>
        </div>
        <BigButtons
          isLoading={submitting}
          type="submit"
          onClick={handleSubmit}
          black
          label={"POST"}
        />
      </form>
    </div>
  );
};

export default ReferenceForm;
