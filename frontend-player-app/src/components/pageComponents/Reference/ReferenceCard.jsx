const ReferenceCard = ({ name, title, date, message, photo, relationship }) => {
  return (
    <div className="py-3">
      <div className="flex border-b pb-5">
        <img src={photo || '/images/profile.png'} className="w-[42px] h-[42px] rounded-full" />
        <div className="px-5">
          <h3 className="font-bold text-lg">{name}</h3>
          <p>{relationship} / {title}  </p>
          <p className="text-black text-opacity-40 mb-5">{date}</p>
          <p className="font-poppins">{message}</p>
        </div>
      </div>
    </div>
  );
};

export default ReferenceCard;
