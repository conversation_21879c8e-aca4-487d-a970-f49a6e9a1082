import React, { memo, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";

import { dispatch } from "../../../redux/store";
import { NewCommentIcon, NewShareIcon } from "../../svgIcons";
import {
  getUniqueEmojis,
  getVideoStatus,
  shouldShowWaitIcon
} from "../../../utils/helpers";
import NewSmile from "../../svgIcons/NewSmile";
import VideoCarousel from "../../reusable/VideoCarousel.jsx";
import moment from "moment";
import GreenPin from "../../../assets/pinGreen.png";
import AnnoucementPlayerVideo from "../Announcement/AnnoucementPlayerVideo.jsx";
import TextWithPlayerAppMention from "../Announcement/TextWithPlayerAppMention.jsx";
import LoadingFeedVideo from "../Feeds/LoadingFeedVideo.jsx";
import Analytics from "../../../utils/google-analytics.js";
import { TRACKING_EVENTS } from "../../../utils/constants.js";
import StackedReactions from "../Feeds/StackedReactions.jsx";

const Competition = memo(
  ({
    loggedUserId,
    liveLikeProfileToken,
    reactionPacks,
    isDetailPage,
    activeAnnouncements = [],
    onReactionClick = null,
    setCurrentAnnouncement = null
  }) => {
    const [announcements, setAnnouncements] = useState(null);
    const [shouldShowEmojiPanel, setShouldShowEmojiPanel] = useState({
      visible: false,
      highlightId: ""
    });

    const userInfo = useSelector((state) => state.user.data);

    const navigate = useNavigate();

    async function addUserReaction(announcement, reaction) {
      setShouldShowEmojiPanel({ visible: false, highlightId: "" });
      function filterById(reactedBy, userId) {
        const filteredArray = reactedBy?.filter(
          (item) => item.userId !== userId
        );
        return filteredArray;
      }
      // console.log(announcement, reaction);
      try {
        const existingAnnouncement = activeAnnouncements?.find(
          (item) => item.id === announcement.id
        );

        if (existingAnnouncement) {
          const filteredReactions = filterById(
            existingAnnouncement.reactedByUsers,
            userInfo.id
          );

          let reactedByUsersWithNewReaction = [
            {
              userId: userInfo.id,
              others: {
                user: {
                  firstName: userInfo.firstName,
                  lastName: userInfo.lastName,
                  photoUrl: userInfo.photoUrl,
                  cludId: userInfo.cludId,
                  gender: userInfo.gender
                },
                emoji: reaction.file,
                emojiName: reaction.name
              }
            }
          ];

          if (filteredReactions) {
            reactedByUsersWithNewReaction = [
              ...filteredReactions,
              ...reactedByUsersWithNewReaction
            ];
          }

          // Update the state immediately
          setAnnouncements((prev) => ({
            ...prev,
            reactedByUsers: reactedByUsersWithNewReaction
          }));

          if (setCurrentAnnouncement) {
            setCurrentAnnouncement((prev) => ({
              ...prev,
              reactedByUsers: reactedByUsersWithNewReaction
            }));
          }

          dispatch.announcement.updateAnnouncement({
            id: announcement.id,
            reactedByUsers: reactedByUsersWithNewReaction
          });

          Analytics.trackEvent({
            name: TRACKING_EVENTS.REACTION_ADDED,
            metadata: {
              reaction_type: reaction?.name,
              content_id: announcement?.id,
              content_type: "announcement"
            }
          });
        }
      } catch (error) {
        console.log("AN ERROR OCCURED", error);
      }
    }

    function getEndDateTime(timestamp) {
      return moment(timestamp).format("DD/MM/YY [at] hh:mm A");
    }

    const getVideoToDisplay = (cuurentAnnouncement) => {
      const currentStatus = getVideoStatus(cuurentAnnouncement, "minutes", 20);

      switch (currentStatus) {
        case "PROCESSING":
          return (
            <LoadingFeedVideo
              content2="this post, or check back soon..."
              shouldReload
              reloadTime={500000}
            />
          );
        case "SUCCESSFUL":
          return (
            <div className={"w-full mt-1 overflow-hidden h-[520px]"}>
              {cuurentAnnouncement?.announcementType === "VOTE" ? (
                <VideoCarousel
                  voteSubmittedAssets={cuurentAnnouncement?.voteSubmittedAssets}
                />
              ) : (
                <AnnoucementPlayerVideo
                  streamUrl={cuurentAnnouncement.streamUrl}
                  assetUrl={cuurentAnnouncement.assetUrl}
                  isDetailPage={isDetailPage}
                />
              )}
            </div>
          );
        case "FAILED":
          return (
            <LoadingFeedVideo
              iconSrc="/images/Green-Uploadfailedicons.png"
              content1="Unfortunately, there was an error "
              content2="uploading this highlight. Please try again."
            />
          );
        default:
          break;
      }
    };

    useEffect(() => {
      if (!announcements && activeAnnouncements?.length > 0) {
        const latestAnnouncement = activeAnnouncements.sort(
          (a, b) => new Date(b.createdAt) - new Date(a.createdAt)
        )[0];
        setAnnouncements(latestAnnouncement);
      }
    }, [activeAnnouncements]);

    return (
      announcements &&
      Object.keys(announcements).length > 0 && (
        <div
          className={`py-[8px] mb-0 ${isDetailPage ? "pt-7" : "bg-[#F2F2F2]"}`}
        >
          {announcements?.announcementType !== "PINNED_POST" ? (
            <div className="flex justify-between items-center w-full px-3">
              {/* Text Section */}
              <div className="flex-grow max-w-full">
                <p className="text-[20px] font-favela-bold font-[700] leading-[24px]">
                  {announcements?.title}
                </p>
                {!["WINNER", "PINNED_POST"].includes(
                  announcements?.announcementType
                ) && (
                  <p className="text-[12px] font-poppins font-[400] leading-[18px]">
                    Submissions close{" "}
                    {getEndDateTime(announcements?.endDateTime)}
                  </p>
                )}
              </div>

              {/* Pin Section */}
              <div className="flex-shrink-0 ml-6">
                <img src={GreenPin} className="w-[30px] h-[30px]" />
              </div>
            </div>
          ) : (
            <div className="flex justify-between items-center w-full px-3">
              <div className="flex items-center gap-4">
                {/* Profile Image Section */}
                <img
                  src={"/images/favicon.jpeg"}
                  alt="profile pix"
                  className="w-[58px] h-[58px] bg-[#52FF00] object-cover rounded-full p-2"
                />
                {/* Text Section */}
                <div className="h-full flex-grow flex-col justify-between max-w-full my-[4.5px]">
                  <h6 className="text-[14px] text-black font-poppins font-medium">
                    PLAYER
                  </h6>
                  <p className="text-[14px] font-medium opacity-50">
                    {announcements?.title}
                  </p>
                </div>
              </div>

              {/* Pin Section */}
              <div className="flex-shrink-0 ml-6">
                <img src={GreenPin} className="w-[30px] h-[30px]" />
              </div>
            </div>
          )}

          {announcements.type === "PHOTO" && (
            <div
              className={`w-full overflow-hidden mt-6 h-[520px] flex justify-center items-center bg-black`}
            >
              <img
                onClick={() =>
                  navigate(
                    `/user/announcement?announcementId=${announcements.id}`
                  )
                }
                src={announcements.assetUrl}
                alt="feed"
                className="w-full h-[300px] object-cover"
              />
            </div>
          )}

          {announcements.type === "VIDEO" && getVideoToDisplay(announcements)}

          {Boolean(loggedUserId && liveLikeProfileToken) && (
            <div className="flex items-center gap-x-3 absolute mt-[20px] h-[48px] w-[90%] rounded-[10px] mx-auto mr-10 ml-[12px] ">
              <button
                onClick={() =>
                  setShouldShowEmojiPanel({
                    visible:
                      shouldShowEmojiPanel.visible &&
                      announcements.id === shouldShowEmojiPanel.highlightId
                        ? false
                        : true,
                    highlightId: announcements.id
                  })
                }
                className="hover:border-none bg-[#A0A0A0] m-0 p-0 hover:outline-none focus:outline-none w-[32px] h-[32px] rounded-full flex justify-center items-center"
              >
                <div className="w-[20px] h-[20px] flex justify-center items-center">
                  <NewSmile />
                </div>
              </button>

              {/*TODO: Handle button click - to use modal*/}
              <button
                onClick={(e) => {
                  if (onReactionClick) {
                    onReactionClick("comments");
                    navigate(
                      `/user/announcement?announcementId=${
                        announcements.id
                      }&type=${"comments"}`
                    );
                  } else {
                    navigate(
                      `/user/announcement?announcementId=${announcements.id}`
                    );
                  }
                }}
                className="hover:border-none bg-[#A0A0A0] m-0 p-0 hover:outline-none focus:outline-none w-[32px] h-[32px] rounded-full flex justify-center items-center"
              >
                <div className="w-[20px] h-[20px] flex justify-center items-center">
                  <NewCommentIcon />
                </div>
              </button>
              <button
                onClick={() =>
                  navigate("/feed/share", {
                    state: {
                      highlight: announcements,
                      type: "announcement"
                    }
                  })
                }
                className="hover:border-none bg-[#A0A0A0] m-0 p-0 hover:outline-none focus:outline-none w-[32px] h-[32px] rounded-full flex justify-center items-center"
              >
                <div className="w-[20px] h-[20px] flex justify-center items-center">
                  <NewShareIcon />
                </div>
              </button>

              <StackedReactions
                className="-top-[25px]"
                onReactionsClick={() => onReactionClick("reactions")}
                route={`/user/announcement?announcementId=${
                  announcements.id
                }&type=${"reactions"}`}
                highlight={announcements}
                uniqueEmojis={getUniqueEmojis(announcements?.reactedByUsers)}
              />

              {shouldShowEmojiPanel.visible &&
                shouldShowEmojiPanel.highlightId === announcements.id && (
                  <div className="absolute left-10 bg-[#F1F1F1] rounded-[10px] max-[300px]:w-[87%]">
                    {shouldShowEmojiPanel.visible &&
                    shouldShowEmojiPanel.highlightId === announcements.id
                      ? reactionPacks?.emojis.map((item, idx) => {
                          return (
                            <button
                              onBlur={() =>
                                setShouldShowEmojiPanel({
                                  visible: false,
                                  highlightId: ""
                                })
                              }
                              key={idx}
                              onClick={() => {
                                addUserReaction(announcements, item);
                              }}
                              className={`max-[300px]:-mx-[5px] -mx-1 focus-visible:bg-transparent hover:outline-none bg-transparent focus-within:bg-transparent focus:bg-transparent focus:border-none focus-within:border-none border-none focus-visible:outline-none hover:bg-transparent`}
                            >
                              <img
                                src={item.file}
                                className="h-[20px] max-[300px]:w-[20px] max-[300px]:h-[20px] w-[20px]"
                              />
                            </button>
                          );
                        })
                      : ""}
                  </div>
                )}
            </div>
          )}

          {/* //! Winner handle not showing */}
          {isDetailPage ? (
            <p className="mt-20 ml-[15px] mr-[10px] flex flex-col gap-2 space-x-2 text-[#000000] text-[12px] w-[90%]">
              {announcements?.announcementType === "WINNER" && (
                <span className="block mb-1">
                  Your competition winner is{" "}
                  <span
                    onClick={() =>
                      navigate({
                        pathname: "/profile",
                        search: `?id=${announcements?.winnerHandle?.userId}`
                      })
                    }
                    className="cursor-pointer underline text-[#7582B2]"
                  >
                    {announcements?.winnerHandle?.userName}
                  </span>
                </span>
              )}

              <TextWithPlayerAppMention
                text={
                  announcements?.announcementType === "WINNER"
                    ? `${announcements?.subtitle}`
                    : announcements?.subtitle
                }
                stringToReplace={
                  announcements?.announcementType !== "WINNER"
                    ? "@PLAYER"
                    : announcements?.winnerHandle?.userName
                }
                replacerComp={
                  announcements?.announcementType === "WINNER" && (
                    <span
                      className="text-blue-500"
                      onClick={() =>
                        navigate({
                          pathname: "/profile",
                          search: `?id=${announcements?.winnerHandle?.userId}`
                        })
                      }
                    >
                      {announcements?.winnerHandle?.userName}
                    </span>
                  )
                }
              />
            </p>
          ) : (
            <div
              className="mt-[65px] mx-4 relative"
              onClick={() =>
                navigate(
                  `/user/announcement?announcementId=${announcements.id}`
                )
              }
            >
              <div className="flex w-full items-end">
                <p
                  className={`text-[#000000] line-clamp-2 overflow-hidden whitespace-pre-line w-max cursor-pointer text-ellipsis`}
                >
                  {announcements?.announcementType === "PINNED_POST" && (
                    <span className="font-poppins font-[700] leading-[18px] ">
                      PLAYER{" "}
                    </span>
                  )}
                  {announcements?.announcementType === "WINNER" && (
                    <span className="block mb-1">
                      Your competition winner is{" "}
                      <span
                        onClick={(e) => {
                          e.stopPropagation();
                          navigate({
                            pathname: "/profile",
                            search: `?id=${announcements?.winnerHandle?.userId}`
                          });
                        }}
                        className="underline text-[#7582B2]"
                      >
                        {announcements?.winnerHandle?.userName}
                      </span>
                      .
                    </span>
                  )}
                  {/* ${announcements?.announcementType === "WINNER" ? 'line-clamp-1' : 'line-clamp-2'} */}
                  {announcements?.subtitle.slice(
                    0,
                    announcements?.announcementType === "WINNER" ? 47 : 97
                  )}
                  ...
                  <span className="text-[#00000066] text-[12px] underline ml-[3px] w-[100px] cursor-pointer">
                    See more
                  </span>
                </p>
              </div>

              <p className="flex text-[12px] text-[#808080] items-center mt-2">
                View all comments
              </p>
            </div>
          )}
        </div>
      )
    );
  }
);

export default Competition;
