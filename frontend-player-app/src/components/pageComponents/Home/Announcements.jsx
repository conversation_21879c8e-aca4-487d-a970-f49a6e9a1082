import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import ReactionIcon from "../../svgIcons/ReactionIcon";
import CommentIcon from "../../svgIcons/CommentIcon";
import OpenFeed from "../Feeds/OpenFeed";
import { InView } from "react-cool-inview";
import Videos from "../../reusable/Videos";
import { PlayIcon } from "../../svgIcons";
import VideoUI from "../../reusable/VideoUI";
import { useSelector } from "react-redux";
import Pin from "../../svgIcons/Pin";
import { dispatch } from "../../../redux/store";
import { getUniqueEmojis, notifyError } from "../../../utils/helpers";
import Analytics from "../../../utils/google-analytics";
import { TRACKING_EVENTS } from "../../../utils/constants";

const Announcements = ({
  loggedUserId,
  liveLikeProfileToken,
  reactionPacks,
}) => {
  const [openFeed, setOpenFeed] = useState("");
  const [announcements, setAnnouncements] = useState([]);
  const [shouldShowEmojiPanel, setShouldShowEmojiPanel] = useState({
    visible: false,
    highlightId: "",
  });

  const { activeAnnouncements } = useSelector(
    ({ announcement }) => announcement
  );
  const userInfo = useSelector((state) => state.user.data);

  const navigate = useNavigate();

  const handleClose = () => {
    setOpenFeed("");
  };

  async function addUserReaction(announcement, reaction) {
    setShouldShowEmojiPanel({ visible: false, highlightId: "" });
    function filterById(reactedBy, userId) {
      const filteredArray = reactedBy.filter((item) => item.userId !== userId);
      return filteredArray;
    }

    try {
      const existingAnnouncement = activeAnnouncements?.find(
        (item) => item.id === announcement.id
      );

      if (existingAnnouncement) {
        const filteredReactions = filterById(
          existingAnnouncement.reactedByUsers,
          userInfo.id
        );
        const reactedByUsersWithNewReaction = [
          {
            userId: userInfo.id,
            others: {
              user: {
                firstName: userInfo.firstName,
                lastName: userInfo.lastName,
                photoUrl: userInfo.photoUrl,
                cludId: userInfo.cludId,
                gender: userInfo.gender,
              },
              emoji: reaction.file,
              emojiName: reaction.name,
            },
          },
          ...filteredReactions,
        ];
        await dispatch.announcement.updateAnnouncement({
          id: announcement.id,
          reactedByUsers: reactedByUsersWithNewReaction,
        });

        Analytics.trackEvent({
          name: TRACKING_EVENTS.REACTION_ADDED,
          metadata: {
            reaction_type: reaction?.name,
            content_id: announcement?.id,
            content_type: "announcement",
          }
        })
      }
    } catch (error) {
      console.log("AN ERROR OCCURED", error);
    }
  }

  const uniqueEmojis = getUniqueEmojis(
    activeAnnouncements[0]?.reactedByUsers || []
  );

  useEffect(() => {
    if (activeAnnouncements?.length > 0) {
      const latestAnnouncement = activeAnnouncements.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))[0]
      setAnnouncements(latestAnnouncement);

    }
  }, [activeAnnouncements]);

  return (
    announcements &&
    Object.keys(announcements).length > 0 && (
      <div className="pb-32 mb-10 pt-7 bg-[#F4F3F3] px-[15px]">
        <div className="flex justify-between w-full">
          <div className="flex gap-2 items-center">
            <img
              src={announcements.logoUrl}
              alt="profile pix"
              className="w-[58px] h-[58px] object-cover rounded-full"
            />
            <div>
              <p className="text-[14px] font-medium">{announcements?.title}</p>
              <p className="text-[14px] font-medium"></p>
              <p className="text-[14px] font-medium opacity-50">
                {announcements?.subtitle}
              </p>
            </div>
          </div>
          <div className="flex gap-2 items-center">
            <Pin />
          </div>
        </div>
        {announcements.type === "PHOTO" && (
          <div className="w-full rounded-[32px] overflow-hidden mt-6 aspect-[4/5]">
            <img
              onClick={() =>
                navigate(`/user/comment?announcementId=${announcements.id}`)
              }
              src={announcements.assetUrl}
              alt="feed"
              className="w-full h-full object-cover"
            />
            {Boolean(loggedUserId && liveLikeProfileToken) && (
              <div className="flex items-center gap-x-0 absolute mt-[32px]  h-[48px] w-[90%] rounded-[10px] mx-auto mr-10">
                <button
                  onClick={() =>
                    setShouldShowEmojiPanel({
                      visible:
                        shouldShowEmojiPanel.visible &&
                          announcements.id === shouldShowEmojiPanel.highlightId
                          ? false
                          : true,
                      highlightId: announcements.id,
                    })
                  }
                  onBlur={() =>
                    setShouldShowEmojiPanel({ visible: false, highlightId: "" })
                  }
                  className="hover:border-none bg-transparent m-0 p-0 hover:outline-none focus:outline-none"
                >
                  <ReactionIcon />
                </button>
                {/*TODO: Handle button click - to use modal*/}
                <button
                  onClick={() =>
                    navigate(`/user/comment?announcementId=${announcements.id}`)
                  }
                  className="hover:border-none bg-transparent m-0 p-0 hover:outline-none focus:outline-none"
                >
                  <CommentIcon />
                </button>
                <div className="absolute -top-[27px] w-full left-0 flex justify-between">
                  <div
                    onClick={() =>
                      navigate(
                        `/user/comment?announcementId=${announcements.id}`,
                        {
                          state: {
                            data: announcements,
                          },
                        }
                      )
                    }
                    className="flex items-center justify-between"
                  >
                    {uniqueEmojis.length > 0 &&
                      uniqueEmojis?.map((item, idx) => {
                        return (
                          <div
                            key={idx}
                            className="rounded-full flex justify-center items-center bg-[#D9D9D9] h-[22px] w-[22px] -mr-4"
                            onClick={() =>
                              navigate(
                                `/user/comment?announcementId=${announcements.id
                                }&userId=${announcements.userId
                                }&type=${"reactions"}`
                              )
                            }
                          >
                            <img src={item} className="h-[20px] w-[20px]" />
                          </div>
                        );
                      })}
                    <p
                      onClick={() =>
                        navigate(
                          `/user/comment?announcementId=${announcements.id
                          }&type=${"reactions"}`
                        )
                      }
                      className="text-base text-[#808080] ml-6 self-center text-center"
                    >
                      {announcements?.reactedByUsers?.length || "0 reactions"}
                    </p>
                  </div>
                  <div
                    className="flex text-[#808080] items-center"
                    onClick={() =>
                      navigate(
                        `/user/comment?announcementId=${announcements.id}`
                      )
                    }
                  >
                    {Number.isInteger(announcements?.totalCommentCount)
                      ? announcements.totalCommentCount
                      : 0}{" "}
                    {Number(announcements?.totalCommentCount) === 0 ||
                      Number(announcements?.totalCommentCount) > 1
                      ? "comments"
                      : "comment"}
                  </div>
                </div>
                {shouldShowEmojiPanel.visible &&
                  shouldShowEmojiPanel.highlightId === announcements.id && (
                    <div className="absolute left-10 bg-[#F1F1F1] rounded-[10px] max-[300px]:w-[87%]">
                      {shouldShowEmojiPanel.visible &&
                        shouldShowEmojiPanel.highlightId === announcements.id
                        ? reactionPacks?.emojis.map((item, idx) => {
                          return (
                            <button
                              onBlur={() =>
                                setShouldShowEmojiPanel({
                                  visible: false,
                                  highlightId: "",
                                })
                              }
                              key={idx}
                              onClick={() =>
                                addUserReaction(announcements, item)
                              }
                              className={`max-[300px]:-mx-[5px] -mx-1 focus-visible:bg-transparent hover:outline-none bg-transparent focus-within:bg-transparent focus:bg-transparent focus:border-none focus-within:border-none border-none focus-visible:outline-none hover:bg-transparent`}
                            >
                              <img
                                src={item.file}
                                className="h-[20px] max-[300px]:w-[20px] max-[300px]:h-[20px] w-[20px]"
                              />
                            </button>
                          );
                        })
                        : ""}
                    </div>
                  )}
              </div>
            )}
            {openFeed === announcements.assetUrl && (
              <OpenFeed
                isAnnouncement={true}
                highlight={announcements}
                closeModal={handleClose}
              >
                <div
                  onClick={() =>
                    navigate(`/user/comment?announcementId=${announcements.id}`)
                  }
                  className="w-full rounded-[32px] overflow-hidden aspect-[4/5]"
                >
                  <img
                    src={announcements.assetUrl}
                    alt="feed"
                    className="w-full h-full object-cover"
                  />
                </div>
              </OpenFeed>
            )}
          </div>
        )}
        {announcements.type === "VIDEO" && (
          <>
            <div className="w-full rounded-[32px] mt-6 overflow-hidden aspect-video">
              <div
                className="w-full h-full relative"
                onClick={() =>
                  setOpenFeed(openFeed === "" ? announcements.assetUrl : "")
                }
              >
                <InView threshold={1} rootMargin="5px 0px">
                  <Videos
                    // setCurrentVideo={setCurrentVideo}
                    url={announcements.assetUrl}
                    className="w-full h-full bg-black rounded-[32px] overflow-hidden"
                  />
                </InView>
                <div className="absolute top-0 right-0 bottom-0 left-0 flex justify-center items-center">
                  <div className="w-[50px] h-[50px] bg-gray-100 rounded-full flex justify-center items-center">
                    <PlayIcon />
                  </div>
                </div>
                {/* )} */}
              </div>
              {Boolean(loggedUserId && liveLikeProfileToken) && (
                <div className="flex absolute mt-[32px]  h-[48px] w-[90%] rounded-[10px] mx-auto mr-10">
                  <button
                    onClick={() =>
                      setShouldShowEmojiPanel({
                        visible:
                          shouldShowEmojiPanel.visible &&
                            announcements.id === shouldShowEmojiPanel.highlightId
                            ? false
                            : true,
                        highlightId: announcements.id,
                      })
                    }
                    onBlur={() =>
                      setShouldShowEmojiPanel({
                        visible: false,
                        highlightId: "",
                      })
                    }
                    className="hover:border-none bg-transparent m-0 p-0 hover:outline-none focus:outline-none"
                  >
                    <ReactionIcon />
                  </button>
                  {/*TODO: Handle button click - to use modal*/}
                  <button
                    onClick={() =>
                      navigate(
                        `/user/comment?announcementId=${announcements.id}`
                      )
                    }
                    className="hover:border-none bg-transparent m-0 p-0 hover:outline-none focus:outline-none"
                  >
                    <CommentIcon />
                  </button>
                  <div className="absolute -top-[27px] w-full left-0 flex justify-between">
                    <div
                      onClick={() =>
                        navigate("/feed/reaction-list", {
                          state: {
                            announcements,
                          },
                        })
                      }
                      className="flex items-center justify-between"
                    >
                      {uniqueEmojis
                        ? uniqueEmojis?.map((item, idx) => {
                          return (
                            <div
                              key={idx}
                              className="rounded-full flex justify-center items-center bg-[#D9D9D9] h-[22px] w-[22px] -mr-4"
                            >
                              <img src={item} className="h-[20px] w-[20px]" />
                            </div>
                          );
                        })
                        : ""}
                      <p className="text-base text-[#808080] ml-6 self-center text-center mt-1">
                        {announcements?.reactedByUsers?.length || 0}
                      </p>
                    </div>
                    <div
                      className="flex text-[#808080] items-center"
                      onClick={() =>
                        navigate(
                          `/user/comment?announcementId=${announcements.id}`
                        )
                      }
                    >
                      {Number.isInteger(announcements?.totalCommentCount)
                        ? announcements.totalCommentCount
                        : 0}{" "}
                      {Number(announcements?.totalCommentCount) === 0 ||
                        Number(announcements?.totalCommentCount) > 1
                        ? "comments"
                        : "comment"}
                    </div>
                  </div>
                  {shouldShowEmojiPanel.visible &&
                    shouldShowEmojiPanel.highlightId === announcements.id && (
                      <div className="absolute left-10 bg-[#F1F1F1] rounded-[10px] max-[300px]:w-[87%]">
                        {shouldShowEmojiPanel.visible &&
                          shouldShowEmojiPanel.highlightId === announcements.id
                          ? reactionPacks?.emojis.map((item, idx) => {
                            return (
                              <button
                                key={idx}
                                onClick={() =>
                                  addUserReaction(announcements, item)
                                }
                                className={`max-[300px]:-mx-[5px] -mx-1 focus-visible:bg-transparent hover:outline-none bg-transparent focus-within:bg-transparent focus:bg-transparent focus:border-none focus-within:border-none border-none focus-visible:outline-none hover:bg-transparent`}
                              >
                                <img
                                  src={item.file}
                                  className="h-[20px] max-[300px]:w-[20px] max-[300px]:h-[20px] w-[20px]"
                                />
                              </button>
                            );
                          })
                          : ""}
                      </div>
                    )}
                </div>
              )}
            </div>
            {openFeed === announcements.assetUrl && (
              <OpenFeed
                isAnnouncement={true}
                closeModal={handleClose}
                highlight={announcements}
              >
                <VideoUI videoUrl={announcements.assetUrl} />
              </OpenFeed>
            )}
          </>
        )}
      </div>
    )
  );
};

export default Announcements;
