import React, { useCallback, useEffect, useRef, useState } from "react";
import LiveLike from "@livelike/engagementsdk";
import _debounce from "lodash/debounce";
import { useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import { useInView } from "react-cool-inview";

import {
  LiveLikeReactionPacks,
  TRACKING_EVENTS,
  currentEnv,
  liveLikeClientId
} from "../../../utils/constants";
import { getUniqueEmojis, shouldShowWaitIcon } from "../../../utils/helpers";
import { dispatch } from "../../../redux/store";
import Videos from "../../reusable/Videos";
import Share from "../../svgIcons/Share";
import CommentIcon from "../../svgIcons/CommentIcon";
import ReactionIcon from "../../svgIcons/ReactionIcon";
import ShareModal from "../../reusable/ShareModal";
import moment from "moment";
import ResultHighlights from "../Feeds/ResultHighlights";
import ReactPlayerVideo from "../../reusable/ReactPlayerVideo";
import Competition from "./Competition";
import ShareButton from "../../reusable/ShareButton";
import CommentButton from "../../reusable/CommentButton";
import ReactionsButton from "../../reusable/ReactionsButton";
import NewSmile from "../../svgIcons/NewSmile";
import { NewCommentIcon, NewShareIcon } from "../../svgIcons";
import StackedReactions from "../Feeds/StackedReactions";
import ReactCommentShare from "../Feeds/ReactCommentShare";
import LoadingFeedVideo from "../Feeds/LoadingFeedVideo";
import Analytics from "../../../utils/google-analytics";

const Feeds = () => {
  const [shouldShowEmojiPanel, setShouldShowEmojiPanel] = useState({
    visible: false,
    highlightId: ""
  });
  const [isFetching, setIsFetching] = useState(false);
  const [selectedHighlight, setSelectedHighlight] = useState(null);
  const navigate = useNavigate();

  const {
    highlights,
    lastEvaluatedKey,
    data: allHihglightsData
  } = useSelector(({ feed }) => feed);

  const userInfo = useSelector((state) => state.user.data);
  const loadingHighlights = useSelector(
    ({ loading }) => loading.effects.feed.fetchHighlights
  );

  const { activeAnnouncements } = useSelector(
    ({ announcement }) => announcement
  );

  const getUniqueEmoji = useCallback(
    (usersReactions) => getUniqueEmojis(usersReactions),
    [highlights]
  );

  const handleShareModalOpen = (highlight) => {
    setSelectedHighlight(highlight);
  };

  const { observe, updatePosition } = useInView({
    // For better UX, we can grow the root margin so the data will be loaded earlier
    rootMargin: "100px 0px",
    threshold: 0,
    // When the last item comes to the viewport
    onEnter: ({ unobserve }) => {
      // Pause observe when loading data
      unobserve();
      if (
        highlights.length > 0 &&
        !lastEvaluatedKey &&
        allHihglightsData?.length === highlights.length
      ) {
        console.log("No more data to load");
        // setIsFetching(true);
        return;
      }

      // Load more data
      dispatch.feed.fetchHighlights({
        lastEvaluatedKey,
        cb: () => setIsFetching
      });

      window.scrollTo(0, window.document.body.scrollHeight);
      updatePosition();
    }
  });

  const { loggedUserId, liveLikeProfileId, liveLikeProfileToken } = useSelector(
    ({ auth: { authUser }, user }) => ({
      loggedUserId: authUser?.userInfo?.id || "",
      liveLikeProfileId: user?.data?.liveLikeProfileId || "",
      liveLikeProfileToken: user?.data?.liveLikeProfileToken || "",
      authUser
    })
  );

  const handleOpenProfile = (id) => {
    if (id) {
      navigate({ pathname: "/profile", search: `?id=${id}` });
    } else {
      navigate("/login");
    }
  };

  async function addUserReaction(highlight, reaction) {
    setShouldShowEmojiPanel({ visible: false, highlightId: "" });
    function filterById(reactedBy, userId) {
      if (reactedBy) {
        const filteredArray = reactedBy.filter(
          (item) => item.userId !== userId
        );
        return filteredArray;
      }
    }

    try {
      const existingHighlights = highlights?.find(
        (item) => item.id === highlight.id
      );

      if (existingHighlights) {
        const filteredReactions =
          filterById(existingHighlights.reactedByUsers, userInfo?.id) || [];
        const reactedByUsersWithNewReaction = [
          ...filteredReactions,
          {
            userId: userInfo?.id,
            others: {
              user: {
                firstName: userInfo.firstName,
                lastName: userInfo.lastName,
                photoUrl: userInfo.photoUrl,
                cludId: userInfo.cludId,
                gender: userInfo.gender
              },
              emoji: reaction.file,
              emojiName: reaction.name
            }
          }
        ];

        await dispatch.feed.editHighlights({
          userId: highlight.userId,
          id: highlight.id,
          reactedByUsers: reactedByUsersWithNewReaction,
          noNotification: true,
          isEmojiUpdate: true
        });

        Analytics.trackEvent({
          name: TRACKING_EVENTS.REACTION_ADDED,
          metadata: {
            reaction_type: reaction?.name,
            content_id: highlight?.id,
            content_type: "highlights",
          }
        })
      }
    } catch (error) {
      console.log("AN ERROR OCCURED", error);
    }
  }

  // TO BE REMOVED ONCE ALL USERS WHO HAVE ISSUE LOGGING OUT ARE FIXED
  useEffect(() => {
    if (loggedUserId) {
      dispatch.user.getUserProfile({
        id: loggedUserId
      });
    }
  }, []);

  return (
    <div className="">
      <div className="mb-[50px] mt-[20px]">
        <Competition
          activeAnnouncements={activeAnnouncements}
          liveLikeProfileToken={liveLikeProfileToken}
          loggedUserId={loggedUserId}
          reactionPacks={LiveLikeReactionPacks}
        />
      </div>
      <div className="pb-[70px]">
        {highlights?.length > 0
          ? highlights?.map((highlight, idx) => {
            const uniqueEmojis = getUniqueEmoji(highlight?.reactedByUsers);
            return (
              <div
                ref={idx === highlights.length - 1 ? observe : null}
                key={highlight.id + idx}
                className="mb-44"
              >
                {highlight?.type !== "RESULT" && (
                  <div className="flex gap-2 items-center">
                    <img
                      onClick={() => handleOpenProfile(highlight.user.id)}
                      src={`${highlight?.user?.photoUrl?.length > 5
                        ? highlight?.user?.photoUrl
                        : "/images/profile.png"
                        }`}
                      alt="profile pix"
                      className="w-[58px] h-[58px] object-cover rounded-full mx-2"
                    />
                    <div>
                      <p
                        onClick={() => handleOpenProfile(highlight.user.id)}
                        className="text-[14px] font-medium"
                      >
                        {`@${highlight?.user?.firstName} ${highlight?.user?.lastName}`}
                      </p>
                      <p className="text-[14px] font-medium"></p>
                      <p className="text-[14px] font-medium opacity-50">
                        {(highlight?.user?.teamName !== "N/A" &&
                          highlight?.user?.teamName) ||
                          highlight?.user?.clubName}
                      </p>
                      {/* <p className="font-medium opacity-50 text-ellipsis whitespace-nowrap max-w-[300px] overflow-clip ">
                      {highlight?.comment}
                    </p> */}
                    </div>
                  </div>
                )}
                {highlight?.type === "PHOTO" && (
                  <div className="w-full rounded-[0px] overflow-hidden mt-4 h-[520px]">
                    <img
                      onClick={() =>
                        navigate(
                          `/user/comment?highlightId=${highlight.id}&userId=${highlight.userId}`
                        )
                      }
                      src={highlight.url}
                      alt="feed"
                      className="w-full h-full object-cover"
                    />

                    {Boolean(loggedUserId && liveLikeProfileToken) ? (
                      <div className="flex items-start gap-x-2 absolute mt-[32px] h-[48px] w-[90%] rounded-[10px] mx-auto mr-10 ml-[12px]">
                        <ReactCommentShare
                          onClickReact={() =>
                            setShouldShowEmojiPanel({
                              visible:
                                shouldShowEmojiPanel.visible &&
                                  highlight.id ===
                                  shouldShowEmojiPanel.highlightId
                                  ? false
                                  : true,
                              highlightId: highlight.id
                            })
                          }
                          onClickComment={() =>
                            navigate(
                              `/user/comment?highlightId=${highlight.id}&userId=${highlight.userId}`
                            )
                          }
                          onClickShare={() =>
                            navigate("/feed/share", {
                              state: {
                                highlight,
                                type: "highlight"
                              }
                            })
                          }
                        />

                        <StackedReactions
                          highlight={highlight}
                          uniqueEmojis={uniqueEmojis}
                        />

                        {shouldShowEmojiPanel.visible &&
                          shouldShowEmojiPanel.highlightId ===
                          highlight.id && (
                            <div className="absolute left-10 bg-[#F1F1F1] rounded-[10px] max-[300px]:w-[87%]">
                              {shouldShowEmojiPanel.visible &&
                                shouldShowEmojiPanel.highlightId ===
                                highlight.id
                                ? LiveLikeReactionPacks?.emojis.map(
                                  (item, idx) => {
                                    return (
                                      <button
                                        key={idx}
                                        onClick={() =>
                                          addUserReaction(highlight, item)
                                        }
                                        className={`max-[300px]:-mx-[5px] -mx-1 focus-visible:bg-transparent hover:outline-none bg-transparent focus-within:bg-transparent focus:bg-transparent focus:border-none focus-within:border-none border-none focus-visible:outline-none hover:bg-transparent`}
                                      >
                                        <img
                                          src={item.file}
                                          className="h-[20px] max-[300px]:w-[20px] max-[300px]:h-[20px] w-[20px]"
                                        />
                                      </button>
                                    );
                                  }
                                )
                                : ""}
                            </div>
                          )}
                      </div>
                    ) : (
                      <div className="flex items-center gap-x-0 absolute mt-[5px] h-[48px] w-[90%] rounded-[10px] mx-auto mr-10">
                        <ShareModal
                          header={`Share ${`${selectedHighlight?.user?.firstName}'s`} post`}
                          // title={`Check out this ${`${selectedHighlight?.user?.firstName}'s`} PLAYER post: \n \n`}
                          title={`Check out this post PLAYER. \n \n`}
                          url={`${`${window.location.origin}/user/comment?highlightId=${selectedHighlight?.id}&userId=${selectedHighlight?.userId}`}`}
                        />
                        <label
                          onClick={() => handleShareModalOpen(highlight)}
                          htmlFor="share-modal"
                          className="w-[30px] h-[30px] rounded-full cursor-pointer overflow-hidden mr-5"
                        >
                          <Share />
                        </label>
                      </div>
                    )}

                    <div className="font-poppins absolute mt-[70px] h-[48px] px-2 text-[12px] leading-[18px] ml-[11px]">
                      {/* Texts from the given hightlight */}
                      <p className="text-ellipsis whitespace-nowrap max-w-[350px] overflow-clip">
                        <span className="font-bold">{`${highlight?.user?.firstName}${highlight?.user?.lastName} `}</span>
                        {highlight?.comment?.substring(0, 100)}...
                        <span
                          className="underline text-[11px] text-[#00000066]"
                          onClick={() =>
                            navigate(
                              `/user/comment?highlightId=${highlight.id}&userId=${highlight.userId}`
                            )
                          }
                        >
                          See more
                        </span>
                      </p>

                      {/* View all comments */}
                      <p
                        className="flex text-[#808080] items-center mt-2"
                        onClick={() =>
                          navigate(
                            `/user/comment?highlightId=${highlight.id}&userId=${highlight.userId}`
                          )
                        }
                      >
                        View all comments
                      </p>
                    </div>
                  </div>
                )}
                {highlight?.type === "VIDEO" && (
                  <>
                    <div className="w-full rounded-[0px] mt-0 h-[520px] ">
                      <div className="w-full h-full relative">
                        {highlight?.streamUrl &&
                          shouldShowWaitIcon(highlight) ? (
                          <LoadingFeedVideo />
                        ) : highlight?.videoProcessingFailed ? (
                          <LoadingFeedVideo
                            iconSrc="/images/Green-Uploadfailedicons.png"
                            content1="Unfortunately, there was an error "
                            content2="uploading this highlight. Please try again."
                          />
                        ) : (
                          <ReactPlayerVideo
                            highlight={highlight}
                            highlightId={highlight.id}
                            comment={highlight.comment}
                          />
                        )}
                      </div>
                      {Boolean(loggedUserId && liveLikeProfileToken) ? (
                        <div className="flex items-start gap-x-2 absolute mt-[32px] h-[48px] w-[90%] rounded-[10px] mx-auto mr-10 ml-[12px]">
                          <ReactCommentShare
                            onClickReact={() =>
                              setShouldShowEmojiPanel({
                                visible:
                                  shouldShowEmojiPanel.visible &&
                                    highlight.id ===
                                    shouldShowEmojiPanel.highlightId
                                    ? false
                                    : true,
                                highlightId: highlight.id
                              })
                            }
                            onClickComment={() =>
                              navigate(
                                `/user/comment?highlightId=${highlight.id}&userId=${highlight.userId}`
                              )
                            }
                            onClickShare={() =>
                              navigate("/feed/share", {
                                state: {
                                  highlight,
                                  type: "highlight"
                                }
                              })
                            }
                          />

                          {/* Manages the stacked reactions for selected reactions from users */}
                          <StackedReactions
                            highlight={highlight}
                            uniqueEmojis={uniqueEmojis}
                          />

                          {/* Add a reaction to a highlight */}
                          {shouldShowEmojiPanel.visible &&
                            shouldShowEmojiPanel.highlightId ===
                            highlight.id && (
                              <div className="absolute left-10 bg-[#F1F1F1] rounded-[10px] max-[300px]:w-[87%]">
                                {shouldShowEmojiPanel.visible &&
                                  shouldShowEmojiPanel.highlightId ===
                                  highlight.id
                                  ? LiveLikeReactionPacks?.emojis.map(
                                    (item, idx) => {
                                      return (
                                        <button
                                          key={idx}
                                          onClick={() =>
                                            addUserReaction(highlight, item)
                                          }
                                          className={`max-[300px]:-mx-[5px] -mx-1 focus-visible:bg-transparent hover:outline-none bg-transparent focus-within:bg-transparent focus:bg-transparent focus:border-none focus-within:border-none border-none focus-visible:outline-none hover:bg-transparent`}
                                        >
                                          <img
                                            src={item.file}
                                            className="h-[20px] max-[300px]:w-[20px] max-[300px]:h-[20px] w-[20px]"
                                          />
                                        </button>
                                      );
                                    }
                                  )
                                  : ""}
                              </div>
                            )}
                        </div>
                      ) : (
                        <div className="flex items-center gap-x-0 absolute mt-[5px] h-[48px] w-[90%] rounded-[10px] mx-auto mr-10">
                          <ShareModal
                            header={`Share ${`${selectedHighlight?.user?.firstName}'s`} post`}
                            // title={`Check out ${`${selectedHighlight?.user?.firstName}'s`} PLAYER post: \n \n`}
                            title={`Check out this post on PLAYER. \n \n`}
                            url={`${`${window.location.origin}/user/comment?highlightId=${selectedHighlight?.id}&userId=${selectedHighlight?.userId}`}`}
                          />
                          <label
                            onClick={() => handleShareModalOpen(highlight)}
                            htmlFor="share-modal"
                            className="w-[30px] h-[30px] rounded-full cursor-pointer overflow-hidden mr-5"
                          >
                            <Share />
                          </label>
                        </div>
                      )}

                      <div className="font-poppins absolute mt-[70px] h-[48px] px-2 text-[12px] leading-[18px] ml-[11px]">
                        {/* Texts from the given hightlight */}
                        <p className="text-ellipsis whitespace-nowrap max-w-[350px] overflow-clip">
                          <span className="font-bold">{`${highlight?.user?.firstName}${highlight?.user?.lastName} `}</span>
                          {highlight?.comment?.substring(0, 100)}...
                          <span
                            onClick={() =>
                              navigate(
                                `/user/comment?highlightId=${highlight.id}&userId=${highlight.userId}`
                              )
                            }
                            className="underline text-[11px] text-[#00000066]"
                          >
                            See more
                          </span>
                        </p>

                        {/* View all comments */}
                        <p
                          className="flex text-[#808080] items-center mt-2"
                          onClick={() =>
                            navigate(
                              `/user/comment?highlightId=${highlight.id}&userId=${highlight.userId}`
                            )
                          }
                        >
                          View all comments
                        </p>
                      </div>
                    </div>
                    {/* {openFeed === highlight.url && (
                    <OpenFeed closeModal={handleClose} highlight={highlight}>
                      <VideoUI videoUrl={highlight.url} />
                    </OpenFeed>
                  )} */}
                  </>
                )}

                {highlight?.type === "RESULT" &&
                  highlight?.allowMatchOnFeeds && (
                    <div className="w-full rounded-[32px] mt-6 -mb-6">
                      <div
                        onClick={() =>
                          navigate(
                            `/user/comment?highlightId=${highlight.id}&userId=${highlight.userId}`
                          )
                        }
                      >
                        <ResultHighlights highlight={highlight} />
                      </div>
                      {Boolean(loggedUserId && liveLikeProfileToken) ? (
                        <div className="flex items-start gap-x-0 absolute mt-[32px]  h-[48px] w-[90%] rounded-[10px] mx-auto mr-10">
                          <button
                            onClick={() =>
                              setShouldShowEmojiPanel({
                                visible:
                                  shouldShowEmojiPanel.visible &&
                                    highlight.id ===
                                    shouldShowEmojiPanel.highlightId
                                    ? false
                                    : true,
                                highlightId: highlight.id
                              })
                            }
                            className="hover:border-none bg-transparent m-0 p-0 hover:outline-none focus:outline-none"
                          >
                            <ReactionIcon />
                          </button>
                          {/*TODO: Handle button click - to use modal*/}
                          <button
                            onClick={() =>
                              navigate(
                                `/user/comment?highlightId=${highlight.id}&userId=${highlight.userId}`
                              )
                            }
                            className="hover:border-none bg-transparent m-0 p-0 hover:outline-none focus:outline-none"
                          >
                            <CommentIcon />
                          </button>

                          <button
                            onClick={() =>
                              navigate("/feed/share", {
                                state: {
                                  highlight,
                                  type: "highlight"
                                }
                              })
                            }
                            className="hover:border-none bg-transparent m-0 p-0 hover:outline-none focus:outline-none"
                          >
                            <Share />
                          </button>
                          <div className="absolute text-[#808080] -top-[30px] w-full left-0 flex justify-between">
                            {uniqueEmojis.length > 0 ? (
                              <div
                                onClick={() =>
                                  navigate(
                                    `/user/comment?highlightId=${highlight.id
                                    }&userId=${highlight.userId
                                    }&type=${"reactions"}`
                                  )
                                }
                                className="flex w-12 py-1 items-center justify-between"
                              >
                                {uniqueEmojis.map((item, idx) => {
                                  return (
                                    idx < 5 && (
                                      <div
                                        key={idx}
                                        className="rounded-full flex justify-center items-center bg-[#D9D9D9] h-[22px] w-[22px] -mr-4"
                                      >
                                        <img
                                          src={item}
                                          className="h-[20px] w-[20px]"
                                        />
                                      </div>
                                    )
                                  );
                                })}
                                <p
                                  onClick={() =>
                                    navigate(
                                      `/user/comment?highlightId=${highlight.id
                                      }&userId=${highlight.userId
                                      }&type=${"reactions"}`
                                    )
                                  }
                                  className="text-base text-[#808080] ml-6 self-center text-center"
                                >
                                  {highlight?.reactedByUsers
                                    ? highlight?.reactedByUsers?.length
                                    : "0 reactions"}
                                </p>
                              </div>
                            ) : (
                              <p
                                onClick={() =>
                                  navigate(
                                    `/user/comment?highlightId=${highlight.id
                                    }&userId=${highlight.userId
                                    }&type=${"reactions"}`
                                  )
                                }
                                className="text-base text-[#808080] ml-6 self-center text-center"
                              >
                                0 reactions
                              </p>
                            )}
                            <div
                              className="flex text-[#808080] items-center"
                              onClick={() =>
                                navigate(
                                  `/user/comment?highlightId=${highlight.id}&userId=${highlight.userId}`
                                )
                              }
                            >
                              {Number.isInteger(highlight?.totalCommentCount)
                                ? highlight.totalCommentCount
                                : 0}{" "}
                              {Number(highlight?.totalCommentCount) === 0 ||
                                Number(highlight?.totalCommentCount) > 1
                                ? "comments"
                                : "comment"}
                            </div>
                          </div>
                          {shouldShowEmojiPanel.visible &&
                            shouldShowEmojiPanel.highlightId ===
                            highlight.id && (
                              <div className="absolute left-10 bg-[#F1F1F1] rounded-[10px] max-[300px]:w-[87%]">
                                {shouldShowEmojiPanel.visible &&
                                  shouldShowEmojiPanel.highlightId ===
                                  highlight.id
                                  ? LiveLikeReactionPacks?.emojis.map(
                                    (item, idx) => {
                                      return (
                                        <button
                                          key={idx}
                                          onClick={() =>
                                            addUserReaction(highlight, item)
                                          }
                                          className={`max-[300px]:-mx-[5px] -mx-1 focus-visible:bg-transparent hover:outline-none bg-transparent focus-within:bg-transparent focus:bg-transparent focus:border-none focus-within:border-none border-none focus-visible:outline-none hover:bg-transparent`}
                                        >
                                          <img
                                            src={item.file}
                                            className="h-[20px] max-[300px]:w-[20px] max-[300px]:h-[20px] w-[20px]"
                                          />
                                        </button>
                                      );
                                    }
                                  )
                                  : ""}
                              </div>
                            )}
                        </div>
                      ) : (
                        <div className="flex items-center gap-x-0 absolute mt-[5px] h-[48px] w-[90%] rounded-[10px] mx-auto mr-10">
                          <ShareModal
                            header={`Share ${`${selectedHighlight?.user?.firstName}'s`} post`}
                            title={`Check out this post on PLAYER. \n \n`}
                            // title={`Check out ${`${selectedHighlight?.user?.firstName}'s`} PLAYER post: \n \n`}
                            url={`${`${window.location.origin}/user/comment?highlightId=${selectedHighlight?.id}&userId=${selectedHighlight?.userId}`}`}
                          />
                          <label
                            onClick={() => handleShareModalOpen(highlight)}
                            htmlFor="share-modal"
                            className="w-[30px] h-[30px] rounded-full cursor-pointer overflow-hidden mr-5"
                          >
                            <Share />
                          </label>
                        </div>
                      )}
                    </div>
                  )}
              </div>
            );
          })
          : !loadingHighlights && (
            <div className="h-[60vh] flex justify-center items-center">
              Oops!, Please reload the page
            </div>
          )}
        {Boolean(highlights?.length === 0) && loadingHighlights && (
          <div className="h-[60vh] bg-gray-400 w-full animate-pulse rounded-[0px]"></div>
        )}
        {isFetching && (
          <div className="h-[40vh] bg-gray-400 w-full animate-pulse rounded-[0px]"></div>
        )}
      </div>
    </div>
  );
};

export default Feeds;
