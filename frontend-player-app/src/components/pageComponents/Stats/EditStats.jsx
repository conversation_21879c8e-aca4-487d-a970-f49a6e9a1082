import PageSubTitle from "../../reusable/PageTitle";
import { useSelector } from "react-redux";
import { dispatch } from "../../../redux/store";
import { useFormik } from "formik";
import { editStats } from "../../../utils/formSchema";
import { BigButtons } from "../../reusable/buttons/Buttons";

// TODO: REMOVE IF NOT USED ANYWHERE
const EditStats = ({ data }) => {
  const { userInfo } = useSelector((state) => state.auth.authUser);

  const onSubmit = async (values) => {
    await dispatch.auth.userLogin({
      values,
      navigate,
    });
  };

  const initialValues = {
    teamName: "",
    seasonName: "",
    goals: "",
    assists: "",
    appearances: "",
    cleanSheets: "",
  };

  const {
    values,
    errors,
    touched,
    isSubmitting,
    handleBlur,
    handleChange,
    handleSubmit,
  } = useFormik({
    initialValues,
    onSubmit,
    validationSchema: editStats,
  });

  return (
    <div className="">
      {/* The button to open modal */}
      <label htmlFor="my-modal-3">
        <div className="flex rounded-full h-[25px] justify-center items-center mx-auto">
          <img  src="/images/editIcon.png" alt="" />
        </div>
      </label>

      {/* Put this part before </body> tag */}
      <input type="checkbox" id="my-modal-3" className="modal-toggle" />
      <div className="modal bg-[#d9d9d9] ">
        <div className="modal-box relative bg-white">
          <label
            htmlFor="my-modal-3"
            className="btn btn-sm btn-circle absolute right-2 top-2"
          >
            ✕
          </label>
          <PageSubTitle>
            <div className="flex gap-3 text-[16px] uppercase w-full text-center">
              <div>
                <img  src={data?.clubLogo} alt="club logo" />
              </div>
              <div>{data?.teamName}</div>
            </div>
          </PageSubTitle>
          <div className="">
            <form className="mt-7" onSubmit={handleSubmit}>
              <div className="flex flex-col justify-between">
                <div className="flex justify-between">
                  <div className="relative z-0 w-[45%] mb-6 group">
                    <input
                      type="text"
                      value={values.appearances}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      name="appearances"
                      id="appearances"
                      className={`${
                        errors.appearances && touched.appearances
                          ? "border-b-2 border-red-400"
                          : "border-b-2 border-gray-300"
                      } block py-2.5 px-0 w-full text-sm text-gray-900 bg-transparent border-0 border-b-2 border-gray-300 appearance-none dark:text-white dark:border-gray-600 dark:focus:border-black-800 focus:outline-none focus:ring-0 focus:border-gray-600 peer`}
                      placeholder=" "
                    />
                    <label
                      htmlFor="appearances"
                      className="peer-focus:font-medium absolute text-sm text-gray-500 dark:text-gray-400 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-gray-600 peer-focus:dark:text-gray-100 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6"
                    >
                      Appearances
                    </label>
                    <p className="text-red-500 text-xs italic py-1">
                      {errors.appearances && touched.appearances
                        ? errors.appearances
                        : ""}
                    </p>
                  </div>
                  <div className="relative z-0 w-[45%] mb-6 group">
                    <input
                      type="text"
                      value={values.goals}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      name="goals"
                      id="goals"
                      className={`${
                        errors.goals && touched.goals
                          ? "border-b-2 border-red-400"
                          : "border-b-2 border-gray-300"
                      } block py-2.5 px-0 w-full text-sm text-gray-900 bg-transparent border-0 border-b-2 border-gray-300 appearance-none dark:text-white dark:border-gray-600 dark:focus:border-black-800 focus:outline-none focus:ring-0 focus:border-gray-600 peer`}
                    />
                    <label
                      htmlFor="goals"
                      className="peer-focus:font-medium absolute text-sm text-gray-500 dark:text-gray-400 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-gray-600 peer-focus:dark:text-gray-100 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6"
                    >
                      Goals
                    </label>
                    <p className="text-red-500 text-xs italic py-1">
                      {errors.goals && touched.goals ? errors.goals : ""}
                    </p>
                  </div>
                </div>
                <div className="flex justify-between">
                  <div className="relative z-0 w-[45%] mb-6 group">
                    <input
                      type="text"
                      value={values.assists}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      name="assists"
                      id="assists"
                      className={`${
                        errors.assists && touched.assists
                          ? "border-b-2 border-red-400"
                          : "border-b-2 border-gray-300"
                      } block py-2.5 px-0 w-full text-sm text-gray-900 bg-transparent border-0 border-b-2 border-gray-300 appearance-none dark:text-white dark:border-gray-600 dark:focus:border-black-800 focus:outline-none focus:ring-0 focus:border-gray-600 peer`}
                      placeholder=" "
                    />
                    <label
                      htmlFor="assists"
                      className="peer-focus:font-medium absolute text-sm text-gray-500 dark:text-gray-400 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-gray-600 peer-focus:dark:text-gray-100 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6"
                    >
                      Assists
                    </label>
                    <p className="text-red-500 text-xs italic py-1">
                      {errors.assists && touched.assists ? errors.assists : ""}
                    </p>
                  </div>
                  <div className="relative z-0 w-[45%] mb-6 group">
                    <input
                      type="text"
                      value={values.cleanSheets}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      name="cleanSheets"
                      id="cleanSheets"
                      className={`${
                        errors.cleanSheets && touched.cleanSheets
                          ? "border-b-2 border-red-400"
                          : "border-b-2 border-gray-300"
                      } block py-2.5 px-0 w-full text-sm text-gray-900 bg-transparent border-0 border-b-2 border-gray-300 appearance-none dark:text-white dark:border-gray-600 dark:focus:border-black-800 focus:outline-none focus:ring-0 focus:border-gray-600 peer`}
                    />
                    <label
                      htmlFor="cleanSheets"
                      className="peer-focus:font-medium absolute text-sm text-gray-500 dark:text-gray-400 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-gray-600 peer-focus:dark:text-gray-100 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6"
                    >
                      Clean Sheets
                    </label>
                    <p className="text-red-500 text-xs italic py-1">
                      {errors.cleanSheets && touched.cleanSheets
                        ? errors.cleanSheets
                        : ""}
                    </p>
                  </div>
                </div>
                <div>
                  <BigButtons black label="save" isLoading={isSubmitting} />
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};
export default EditStats;
