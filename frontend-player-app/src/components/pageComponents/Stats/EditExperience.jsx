import PageSubTitle from "../../reusable/PageTitle";
import { useSelector } from "react-redux";
import { useEffect, useRef, useState } from "react";
import { dispatch } from "../../../redux/store";
import { Experience } from "../../../services";
import { validateURL } from "../../../utils/helpers";
import ballAvatar from "../../../assets/ball.png";


let experience = {
  teamName: "",
  seasonName: "",
  appearances: "",
  goals: "",
  assists: "",
  cleanSheets: "",
  teamLogo: "",
  minsPlayed: 0
};

const EditExperience = ({ shouldShowModal, data, handleCloseModal }) => {
  const [seasonNameError, setSeasonNameError] = useState("");
  const [userExperience, setUserExperience] = useState(experience);
  const [clubs, setClubs] = useState();

  const modalRef = useRef(null);

  const allClubs = useSelector(({ club }) => club.allClubs);

  const seasonCheck = /^([0-9]{2})\/?([0-9]{2})$/;
  const test = seasonCheck.test(userExperience.seasonName);
  const isDisabled = userExperience.seasonName && test;

  const { userInfo } = useSelector((state) => state.auth.authUser);

  if (userInfo?.teamName) {
    experience["teamName"] = userInfo.teamName || "";
  }

  const handleBlur = () => {
    if (test) {
      setSeasonNameError("");
    } else {
      setSeasonNameError("Must be in YY/YY");
    }
  };
  const handleSubmit = async () => {
    const mergedValues = {
      teamName: userExperience.teamName,
      teamLogo: userExperience.teamLogo,
      userId: userInfo.id,
      appearances: +userExperience.appearances,
      goals: +userExperience.goals,
      assists: +userExperience.assists,
      cleanSheets: +userExperience.cleanSheets,
      seasonName: userExperience.seasonName,
      minsPlayed: +userExperience.minsPlayed
    };

    try {
      await Experience.updateExperience({ id: data.id, data: mergedValues });
      await dispatch.user.getUserProfile({ id: userInfo.id });
    } catch (error) {
      notifyError(error?.response?.message || error.message);
    }
    setUserExperience(experience);
  };

  const fetchClubs = async () => {
    const res = await dispatch.club.getAllClubs();
    setClubs(res);
  };

  useEffect(() => {
    if (shouldShowModal && data) {
      setUserExperience(data);
    }
    modalRef.current.click();
  }, [shouldShowModal, data]);

  useEffect(() => {
    // if (!allClubs.length) {
      fetchClubs();
    // }
    setClubs(allClubs);
  }, []);

  return (
    <div>
      {/* Put this part before </body> tag */}
      <input
        ref={modalRef}
        type="checkbox"
        id="my-modal-6"
        className="modal-toggle"
      />
      {shouldShowModal && (
        <div className="modal bg-[#d9d9d9] ">
          <div className="modal-box relative bg-white">
            <label
              onClick={() => handleCloseModal({ visible: false, data: null })}
              htmlFor="my-modal-6"
              className="btn btn-sm btn-circle absolute right-2 top-2"
            >
              ✕
            </label>
            <PageSubTitle>
              <div className="text-[16px] uppercase w-full text-center my-5 flex justify-center align-middle items-center">
               <img  className="w-[60px] pr-5" src={validateURL(data?.teamLogo)
                    ? data?.teamLogo
                    : ballAvatar} /> <p className="text-black">{data.teamName}</p>
              </div>
            </PageSubTitle>
            {/* <div className="flex gap-6 mt-6">
              <div className="relative z-0 w-full mb-7 group">
                <input
                  value={userExperience.teamName}
                  onChange={(e) => handleTeamName(e)}
                  className="border-b-2 border-gray-600 block pt-3 pb-1 px-0 w-full text-sm text-gray-900 bg-transparent appearance-none  focus:outline-none focus:ring-0 focus:border-gray-600 peer"
                  placeholder=" "
                  list="teamName"
                  name="edit_teamName"
                  id="edit_teamNames"
                />
                <datalist type="text" name="teamName" id="teamName">
                  {clubs?.map((club) => (
                    <option
                      key={club.id}
                      defaultValue={club.clubName === userInfo.clubName}
                      value={club.clubName}
                    >
                      {club.clubName}
                    </option>
                  ))}
                </datalist>
                <label
                  htmlFor="teamName"
                  className="peer-focus:font-medium absolute text-sm text-gray-500 dark:text-gray-400 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-gray-600 peer-focus:dark:text-gray-100 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6"
                >
                  Football Club
                </label>
              </div>
            </div> */}
            <div className="relative z-0 w-full mb-7 group">
              <input
                type="text"
                name="seasonName"
                id="edit_seasonNames"
                onBlur={handleBlur}
                onChange={({ target }) =>
                  setUserExperience({
                    ...userExperience,
                    seasonName: target.value
                  })
                }
                value={userExperience.seasonName}
                className="border-b-2 border-gray-600 block pt-3 pb-1 px-0 w-full text-sm text-gray-900 bg-transparent appearance-none  focus:outline-none focus:ring-0 focus:border-gray-600 peer"
                placeholder="YY/YY or eg. 22/23"
              />
              <label
                htmlFor="edit_seasonNames"
                className="peer-focus:font-medium absolute text-sm text-gray-500 dark:text-gray-400 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-gray-600 peer-focus:dark:text-gray-100 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6"
              >
                Season (YY/YY)
              </label>
              <p className="absolute flex mt-1 justify-center text-red-600 text-xs">
                {seasonNameError ? seasonNameError : ""}
              </p>
            </div>
            <div className="my-5 grid grid-cols-2 gap-6 mt-6">
              <div className="relative z-0 w-full mb-7 group">
                <input
                  type="text"
                  name="appearances"
                  id="edit_appearances"
                  onChange={({ target }) =>
                    setUserExperience({
                      ...userExperience,
                      appearances: target.value
                    })
                  }
                  value={userExperience.appearances}
                  className="border-b-2 border-gray-600 block pt-3 pb-1 px-0 w-full text-sm text-gray-900 bg-transparent appearance-none  focus:outline-none focus:ring-0 focus:border-gray-600 peer"
                  placeholder=" "
                />
                <label
                  htmlFor="edit_appearances"
                  className="peer-focus:font-medium absolute text-sm text-gray-500 dark:text-gray-400 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-gray-600 peer-focus:dark:text-gray-100 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6"
                >
                  Appearance
                </label>
              </div>
              <div className="relative z-0 w-full mb-7 group">
                <input
                  type="text"
                  name="goals"
                  id="edit_goalss"
                  onChange={({ target }) =>
                    setUserExperience({
                      ...userExperience,
                      goals: target.value
                    })
                  }
                  value={userExperience.goals}
                  className="border-b-2 border-gray-600 block pt-3 pb-1 px-0 w-full text-sm text-gray-900 bg-transparent appearance-none  focus:outline-none focus:ring-0 focus:border-gray-600 peer"
                  placeholder=" "
                />
                <label
                  htmlFor="edit_goalss"
                  className="peer-focus:font-medium absolute text-sm text-gray-500 dark:text-gray-400 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-gray-600 peer-focus:dark:text-gray-100 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6"
                >
                  Goals
                </label>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-6">
              <div className="relative z-0 w-full mb-7 group">
                <input
                  type="text"
                  name="assists"
                  id="edit_assists"
                  onChange={({ target }) =>
                    setUserExperience({
                      ...userExperience,
                      assists: target.value
                    })
                  }
                  value={userExperience.assists}
                  className="border-b-2 border-gray-600 block pt-3 pb-1 px-0 w-full text-sm text-gray-900 bg-transparent appearance-none  focus:outline-none focus:ring-0 focus:border-gray-600 peer"
                  placeholder=" "
                />
                <label
                  htmlFor="edit_assists"
                  className="peer-focus:font-medium absolute text-sm text-gray-500 dark:text-gray-400 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-gray-600 peer-focus:dark:text-gray-100 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6"
                >
                  Assists
                </label>
              </div>
              <div className="relative z-0 w-full mb-7 group">
                <input
                  type="text"
                  name="cleanSheets"
                  id="edit_cleanSheets"
                  onChange={({ target }) =>
                    setUserExperience({
                      ...userExperience,
                      cleanSheets: target.value
                    })
                  }
                  value={userExperience.cleanSheets}
                  className="border-b-2 border-gray-600 block pt-3 pb-1 px-0 w-full text-sm text-gray-900 bg-transparent appearance-none  focus:outline-none focus:ring-0 focus:border-gray-600 peer"
                  placeholder=" "
                />
                <label
                  htmlFor="edit_cleanSheets"
                  className="peer-focus:font-medium absolute text-sm text-gray-500 dark:text-gray-400 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-gray-600 peer-focus:dark:text-gray-100 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6"
                >
                  Clean Sheet
                </label>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-6">
              <div className="relative z-0 w-full mb-7 group">
                <input
                  type="text"
                  name="minsPlayed"
                  id="minsPlayed"
                  onChange={({ target }) =>
                    setUserExperience({
                      ...userExperience,
                      minsPlayed: target.value
                    })
                  }
                  value={userExperience.minsPlayed}
                  className="border-b-2 border-gray-600 block pt-3 pb-1 px-0 w-full text-sm text-gray-900 bg-transparent appearance-none  focus:outline-none focus:ring-0 focus:border-gray-600 peer"
                  placeholder=" "
                />
                <label
                  htmlFor="minsPlayed"
                  className="peer-focus:font-medium absolute text-sm text-gray-500 dark:text-gray-400 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-gray-600 peer-focus:dark:text-gray-100 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6"
                >
                  Minutes Played
                </label>
              </div>
            </div>
            <div className="flex items-center justify-center w-full">
              <label
                htmlFor="my-modal-6"
                onClick={handleSubmit}
                className="text-white px-10 py-3 rounded-full text-[22px] bg-black"
                disabled={!isDisabled}
              >
                Save
              </label>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
export default EditExperience;
