import React, { useEffect, useState } from "react";
import { useFormik } from "formik";
import { useSelector } from "react-redux";
import { dispatch } from "../../../redux/store";
import {
  forgotPassword2Schema,
  forgotPasswordSchema,
} from "../../../utils/formSchema";
import { BigButtons, SmallButtons } from "../../reusable/buttons/Buttons";
import { X } from "lucide-react";

const ForgotPassword = () => {
  const [email, setEmail] = useState("");
  const [visible, setVisible] = useState(false);
  const [showFields, setShowFields] = useState(false);

  const sendEmailLoading = useSelector(
    ({ loading }) => loading.effects.auth.forgotPassword
  );

  const sendCodeLoading = useSelector(
    ({ loading }) => loading.effects.auth.resetPassword
  );

  const handleCancel = async () => {
    setVisible(false);
    setEmail("");
    setShowFields(false);
    await dispatch.auth.forgotPasswordModalBox({ visible: false });
  };
  const authModel = useSelector(({ auth }) => auth);

  useEffect(() => {
    setVisible(authModel?.forgotPasswordModal?.visible);
  }, [authModel?.forgotPasswordModal?.visible]);

  const submitCode = async (values) => {
    const val = {
      ...values,
      email,
    };
    const res = await dispatch.auth.resetPassword(val);
    if (res === 1) dispatch.auth.forgotPasswordModalBox({ visible: false });
  };
  const submitEmail = async (values) => {
    setEmail(values.email);
    const res = await dispatch.auth.forgotPassword(values);
    if (res === 1) setShowFields(true);
  };

  const initialValues1 = {
    email: "",
  };

  const initialValues2 = {
    email: "",
    verificationCode: "",
    password: "",
  };

  const {
    values,
    errors,
    touched,
    isSubmitting,
    handleBlur,
    handleChange,
    handleSubmit,
  } = useFormik({
    initialValues: initialValues2,
    onSubmit: submitCode,
    validationSchema: forgotPassword2Schema,
  });

  const {
    values: values1,
    errors: errors1,
    touched: touched1,
    isSubmitting: isSubmitting1,
    handleBlur: handleBlur1,
    handleChange: handleEmailChange,
    handleSubmit: handleSubmitEmail,
  } = useFormik({
    initialValues: initialValues1,
    onSubmit: submitEmail,
    validationSchema: forgotPasswordSchema,
  });

  return visible ? (
    <div className="fixed h-screen flex overflow-y-auto flex-col justify-center inset-0 w-full bg-black bg-opacity-60 z-50">
      <div
        style={{ maxWidth: "400px" }}
        className="relative bg-white mx-auto mt-10 pb-10 px-5 pt-6 w-11/12 rounded-[30px] border-[6px] border-[#14FF00] flex flex-col gap-4 justify-center items-center text-center"
      >
        {/* <div className="absolute -right-10 top-2 w-1/2 h-24">
          <SmallButtons onClick={handleCancel} black label="X" />
        </div>
        <div>
          <div className="px-5">
            <h3
              style={{ color: "#091E42" }}
              className="text-lg text-center font-semibold"
            >
              Password Recovery
            </h3>
            {showFields ? (
              <p> Enter the code you got in the Email we sent</p>
            ) : (
              <p className="pt-2 text-center text-base">
                Enter the email you used to create an account.
              </p>
            )}

            {showFields ? (
              <form onSubmit={handleSubmit}>
                <input
                  className="border border-gray-600 rounded-xl w-full py-3 px-3 text-mainLight placeholder:text-gray-700 mt-7"
                  onChange={handleChange}
                  placeholder="Enter received code"
                  id="verificationCode"
                  type="text"
                  onBlur={handleBlur}
                  value={values.verificationCode}
                />
                <p className="absolute flex justify-center text-red-600 text-xs">
                  {errors.verificationCode && touched.verificationCode
                    ? errors.verificationCode
                    : ""}
                </p>
                <input
                  className="border border-gray-600 mb-7 rounded-xl w-full py-3 px-3 text-mainLight placeholder:text-gray-700 mt-7"
                  onChange={handleChange}
                  placeholder="Enter new password"
                  id="password"
                  type="text"
                  onBlur={handleBlur}
                  value={values.password}
                />
                <p className="absolute flex justify-center text-red-600 text-xs">
                  {errors.password && touched.password ? errors.password : ""}
                </p>
                <BigButtons
                  isLoading={sendCodeLoading}
                  black
                  label="Recover Password"
                />
              </form>
            ) : (
              <form onSubmit={handleSubmitEmail}>
                <div className="">
                  <input
                    className="border border-gray-600 mb-7 rounded-xl w-full py-3 px-3 text-mainLight placeholder:text-gray-700 mt-7"
                    onChange={handleEmailChange}
                    placeholder="Enter email address"
                    id="email"
                    type="text"
                    onBlur={handleBlur1}
                    value={values1.email}
                  />
                  <p className="absolute flex justify-center text-red-600 text-xs">
                    {errors1.email && touched1.email ? errors1.email : ""}
                  </p>
                </div>
                <BigButtons
                  isLoading={sendEmailLoading}
                  green
                  label="Send Code"
                />
              </form>
            )}
          </div>
        </div> */}

        {/* Password Recovery - Temporary */}
        <div className="flex w-full justify-end">
          <X onClick={handleCancel} className="bg-[#EDEDED] rounded-full text-black font-normal p-1" />
        </div>
        <h6 className="font-favela-bold font-bold text-[16px]">PASSWORD RECOVERY</h6>
        <p className="text-[#00000080] text-[16px] w-[95%]">Reach out to your community manager, or email <a href="mailto:<EMAIL>" className="font-bold no-underline text-[#00000080]"><EMAIL></a> for help.</p>
        <BigButtons
        className={"mt-5"}
        onClick={handleCancel}
                  isLoading={false}
                  black
                  label="Close"
                />
      </div>
    </div>
  ) : (
    <div></div>
  );
};
export default ForgotPassword;
