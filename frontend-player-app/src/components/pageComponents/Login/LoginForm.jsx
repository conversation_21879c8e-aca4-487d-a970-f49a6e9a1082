import { useFormik } from "formik";
import React, { useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { dispatch } from "../../../redux/store";
import { loginSchema } from "../../../utils/formSchema";
import { BigButtons } from "../../reusable/buttons/Buttons";
import ForgotPassword from "./ForgotPassword";
import Analytics from "../../../utils/google-analytics";
import { TRACKING_EVENTS } from "../../../utils/constants";

const LoginForm = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { from } = location.state || { from: { pathname: "/" } };

  const [visible, setVisible] = useState(false);

  const onForgotPasswordClicked = () => {
    Analytics.trackEvent({
      name: TRACKING_EVENTS.FORGOT_PASSWORD_CLICKED,
      metadata: {}
    })
    dispatch.auth.forgotPasswordModalBox({ visible: true })
  }

  const onSubmit = async (values) => {
    await dispatch.auth.userLogin({
      values,
      navigate,
      from
    });
  };

  const initialValues = {
    email: "",
    password: ""
  };

  const {
    values,
    errors,
    touched,
    isSubmitting,
    handleBlur,
    handleChange,
    handleSubmit
  } = useFormik({
    initialValues,
    onSubmit,
    validationSchema: loginSchema
  });

  return (
    <>
      <ForgotPassword />
      <form className="mt-7" onSubmit={handleSubmit}>
        <div className="flex flex-col justify-between min-h-[58vh]">
          <div>
            <div className="relative z-0 w-full mb-6 group">
              <input
                type="email"
                value={values.email}
                onChange={handleChange}
                onBlur={handleBlur}
                name="email"
                id="email"
                className={`${errors.email && touched.email
                  ? "border-b-2 border-red-400"
                  : "border-b-2 border-gray-300"
                  } block py-2.5 px-0 w-full text-sm text-gray-900 bg-transparent border-0 border-b-2 border-gray-300 appearance-none dark:text-gray-900 dark:border-gray-600 dark:focus:border-black-800 focus:outline-none focus:ring-0 focus:border-gray-600 peer`}
                placeholder=" "
              />
              <label
                htmlFor="email"
                className="peer-focus:font-medium absolute text-sm text-gray-500 dark:text-gray-400 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-gray-600 peer-focus:dark:text-gray-100 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6"
              >
                Email
              </label>
              <p className="text-red-500 text-xs italic py-1">
                {errors.email && touched.email ? errors.email : ""}
              </p>
            </div>
            <div className="relative z-0 w-full mb-6 group">
              <input
                type={visible ? "text" : "password"}
                value={values.password}
                onChange={handleChange}
                onBlur={handleBlur}
                name="password"
                id="password"
                className={`${errors.password && touched.password
                  ? "border-b-2 border-red-400"
                  : "border-b-2 border-gray-300"
                  } block py-2.5 px-0 w-full text-sm text-gray-900 bg-transparent border-0 border-b-2 border-gray-300 appearance-none dark:text-gray-900 dark:border-gray-600 dark:focus:border-black-800 focus:outline-none focus:ring-0 focus:border-gray-600 peer`}
                placeholder="****"
              />
              <img
                onClick={() => setVisible(!visible)}
                src={visible ? "/images/eye_show.png" : "/images/eye_hide.png"}
                alt="visible"
                className="absolute right-2 top-1/2 transform -translate-y-1/2 w-[20px]"
              />
              <label
                htmlFor="password"
                className="peer-focus:font-medium absolute text-sm text-gray-500 dark:text-gray-400 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-gray-600 peer-focus:dark:text-gray-100 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6"
              >
                Password
              </label>
              <p className="text-red-500 text-xs italic py-1">
                {errors.password && touched.password ? errors.password : ""}
              </p>
            </div>

            <div className="p-3 flex justify-center">
              <div className="flex items-center mr-4 mb-2">
                <input
                  type="checkbox"
                  // id="A3-yes"
                  // name="A3-confirmation"
                  value=""
                  className="appearance-none outline-none opacity-0 absolute h-4 w-4"
                />
                <div className="border-2 rounded-full border-gray-900 w-6 h-6 flex flex-shrink-0 justify-center items-center mr-2 focus-within:border-gray-500">
                  <svg
                    className="hidden w-4 h-4 pointer-events-none"
                    version="1.1"
                    viewBox="0 0 17 12"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g fill="none" fillRule="evenodd">
                      <g
                        transform="translate(-9 -11)"
                        fill="white"
                        fillRule="nonzero"
                      >
                        <path d="m25.576 11.414c0.56558 0.55188 0.56558 1.4439 0 1.9961l-9.404 9.176c-0.28213 0.27529-0.65247 0.41385-1.0228 0.41385-0.37034 0-0.74068-0.13855-1.0228-0.41385l-4.7019-4.588c-0.56584-0.55188-0.56584-1.4442 0-1.9961 0.56558-0.55214 1.4798-0.55214 2.0456 0l3.679 3.5899 8.3812-8.1779c0.56558-0.55214 1.4798-0.55214 2.0456 0z" />
                      </g>
                    </g>
                  </svg>
                </div>
                <label htmlFor="A3-yes" className="select-none">
                  Remember me
                </label>
              </div>
            </div>
          </div>
          <div>
            <BigButtons black label="LOG IN" isLoading={isSubmitting} />
          </div>
        </div>
      </form>
      <div
        onClick={onForgotPasswordClicked}
        className="border-b mt-4 text-sm text-gray-700 w-max mx-auto mb-2 border-gray-300"
      >
        Forgotten your Password?
      </div>
    </>
  );
};

export default LoginForm;
