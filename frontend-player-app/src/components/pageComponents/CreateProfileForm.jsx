import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useFormik } from "formik";
import { createProfile1 } from "../../utils/formSchema";
import { BigButtons } from "../reusable/buttons/Buttons";
import { dispatch } from "../../redux/store";
import { useSelector } from "react-redux";
import { cleanDate, PreferredFoot } from "../../utils/helpers";
import DropdownWithCheckBox from "./Custom/DropdownWithCheckBox";
import SelectWithPicture from "../reusable/SelectWithPicture";
import moment from "moment";

const CreateProfileForm = ({ s3Loading }) => {
  const [selectedClub, setSelectClub] = useState({});
  const [searchValue, setSearchValue] = useState("");

  const navigate = useNavigate();

  var dateThreshold = moment().subtract(5, "years").format("YYYY-MM-DD");

  const { userInfo } = useSelector((state) => state.auth.authUser);
  //For date picker component
  // const [dateValue, setDateValue] = React.useState(userInfo?.birthday || null);

  const updateUserLoading = useSelector(
    ({ loading }) => loading.effects.user.updateUser
  );

  const onSubmit = async (values) => {
    const mergedValues = {
      ...values,
      clubName: values?.club?.clubName,
      cludId: values?.club?.id,
      id: userInfo.id
    };

    for (const key in mergedValues) {
      if (!Boolean(mergedValues[key])) delete mergedValues[key];
    }
    delete mergedValues["club"];
    const res = await dispatch.user.updateUser(mergedValues);

    if (res === 1){
      navigate(mergedValues.userType !== "NON_PLAYER" ? "/club-select" : "/account-setup");
    }
  };

  const initialValues = {
    position: userInfo.position || "",
    birthday: userInfo.birthday || dateThreshold,
    preferredFoot: userInfo.preferredFoot || "",
    location: userInfo.location || "",
    height: userInfo.height || "",
    gender: userInfo.gender || "",
    club: { id: userInfo?.clubId, clubName: userInfo?.clubName },
    userType: userInfo?.userType || "PLAYER",
    nonPlayerRole: userInfo?.nonPlayerRole || []
  };

  const {
    values,
    errors,
    touched,
    handleBlur,
    handleChange,
    handleSubmit,
    setFieldValue
  } = useFormik({
    initialValues,
    onSubmit,
    validationSchema: createProfile1
  });

  const handleRoleChange = (roles) => {
    setFieldValue("nonPlayerRole", roles);
  };

  const handleSetSelectedClub = (club) => {
    setSelectClub(club);
    setFieldValue("club", club);
  };

  return (
    <div className="mt-7">
      <form onSubmit={handleSubmit} className="w-full">
        {userInfo.userType !== "NON_PLAYER" && (
          <div className="mt-7">
            <label className="font-favela-bold uppercase">Position</label>
            <div className="flex items-center border-b border-black py-1">
              <select
                value={values.position}
                onChange={handleChange}
                onBlur={handleBlur}
                name="position"
                id="position"
                className="appearance-none bg-transparent border-none w-full text-gray-700 mr-3 py-7 leading-tight focus:outline-none"
              >
                <option disabled={true} value="">
                  - -
                </option>
                <option value="GOAL_KEEPERS">Goalkeeper</option>
                <option value="CENTRE_BACK">Centre Back</option>
                <option value="RIGHT_BACK">Right Back</option>
                <option value="LEFT_BACK">Left Back</option>
                <option value="WING_BACK">Wing Back</option>
                <option value="WINGER">Winger</option>
                <option value="DEFENSIVE_MIDFIELD">Defensive Midfield</option>
                <option value="CENTRAL_MIDFIELD">Central Midfield</option>
                <option value="ATTACKING_MIDFIELD">Attacking Midfield</option>
                <option value="FORWARD">Forward</option>
                <option value="STRIKER">Striker</option>
              </select>
            </div>
            <p className="absolute flex justify-center text-red-600 text-xs">
              {errors.position && touched.position ? errors.position : ""}
            </p>
          </div>
        )}

        {userInfo.userType === "NON_PLAYER" && (
          <div className="mt-7">
            <label className="font-favela-bold uppercase">Role</label>
            <div className="flex items-center border-b border-black py-1">
              <div className="appearance-none bg-transparent border-none w-full text-[#000000] mr-3 py-1 leading-tight focus:outline-none font-[400]">
                <DropdownWithCheckBox
                  defaultOptions={values.nonPlayerRole}
                  onChange={handleRoleChange}
                />
              </div>
            </div>

            <p className="absolute flex justify-center text-red-600 text-xs">
              {errors.nonPlayerRole && touched.nonPlayerRole
                ? errors.nonPlayerRole
                : ""}
            </p>
          </div>
        )}

        {userInfo.userType === "NON_PLAYER" && (
          <div className="mt-7">
            <label htmlFor="clubs" className="font-favela-bold uppercase">
              Organisation Name
            </label>
            <div>
              <SelectWithPicture
                className="card shadow-lg px-5"
                setSelectClub={handleSetSelectedClub}
                selectClub={selectedClub}
                searchValue={searchValue}
                userInfo={userInfo}
                setSearchValue={setSearchValue}
                getTeamsBasedOnSelectedClub={null}
              />
            </div>
            <p className="absolute flex justify-center text-red-600 text-xs">
              {errors.club && touched.club ? errors.club : ""}
            </p>
          </div>
        )}

        {userInfo.userType !== "NON_PLAYER" && (
          <div className="mt-7">
            <label className="font-favela-bold uppercase">Preferred Foot</label>
            <div className="flex items-center border-b border-black py-7">
              <select
                value={values.preferredFoot}
                onChange={handleChange}
                onBlur={handleBlur}
                name="preferredFoot"
                id="preferredFoot"
                className="appearance-none bg-transparent border-none w-full text-gray-700 mr-3 py-1 leading-tight focus:outline-none"
              >
                <option key={"--"} disabled={true} value="">
                  - -
                </option>

                {Object.keys(PreferredFoot).map((key) => {
                  return (
                    <option key={key} value={key}>
                      {PreferredFoot[key]}
                    </option>
                  );
                })}
              </select>
            </div>
            <p className="absolute flex justify-center text-red-600 text-xs">
              {errors.preferredFoot && touched.preferredFoot
                ? errors.preferredFoot
                : ""}
            </p>
          </div>
        )}

        {userInfo.userType !== "NON_PLAYER" && (
          <div className="mt-7">
            <label className="font-favela-bold uppercase">Date of Birth</label>
            <div className="flex items-center border-b border-black">
              <div className="appearance-none bg-transparent border-none w-full text-[#00000] text-[14px] mr-3 py-7 leading-[24px] focus:outline-none">
                <input
                  type="date"
                  onChange={(e) => {
                    setFieldValue("birthday", cleanDate(e.target.value));
                  }}
                  value={cleanDate(values.birthday)}
                  max={dateThreshold}
                  className="bg-[#ffffff]"
                />
              </div>
            </div>
          </div>
        )}
        <div className="mt-7">
          <label className="font-favela-bold uppercase">Your Location</label>
          <div className="flex items-center border-b border-black py-1">
            <input
              className="appearance-none bg-transparent border-none w-full text-[#000000] text-[14px] mr-3 py-7 leading-[24px] focus:outline-none"
              type="text"
              placeholder="For example: Hackney, London"
              value={values.location}
              onChange={handleChange}
              onBlur={handleBlur}
              name="location"
              id="location"
            />
          </div>
          <p className="absolute flex justify-center text-red-600 text-xs">
            {errors.location && touched.location ? errors.location : ""}
          </p>
        </div>

        {userInfo.userType !== "NON_PLAYER" && (
          <div className="mt-7">
            <label className="font-favela-bold uppercase">Height</label>
            <div className="flex items-center border-b border-black py-7">
              <input
                className="appearance-none bg-transparent border-none w-full text-gray-700 mr-3 py-1 leading-tight focus:outline-none"
                type="text"
                placeholder="Meters (e.g 1.83m)"
                value={values.height}
                onChange={handleChange}
                onBlur={handleBlur}
                name="height"
                id="height"
              />
            </div>
            <p className="absolute flex justify-center text-red-600 text-xs">
              {errors.height && touched.height ? errors.height : ""}
            </p>
          </div>
        )}
        {/* Used to help validate non players. Not visibale for users */}
        <input name="userType" hidden value={userInfo.userType} />

        <div className="mt-7">
          <label className="font-favela-bold uppercase">
            {userInfo.userType === "PLAYER" ? "I PLAY…" : "I WORK IN…"}
          </label>
          <div className="flex items-center border-b border-black">
            <select
              value={values.gender}
              onChange={handleChange}
              onBlur={handleBlur}
              name="gender"
              id="gender"
              className="appearance-none bg-transparent border-none w-full text-[#000000] text-[14px] mr-3 py-7 leading-[24px] focus:outline-none"
            >
              <option disabled={true} value="">
                - -
              </option>
              <option value="Men’s football">Men’s football</option>
              <option value="Women’s football">Women’s football</option>
              {userInfo.userType === "NON_PLAYER" && (
                <option value="Both, men’s and women’s football">
                  Both, men’s and women’s football
                </option>
              )}
            </select>
          </div>
          <p className="absolute flex justify-center text-red-600 text-xs">
            {errors.gender && touched.gender ? errors.gender : ""}
          </p>
        </div>
        <div className="mt-7 mb-7 md:mt-14 md:mb-14">
          <BigButtons
            isLoading={!s3Loading && updateUserLoading}
            black
            label="Done"
          />
        </div>
      </form>
    </div>
  );
};

export default CreateProfileForm;
