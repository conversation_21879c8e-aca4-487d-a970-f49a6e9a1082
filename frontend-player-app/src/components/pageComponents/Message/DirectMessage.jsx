import React, { useEffect, useMemo, useState } from "react";
import { useSelector } from "react-redux";
import { useLocation, useSearchParams } from "react-router-dom";
import { v4 as uuidv4 } from "uuid";
import { MESSAGE_REFRESH_INTERVAL } from "../../../pages/MessageList";
import { dispatch } from "../../../redux/store";
import { notifyError } from "../../../utils/helpers";
import BottomNavigation from "../../reusable/BottomNav";
import ChatHeader from "../../reusable/chatitems/ChatHeader";
import ChatInput from "../../reusable/chatitems/ChatInput";
import ChatMessage from "../../reusable/chatitems/ChatMessage";
import Spinner from "../../reusable/spinner/Spinner";

const DirectMessage = () => {
  const { pathname } = useLocation();
  const [searchParams] = useSearchParams();
  const recipientId = useMemo(
    () => searchParams.get("recipientId"),
    [searchParams]
  );
  const temporaryChatroomId = useMemo(() => uuidv4(), []);

  const [isFirstLoad, setIsFirstLoad] = useState(true);
  const [chatroomId, setChatroomId] = useState(searchParams.get("chatroomId"));

  const userInfo = useSelector((state) => state.user.data);
  const chatroom = useSelector((state) =>
    state.chat.chats?.find(
      (chat) =>
        (chatroomId && chat.chatroomId === chatroomId) ||
        chat.temporaryChatroomId === temporaryChatroomId
    )
  );
  const otherUser = chatroom?.otherUser;
  const messages = chatroom?.messages || [];
  const pagination = chatroom?.pagination;

  const fetchChat = async (cursor = null) => {
    await dispatch.chat.getChat({ chatroomId, cursor });
    if (isFirstLoad) {
      setIsFirstLoad(false);
    }
  };

  const fetchChatByRecipient = async () => {
    try {
      const data = await dispatch.chat.getChatByRecipient({
        recipientId,
        temporaryChatroomId,
      });
      if (data?.chatroomId) {
        setChatroomId(data.chatroomId);
      }
    } finally {
      setIsFirstLoad(false);
    }
  };

  const fetchOtherUser = async () => {
    await dispatch.chat.getOtherUser({
      chatroomId,
      temporaryChatroomId,
      recipientId,
    });
  };

  useEffect(() => {
    const fetchData = async () => {
      await fetchOtherUser();
      if (chatroomId) {
        await fetchChat();
      } else if (recipientId) {
        await fetchChatByRecipient();
      }
    };

    fetchData();

    return async () => {
      await dispatch.chat.clearTemporaryChats();
    };
  }, []);

  useEffect(() => {
    if (chatroomId) {
      searchParams.set("chatroomId", chatroomId);
    } else {
      searchParams.delete("chatroomId");
    }

    window.history.replaceState(
      null,
      "",
      `${pathname}?${searchParams.toString()}`
    );
  }, [chatroomId]);

  const sendMessage = async (data) => {
    try {
      let liveLikeChatroomId = chatroomId;
      const temporaryMessageId = uuidv4();

      dispatch.chat.createTemporaryMessage({
        chatroomId: liveLikeChatroomId,
        temporaryChatroomId,
        temporaryMessageId,
        userInfo,
        data,
      });

      if (!liveLikeChatroomId) {
        liveLikeChatroomId = await dispatch.chat.createChat({
          recipientId,
          temporaryChatroomId,
        });
        setChatroomId(liveLikeChatroomId);
      }

      await dispatch.chat.createMessage({
        chatroomId: liveLikeChatroomId,
        temporaryChatroomId,
        temporaryMessageId,
        data,
      });
    } catch (error) {
      console.log(error);
      notifyError("Error sending message");
    }
  };

  const sendTextMessage = (message) =>
    sendMessage({
      type: "text",
      message,
    });

  const sendImageMessage = (photoUrl) =>
    sendMessage({
      type: "image",
      image_url: photoUrl,
    });

  useEffect(() => {
    if (!chatroomId) return;

    const intervalId = setInterval(() => {
      fetchChat();
    }, MESSAGE_REFRESH_INTERVAL);

    return () => {
      clearInterval(intervalId);
    };
  }, [chatroomId]);

  return (
    <div className="pb-[70px] page">
      <ChatHeader userDetails={otherUser} />
      {isFirstLoad && !messages.length ? (
        <div className="flex flex-col items-center justify-center flex-1 w-full h-full pt-[79px] mb-[79px]">
          <Spinner />
        </div>
      ) : (
        <>
          {pagination?.hasMore && (
            <div className="flex justify-center mb-2 pt-[100px]">
              <button
                className="px-4 py-2 text-xs bg-gray-200 rounded hover:bg-gray-300"
                onClick={() => fetchChat(pagination.nextCursor)}
              >
                Load More
              </button>
            </div>
          )}
          <ChatMessage chats={messages} />
        </>
      )}
      <ChatInput
        sendTextMessage={sendTextMessage}
        sendImageMessage={sendImageMessage}
      />
      <BottomNavigation />
    </div>
  );
};

export default React.memo(DirectMessage);
