import React from 'react';
import { useNavigate } from 'react-router-dom';
import { notifyError, } from "../../../utils/helpers";

// Helper function to detect "@" mentions
const parseMentions = (text, handleClick) => {
  const parts = text.split(/(@\w+)/); // Split text by words starting with @
  return parts.map((part, index) => {
    if (part.startsWith('@')) {
      return (
        <span
          key={index}
          className="text-blue-500 cursor-pointer"
          onClick={() => handleClick(part)}
        >
          {part}
        </span>
      );
    }
    return part; // Return non-mention text normally without style or onCClick
  });
};

export const MentionText = ({ text, userList }) => {

  const navigate = useNavigate();

  const handleMentionClick = (mention) => {
    if (mention === "@PLAYER") {
        window.open("mailto:<EMAIL>.")
    }
    const username = mention.substring(1); // Remove the "@" from the mention

    // Find the user by matching firstName and lastName (case insensitive)
    const user = userList.find(
      (user) =>
        `${user.firstName}${user.lastName}`.toLowerCase() === username.toLowerCase()
    );

    if (user) {
      navigate({
        pathname: "/profile",
        search: `?id=${user.id}`
      });
    } else {
        notifyError("User not found");
    }
  };

  return (
    <p className="text-[12px] w-full overflow-y-scroll break-words text-black font-bold text-opacity-75">
      {parseMentions(text, handleMentionClick)}
    </p>
  );
};
