import { Fragment } from "react";

const TextWithPlayerAppMention = ({ text, stringToReplace, replacerComp }) => {

  const replacePlayerWithLink = (textInput) => {
    const mailtoLink = replacerComp ? (
      replacerComp
    ) : (
      <a href="mailto:<EMAIL>." className="text-blue-500">
        {stringToReplace}
      </a>
    );

    const parts = textInput.split(stringToReplace);

    return (
      <>
        {parts.map((part, index) => (
          <Fragment key={index}>
            {part}
            {index < parts.length - 1 && mailtoLink}
          </Fragment>
        ))}
      </>
    );
  };

  return <span className="whitespace-pre-wrap">{replacePlayerWithLink(text)}</span>;
};

export default TextWithPlayerAppMention;
