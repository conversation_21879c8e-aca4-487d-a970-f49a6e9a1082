import ReactPlayer from "react-player/lazy";
import { memo, useCallback, useState } from "react";
import { PlayIcon2 } from "../../svgIcons";
import Spinner from "../../reusable/spinner/Spinner";
import { uploadedStreamsBaseUrl } from "../../../utils/constants";
import styled from "@emotion/styled";

const AnnoucementPlayerVideo = memo(
  ({ streamUrl, assetUrl, currentViewIndex, index, isDetailPage = true }) => {
    const [isReady, setIsReady] = useState(false);
    const [playing, setPlaying] = useState(false);
    const [volume, setVolume] = useState(1);

    const handleReady = () => {
      setTimeout(() => {
        setIsReady(true);
      }, 200);
    };

    const handleSetPlaying = useCallback(() => {
      setPlaying(!playing);
    }, [playing, currentViewIndex, index]);

    const getVideoUrl = (type) => {
      if (streamUrl?.baseUrl) {
        return `${uploadedStreamsBaseUrl}/${
          streamUrl?.key?.split("--")[1]
        }/${type}`;
      }
      return "";
    };

    const thumbnail = getVideoUrl("thumbnail.png");
    const videoUrl = getVideoUrl("index.m3u8");

    return (
      <div className={`w-full h-full ${!isDetailPage ? "mt-7" : ""} relative`}>
        {!isReady && (
          <>
            <div className="absolute top-0 right-0 bottom-0 left-0 flex justify-center items-center z-40">
              <Spinner />
            </div>
            <img
              className="h-[520px] absolute w-full object-cover rounded-[0px]"
              src={thumbnail}
            />
          </>
        )}
        <div className="h-[520px] flex justify-center items-center rounded-[0px]">
          <ReactPlayer
            onReady={handleReady}
            url={videoUrl}
            onPlay={() => {
              setPlaying(true);
            }}
            onPause={() => setPlaying(false)}
            playing={playing && currentViewIndex === index}
            controls={false}
            // onError={handleError}
            volume={Number(volume)}
            // wrapper={(props) => (
            //   <VideoWrapper {...props} objectFit={getObjectFit()} />
            // )}
            width="100%"
            height="100%"
            style={{ backgroundColor: "#000" }}
            poster={thumbnail}
            className="react-player"
            fallback={
              <img
                className="h-[520px] absolute w-full object-cover rounded-[0px]"
                src={thumbnail}
              />
            }
          />
        </div>

        {/* Play video */}
        {playing && isReady ? (
          <div
            onClick={handleSetPlaying}
            className="absolute top-0 right-0 bottom-0 left-0 flex justify-center items-center z-40"
          />
        ) : (
          isReady && (
            <div
              onClick={handleSetPlaying}
              className="absolute top-0 right-0 bottom-0 left-0 flex justify-center items-center"
            >
              <div className="w-[50px] h-[50px] bg-transparent rounded-full flex justify-center items-center">
                <PlayIcon2 />
              </div>
            </div>
          )
        )}
      </div>
    );
  }
);

const VideoWrapper = styled.div`
  width: 100%;
  height: 100%;
  position: absolute;
  background-color: #000000;
  background-image: url(${({ poster }) => poster});
  background-size: cover;
  background-repeat: no-repeat;
  inset: 0;
  @media (max-width: 768px) {
    border-radius: 0px;
  }
  video {
    object-fit: contain;
  }
`;
const MemoVideoWrapper = memo(VideoWrapper);

export default AnnoucementPlayerVideo;
