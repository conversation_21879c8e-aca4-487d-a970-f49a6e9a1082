import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";

const ConnectionCards = ({
  connection,
  follow,
  unfollow,
  nickname,
  relationship,
  isLoggedUserFollowingProfile,
  isProfileFollowingLoggedUser
}) => {
  const [followerObject, setFollowObject] = useState(null);
  const { userInfo, existingUsers } = useSelector(({ user }) => ({
    userInfo: user.data,
    existingUsers: user.usersByProjection
  }));

  const navigate = useNavigate();

  const thisUser = followerObject?.userId === userInfo?.id;

  useEffect(() => {
    if (connection) {
      setFollowObject(JSON.parse(connection));
    } else {
      existingUsers?.forEach((user) => {
        if (user.liveLikeProfileId === relationship?.id) {
          setFollowObject(user);
        }
      });
    }
  }, [connection, relationship, existingUsers]);

  return followerObject ? (
    <div className="flex active:bg-gray-600 transition-all duration-500 px-1 py-3 text-[12px] justify-between items-center w-full">
      <div className="flex items-center gap-3">
        <img
          src={followerObject.photoUrl || "/images/profile.png"}
          alt={followerObject.firstName}
          className="w-10 h-10 rounded-full"
        />
        <div
          onClick={() =>
            navigate({
              pathname: "/profile",
              search: `?id=${followerObject.userId}`
            })
          }
          className="flex flex-col"
        >
          <div className="font-semibold">@{followerObject?.firstName+followerObject?.lastName}</div>
          <div className="-mt-1 opacity-50">
            {followerObject.teamName === "N/A"
              ? followerObject.clubName
              : followerObject.teamName}
          </div>
        </div>
      </div>
      {thisUser ? (
        <div></div>
      ) : Boolean(isLoggedUserFollowingProfile) ? (
        <div
          className={`dropdown dropdown-hover dropdown-bottom text-white font-poppins text-[12px] font-medium flex items-center justify-center w-[35%] py-1 rounded-full"`}
        >
          <label tabIndex={0} className="w-full">
            <div className="text-white font-poppins text-[12px] font-medium flex items-center justify-center w-[100%] bg-[#ffffff8a] py-1 rounded-full cursor-pointer select-none">
              Following
              <img
                className="ml-2"
                src="/images/arrowDown.svg"
                alt="arrow-drop"
              />
            </div>
          </label>
          <div tabIndex={0} className="dropdown-content w-[100%] shadow bg-white">
            <div
              onClick={() => {
                unfollow(
                  isLoggedUserFollowingProfile.relationshipId,
                  followerObject,
                  nickname
                );
              }}
              className="absolute h-[43px] mt-1 text-black font-medium flex items-center justify-center w-full bg-[#ffffff] rounded-[10px] select-none cursor-pointer"
            >
              Unfollow
            </div>
          </div>
        </div>
      ) : (
        <div
          onClick={() => {
            follow(followerObject, nickname);
          }}
          className="font-poppins text-[12px] flex text-black bg-[#52FF00] justify-center w-[35%] py-1 rounded-full cursor-pointer select-none"
        >
          {isProfileFollowingLoggedUser ? "Follow Back" : "Follow"}
        </div>
      )}
    </div>
  ) : (
    ""
  );
};

export default ConnectionCards;
