import React from "react";
import { useNavigate } from "react-router-dom";
import moment from "moment";
import { dispatch } from "../../../redux/store";
import { useSelector } from "react-redux";
import { notifyError, notifySuccess } from "../../../utils/helpers";
import SwitchButton from "../Custom/Switch";
import { PUBLISHSTATUS } from "../../../utils/constants";

const Results = ({ selected, data, currentTeamId }) => {
  const navigate = useNavigate();

  const userInfo = useSelector((state) => state.user.data);
  const teamManager =
    userInfo?.teamId === currentTeamId &&
    userInfo?.isManager !== undefined &&
    userInfo?.isManager;

  const { publishing } = useSelector(({ loading }) => ({
    publishing: loading.effects.matches.publishResult
  }));

  const handleClickResult = (detail) => {
    navigate("/team-dashboard/result-details", {
      state: { data: detail }
    });
  };

  const handleMore = () => {
    navigate("/all-results", {
      state: { data }
    });
  };

  const handlePublishResult = async (result) => {
    try {
      await dispatch.matches.publishResult({
        matchId: result.id,
        teamId: currentTeamId,
        userId: userInfo.id,
        publishStatus:
          result.publishStatus !== "PUBLISHED"
            ? PUBLISHSTATUS.PUBLISHED
            : PUBLISHSTATUS.DRAFT
      });
    } catch (error) {
      console.log(error);
      notifyError(error?.response?.data?.message);
    }
  };

  return (
    <div
      className={`${
        selected === "results" ? "block" : "hidden"
      } mt-7 md:w-8/12 mx-auto md:text-center`}
    >
      {data
          ?.sort((a, b) => {
            return (
              moment(b.dateTimePlayed, "DD/MM/YYYY").unix() -
              moment(a.dateTimePlayed, "DD/MM/YYYY").unix()
            );
          })
          ?.sort((a, b) => {
            return (
              Number(b.seasonTitle.split("/")[1]) -
              Number(a.seasonTitle.split("/")[1])
            );
          })
      .map((result, key) => (
        <div
          key={key}
          className={`${
            key > 2 ? "hidden" : ""
          } mb-7 bg-slate-400 bg-opacity-10 p-3 rounded-md`}
        >
          <div className="flex justify-between items-center uppercase font-favela-bold text-xs mb-3 md:text-left">
            {moment(result.dateTimePlayed, "DD/MM/YYYY").format("Do MMM YYYY")}
            {teamManager && (
              <div className="flex items-center gap-2">
                <SwitchButton
                  labelPosition="left"
                  isLoading={publishing}
                  small
                  text={
                    result.publishStatus === "PUBLISHED"
                      ? "UNPUBLISH"
                      : "PUBLISH"
                  }
                  handleCheckboxChange={() => handlePublishResult(result)}
                  checked={
                    result?.publishStatus &&
                    result?.publishStatus === PUBLISHSTATUS.PUBLISHED
                  }
                />
              </div>
            )}
          </div>
          <div
            onClick={() => handleClickResult(result)}
            className="flex capitalize justify-center mb-4 gap-4 items-center"
          >
            <div className="w-5/12">
              <p title={`${result?.homeTeam?.data?.clubName}`}>
                {result?.homeTeam?.data?.clubName.substr(0, 15)}
                {result?.homeTeam?.data?.clubName?.length > 15 ? "..." : ""}
              </p>
              {/* {result?.homeTeam?.data?.teamName !==
                result?.homeTeam?.data?.clubName && (
                <p
                  title={`${result?.homeTeam?.data?.teamName}`}
                  className="text-xs text-ellipsis"
                >
                  {result?.homeTeam?.data?.teamName?.substr(0, 14)}
                  {result?.homeTeam?.data?.teamName?.length > 14 ? "..." : ""}
                </p>
              )} */}
            </div>
            <div className="flex w-2/12 gap-1 flex-nowrap font-favela-bold">
              <div>{result?.homeTeam?.score}</div>
              <div>&#8211;</div>
              <div>{result?.awayTeam?.score}</div>
            </div>
            <div className="w-5/12">
              <p title={`${result?.awayTeam?.data?.clubName}`}>
                {result?.awayTeam?.data?.clubName.substr(0, 15)}
                {result?.awayTeam?.data?.clubName?.length > 15 ? "..." : ""}
              </p>
              {/* {result?.awayTeam?.data?.teamName !==
                result?.awayTeam?.data?.clubName && (
                <p
                  title={`${result?.awayTeam?.data?.teamName}`}
                  className="text-xs text-ellipsis"
                >
                  {result?.awayTeam?.data?.teamName?.substr(0, 14)}
                  {result?.awayTeam?.data?.teamName?.length > 14 ? "..." : ""}
                </p>
              )} */}
            </div>
          </div>
        </div>
      ))}
      {data.length > 3 && (
        <div
          onClick={() => handleMore()}
          className="text-center border-b text-sm text-gray-600 w-max mx-auto mb-2 border-gray-300"
        >
          See All Results
        </div>
      )}
    </div>
  );
};

export default Results;
