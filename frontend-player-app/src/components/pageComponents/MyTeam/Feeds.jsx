import React, { useRef, useState } from "react";
import VideoUI from "../../reusable/VideoUI";
import { PlayIcon } from "../../svgIcons";
import OpenFeed from "../Feeds/OpenFeed";

const Feeds = ({ highlights }) => {
  const [openFeed, setOpenFeed] = useState("");

  const videoRef = useRef(null);

  const handleClose = () => {
    setOpenFeed("");
  };

  return (
    <>
      <div className="mt-6">
        {highlights?.length > 0 ? (
          highlights?.map((highlight, idx) => (
            <div key={highlight.id} className="my-4">
              {idx !== 0 && (
                <div className="divider  after:bg-gray-100 before:bg-gray-100"></div>
              )}
              <div className="flex gap-2 items-center">
                <img 
                  src={`${
                    highlight?.user?.photoUrl?.length > 5
                      ? highlight?.user?.photoUrl
                      : "/images/profile.png"
                  }`}
                  alt="profile pix"
                  className="w-[58px] h-[58px] object-cover rounded-full"
                />
                <div>
                  <p className="text-[14px] font-medium">
                    {`@${highlight?.user?.firstName}`}
                  </p>
                  <p className="text-[14px] font-medium opacity-50">
                    {highlight?.user?.teamName}
                  </p>
                </div>
              </div>
              {highlight.type === "PHOTO" && (
                <div
                  onClick={() =>
                    setOpenFeed(openFeed === "" ? highlight.url : "")
                  }
                  className="w-full rounded-[32px] mt-6 overflow-hidden aspect-[4/5]"
                >
                  <img 
                    src={highlight.url}
                    alt="feed"
                    className="w-full h-full object-cover"
                  />
                  {openFeed === highlight.url && (
                    <OpenFeed highlight={highlight} closeModal={handleClose}>
                      <div className="w-full rounded-[32px] overflow-hidden aspect-[4/5]">
                        <img 
                          src={highlight.url}
                          alt="feed"
                          className="w-full h-full object-cover"
                        />
                      </div>
                    </OpenFeed>
                  )}
                </div>
              )}
              {highlight.type === "VIDEO" && (
                <>
                  <div
                    onClick={() =>
                      setOpenFeed(openFeed === "" ? highlight.url : "")
                    }
                    className="w-full rounded-[32px] mt-6 overflow-hidden aspect-video"
                  >
                    <div className="w-full h-full relative">
                       <video 
                        ref={videoRef}
                        className="w-full h-full bg-black rounded-[32px] overflow-hidden"
                      >
                        <source src={`${highlight.url}#t=0.001`} type="video/mp4" />
                      </video>
                      <div className="absolute top-0 right-0 bottom-0 left-0 flex justify-center items-center">
                        <div className="w-[50px] h-[50px] bg-gray-100 rounded-full flex justify-center items-center">
                          <PlayIcon />
                        </div>
                      </div>
                    </div>
                  </div>
                  {openFeed === highlight.url && (
                    <OpenFeed closeModal={handleClose} highlight={highlight}>
                       <VideoUI videoUrl={highlight.url} />
                    </OpenFeed>
                  )}
                </>
              )}
            </div>
          ))
        ) : (
          <div className="h-[60vh] flex justify-center items-center">
            No Highlights Yet
          </div>
        )}
      </div>
    </>
  );
};

export default Feeds;
