import React, { memo, useEffect } from "react";
import { BigButtons } from "../../reusable/buttons/Buttons";
import { useNavigate } from "react-router-dom";
import Header from "../../reusable/Header";
import BackButton from "../../reusable/buttons/BackButton";
import Logo from "../../reusable/Logo";
import BottomNavigation from "../../reusable/BottomNav";
import { useSelector } from "react-redux";
import Popup from "./Popup";

const Force2UpdateProfile = memo(() => {
  const navigate = useNavigate();

  const {
    auth: { authUser },
    user
  } = useSelector(({ auth, user }) => ({
    auth,
    user
  }));

  const navToMyTeams = async () => {
    if (!authUser?.userInfo?.id) {
      navigate("/login");
      return;
    }
    if (user?.data?.cludId?.length > 4 || user?.data?.teamId > 4) {
      navigate(
        "/team-dashboard?teamId=" +
          user.data.teamId +
          "&clubId=" +
          user.data.cludId
      );
    }
  };

  useEffect(() => {
    navToMyTeams();
  }, []);

  // used to disable teams page. once we are ready. we will remove this one
  return (
    <Popup
      textComp={
        <>
          <p>COMING SOON.</p>
          <p>THE CLUB & TEAM FEATURE IS NOT AVAILABLE YET. </p>
          <p>SET TO LAUNCH IN 2026.</p>
        </>
      }
      shouldShowTextComp={true}
      shouldShowEmail={false}
    />
  );

  return (
    <div className="pb-[70px] page">
      <Header>
        <BackButton onClick={() => navigate(-1)} />
        <Logo />
        <div />
      </Header>
      <div className="flex flex-col justify-between h-[75vh] text-center">
        <h3 className="text-center text-[24px] font-favela-bold opacity-40 text-black mt-[30%]">
          OOOPS...
        </h3>
        <div className="text-center text-[16px] font-poppins h-[50%] text-black font-bold">
          <p>
            You cannot access your team's page because you have not selected
            your club / team yet
          </p>
          <p className="mt-10">
            Please edit your profile in order to access your team's page:
          </p>
        </div>
        <BigButtons
          label="Edit Profile"
          green
          onClick={() => navigate("/edit-profile")}
        />
      </div>
      <BottomNavigation />
    </div>
  );
});

export default Force2UpdateProfile;
