import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { fixtures } from "../../reusable/data";
import moment from "moment";

const Fixtures = ({ selected, data }) => {
  const navigate = useNavigate();

  const handleClickResult = (detail) => {
    navigate("/team-dashboard/result-details", {
      state: { data: detail }
    });
  };

  const handleMore = () => {
    navigate("/fixtures");
  };

  return (
    <div
      className={`${
        selected === "fixtures" ? "block" : "hidden"
      } mt-7 md:w-8/12 mx-auto md:text-center`}
    >
      {data?.sort((a, b) => {
       return moment(a.dateTimePlayed, "DD/MM/YYYY").unix() -  moment(b.dateTimePlayed, "DD/MM/YYYY").unix()
      }).map((result, key) => (
        <div key={key} className="mb-7">
          <div className="font-favela-bold text-xs mb-3 md:text-left">
            {moment(result.dateTimePlayed, "DD/MM/YYYY, h:mm").format('Do MMM YYYY')}
          </div>
          <div
            onClick={() => handleClickResult(result)}
            className="flex justify-center mb-4 gap-4 items-center"
          >
            <div className="w-5/12">
              <p title={`${result?.homeTeam?.data?.clubName}`}>
                {result?.homeTeam?.data?.clubName.substr(0, 15)}
                {result?.homeTeam?.data?.clubName?.length > 15 ? "..." : ""}
              </p>
            </div>
            <div className="flex w-2/12 gap-1 flex-nowrap font-favela-bold ml-[40px]">
              <div>&#8211;</div>
            </div>
            <div className="w-5/12">
              <p title={`${result?.awayTeam?.data?.clubName}`}>
                {result?.awayTeam?.data?.clubName.substr(0, 15)}
                {result?.awayTeam?.data?.clubName?.length > 15 ? "..." : ""}
              </p>
            </div>
          </div>
        </div>
      ))}
      {/* {data.length > 3 && (
        <div
          // onClick={() => handleMore()}
          className="text-center border-b text-sm text-gray-600 w-max mx-auto mb-2 border-gray-300"
        >
          See All Results
        </div>
      )} */}
    </div>
  );
};

export default Fixtures;
