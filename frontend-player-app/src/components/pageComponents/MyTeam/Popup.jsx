import React from "react";
import { MediumButtons } from "../../reusable/buttons/Buttons";
import { useNavigate } from "react-router-dom";
const defaultText =
  "This team has not been activated yet during beta. Please check back later. If you have any questions, reach out to";

const Popup = ({
  text = defaultText,
  textComp = <></>,
  actionBtn = "back",
  btnText = "Go Back",
  shouldShowEmail = true,
  shouldShowTextComp = false
}) => {
  const navigate = useNavigate();

  const navigateByAction = () => {
    switch (actionBtn) {
      case "back":
        navigate(-1);
        break;
      case "editProfile":
        navigate("/edit-profile");
        break;
      default:
        break;
    }
  };

  return (
    <div className="absolute top-0 left-0 flex items-center justify-center w-[100vw] h-[100vh] bg-black">
      <div className="flex z-40 opacity-100 bg-white flex-col text-center text-[16px] leading-loose font-semibold font-poppins justify-center items-center p-8 max-w-[80%] rounded-[20px]">
        {shouldShowTextComp ? textComp : text}
        {shouldShowEmail ? (
          <a
            className="w-full text-gray-700 underline"
            href="mailto: <EMAIL>"
          >
            <EMAIL>
          </a>
        ) : (
          ""
        )}
        <MediumButtons
          onClick={navigateByAction}
          className={"mt-7"}
          black
          label={btnText}
        />
      </div>
    </div>
  );
};

export default Popup;
