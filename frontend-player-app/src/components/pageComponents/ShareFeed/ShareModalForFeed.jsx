import React from "react";
import {
  EmailShareButton,
  TelegramShareButton,
  WhatsappShareButton,
} from "react-share";
import { handleCopyToClipBoard } from "../../../utils/helpers";
import Analytics from "../../../utils/google-analytics";
import { TRACKING_EVENTS } from "../../../utils/constants";

const ShareModalForFeed = ({
  url = "https://www.playerapp.co/invite",
  header = "Invite your friends",
  title = "Join your team on PLAYER",
  closeIconPath = "/images/closeModalIcon.svg",
}) => {
  const link = encodeURI(url);
  const styles = {
    outline: "none",
  };

  const clipText = `${title} \n ${link}`;

  const onBtnClick = (share_method, isCopy = false) => {
    if (isCopy) {
      handleCopyToClipBoard(clipText)
    }

    Analytics.trackEvent({
      name: TRACKING_EVENTS.CONTENT_SHARED,
      metadata: {
        share_method,
        url: link,
      }
    })
  }

  return (
    <div className="w-full absolute bottom-0 py-3 left-0 border-t-2 bg-white">
      <div className="flex w-full justify-around items-center">
        <EmailShareButton onClick={() => onBtnClick('email')} url={link} subject={title} style={styles}>
          <div className="w-[65px] h-[65px] rounded-[20px] border-[1px] border-[#000000] flex justify-center items-center">
            <img src="/images/gmailIcon.svg" alt="gmailIcon" />
          </div>
        </EmailShareButton>
        <WhatsappShareButton onClick={() => onBtnClick('whatsapp')} url={link} title={title} style={styles}>
          <div className="w-[65px] h-[65px] rounded-[20px] border-[1px] border-[#000000] flex justify-center items-center">
            <img src="/images/whatsappIcon.svg" alt="whatsappIcon" />
          </div>
        </WhatsappShareButton>
        {/* <TelegramShareButton url={link} title={title} style={styles}>
          <div className="w-[65px] h-[65px] rounded-[20px] border-[1px] border-[#000000] flex justify-center items-center">
            <img src="/images/telegramIcon.svg" alt="telegramIcon" />
          </div>
        </TelegramShareButton> */}
        <button
          onClick={() => onBtnClick('copy', true)}
          className="m-0 p-0 dark:bg-white "
        >
          <div className="w-[65px] h-[65px] dark:bg-white rounded-[20px] border-[1px] border-[#000000] flex justify-center items-center">
            <img src="/images/copyIcon.svg" alt="copyIcon" />
          </div>
        </button>
      </div>
    </div>
  );
};

export default ShareModalForFeed;
