const UnblockModal = ({ isOpen, onClose, onUnblock }) => {
    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
            <div className="bg-white rounded-[30px] p-6 w-11/12 flex flex-col">
                <div className="flex-1 flex flex-col justify-between">
                    <div className="m-3">
                        <h2 className="text-2xl mb-4">
                            Are you sure you want to <span className="text-[#FF5B5B]">unblock</span> this user?
                        </h2>
                        <p className="text-xl text-[#A5A5A5] mb-6 text-justify">
                            If you unblock this user, they will be able to view your posts and interact with you again. They
                            will not be notified that you unblocked them
                        </p>
                    </div>
                    <div className="flex justify-center space-x-6">
                        <button
                            className="btn btn-primary px-8 rounded-full bg-[#000000] border-[#000000] text-[#FFFFFF] text-lg"
                            onClick={onUnblock}
                        >
                            Unblock
                        </button>
                        <button
                            className="btn btn-outline text-[#A5A5A5] px-8 rounded-full text-lg"
                            onClick={onClose}
                        >
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default UnblockModal