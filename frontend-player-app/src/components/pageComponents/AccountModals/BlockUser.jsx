import React from "react";

const BlockModal = ({ isOpen, onClose, onUnblock }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[1000]">
      <div className="bg-white rounded-[30px] p-6 w-11/12 flex flex-col">
        <div className="flex-1 flex flex-col justify-between">
          <div className="m-3">
            <h2 className="text-2xl mb-4 text-[#000000] rounded-xl">
              Are you sure you want to{" "}
              <span className="text-[#FF5B5B]">block</span> this user?
            </h2>
          </div>
          <div className="flex justify-center space-x-6">
            <button
              className="btn btn-primary px-10 rounded-full bg-[#000000] border-[#000000] text-[#FFFFFF] text-lg"
              onClick={onUnblock}
            >
              Yes
            </button>
            <button
              className="btn btn-outline text-[#A5A5A5] px-10 rounded-full text-lg"
              onClick={onClose}
            >
              No
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BlockModal;
