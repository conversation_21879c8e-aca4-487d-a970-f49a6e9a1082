import React, { forwardRef } from "react";
import Logo from "../../reusable/Logo";

const VerificationConfirmModal = forwardRef((props, ref) => {
  return (
    <div>
      <p
        className="btn btn-ghost"
        ref={ref}
        onClick={() =>
          document
            .getElementById("non_player_verification_4_player")
            .showModal()
        }
      ></p>
      <dialog id="non_player_verification_4_player" className="modal">
        <div className="modal-box ">
          <div className="flex justify-center my-5">
            <Logo />
          </div>
          <div className="flex flex-col items-center text-center">
            <p className="font-favela-bold text-[20px] my-5">
              VERIFY A PLAYER’S PROFILE
            </p>
            <p className="text-[22px] font-poppins my-5 leading-10">
              {props.playerN<PERSON>} has requested you to verify their profile.
            </p>
            <p className="text-[18px] font-poppins text-opacity-50  text-black leading-10">
              To verify, please visit their profile here, and click the green
              Verify button (with visual representation of the toggle / button)
              to confirm their statistics and club tenures.
            </p>
            <p className="text-[18px] font-poppins text-opacity-50 text-black leading-10">
              You can add a footballing & character reference on their behalf,
              which will aid their career.
            </p>
          </div>
          {props.isError && (
            <p className="text-red-600 text-center">
              Please reload to load players data
            </p>
          )}
        </div>

        <form method="dialog" className="modal-backdrop">
          <button className="bg-transparent">close</button>
        </form>
      </dialog>
    </div>
  );
});

export default VerificationConfirmModal;
