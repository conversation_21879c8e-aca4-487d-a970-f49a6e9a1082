import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { dispatch } from "../../../redux/store";
import { BigButtons, SmallButtons } from "../../reusable/buttons/Buttons";

const ConfirmAccount = () => {
  const [code, setCode] = useState("");
  const [visible, setVisible] = useState(false);
  const [email, setEmail] = useState("");
  const navigate = useNavigate();

  const authModel = useSelector(({ auth }) => auth);

  useEffect(() => {
    setVisible(authModel?.confirmSignUpModal?.visible);
    setEmail(authModel?.confirmSignUpModal?.email);
  }, [
    authModel?.confirmSignUpModal?.email,
    authModel?.confirmSignUpModal?.visible,
  ]);

  const handleCancel = () => {
    dispatch.auth.confirmSignUpModalBox({ visible: false, email: null });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    dispatch.auth.confirmSignUp({
      code,
      email,
      navigate,
    });
  };

  const handleResend = (e) => {
    e.preventDefault();
    dispatch.auth.resendVerifyCode({
      email,
    });
  };

  return visible ? (
    <div className="fixed h-screen flex overflow-y-auto flex-col justify-center inset-0 w-full bg-black bg-opacity-60 z-50">
      <div
        style={{ maxWidth: "400px" }}
        className="relative bg-white mx-auto mt-10 pb-10 pt-16 w-11/12 rounded-lg"
      >
        <div className="absolute -right-10 top-2 w-1/2 h-24">
          <SmallButtons onClick={handleCancel} black label="X" />
        </div>

        <div className="px-5">
          <h3
            style={{ color: "#091E42" }}
            className="text-lg text-center font-semibold"
          >
            Account Created!
          </h3>
          <p className="pt-2 text-center text-base">
            One more step, check your email and enter the 6 digit code we sent
            you to confirm your account
          </p>
          <form>
            <div className="">
              <input
                className="border border-gray-600 rounded-xl w-full py-3 px-3 text-mainLight placeholder:text-gray-700 mt-7"
                onChange={(e) => setCode(e.target.value)}
                placeholder="Enter Confirm Code"
                id="code"
                type="password"
              />
              <div className="flex mt-10 flex-col gap-6">
                <BigButtons
                  onClick={handleSubmit}
                  green
                  label="CONFIRM ACCOUNT"
                />
                <BigButtons onClick={handleResend} black label="RESEND CODE" />
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  ) : (
    <div></div>
  );
};
export default ConfirmAccount;
