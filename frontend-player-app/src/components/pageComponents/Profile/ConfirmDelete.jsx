import { dispatch } from "../../../redux/store";
import { SmallButtons } from "../../reusable/buttons/Buttons";
import { useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";

const ConfirmDelete = ({ shouldShowModal, handleCloseModal, data }) => {
  const navigate = useNavigate();

  const deleting = useSelector(
    ({ loading }) => loading.effects.feed.deleteHighlights
  );

  const handleDelete = async () => {
    const res = await dispatch.feed.deleteHighlights({
      id: data.id,
      userId: data.userId,
    });

    if (res.status === 1) {
      handleCloseModal(false);
      navigate(-1);
    }
  };

  return (
    shouldShowModal &&
    data && (
      <div className="absolute z-50 top-0 left-0 bg-black bg-opacity-90 w-[100vw] h-[100vh]">
        <div className="flex justify-center items-center h-full">
          <div className="bg-white w-[60%] px-5 py-3 m-auto rounded-3xl text-center">
            <p>Are you sure you want to delete this?</p>
            <div className="mt-5 flex gap-3 overflow-scroll">
              <SmallButtons
                isLoading={deleting}
                onClick={handleDelete}
                black
                label="Yes"
              />
              <SmallButtons
                onClick={() => handleCloseModal(false)}
                label="No"
              />
            </div>
          </div>
        </div>
      </div>
    )
  );
};
export default ConfirmDelete;
