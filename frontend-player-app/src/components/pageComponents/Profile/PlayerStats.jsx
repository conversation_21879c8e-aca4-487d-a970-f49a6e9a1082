import { useEffect, useState } from "react";
import EditExperience from "../Stats/EditExperience";
import { notifyError } from "../../../utils/helpers";
import { Experience } from "../../../services";
import { dispatch } from "../../../redux/store";
import { useSelector } from "react-redux";
import Loader from "../../reusable/loading/Loader";

const PlayerStats = ({ data = {}, shouldEdit }) => {
  const [editModal, setEditModal] = useState({ visible: false, data: null });
  const [isLoading, setLoading] = useState(false);

  const { userId } = useSelector(({ auth }) => ({
    userId: auth.authUser?.userInfo.id
  }));

  async function handleDelete(id) {
    try {
      setLoading(true);
      await Experience.deleteExperience(id);
      await dispatch.user.getUserProfile({ id: userId });
    } catch (error) {
      setLoading(false);
      notifyError(error.message);
    }

    setLoading(false);
  }

  if (!Object.keys(data).length) {
    return (
      <div className="text-white flex justify-center items-center">
        Update your experience to display your stats
      </div>
    );
  }

  if (isLoading) {
    return <Loader />;
  }

  return (
    <>
      {editModal.visible ? (
        <EditExperience
          handleCloseModal={setEditModal}
          shouldShowModal={editModal.visible}
          data={editModal.data}
        />
      ) : (
        ""
      )}
      {Object.keys(data)
        ?.reduce((prev, curr, i) => {
          if (i === 0) {
            return [...(data[prev] || []), ...data[curr]];
          } else {
            return [...(prev || []), ...data[curr]];
          }
        }, [])
        ?.map((stat, i) => {
          return (
            <div key={stat?.id + i} className="flex flex-col gap-3 mb-7">
              <div key={i + stat.id} className="flex justify-between w-full">
                <p className="pr-2 text-[18px] text-white">
                  Season {stat.seasonName}
                </p>{" "}
                {shouldEdit ? (
                  <div className="flex">
                    <button
                      className="bg-transparent flex justify-end justify-items-end px-0 mx-0 mr-5"
                      onClick={() => handleDelete(stat.id)}
                    >
                      <img  className="md:hidden" src="/images/deleteIcon.svg" alt="" />
                      <img  className="hidden md:block" src="/images/desktopDeleteIcon.svg" alt="" />
                    </button>
                    <button
                      className="bg-transparent flex justify-end justify-items-end px-0 mx-0"
                      onClick={() =>
                        setEditModal({ visible: true, data: stat })
                      }
                    >
                       <img  className="md:hidden" src="/images/editIcon.svg" alt="" />
                      <img  className="hidden md:block" src="/images/editDesktopIcon.svg" alt="" />
                    </button>
                  </div>
                ) : (
                  ""
                )}
              </div>

              <div className="flex justify-between items-center text-white">
                <p className="text-[14px] font-normal text-white">
                  Appearances
                </p>
                <p>{stat?.appearances || "N/A"}</p>
              </div>
              <div className="flex justify-between items-center text-white">
                <p className="text-[14px] font-normal text-white">Goals</p>
                <p>{stat?.goals || "N/A"}</p>
              </div>
              <div className="flex justify-between items-center text-white">
                <p className="text-[14px] font-normal text-white">Assists</p>
                <p>{stat?.assists || "N/A"}</p>
              </div>
              <div className="flex justify-between items-center text-white">
                <p className="text-[14px] font-normal text-white">
                  Clean Sheets
                </p>
                <p>{stat?.cleanSheets || "N/A"}</p>
              </div>
            </div>
          );
        })
        .reverse()}
    </>
  );
};

export default PlayerStats;
