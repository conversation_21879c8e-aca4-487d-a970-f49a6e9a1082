// import { dispatch } from "../../../redux/store";
// import { SmallButtons } from "../../reusable/buttons/Buttons";
// import { useNavigate } from "react-router-dom";
// import { useSelector } from "react-redux";
// import { useState } from "react";

// const ReportModal = ({ shouldShowModal, handleCloseModal, data }) => {
//   const navigate = useNavigate();

//   const reporting = useSelector(
//     ({ loading }) => loading.effects.feed.reportHighlights
//   );

//   const handleReporting = async () => {
//     const res = await dispatch.feed.reportHighlights(data);

//     if (res.status === 1) {
//       handleCloseModal(true);
//     //   navigate(-1);
//     }
//   };

//   return (
//     shouldShowModal &&
//     (
//       <div className="absolute z-50 top-100 left-0 bg-transparent bg-opacity-90 w-[100vw] h-[100vh]">
//         <div className="flex justify-center items-center h-full">
//           <div className="bg-white w-[60%] px-5 py-3 m-auto rounded-3xl text-center">
//             <p>Reason for reporting this post?</p>
//             <textarea 
//                 className="bg-gray-200 appearance-none border-2 border-gray-200 rounded w-full py-2 px-4 text-gray-700 leading-tight focus:outline-none focus:bg-white focus:border-purple-500"
//                 id="report"
//                 type="text"
//                 value={reason}
//                 onChange={(e) => setReason(e.target.value)}
//             />
//             <div className="mt-5 flex gap-3 overflow-scroll">
//               <SmallButtons
//                 isLoading={reporting}
//                 onClick={handleReporting}
//                 red
//                 label="Report"
//               />
//               <SmallButtons
//                 onClick={() => handleCloseModal(false)}
//                 label="Close"
//               />
//             </div>
//           </div>
//         </div>
//       </div>
//     )
//   );
// };
// export default ReportModal;
