import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import _uniqWith from "lodash.uniqwith";
import _iseEqual from "lodash.isequal";

import { uploadedStreamsBaseUrl } from "../../../utils/constants";
import moment from "moment";
import { useSelector } from "react-redux";
import { dispatch } from "../../../redux/store";
import LoadingCards from "../../Highligts/Loading";
import { shouldShowWaitIcon } from "../../../utils/helpers";
import hourglassIcon from "../../../assets/hourglass.svg";
import UploadViaAppBanner from "../../reusable/UploadViaAppBanner";

const Highlights = ({ userId, ownProfile, textColor, isNonPlayer }) => {
  const [profileHighlights, setUserHighlights] = useState([]);
  const [openAppBanner, setOpenAppBanner] = useState(false);

  const navigate = useNavigate();

  const fetchingHighlight = useSelector(
    ({ loading }) => loading.effects.feed.fetchHighlightsByUserId
  );

  const handleNavigateToHighlight = (highlight) => {
    navigate(
      `/user/comment?highlightId=${highlight.id}&userId=${highlight.userId}&from=profile?id=${highlight.userId}%26selected%3Dhighlights`
    );
  };

  //Convert duration seconds to minutes and seconds
  function convertSecondsToMinutesAndSeconds(totalSeconds) {
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = Math.floor(totalSeconds % 60);
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  }

  /**
   * @description Fetch user highlights
   */
  const getUserHighlights = async (requestPayload = { limit: 1000 }) => {
    if (userId) {
      const data = await dispatch.feed.fetchHighlightsByUserId({
        id: userId,
        requestPayload
      });

      setUserHighlights((prev) => {
        const newData = {
          data: _uniqWith([...(prev?.data || []), ...data?.data], _iseEqual)
        };

        return newData.data;
      });
    }
  };

  const uploadFeed = () => {
    setOpenAppBanner(true);
  };

  useEffect(() => {
    if (userId) {
      getUserHighlights();
    } else {
      navigate(-1);
    }
  }, [userId]);

  if (fetchingHighlight) {
    return <LoadingCards />;
  }

  return (
    <div className="">
      {userId && ownProfile && (
        <div
          onClick={uploadFeed}
          className="rounded-[32px] flex justify-center items-center"
        >
          <img
            src={
              isNonPlayer ? "/images/plus-black.png" : "/images/plus-white.png"
            }
          />
        </div>
      )}

      <div className="grid grid-cols-3 my-3 gap-x-[2px] gap-y-[1px] sm:grid-cols-3 max-h-[300px] overflow-y-scroll">
        {profileHighlights?.length > 0 ? (
          <>
            {profileHighlights
              ?.filter(
                (phlight) =>
                  phlight.type === "PHOTO" || phlight.type === "VIDEO"
              )
              .sort(
                (a, b) =>
                  moment(b.createdAt).unix() - moment(a.createdAt).unix()
              )
              .map((highlight, idx) => {
                return (
                  <div key={highlight.id} className="w-full bg-white h-[112px]" hidden={highlight?.videoProcessingFailed && !ownProfile}>
                    {highlight.type === "PHOTO" && (
                      <div className="h-full">
                        <img
                          onClick={() =>
                            navigate(
                              `/user/comment?highlightId=${highlight.id}&userId=${highlight.userId}&from=profile?id=${highlight.userId}%26selected%3Dhighlights`
                            )
                          }
                          key={highlight.id}
                          src={highlight.url}
                          alt=""
                          className="w-full object-cover h-[120px]"
                        />
                      </div>
                    )}
                    {highlight.type === "VIDEO" && (
                      <div
                        onClick={() => handleNavigateToHighlight(highlight)}
                        className={`w-full h-[110px] bg-white ${
                          highlight?.videoProcessingFailed && !ownProfile
                            ? "hidden"
                            : ""
                        }`}
                      >
                        <div className="w-full  h-full relative">
                          {highlight?.streamUrl ? (
                            <>
                              <img
                                alt="thumbnail"
                                className="object-cover w-full h-full bg-white"
                                src={
                                  shouldShowWaitIcon(highlight)
                                    ? hourglassIcon
                                    : Boolean(highlight?.videoProcessingFailed)
                                    ? "/images/Green-Uploadfailedicons.png"
                                    : uploadedStreamsBaseUrl +
                                      `/${
                                        highlight?.streamUrl?.key?.split(
                                          "--"
                                        )[1]
                                      }/thumbnail.png`
                                }
                              />
                              <div className="absolute top-0 right-0 bottom-0 left-0 flex justify-center items-center w-full h-full bg-black bg-opacity-70 z-1000">
                                <div className="flex justify-center items-center max-h-24 pt-5 overflow-scroll">
                                  <div className="text-white text-sm font-poppins font-black text-center px-4 py-2 max-w-[305px]">
                                    {highlight?.comment}
                                  </div>
                                </div>
                              </div>
                              <div className="absolute mb-1 bottom-1 right-2 bg-black bg-opacity-50 px-2 py-1 text-white text-[8.7px] font-poppins font-bold rounded-full">
                                {highlight?.streamUrl?.duration
                                  ? convertSecondsToMinutesAndSeconds(
                                      highlight?.streamUrl?.duration
                                    )
                                  : "--:--"}
                              </div>
                            </>
                          ) : (
                            <video
                              // ref={videoRef}
                              className="absolute inset-0 object-cover w-full h-full"
                            >
                              <source
                                src={`${highlight.url}#t=0.001`}
                                type="video/mp4"
                              />
                            </video>
                          )}
                          {/* <div className="absolute top-0 right-0 bottom-0 left-0 flex justify-center items-center">
                                <div className="w-[40px] h-[40px] bg-gray-100 bg-opacity-50 rounded-full flex justify-center items-center">
                                  <PlayIcon />
                                </div>
                              </div> */}
                        </div>
                      </div>
                    )}
                  </div>
                );
              })}
            {/* {fetchingMore && (
                <div className="h-[112px] bg-gray-400 w-full animate-pulse rounded-[10px]"></div>
              )} */}
          </>
        ) : (
          <div
            className={`${textColor} flex justify-center items-center  w-full col-span-3 text-opacity-50 text-center`}
          >
            {ownProfile
              ? "Upload football content. Just tap the + button above."
              : "This player has no highlights yet."}
          </div>
        )}
      </div>
      {openAppBanner && (
        <UploadViaAppBanner onClose={() => setOpenAppBanner(false)} />
      )}
    </div>
  );
};

export default React.memo(Highlights);
