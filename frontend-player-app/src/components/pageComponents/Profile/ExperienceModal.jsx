import { useCallback, useEffect, useState } from "react";
import { useSelector } from "react-redux";
import _debounce from "lodash/debounce";

import PageSubTitle from "../../reusable/PageTitle";
import { dispatch } from "../../../redux/store";
import { Experience } from "../../../services";
import { notifyError } from "../../../utils/helpers";
import SelectWithPicture from "../../reusable/SelectWithPicture";
import { useSearchParams } from "react-router-dom";
import PlusIcon from "../../svgIcons/PlusIcon";
import { toast } from "react-toastify";

let experience = {
  teamName: "",
  seasonName: "",
  appearances: 0,
  goals: 0,
  assists: 0,
  cleanSheets: 0,
  teamLogo: ""
};

const ExperienceModal = ({ textColor, isNonPlayer }) => {
  const [seasonNameError, setSeasonNameError] = useState("");
  const [userExperience, setUserExperience] = useState(experience);
  const [clubs, setClubs] = useState();
  const [selectClub, setSelectClub] = useState({});
  const [searchValue, setSearchValue] = useState("");
  const [errorMessage, setNotFoundClubError] = useState(false);
  const [teamLogo, setTeamLogo] = useState("");
  const [teams, setTeams] = useState([]);
  const [selectTeam, setSelectTeam] = useState({});

  const [searchParams] = useSearchParams();
  const selectedTab = searchParams.get("selected");

  const seasonCheck = /^([0-9]{2})\/?([0-9]{2})$/;
  const validSeasonName = seasonCheck.test(userExperience.seasonName);
  const isDisabled = userExperience.seasonName && !validSeasonName;

  const { userInfo } = useSelector((state) => state.auth.authUser);
  const allClubs = useSelector(({ club }) => club.allClubs);
  const { teamsModel } = useSelector(({ team }) => ({
    teamsModel: team.allTeams
  }));

  if (userInfo?.teamName) {
    experience["teamName"] = userInfo.teamName || "";
  }

  const handleTeamChange = ({ target }) => {
    setSelectTeam(
      teams.find((item) =>
        item?.teamName?.toLowerCase()?.includes(target.value?.toLowerCase())
      ) || {}
    );
  };

  const handleClubSelection = async (clubId) => {
    const res = await dispatch.team.getTeamsByClubId(clubId);

    setTeams(res?.data || []);
  };

  const getTeamsBasedOnSelectedClub = useCallback(
    _debounce(handleClubSelection, 100),
    []
  );

  const handleSeasonName = (e) => {
    const inputValue = e.target.value;
    const regex = /^\d{2}\/\d{2}$/;
    const currentYear = new Date().getFullYear().toString().slice(-2);

    if (regex.test(inputValue)) {
      const [startYear, endYear] = inputValue.split("/");

      if (parseInt(startYear, 10) > parseInt(currentYear, 10)) {
        setSeasonNameError("Start year cannot be in the future.");
      } else if (parseInt(endYear, 10) !== parseInt(startYear, 10) + 1) {
        setSeasonNameError("End year must be one year after the start year.");
      } else {
        setSeasonNameError("");
      }
    } else if (inputValue) {
      setSeasonNameError("Invalid format. Use YY/YY (e.g., 22/23).");
    }
    setUserExperience({ ...userExperience, seasonName: inputValue });
  };

  const handleAppearance = (e) => {
    setUserExperience({ ...userExperience, appearances: e["target"]["value"] });
  };
  const handleGoals = (e) => {
    setUserExperience({ ...userExperience, goals: e["target"]["value"] });
  };
  const handleAssist = (e) => {
    setUserExperience({ ...userExperience, assists: e["target"]["value"] });
  };
  const handleCleansheet = (e) => {
    setUserExperience({ ...userExperience, cleanSheets: e["target"]["value"] });
  };
  const handleminsPlayed = (e) => {
    setUserExperience({ ...userExperience, minsPlayed: e["target"]["value"] });
  };

  const handleBlur = () => {
    if (validSeasonName && !seasonNameError) {
      setSeasonNameError("");
    } else {
      setSeasonNameError("Must be in YY/YY format as required");
    }
  };
  const handleSubmit = async () => {
    if (isDisabled || seasonNameError) {
      toast.error("Ensure all fields are filled correctly");
      return;
    }
    const mergedValues = {
      teamName: selectTeam?.teamName || selectClub?.clubName || searchValue,
      teamLogo: selectTeam?.logoUrl || teamLogo,
      userId: userInfo.id,
      appearances: +userExperience.appearances,
      goals: +userExperience.goals,
      minsPlayed: +userExperience.minsPlayed,
      assists: +userExperience.assists,
      cleanSheets: +userExperience.cleanSheets,
      seasonName: userExperience.seasonName,
      teamId: selectTeam?.id || "",
      clubId: selectClub?.id || ""
    };

    try {
      await Experience.addExperience(mergedValues);
      await dispatch.user.getUserProfile({ id: userInfo.id });
    } catch (error) {
      notifyError(error?.response?.message || error.message);
    }
  };

  const fetchClubs = async () => {
    const res = await dispatch.club.getAllClubs();
    setClubs(res);
  };

  useEffect(() => {
    // if (!allClubs.length) {
      fetchClubs();
    // }
    // on mount set the error
    handleBlur();
    setClubs(allClubs);
  }, []);

  return (
    <div className="">
      {/* The button to open modal */}
      <label htmlFor="my-modal-4">
        <div
          className={`${
            selectedTab !== "highlights" ? "block" : "hidden"
          } flex w-[25px] h-[25px] justify-center items-center mx-auto mt-5`}
        >
          <img
            src={
              isNonPlayer ? "/images/plus-black.png" : "/images/plus-white.png"
            }
          />
          {/* <PlusIcon fill1={isNonPlayer ? 'white' : 'black'} fill2={isNonPlayer ? 'black' : 'white'} /> */}

          {/* <p className={`${textColor} font-bold`}>+</p> */}
        </div>
      </label>

      {/* Put this part before </body> tag */}
      <input type="checkbox" id="my-modal-4" className="modal-toggle" />
      <div className="modal bg-[#d9d9d9] ">
        <div className="modal-box relative bg-white">
          <label
            htmlFor="my-modal-4"
            className="btn btn-sm btn-circle absolute right-2 top-2"
          >
            ✕
          </label>
          <PageSubTitle>
            <div className="text-[16px] uppercase w-full text-center">
              Add New Experience
            </div>
          </PageSubTitle>
          <div className="flex gap-6 mt-6">
            <div className="relative z-0 w-full mb-7 group">
              <div className="appearance-none pt-3 border-none w-full text-gray-700 py-1 leading-tight">
                <SelectWithPicture
                  setSelectClub={setSelectClub}
                  selectClub={selectClub}
                  searchValue={searchValue}
                  setSearchValue={setSearchValue}
                  setTeams={setTeams}
                  setNotFoundClubError={setNotFoundClubError}
                  getTeamsBasedOnSelectedClub={getTeamsBasedOnSelectedClub}
                  setTeamLogo={setTeamLogo}
                  setSelectTeam={setSelectTeam}
                />
              </div>
              <label
                htmlFor="teamName"
                className="peer-focus:font-medium absolute text-sm text-gray-500 dark:text-gray-400 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-gray-600 peer-focus:dark:text-gray-100 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6"
              >
                Football Club / Organisation
              </label>
            </div>
          </div>
          {teams?.length ? (
            <div className="relative z-0 w-full mb-7 group">
              <div className="flex items-center">
                <div className="appearance-none border-none w-full text-gray-700 py-1 leading-tight">
                  {/* <input
                    onChange={handleTeamChange}
                    defaultValue={selectTeam?.teamName}
                    className="appearance-none bg-transparent border-b-2 border-gray-600 w-full text-gray-700 py-1 pl-2 leading-tight focus:outline-none"
                    placeholder=" "
                    list="teams"
                  /> */}
                  {/* <datalist type="text" id="teams"> */}
                  <select
                    onChange={handleTeamChange}
                    className="appearance-none bg-transparent border-b-2 border-gray-600 w-full text-gray-700 py-1 pl-2 leading-tight focus:outline-none"
                  >
                    <option value="">Select team</option>
                    {teams
                      .sort((a, b) => b.index - a.index)
                      .map((team, idx) => (
                        <option
                          defaultValue={selectTeam?.teamName}
                          key={idx}
                          value={team?.teamName}
                        >
                          {team?.teamName}
                        </option>
                      ))}
                  </select>
                  {/* </datalist> */}
                </div>
              </div>
              <label
                htmlFor="teamName"
                className="peer-focus:font-medium absolute text-sm text-gray-500 dark:text-gray-400 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-gray-600 peer-focus:dark:text-gray-100 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6"
              >
                Team
              </label>
            </div>
          ) : (
            <>
              {errorMessage && searchValue.length > 2 && (
                <div className="text-red-600 font-poppins -mt-[20px] italic my-10 text-xs">
                  <p>Team not in database.</p>
                  <p>
                    Please contact{" "}
                    <a
                      className="text-red-600"
                      href={`mailto:<EMAIL>?subject=Add team to database&body=Hello PLAYER, \n Please add '${searchValue}' to your system`}
                    >
                      <EMAIL>{" "}
                    </a>
                    to get it added.
                  </p>
                </div>
              )}
            </>
          )}
          <div className="relative z-0 w-full mb-7 group">
            <input
              type="text"
              name="seasonName"
              id="edit_seasonName"
              onBlur={handleBlur}
              onChange={(e) => handleSeasonName(e)}
              value={userExperience.seasonName}
              className="border-b-2 border-gray-600 block pt-3 pb-1 px-0 w-full text-sm text-gray-900 bg-transparent appearance-none  focus:outline-none focus:ring-0 focus:border-gray-600 peer"
              placeholder="YY/YY or eg. 22/23"
            />
            <label
              htmlFor="edit_seasonName"
              className="text-xs text-gray-500 dark:text-gray-400  absolute -top-3"
            >
              Season (YY/YY)
            </label>
            <p className="absolute flex mt-1 justify-center text-red-600 text-xs italic">
              {seasonNameError ? seasonNameError : ""}
            </p>
          </div>

          {userInfo.userType !== "NON_PLAYER" && (
            <>
              <div className="my-5 grid grid-cols-2 gap-6 mt-6">
                <div className="relative z-0 w-full mb-7 group">
                  <input
                    type="number"
                    name="appearances"
                    id="edit_appearance"
                    onChange={(e) => handleAppearance(e)}
                    value={userExperience.appearances}
                    className="border-b-2 border-gray-600 block pt-3 pb-1 px-0 w-full text-sm text-gray-900 bg-transparent appearance-none  focus:outline-none focus:ring-0 focus:border-gray-600 peer"
                    placeholder=" "
                  />
                  <label
                    htmlFor="edit_appearance"
                    className="peer-focus:font-medium absolute text-sm text-gray-500 dark:text-gray-400 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-gray-600 peer-focus:dark:text-gray-100 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6"
                  >
                    Appearance
                  </label>
                </div>
                <div className="relative z-0 w-full mb-7 group">
                  <input
                    type="number"
                    name="goals"
                    id="edit_goals"
                    onChange={(e) => handleGoals(e)}
                    value={userExperience.goals}
                    className="border-b-2 border-gray-600 block pt-3 pb-1 px-0 w-full text-sm text-gray-900 bg-transparent appearance-none  focus:outline-none focus:ring-0 focus:border-gray-600 peer"
                    placeholder=" "
                  />
                  <label
                    htmlFor="edit_goals"
                    className="peer-focus:font-medium absolute text-sm text-gray-500 dark:text-gray-400 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-gray-600 peer-focus:dark:text-gray-100 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6"
                  >
                    Goals
                  </label>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-6">
                <div className="relative z-0 w-full mb-7 group">
                  <input
                    type="number"
                    name="assists"
                    id="edit_assist"
                    onChange={(e) => handleAssist(e)}
                    value={userExperience.assists}
                    className="border-b-2 border-gray-600 block pt-3 pb-1 px-0 w-full text-sm text-gray-900 bg-transparent appearance-none  focus:outline-none focus:ring-0 focus:border-gray-600 peer"
                    placeholder=" "
                  />
                  <label
                    htmlFor="edit_assist"
                    className="peer-focus:font-medium absolute text-sm text-gray-500 dark:text-gray-400 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-gray-600 peer-focus:dark:text-gray-100 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6"
                  >
                    Assists
                  </label>
                </div>
                <div className="relative z-0 w-full mb-7 group">
                  <input
                    type="number"
                    name="cleanSheets"
                    id="edit_cleanSheet"
                    onChange={(e) => handleCleansheet(e)}
                    value={userExperience.cleanSheets}
                    className="border-b-2 border-gray-600 block pt-3 pb-1 px-0 w-full text-sm text-gray-900 bg-transparent appearance-none  focus:outline-none focus:ring-0 focus:border-gray-600 peer"
                    placeholder=" "
                  />
                  <label
                    htmlFor="edit_cleanSheet"
                    className="peer-focus:font-medium absolute text-sm text-gray-500 dark:text-gray-400 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-gray-600 peer-focus:dark:text-gray-100 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6"
                  >
                    Clean Sheet
                  </label>
                </div>
                <div className="relative z-0 w-full mb-7 group">
                  <input
                    type="number"
                    name="minsPlayed"
                    id="minsPlayed"
                    onChange={(e) => handleminsPlayed(e)}
                    value={userExperience.minsPlayed}
                    className="border-b-2 border-gray-600 block pt-3 pb-1 px-0 w-full text-sm text-gray-900 bg-transparent appearance-none  focus:outline-none focus:ring-0 focus:border-gray-600 peer"
                    placeholder=" "
                  />
                  <label
                    htmlFor="minsPlayed"
                    className="peer-focus:font-medium absolute text-sm text-gray-500 dark:text-gray-400 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-gray-600 peer-focus:dark:text-gray-100 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6"
                  >
                    Minutes Played
                  </label>
                </div>
              </div>
            </>
          )}
          <div className="flex items-center justify-center w-full">
            <button
              htmlFor="my-modal-4"
              onClick={handleSubmit}
              className="text-white px-10 py-3 rounded-full text-[22px] bg-black"
              disabled={isDisabled || seasonNameError}
            >
              Save
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
export default ExperienceModal;
