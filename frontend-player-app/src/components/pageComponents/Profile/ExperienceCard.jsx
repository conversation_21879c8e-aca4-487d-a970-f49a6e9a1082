import React, { useEffect, useRef, useState } from "react";
import ballAvatar from "../../../assets/ball.png";
import { notifyError, validateURL } from "../../../utils/helpers";
import EditExperience from "../Stats/EditExperience";
import { Experience } from "../../../services";
import { dispatch } from "../../../redux/store";
import { useSelector } from "react-redux";
import Loader from "../../reusable/loading/Loader";
import { useNavigate } from "react-router-dom";
import DownArrowIcon from "../../svgIcons/DownIcon";
import DeleteIcon from "../../svgIcons/DeleteIcon";

const ExperienceCard = ({
  data = {},
  shoudEdit,
  bgColorAndText,
  textColor,
  userType
}) => {
  const [isLoading, setLoading] = useState(false);
  const [editModal, setEditModal] = useState({ visible: false, data: null });
  const editDropDown = useRef(false);
  const navigate = useNavigate();
  const docRef = useRef();

  const { userId } = useSelector(({ auth }) => ({
    userId: auth.authUser?.userInfo.id,
    userInfo: auth.authUser?.userInfo
  }));

  const handleCloseModal = (e) => {
    // console.log(e.target);

    docRef.current.click();
  };

  async function handleDelete(id) {
    try {
      setLoading(true);
      await Experience.deleteExperience(id);
      await dispatch.user.getUserProfile({ id: userId });
    } catch (error) {
      setLoading(false);
      notifyError(error.message);
    }

    setLoading(false);
  }

  if (!Object.keys(data).length) {
    return (
      <div
        className={`w-full h-32 bg-gray-800 flex items-center justify-center ${bgColorAndText}`}
      >
        Add your club history & stats
      </div>
    );
  }

  if (isLoading) {
    return <Loader />;
  }

  return (
    <div ref={docRef}>
      {editModal.visible ? (
        <EditExperience
          handleCloseModal={setEditModal}
          shouldShowModal={editModal.visible}
          data={editModal.data}
        />
      ) : (
        ""
      )}
      {Object.entries(data)
        .map((club, key) => {
          return (
            <div className="my-[10px]" key={key + club[0] + club[1][0]?.id}>
              <div
                className="flex gap-3"
                onClick={() => {
                  if (club[1][0]?.clubId && club[1][0]?.teamId) {
                    navigate(
                      `/team-dashboard?clubId=${club[1][0]?.clubId}&teamId=${club[1][0]?.teamId}`
                    );
                  }
                }}
              >
                <img
                  src={
                    validateURL(club[1][0]?.teamLogo)
                      ? club[1][0]?.teamLogo
                      : ballAvatar
                  }
                  alt=""
                  className="w-[28px] h-[28px] rounded-full object-contain"
                />
                <p
                  className={`uppercase text-[16px] font-favela-bold font-bold ${textColor}`}
                >
                  {club[0] || "No name provided"}
                </p>
              </div>
              {shoudEdit && (
                <div className="flex gap-4 justify-end justify-items-end">
                  {/* Start Drop down for delete items */}
                  <div className="dropdown dropdown-left">
                    <label tabIndex={0} className="">
                      <DeleteIcon
                        fill2={textColor.split("-")[1]}
                        className="md:hidden"
                      />
                      <img
                        className="hidden md:block"
                        src="/images/desktopDeleteIcon.svg"
                        alt=""
                      />
                    </label>
                    <div
                      tabIndex={0}
                      className="grid grid-cols-3 p-5 gap-y-4 gap-x-10 dropdown-content menu shadow bg-base-100 rounded-box min-w-[21rem] min-h-[70px]"
                    >
                      {club[1]?.map((item, idx) => {
                        return (
                          <div key={idx} onClick={() => handleDelete(item.id)}>
                            <p className={"text-black"}>
                              {item?.seasonName}{" "}
                              <span className="text-red-600 pl-2">x</span>
                            </p>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                  {/*End of Drop down for delete items */}

                  {/* Start Drop down for edit items */}
                  {userType !== "NON_PLAYER" && (
                    <div className="dropdown dropdown-left">
                      <label tabIndex={0} className="">
                        <img
                          className="md:hidden"
                          src="/images/editIcon.svg"
                          alt=""
                        />
                        <img
                          className="hidden md:block"
                          src="/images/editDesktopIcon.svg"
                          alt=""
                        />
                      </label>
                      <div
                        hidden
                        ref={editDropDown}
                        tabIndex={0}
                        className="grid grid-cols-3 p-5 gap-y-4 gap-x-10 dropdown-content menu shadow bg-base-100 rounded-box min-w-[21rem] min-h-[70px] text-black"
                      >
                        {club[1]?.map((item, idx) => {
                          return (
                            <div key={idx + item.id}>
                              <button
                                className="bg-transparent flex"
                                onClick={() =>
                                  setEditModal({ visible: true, data: item })
                                }
                              >
                                <span className="pr-2 text-black">
                                  {item?.seasonName}
                                </span>{" "}
                                <svg
                                  width="10"
                                  height="10"
                                  viewBox="0 0 10 10"
                                  fill="none"
                                  xmlns="http://www.w3.org/2000/svg"
                                  className="inline"
                                >
                                  <path
                                    d="M9.41659 0.485215C9.07612 0.158965 8.70054 -0.00634766 8.2996 -0.00634766C7.67273 -0.00634766 7.21539 0.398652 7.09022 0.522715C6.91413 0.69709 0.907095 6.70631 0.907095 6.70631C0.867726 6.74587 0.839303 6.79497 0.824604 6.84881C0.689136 7.34942 0.0105419 9.57068 0.00380357 9.59303C-0.0310304 9.70678 5.35715e-05 9.83069 0.0839598 9.91459C0.143148 9.97362 0.223327 10.0068 0.306919 10.0068C0.339263 10.0068 0.371909 10.0019 0.403784 9.99146C0.426753 9.98396 2.71957 9.24287 3.0969 9.13023C3.14669 9.11522 3.19206 9.08829 3.22909 9.05178C3.46752 8.81615 9.07018 3.27584 9.44206 2.89178C9.82674 2.49537 10.0178 2.08256 10.0105 1.66506C10.0028 1.25287 9.80299 0.855996 9.41659 0.485215ZM6.9907 1.52209C7.14992 1.56068 7.5254 1.68676 7.90883 2.07364C8.29633 2.46458 8.40118 2.91146 8.42071 3.01317C7.19196 4.23552 4.36352 7.03395 3.24852 8.13692C3.14555 7.89708 2.97915 7.60817 2.71148 7.33849C2.38461 7.00911 2.05195 6.82303 1.79539 6.71834C2.89882 5.61475 5.78054 2.73178 6.9907 1.52209ZM1.37102 7.24582C1.54274 7.29145 1.89945 7.42379 2.26305 7.79035C2.54305 8.0727 2.67524 8.3841 2.73461 8.57395C2.30024 8.71363 1.34915 9.04191 0.74477 9.23676C0.923833 8.64754 1.22632 7.72988 1.37102 7.24582ZM8.98851 2.4452C8.96882 2.4653 8.94907 2.48535 8.92928 2.50535C8.83052 2.25097 8.65724 1.9241 8.35724 1.6216C8.05069 1.31254 7.73975 1.1302 7.49054 1.02238C7.51178 1.00129 7.52819 0.984717 7.53459 0.978477C7.57038 0.943154 7.89492 0.633008 8.29944 0.633008C8.53225 0.633008 8.76084 0.739258 8.97929 0.948633C9.23819 1.19691 9.37163 1.44191 9.37601 1.67707C9.38022 1.91707 9.24991 2.17551 8.98851 2.4452Z"
                                    fill="#000"
                                  />
                                </svg>
                              </button>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  )}
                  {/* End Drop down for edit items */}
                </div>
              )}
              <div className="flex gap-5 my-5 min-[420px]:flex-row">
                <div>
                  <p className={`${textColor} text-[18px] font-medium`}>
                    {userType !== "NON_PLAYER" ? "Stats" : "Seasons"}
                  </p>
                </div>
                <div className="grid grid-cols-3 gap-3 flex-1">
                  {club[1]?.map((season, i) => (
                    <div
                      key={i}
                      className={`${
                        // If the user type is not player, we want to disable dropdown
                        userType !== "NON_PLAYER"
                          ? "dropdown dropdown-hover"
                          : ""
                      } ${
                        i % 3 === 0
                          ? "dropdown-bottom"
                          : "dropdown-end dropdown-bottom"
                      }`}
                    >
                      <button id="closeBtn hidden" hidden></button>
                      <label tabIndex={i} className="flex items-center">
                        <p
                          className={`text-[14px] font-normal justify-self-center ${textColor}`}
                        >
                          {season?.seasonName || season?.name}
                        </p>
                        {userType !== "NON_PLAYER" && (
                          <span className="ml-2">
                            <DownArrowIcon color={textColor.split("-")[1]} />
                          </span>
                        )}
                      </label>
                      {userType !== "NON_PLAYER" && (
                        <div className="dropdown-content card card-compact w-64 shadow bg-white text-primary-content">
                          <div className="flex justify-end ">
                            <label
                              htmlFor="closeBtn"
                              className="bg-gray-200 px-2 rounded-full m-2 text-black select-none cursor-pointer"
                            >
                              X
                            </label>
                          </div>
                          <div className="p-4 text-black ">
                            <p className="text-[22px] font-normal text-center">
                              Season {season?.seasonName || season?.name}
                            </p>
                            <div className="mt-[10px] w-full">
                              <div className="flex justify-between items-center w-full my-2">
                                <p className="text-[14px] font-normal">
                                  Appearances
                                </p>
                                <p>{season?.appearances || 0}</p>
                              </div>
                              <div className="flex justify-between items-center my-2">
                                <p className="text-[14px] font-normal">Goals</p>
                                <p>{season?.goals || 0}</p>
                              </div>
                              <div className="flex justify-between items-center my-2">
                                <p className="text-[14px] font-normal">
                                  Assists
                                </p>
                                <p>{season?.assists || 0}</p>
                              </div>
                              <div className="flex justify-between items-center my-2">
                                <p className="text-[14px] font-normal">
                                  Clean Sheets
                                </p>
                                <p>{season?.cleanSheets || "N/A"}</p>
                              </div>
                              <div className="flex justify-between items-center my-2">
                                <p className="text-[14px] font-normal">
                                  Minutes Played
                                </p>
                                <p>{season?.minsPlayed || 0}</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
              <div className="w-[60%] md:w-full border-2 mx-auto mt-[10px] border-[#FFFFFF33]" />
            </div>
          );
        })
        .reverse()}
    </div>
  );
};

export default ExperienceCard;
