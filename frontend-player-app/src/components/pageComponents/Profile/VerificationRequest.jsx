import React, { useRef } from "react";
import ShareProfile from "./ShareProfile";

const VerificationRequest = ({ userDetails, userInfo, enableVerifyBtn }) => {
  const [shouldVerify, setShouldVerify] = React.useState(false);
  const closeModalRef = useRef();

  const verificationHeader = "Share your profile for verification";

  const verificationTitle = `You are invited to verify ${
    userDetails?.id === userInfo?.id ? "my" : `${userDetails?.firstName}'s`
  } PLAYER profile: \n \n`;

  const verificationUrl = `${`${window.document.location.host}/signup?id=${userDetails?.id}`}`;

  if (!enableVerifyBtn) {
    return ""
  }

  return (
    <div className="w-full">
      {/* Open the modal using document.getElementById('ID').showModal() method */}
      <button
        className="bg-[#52FF00] text-black rounded-3xl py-1 text-sm"
        onClick={() => {
          setShouldVerify(true);
          document.getElementById("verification_modal").showModal();
        }}
      >
        Get verified
      </button>
      <dialog id="verification_modal" className="modal">
        <div className="modal-box rounded-3xl">
          <p className="font-poppins text-[22px] text-base text-black">
            Invite a former coach or colleague onto PLAYER to verify your
            profile.
          </p>
          <p className="font-poppins text-[18px] text-base text-black py-4 text-opacity-50">
            Coaches & colleagues can also write a reference on your profile
          </p>

          <div className="flex flex-col justify-center items-center">
             {shouldVerify && <ShareProfile
                title={verificationTitle}
                header={verificationHeader}
                url={verificationUrl}
                id="verification_modal_"
                Label={() => (
                  <label
                    htmlFor="verification_modal_"
                    className="bg-[#000] text-white rounded-3xl p-2 px-4"
                  >
                    Get Verified
                  </label>
                )}
              />}

            <form method="dialog">
              <button
                ref={closeModalRef}
                className="btn text-[18px] btn-ghost text-black rounded-3xl py-1"
              >
                Cancel
              </button>
            </form>
          </div>
        </div>
      </dialog>
    </div>
  );
};

export default VerificationRequest;
