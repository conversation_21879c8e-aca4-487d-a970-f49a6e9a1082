import { memo } from "react";
import { useNavigate } from "react-router-dom";
import ShareModal from "../../reusable/ShareModal";
import ShareIcon from "../../svgIcons/ShareIcon";

const ShareProfile = ({ Label, header, title, url, id, color, profile }) => {
  const navigate = useNavigate();

  return (
    <div className="flex items-center justify-end col-start-3">
      <ShareModal id={id} header={header} title={title} url={url} />
      {Label ? (
        <Label />
      ) : (
        <label
          // htmlFor={id}
          onClick={() =>
            navigate("/feed/share", {
              state: {
                type: "profile",
                profile,
                url,
                title,
              },
            })
          }
          className="mr-4 rounded-full cursor-pointer"
        >
          {/* <img
            src="/images/shareIcon.svg"
            alt="profile picture"
            className="object-cover w-full h-full"
          /> */}
          <div className="object-cover w-full h-full">
            <ShareIcon fill={color} />
          </div>
        </label>
      )}
    </div>
  );
};

export default memo(ShareProfile);
