import React, { useCallback, useEffect, useState } from "react";
import PlusSign from "../../svgIcons/PlusSign";
import { UserApi } from "../../../services";
import { useSelector } from "react-redux";
import {
  notifyError,
  notifySuc<PERSON>,
  removeEmpty
} from "../../../utils/helpers";
import _debounce from "lodash/debounce";
import isEqual from "lodash.isequal";

const PhysicalData = ({ userInfo, textColor }) => {
  const [existintPhysicalData, setExistintPhysicalData] = useState(null);
  const loggedUserInfo = useSelector(({ auth }) => auth.authUser.userInfo);
  const isSameUser = userInfo.id === loggedUserInfo.id;
  const [data, setData] = useState(null);

  async function getPlayerPhysicalData() {
    try {
      const {
        data: { data: physicalData }
      } = await UserApi.getUserPhysicalData(userInfo.id);

      if (physicalData) {
        const { updatedAt, createdAt, id, ...responseData } = physicalData;
        setExistintPhysicalData(responseData);
      }
      setData({
        userId: loggedUserInfo.id,
        sprintTime5M: physicalData?.sprintTime5M || "",
        sprintTime10M: physicalData?.sprintTime10M || "",
        sprintTime20M: physicalData?.sprintTime20M || "",
        maxSpeed: physicalData?.maxSpeed || "",
        runTime1Kmins: physicalData?.runTime1Kmins || "",
        runTime1Ksecs: physicalData?.runTime1Ksecs || "",
        matchAvgDistance: physicalData?.matchAvgDistance || "",
        counterMovementJump: physicalData?.counterMovementJump || "",
      });
    } catch (error) {
      notifyError("An error occured");
    }
  }

  const handleChange = (e) => {
    const { name, value } = e.target;

    const isWholeNumberField = ["runTime1Kmins", "runTime1Ksecs"].includes(name);

    const regex = isWholeNumberField ? /^\d*$/ : /^(\d+)?(\.\d{0,2})?$/;

    const isValid = value === "" || regex.test(value);

    if (isValid) {
      setData({ ...data, [name]: value });
    }

  };

  const handleSubmit = async (type, inputData) => {
    const requestFn =
      type === "new"
        ? UserApi.postUserPhysicalData
        : UserApi.updateUserPhysicalData;
    try {
      await requestFn(inputData);
      if (type === "new") {
        console.log("created data for player");
      }
    } catch (error) {
      console.log(error);
      notifyError("An error occured");
    }
  };

  const debounceSubmit = useCallback(_debounce(handleSubmit, 800), []);

  useEffect(() => {
    getPlayerPhysicalData();
  }, []);

  useEffect(() => {
    if (data && isSameUser) {
      const { userId = "", ...others } = data;
      if (
        Object.values(removeEmpty(others)).length > 0 &&
        !isEqual(data, existintPhysicalData)
      ) {
        debounceSubmit(!Boolean(existintPhysicalData) ? "new" : "update", data);
      }
    }
  }, [data]);

  const inputClass = "w-[32px] text-center border-b bg-transparent outline-none focus:outline-none [appearance:textfield] [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none"

  return (
    <div className={`${textColor}`}>
      <label htmlFor="physical-data-modal">
        <PlusSign />
      </label>

      <input
        type="checkbox"
        id="physical-data-modal"
        className="modal-toggle"
      />
      <div className="modal" role="dialog">
        <div className="modal-box">
          <label
            htmlFor="physical-data-modal"
            className="btn btn-sm btn-circle absolute right-2 top-2"
          >
            ✕
          </label>
          <p
            className={`uppercase text-[16px] font-favela-bold font-bold text-center ${textColor}`}
          >
            PHYSICAL DATA
          </p>
          <div className="flex justify-center w-full">
            <table className="w-full text-center px-10 mt-[50px]">
              {/* head */}
              <tbody>
                {/* row 1 */}
                <tr className="border-b">
                  <td className="border-r py-5 px-3 w-1/2">5m Sprint Time</td>
                  <td className="py-5 px-3 w-1/2">
                    {isSameUser ? (
                      <div className="flex items-center justify-center gap-1">
                        <input
                          type="text"
                          name={"sprintTime5M"}
                          onChange={handleChange}
                          value={data?.sprintTime5M || ""}
                          className={inputClass}
                          min="0"
                        />
                        <span>s</span></div>
                    ) : (
                      <span>{existintPhysicalData?.sprintTime5M ? `${existintPhysicalData?.sprintTime5M}s` : ""}</span>
                    )}
                  </td>
                </tr>

                <tr className="border-b">
                  <td className="border-r py-5 px-3 w-1/2">10m Sprint Time</td>
                  <td className="py-5 px-3 w-1/2">
                    {isSameUser ? (
                      <div className="flex items-center justify-center gap-1">
                        <input
                          type="number"
                          name={"sprintTime10M"}
                          onChange={handleChange}
                          value={data?.sprintTime10M}
                          className={inputClass}
                          step="0.1"
                          min="0"
                        />
                        <span>s</span></div>
                    ) : (
                      <span>{existintPhysicalData?.sprintTime10M ? `${existintPhysicalData?.sprintTime10M}s` : ""}</span>
                    )}
                  </td>
                </tr>
                <tr className="border-b">
                  <td className="border-r py-5 px-3 w-1/2">20m Sprint Time</td>
                  <td className="py-5 px-3 w-1/2">
                    {isSameUser ? (
                      <div className="flex items-center justify-center gap-1">
                        <input
                          type="number"
                          name={"sprintTime20M"}
                          onChange={handleChange}
                          value={data?.sprintTime20M}
                          className={inputClass}
                          step="0.1"
                          min="0"
                        />
                        <span>s</span></div>
                    ) : (
                      <span>{existintPhysicalData?.sprintTime20M ? `${existintPhysicalData?.sprintTime20M}s` : ""}</span>
                    )}
                  </td>
                </tr>
                <tr className="border-b">
                  <td className="border-r py-5 px-3 w-1/2">Max. Speed</td>
                  <td className="py-5 px-3 w-1/2">
                    {isSameUser ? (
                      <div className="flex items-center justify-center gap-1">
                        <input
                          type="number"
                          name={"maxSpeed"}
                          onChange={handleChange}
                          value={data?.maxSpeed}
                          className={inputClass}
                          step="0.1"
                          min="0"
                        />
                        <span>m/s</span></div>
                    ) : (
                      <span>{existintPhysicalData?.maxSpeed ? `${existintPhysicalData?.maxSpeed}m/s` : ""}</span>
                    )}
                  </td>
                </tr>
                <tr className="border-b">
                  <td className="border-r py-5 px-3 w-1/2">1k Run Time</td>
                  <td className="py-5 px-3 w-1/2">
                    {isSameUser ? (
                      <div className="flex flex-row gap-1 justify-center">
                        <div className="flex items-center justify-center gap-1">
                          <input
                            type="number"
                            name={"runTime1Kmins"}
                            onChange={handleChange}
                            value={data?.runTime1Kmins}
                            className={inputClass}
                            step="1"
                            min="0"
                          />
                          <span>mins</span></div>
                        <div className="flex items-center justify-center gap-1">
                          <input
                            type="number"
                            name={"runTime1Ksecs"}
                            onChange={handleChange}
                            value={data?.runTime1Ksecs}
                            className={inputClass}
                            step="1"
                            min="0"
                          />
                          <span>s</span></div>
                      </div>
                    ) : (
                      <span>{existintPhysicalData?.runTime1Kmins ? `${existintPhysicalData?.runTime1Kmins}m` : ""}{existintPhysicalData?.runTime1Ksecs ? `/${existintPhysicalData?.runTime1Ksecs}s` : ""}</span>
                    )}
                  </td>
                </tr>
                <tr className="border-b">
                  <td className="border-r py-5 px-3 w-1/2">
                    Match Avg Distance
                  </td>
                  <td className="py-5 px-3 w-1/2">
                    {isSameUser ? (
                      <div className="flex items-center justify-center gap-1">
                        <input
                          type="number"
                          name={"matchAvgDistance"}
                          onChange={handleChange}
                          value={data?.matchAvgDistance}
                          className={inputClass}
                          step="0.1"
                          min="0"
                        />
                        <span>m</span></div>
                    ) : (
                      <span>{existintPhysicalData?.matchAvgDistance ? `${existintPhysicalData?.matchAvgDistance}m` : ""}</span>
                    )}
                  </td>
                </tr>
                <tr>
                  <td className="border-r py-5 px-3 w-1/2">
                    Counter Movement Jump
                  </td>
                  <td className="py-5 px-3 w-1/2">
                    {isSameUser ? (
                      <div className="flex items-center justify-center gap-1">
                        <input
                          type="number"
                          name={"counterMovementJump"}
                          onChange={handleChange}
                          value={data?.counterMovementJump}
                          className={inputClass}
                          step="0.1"
                          min="0"
                        />
                        <span>cm</span></div>
                    ) : (
                      <span>{existintPhysicalData?.counterMovementJump ? `${existintPhysicalData?.counterMovementJump}cm` : ""}</span>
                    )}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default React.memo(PhysicalData);
