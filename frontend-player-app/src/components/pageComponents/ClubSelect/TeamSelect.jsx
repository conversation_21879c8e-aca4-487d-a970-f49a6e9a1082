import React, { useEffect } from "react";
import PageTitle from "../../reusable/PageTitle";

const TeamSelect = ({
  visible,
  teams,
  clubDetails,
  setSelectedTeam,
  selectedTeam
}) => {
  const handleTeamSelect = (team) => {
    setSelectedTeam(team);
  };

  useEffect(() => {
    if (teams?.length === 1) {
      setSelectedTeam(teams[0]);
      setBeforeSelect(false);
      setAfterSelect(true);
    }
  }, [teams]);

  return (
    visible && (
      <div>
        <div className="w-[90%]">
          <PageTitle>
            <div>{clubDetails?.clubName}</div>
            <img className="w-16 h-16" src={clubDetails.clubLogoUrl} alt="" />
          </PageTitle>
        </div>
        <div className="mt-5 font-favela-bold">Choose Your Team:</div>
        <ul className="mt-5 overflow-y-auto h-[35vh] bg-white w-full flex flex-col gap-5 rounded-xl">
          {teams
            ?.sort((a, b) => b.index - a.index)
            .map((team) => (
              <li
                onClick={() => handleTeamSelect(team)}
                key={team.id}
                className={`pb-1 border-b-[2px] border-gray-200 flex justify-between items-center`}
              >
                <p>{team.teamName}</p>{" "}
                <p>{selectedTeam?.id === team?.id && "✅"}</p>
              </li>
            ))}
        </ul>
      </div>
    )
  );
};

export default React.memo(TeamSelect);
