import { useState, useEffect, useRef } from "react";

const useVideoThumbnail = (videoUrl, seekTime = 2) => {
  const [thumbnail, setThumbnail] = useState(null);
  const videoRef = useRef(document.createElement("video"));
  const canvasRef = useRef(document.createElement("canvas"));

  useEffect(() => {
    if (!videoUrl) return;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext("2d");

    video.src = videoUrl;
    video.crossOrigin = "anonymous"; // Allow cross-origin access

    video.onloadedmetadata = () => {
      video.currentTime = seekTime; // Seek to frame
    };

    video.onseeked = () => {
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

      try {
        const imageUrl = canvas.toDataURL("image/png");
        console.log(imageUrl, "imageUrl");
        
        setThumbnail(imageUrl);
      } catch (error) {
        console.error("SecurityError: CORS issue detected", error);
      }
    };

    return () => {
      video.src = ""; // Cleanup
    };
  }, [videoUrl, seekTime]);

  return thumbnail;
};

export default useVideoThumbnail;
