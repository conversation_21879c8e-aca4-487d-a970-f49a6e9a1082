import { useEffect } from "react";

const useDisableScroll = (isDisabled) => {
    useEffect(() => {
        if (isDisabled) {
            document.body.style.overflow = "hidden";
        } else {
            document.body.style.overflow = "";
        }

        return () => {
            document.body.style.overflow = ""; // Cleanup when unmounting
        };
    }, [isDisabled]);
};

export default useDisableScroll;
