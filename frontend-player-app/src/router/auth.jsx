import { Navigate, useLocation, useSearchParams } from 'react-router-dom';
import { getSingleModel } from '../utils/helpers';
import { dispatch } from '../redux/store';

const AuthRoute = ({ element, ...rest }) => {
  const location = useLocation();
  const quey = useSearchParams()[0]

  const isAuthenticated = () => {
    return getSingleModel('auth')?.authUser?.authenticated
  };

  if (quey.get('id')) {
    dispatch.auth.logout()
  }

  return !isAuthenticated()
    ? element
    : <Navigate to="/" replace />;
};

export default AuthRoute;