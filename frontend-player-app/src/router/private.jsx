import { useEffect, useState } from "react";
import { Navigate, useLocation } from "react-router-dom";
import Spinner from "../components/reusable/spinner/Spinner";
import { getSingleModel } from "../utils/helpers";
import { initializeLiveLike } from "../utils/LiveLike";

const PrivateRoute = ({ element, ...rest }) => {
  const location = useLocation();
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = () => {
    return getSingleModel("auth")?.authUser?.authenticated;
  };

  useEffect(() => {
    if (isAuthenticated()) {
      initializeLiveLike();
      setIsLoading(false);
    }
  }, []);

  return isAuthenticated() ? (
    isLoading ? (
      <Spinner />
    ) : (
      element
    )
  ) : (
    <Navigate to="/login" state={{ from: location }} />
  );
};

export default PrivateRoute;
