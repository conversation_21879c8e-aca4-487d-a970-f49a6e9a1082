/* eslint-disable import/no-cycle */
/* eslint-disable no-nested-ternary */
/* eslint-disable import/no-anonymous-default-export */
import axios from "axios";
import moment from "moment";
import { BASE_URL } from "../utils/constants";
import { getSingleModel, notifyError } from "../utils/helpers";
import { dispatch } from "../redux/store";

const now = moment().unix();

async function handleRequest(req) {
  req.headers["Content-Type"] = "application/json";
  req.headers.Accept = "application/json";

  const authModel = getSingleModel("auth");
  // const expTokenTime = authModel?.authUser?.expiresIn;

  if (authModel?.authUser?.token) {
    req.headers.Authorization = `Bearer ${authModel.authUser?.token}`;
    return req;
  }
  return req;
}

async function handleResponseError(error) {
  const status = error?.response?.status;
  const message = error?.response?.data?.message;

  if (status === 401 && message === "jwt expired") {
    const newAccessToken = await dispatch.auth.refreshToken();
    if (newAccessToken) {
      const config = error.config;
      config.headers["Authorization"] = `Bearer ${newAccessToken}`;
      return axios(config); // Retry the original request with the new token
    }
  }

  return Promise.reject(error);
}

/**
 * Axios request interceptor to handle authorization headers
 */
axios.interceptors.request.use(
  async (req) => {
    const splitedUrl = req.url.split("/");
    const currentEndpoint = splitedUrl[splitedUrl.length - 1];
    if (currentEndpoint === "refresh-token") {
      const authModel = getSingleModel("auth");
      const refreshToken = authModel?.authUser?.refreshToken;
      req.headers.Authorization = `Bearer ${refreshToken}`;
      return req;
    }
    return handleRequest(req);
  },
  (error) => Promise.reject(error)
);

/**
 * Axios response interceptor to handle token expiration
 */
axios.interceptors.response.use(
  (response) => response,
  (error) => handleResponseError(error)
);

/**
 * The ApiHandler framework with observable
 */
export default {
  post: async (url, data, options) =>
    axios.post(
      options?.fullPath ? url : BASE_URL + url,
      data,
      options && { headers: options }
    ),
  patch: async (url, data, options) =>
    axios.patch(
      options?.fullPath ? url : BASE_URL + url,
      data,
      options && { headers: options }
    ),
  put: async (url, data, options) =>
    axios.put(
      options?.fullPath ? url : BASE_URL + url,
      data,
      options && { headers: options }
    ),
  delete: async (url, data, options) => {
    let modData = data;
    modData = data
      ? data instanceof Object && !Object.keys(data).length
        ? null
        : data
      : null;
    const config = modData ? { headers: options, data } : { headers: options };
    return axios.delete(options?.fullPath ? url : BASE_URL + url, config);
  },
  get: async (url, params, options) => {
    let modParams = params;
    modParams = params
      ? params instanceof Object && !Object.keys(params).length
        ? null
        : params
      : null;
    const config = modParams
      ? { headers: options, params }
      : { headers: options };
    return axios.get(options?.fullPath ? url : BASE_URL + url, config);
  }
};
