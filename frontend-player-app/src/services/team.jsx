/* eslint-disable import/no-anonymous-default-export */
/* eslint-disable import/no-cycle */
import <PERSON>piHand<PERSON> from "./ApiHandler";

export default {
  getTeam: (id) => ApiHandler.post(`/auth/login?id=${id}`, data),
  getAllTeams: (isPrivate) => ApiHandler.get(`/teams/all`),
  teamsByClubId: (id, isPrivate) => ApiHandler.get(`/teams?clubId=${id}`),
  getAllTeamPlayers: (id) =>
    ApiHandler.get(`/teams/players?teamId=${id}&groupBy=position`),
  getSingleTeam: (teamId, clubId) =>
    ApiHandler.get(`/teams/${teamId}?clubId=${clubId}`),
  getTeamsByProjection: () => ApiHandler.get("/teams/get-by-projection"),
  teamSearch: (data) => ApiHandler.get(`/teams/search?query=${data}`),
};
