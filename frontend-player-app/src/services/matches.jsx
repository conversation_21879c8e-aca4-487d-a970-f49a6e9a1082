/* eslint-disable import/no-anonymous-default-export */
/* eslint-disable import/no-cycle */
import <PERSON><PERSON><PERSON>and<PERSON> from "./ApiHandler";

export default {
  // SEASON ID IS OPTIONAL
  getMatchesbyTeamAndSeason: ({ seasonId, teamId }) =>
    ApiHandler.get(
      `/matches/by-season-team?teamId=${teamId}${
        seasonId ? `&seasonId=${seasonId}` : ""
      }`
    ),
  publishResult: (data) =>
    ApiHandler.post(`/highlights/publish-result-to-highlights`, data),
};
