/* eslint-disable import/no-anonymous-default-export */
/* eslint-disable import/no-cycle */
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "./ApiHandler";

export default {
  login: (data) => ApiHandler.post("/auth/login", data),
  signup: (data) => ApiHandler.post("/auth/signup", data),
  checkForSignup: (data) => ApiHandler.post("/auth/allowed-to-signup", data),
  verifyAccount: (data) => ApiHandler.post("/auth/verify", data),
  resendVerifyAccount: (data) =>
    ApiHandler.post("/auth/resend-verification", data),
  forgotPassword: (data) => ApiHandler.post("/auth/forgot-password", data),
  resetPassword: (data) => ApiHandler.post("/auth/reset-password", data),
  changePassword: (data) => ApiHandler.post("/auth/change-password", data),
  deleteAccount: (data) => ApiHandler.post("/auth/delete-account", data),
  refreshToken: (refreshToken) => ApiHandler.post('/auth/refresh-token', { refreshToken })
};
