/* eslint-disable import/no-anonymous-default-export */
/* eslint-disable import/no-cycle */
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "./ApiHandler";

export default {
  getChats: () => ApiHandler.get("/p2p-chat/v2"),
  getChat: (chatroomId, limit, cursor) => ApiHandler.get(`/p2p-chat/v2/${chatroomId}?limit=${limit}${cursor ? `&cursor=${cursor}` : ''}`),
  getChatByRecipient: (recipientId) =>
    ApiHandler.get(`/p2p-chat/v2/recipient/${recipientId}`),
  createChat: (recipientId) => ApiHandler.post(`/p2p-chat/v2`, { recipientId }),
  createMessage: (chatroomId, data) =>
    ApiHandler.post(`/p2p-chat/v2/${chatroomId}/message`, data),
  share: (data) => ApiHandler.post(`/p2p-chat/v2/share`, data),
  getUnreadMessageCount: () => ApiHandler.get("/p2p-chat/v2/unread"),
};
