/* eslint-disable import/no-anonymous-default-export */
/* eslint-disable import/no-cycle */
import <PERSON>pi<PERSON>and<PERSON> from "./ApiHandler";

export default {
  highlights: (data) => ApiHandler.post("/highlights/get", data),
  createHighlight: (data) => ApiHandler.post("/highlights", data),
  getUsersHighlight: (id, data) =>
    ApiHandler.post(`/highlights/user-highlights/${id}`, data),
  reportHighlight: (data) =>
    ApiHandler.post(`/reports`, data),
  getTeamHighlights: (id) =>
    ApiHandler.get(`/highlights/team-highlights/${id}`),
  deleteHighlight: (payload) =>
    ApiHandler.delete(`/highlights/${payload.id}?userId=${payload.userId}`),
  fetchHighlight: (payload) =>
    ApiHandler.get(
      `/highlights/${payload.highlightId}?userId=${payload.userId}`
    ),
  editHighlight: (data) => ApiHandler.put("/highlights/update", data),
};
