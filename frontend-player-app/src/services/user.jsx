/* eslint-disable import/no-anonymous-default-export */
/* eslint-disable import/no-cycle */
import <PERSON>piHand<PERSON> from "./ApiHandler";

export default {
  update: (data) => ApiHandler.put("/users/update", data),
  getUserProfile: (id) => ApiHandler.get(`/users/get?id=${id}`),
  getGuestUserProfile: (id, projectedDataToFetch = '') =>
    ApiHandler.get(`/users/user-by-projection/${id}?projectionExpression=${projectedDataToFetch}`),
  getUserPhysicalData: (userId) =>
    ApiHandler.get(`/users/physical-data?userId=${userId}`),
  postUserPhysicalData: (data) => ApiHandler.post("/users/physical-data", data),
  updateUserPhysicalData: (data) =>
    ApiHandler.put("/users/physical-data", data),
  userSearch: (query) => ApiHandler.get(`/users/search?query=${query}`),
  userSearchByProjection: () =>
    ApiHandler.get(
      "/users/get-by-projection?projectionExpression=liveLikeProfileId,firstName,lastName,teamName,clubName,photoUrl,id"
    ),
  blockUser: (data) => ApiHandler.post("/users/block-user", data),
  unBlockUser: (data) => ApiHandler.post("/users/unblock-user", data),
  fetchBlockedUsers: () => ApiHandler.get("/users/list-blocked-users")
};
