import React from "react";
import ReactDOM from "react-dom/client";
import * as Sen<PERSON> from "@sentry/react";
import { RouterProvider } from "react-router-dom";
import "./index.css";
import "react-toastify/dist/ReactToastify.css";
import { router } from "./router";
import { Provider } from "react-redux";
import { ToastContainer } from "react-toastify";
import store from "./redux/store";
import ScreenSizeCheck from "./components/reusable/ScreenSizeCheck";

import TagManager from "react-gtm-module";
import ZoomControlWrapper from "./components/reusable/ZoomControlWrapper";
import PageViewTracker from "./components/PageTracker";
import Analytics from "./utils/google-analytics";
import GlobalClickTracker from "./components/GlobalClickTracker";

if (import.meta.env.VITE_APP_ENV === 'production') {
  const tagManagerArgs = {
    gtmId: "GTM-N437QRQ"
  };

  TagManager.initialize(tagManagerArgs);
}

if (window.location.hostname !== "localhost") {
  Analytics.initialize();

  // Initialize Sentry
  Sentry.init({
    dsn: "https://<EMAIL>/4508715293409360",
    integrations: [
      Sentry.browserTracingIntegration(),
      Sentry.replayIntegration(),
    ],
    // Tracing
    tracesSampleRate: 1.0, //  Capture 100% of the transactions
    // Set 'tracePropagationTargets' to control for which URLs distributed tracing should be enabled
    // tracePropagationTargets: [
    //   "localhost", 
    //   /^https:\/\/playerapp\.co(\/|$)/, 
    //   /^https:\/\/d2upejw2mgdjoj\.cloudfront\.net(\/|$)/
    // ],
    // tracePropagationTargets: ["localhost", /^https:\/\/playerapp\.co\/api/],
    // Session Replay
    replaysSessionSampleRate: 1.0, // This sets the sample rate at 10%. You may want to change it to 100% while in development and then sample at a lower rate in production.
    replaysOnErrorSampleRate: 1.0, // If you're not already sampling the entire session, change the sample rate to 100% when sampling sessions where errors occur.
  });
}


ReactDOM.createRoot(document.getElementById("root")).render(
  <div>
    <Provider store={store}>
      <ToastContainer
        position="top-right"
        hideProgressBar
        newestOnTop={false}
        closeOnClick
        rtl={false}
        draggable
      />
      <ScreenSizeCheck>
        <ZoomControlWrapper>
          <GlobalClickTracker />
          <PageViewTracker router={router} />
          <RouterProvider router={router} />
        </ZoomControlWrapper>
      </ScreenSizeCheck>
    </Provider>
  </div>
);
