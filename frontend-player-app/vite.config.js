import { sentryVitePlugin } from "@sentry/vite-plugin";
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import crossOriginIsolation from "vite-plugin-cross-origin-isolation";
// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react(), {
    name: 'configure-server',
  
    configureServer(server) {
      server.middlewares.use((_req, res, next) => {
        res.setHeader("Cross-Origin-Opener-Policy", "same-origin");
        res.setHeader("Cross-Origin-Embedder-Policy", "credentialless");
        next();
      });
    }
  
  }, sentryVitePlugin({
    org: "playerapp",
    project: "player-frontend"
  })],

  define: {
    "process.env": { ...process.env }
  },

  build: {
    sourcemap: true
  }
});