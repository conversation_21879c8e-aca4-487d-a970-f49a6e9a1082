# Mobile app

## Analytics

### Debug mode

Turning on [debug mode](https://firebase.google.com/docs/analytics/debugview) allows you to see sandboxed real-time events from your device in the Firebase Analytics dashboard (rather than the default batched events). Events can be viewed in the
DebugView section of the Firebase Analytics web dashboard.

#### Android

Enable by running `adb shell setprop debug.firebase.analytics.app com.playerapp.playerapp` after the app is launched.

Disable by running `adb shell setprop debug.firebase.analytics.app .none.`

#### iOS

Enable by running with command line argument `-FIRDebugEnabled`.

Disable by running with command line argument `-FIRDebugDisabled`.

Please note: The staging Xcode scheme automatically runs with debug mode enabled.

## Creating & publishing builds

### iOS

#### Prerequisites

##### Tools

- Xcode
- Ruby (`brew install ruby`)
- CocoaPods (`brew install cocoapods`)

##### Certificates

Download and install `PLAYER iOS Distribution.p12`, which contains the required AppStoreConnect permissions. You can get this file from the team manager.

#### To create a build

##### Via CLI

```
yarn build-ios-staging
yarn build-ios-production
```

###### Via Xcode UI

1. Install Pods

```
yarn ios-install-pods
```

2.  Set team to "Playerapp Limited" in Xcode (Targets > PLAYER > Signing & Capabilities > Signing)

3.  Set version and build number in Xcode (Targets > PLAYER > General > Identity)

4.  Select the scheme for the required environment and start a build with Xcode > Product > Build for... > Running.

#### To create & publish an archive

##### Via CLI

```
yarn distribute-ios-staging
yarn distribute-ios-production
```

###### Via Xcode UI

1. Install Pods

```
yarn ios-install-pods
```

2.  Set team to "Playerapp Limited" in Xcode (Targets > PLAYER > Signing & Capabilities > Signing)

3.  Set version and build number in Xcode (Targets > PLAYER > General > Identity)

4.  Select the scheme for the required environment and start a release build with Xcode > Product > Archive.

5.  When the build completes, Xcode will open the Archives window. Highlight your new build and click "Distribute App".

6.  Choose the "Custom" workflow and leave all settings as default _apart from_ "Manually manage signing" - selecting this allows you to choose the "PLAYER" profile from the dropdown on the subsequent screen.

7.  Click "Upload" on the final screen and when complete your build should be uploaded to TestFlight.

## Optimising performance

### Debugging prop changes

To debug which props are changing on a component, you can use the following helper function. Use `memo` from React to wrap your component and pass in this function as the second argument.

```
const logChangedProps = (prevProps, nextProps) => {
  const allKeys = new Set([
    ...Object.keys(prevProps),
    ...Object.keys(nextProps),
  ]);

  for (let key of allKeys) {
    if (prevProps[key] !== nextProps[key]) {
      console.debug(`Prop changed:`, key, {
        from: prevProps[key],
        to: nextProps[key],
      });
    }
  }

  return false;
};
```
