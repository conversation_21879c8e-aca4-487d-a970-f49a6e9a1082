{"name": "players", "version": "1.0.1", "scripts": {"refresh": "yarn install && yarn run podInstall && yarn run openXcode", "copy-google-services": "node scripts/copyGoogleServices.js", "android": "cross-env ENVFILE=.env.production yarn copy-google-services && react-native run-android", "ios": "cross-env ENVFILE=.env.production yarn copy-google-services && react-native run-ios", "start": "expo start --dev-client", "test": "jest", "lint": "eslint .", "android-development": "cross-env ENVFILE=.env.development react-native run-android", "android-staging": "cross-env ENVFILE=.env.staging react-native run-android", "android-production": "cross-env ENVFILE=.env.production react-native run-android", "build-android-development": "cd android && cross-env ENVFILE=.env.development ./gradlew bundleRelease && cd ..", "build-android-staging": "cd android && cross-env ENVFILE=.env.staging ./gradlew bundleRelease && cd ..", "build-android-production": "cd android && cross-env ENVFILE=.env.production ./gradlew bundleRelease && cd ..", "build-android-development-apk": "cd android && cross-env ENVFILE=.env.development ./gradlew assembleRelease && cd ..", "build-android-staging-apk": "cd android && cross-env ENVFILE=.env.staging ./gradlew assembleRelease && cd ..", "build-android-production-apk": "cd android && cross-env ENVFILE=.env.production ./gradlew assembleRelease && cd ..", "ios-development": "cross-env ENVFILE=.env.development react-native run-ios --scheme \"PLAYER staging\"", "ios-staging": "react-native run-ios --scheme \"PLAYER staging\"", "ios-production": "react-native run-ios --scheme PLAYER", "podInstall": "cd ios && pod install && cd ..", "openXcode": "xed -b ios", "build-ios-development": "cd ios && xcodebuild -workspace PLAYER.xcworkspace -scheme PLAYER -configuration Release -sdk iphoneos -archivePath PLAYER.xcarchive archive && cd ..", "build-ios-staging": "cd ios && xcodebuild -workspace PLAYER.xcworkspace -scheme \"PLAYER staging\" -configuration Staging -sdk iphoneos -archivePath PLAYER.staging.xcarchive archive && cd ..", "build-ios-production": "cd ios && xcodebuild -workspace PLAYER.xcworkspace -scheme PLAYER -configuration Release -sdk iphoneos -archivePath PLAYER.xcarchive archive && cd ..", "distribute-ios-staging": "yarn build-ios-staging && cd ios && xcodebuild -exportArchive -archivePath PLAYER.staging.xcarchive -exportOptionsPlist exportOptions.plist -exportPath . && cd ..", "distribute-ios-production": "yarn build-ios-production cd ios && xcodebuild -exportArchive -archivePath PLAYER.xcarchive -exportOptionsPlist exportOptions.plist -exportPath . && cd .."}, "dependencies": {"@aws-sdk/client-s3": "^3.617.0", "@expo/config": "~9.0.0", "@expo/metro-config": "~0.18.1", "@expo/vector-icons": "^14.0.2", "@gorhom/bottom-sheet": "5", "@livelike/javascript": "^1.3.3", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-clipboard/clipboard": "^1.16.2", "@react-native-community/blur": "^4.4.1", "@react-native-community/hooks": "^3.0.0", "@react-native-community/masked-view": "^0.1.11", "@react-native-community/slider": "^4.5.6", "@react-native-firebase/analytics": "22.2.0", "@react-native-firebase/app": "22.2.0", "@react-native-firebase/messaging": "^22.2.0", "@react-navigation/bottom-tabs": "^6.5.20", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^6.1.17", "@react-navigation/native-stack": "^6.9.26", "@reduxjs/toolkit": "^2.2.5", "@rematch/core": "^2.2.0", "@rematch/loading": "^2.1.2", "@rematch/persist": "^2.1.2", "@rematch/select": "^3.1.2", "axios": "^1.6.8", "compressorjs": "^1.2.1", "cross-env": "^7.0.3", "eas": "^0.1.0", "expo": "^51.0.0", "expo-app-loading": "^2.1.1", "expo-av": "~14.0.6", "expo-constants": "~16.0.2", "expo-env": "^1.1.1", "expo-file-system": "^17.0.1", "expo-font": "~12.0.9", "expo-image-manipulator": "~12.0.5", "expo-image-picker": "~15.0.7", "expo-linear-gradient": "13.0.2", "expo-linking": "~6.3.1", "expo-media-library": "~16.0.3", "expo-secure-store": "~13.0.2", "expo-sharing": "~12.0.1", "expo-splash-screen": "^0.27.5", "expo-status-bar": "~1.12.1", "formik": "^2.4.6", "lodash": "^4.17.21", "moment": "^2.30.1", "react": "18.2.0", "react-native": "0.74.5", "react-native-animatable": "^1.4.0", "react-native-compressor": "^1.10.3", "react-native-config": "1.5.3", "react-native-date-picker": "^5.0.10", "react-native-device-info": "^14.0.4", "react-native-dotenv": "^3.4.11", "react-native-dropdown-picker": "^5.4.6", "react-native-fast-image": "^8.6.3", "react-native-gesture-handler": "~2.16.1", "react-native-gradle-plugin": "^0.71.19", "react-native-intersection-observer": "^0.2.0", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-modal": "^14.0.0-rc.1", "react-native-modern-datepicker": "^1.0.0-beta.91", "react-native-phone-number-input": "^2.1.0", "react-native-popup-menu": "^0.16.1", "react-native-progress": "^5.0.1", "react-native-reanimated": "3.15.0", "react-native-safe-area-context": "4.10.5", "react-native-screens": "3.31.1", "react-native-svg": "^14.1.0", "react-native-svg-transformer": "^1.5.0", "react-native-toast-message": "^2.2.0", "react-native-uuid": "^2.0.3", "react-native-video": "^6.14.1", "react-native-webview": "13.8.6", "react-redux": "^9.1.2", "readable-stream": "^4.5.2", "redux": "^5.0.1", "redux-persist": "^6.0.0", "yup": "^1.4.0"}, "devDependencies": {"@babel/core": "^7.24.0", "@types/pubnub": "^7.4.2", "babel-plugin-dotenv-import": "^3.0.1", "cpy-cli": "^5.0.0"}, "private": true}