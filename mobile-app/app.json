{"expo": {"name": "PLAYER", "slug": "players", "version": "1.0.36", "scheme": "playerapp.co", "orientation": "portrait", "icon": "./assets/appIcon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash2.png", "resizeMode": "cover", "backgroundColor": "#ffffff"}, "plugins": [["expo-image-picker", {"photosPermission": "The app accesses your photos to let you share them with your friends."}], ["expo-av", {"microphonePermission": "Allow $(PRODUCT_NAME) to access your microphone."}], "react-native-compressor", "@react-native-firebase/app"], "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.playerapp.playerapp", "buildNumber": "64", "infoPlist": {"ITSAppUsesNonExemptEncryption": false, "NSPhotoLibraryUsageDescription": "We need access to your media library to select photos and videos."}, "permissions": ["CAMERA", "MICROPHONE", "AUDIO_RECORDING", "MEDIA_LIBRARY"]}, "android": {"versionCode": 78, "package": "com.playerapp.playerapp", "googleServicesFile": "./assets/google-services.json", "adaptiveIcon": {"foregroundImage": "./assets/appIcon.png", "backgroundColor": "#ffffff"}, "intentFilters": [{"action": "VIEW", "autoVerify": true, "data": [{"scheme": "https", "host": "playerapp.co", "path": "/"}], "category": ["BROWSABLE", "DEFAULT"]}], "permissions": ["android.permission.RECORD_AUDIO", "android.permission.MODIFY_AUDIO_SETTINGS", "android.permission.INTERNET", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE"]}, "web": {"favicon": "./assets/favicon.png"}, "updates": {"useClassicUpdates": true}, "runtimeVersion": {"policy": "sdkVersion"}, "extra": {"eas": {"projectId": "d039be93-0c5b-4361-9434-1e8983ffb0c2"}}}}