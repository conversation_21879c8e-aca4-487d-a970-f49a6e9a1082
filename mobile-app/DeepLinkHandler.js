import { useEffect } from 'react';
import { useNavigation } from '@react-navigation/native';
import * as Linking from 'expo-linking';

const DeepLinkHandler = () => {
  const navigation = useNavigation();

  useEffect(() => {
    const handleDeepLink = (event) => {
      const { path, queryParams } = Linking.parse(event.url);

      // Extracting userId from the deep link URL
      if (path.startsWith('profile/')) {
        const userId = path.split('/')[1]; // Extract userId after 'profile/'
        
        // Navigate to the MyProfile screen with the userId as a param
        navigation.navigate('MyProfileScreenStack', {
          screen: 'MyProfile',
          params: { userId },
        });
      } else if (path.startsWith('announcement/')) {
        const announcementId = path.split('/')[1]; // Extract announcementId
        navigation.navigate('AnnouncementPage', { announcementId });
      } else if (path.startsWith('highlight/')) {
        const [, highlightId, userId] = path.split('/'); // Extract highlightId and userId
        navigation.navigate('FeedDetail', { highlightId, userId });
      }
    };

    // Add event listener for deep links
    Linking.addEventListener('url', handleDeepLink);

    // Cleanup on unmount
    return () => {
      Linking.removeEventListener('url', handleDeepLink);
    };
  }, [navigation]);

  return null;
};

export default DeepLinkHandler;
