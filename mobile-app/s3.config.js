// PLUGINS //
import { manipulateAsync, SaveFormat } from "expo-image-manipulator";
import { Video } from "react-native-compressor";
import Config from "react-native-config";

// SERVICES //
import Uploader<PERSON>piHandler from "./app/services/uploader";

// CONSTANTS //
import { notifyError } from "./app/constants/misc";
import { uploadPartWithProgress } from "./app/utils/trackUpload";

// CONSTANT VALUES //
const MAX_VIDEO_WIDTH = 1920; // Full HD
const MAX_VIDEO_HEIGHT = 1080; // Full HD

/** Function to get a pre-signed upload URL and upload a file to it */
const getUrlAndUpload = async (params, file) => {
  // Step 1: Call the API to get the pre-signed upload URL using the provided params
  const {
    data: { data },
  } = await UploaderApiHandler.getUploadUrl(params);

  // Step 2: Use the fetched URL to upload the file using a PUT request
  return await fetch(data.url, {
    method: "PUT",
    body: file,
  });
};

const compressVideo = async (uri, onError) => {
  try {
    const compressedUri = await Video.compress(
      uri,
      {
        compressionMethod: "manual",
        quality: 0.8,
        maxSize: 1280,
      },
      (progress) => {
        // console.log("Compression Progress:", progress);
      }
    );
    return compressedUri;
  } catch (error) {
    console.error("Compression failed:", error);
    if (typeof onError === "function") {
      onError({
        type: "FILE_LIMITS",
        message: "OOPS!",
        description: "We couldn't upload your video :(",
        details:
          "It looks like your video file was too large! Please make sure the file is under 200mb and less than 3 mins long. Or try a different video.",
      });
    }
    throw error;
  }
};

const compressAndUpload = async (uri, bucket, key) => {
  try {
    console.debug("[Upload] Compressing image...");
    const result = await manipulateAsync(uri, [{ resize: { width: 1000 } }], {
      format: SaveFormat.JPEG, // Change to JPEG format
      compress: 0.8,
    });
    console.debug("[Upload] Image compressed successfully", result);

    const fetchCompressedResponse = await fetch(result.uri);
    if (!fetchCompressedResponse.ok) {
      throw new Error(
        `Failed to fetch compressed file. Status: ${fetchCompressedResponse.status}`
      );
    }
    const compressedBlob = await fetchCompressedResponse.blob();

    const uploadParams = {
      bucket,
      key,
      contentLength: result.size,
      isMultiPart: false,
      metaData: {
        originalName: compressedBlob.name,
        size: String(compressedBlob.size),
        type: compressedBlob.type,
      },
    };

    await getUrlAndUpload(uploadParams, compressedBlob);
    return `${Config.S3_BASE_PHOTO_URL}/${key}`;
  } catch (error) {
    console.error("[Upload] Error compressing and uploading image:", error);
    throw error;
  }
};

const uploadMultiPart = async (
  file,
  bucket,
  key,
  onProgress,
  timeStamp,
  width,
  height,
  duration
) => {
  const multipartUploadParams = {
    bucket,
    key,
    contentType: file.type,
    metadata: {
      width: String(width),
      height: String(height),
      duration: String(duration),
      fileSizeInBits: String(file.size),
      timeStamp: String(timeStamp),
    },
    isMultiPart: true,
  };

  let uploadId;
  try {
    // Step 1: Create multipart upload and get the UploadId
    const {
      data: { data },
    } = await UploaderApiHandler.getUploadUrl(multipartUploadParams);

    uploadId = data.uploadId;
    if (!uploadId) throw new Error("Failed to initiate multipart upload.");

    const partSize = 5 * 1024 * 1024; // 5 MB
    const parts = []; // To store part info for completing the upload
    const totalParts = Math.ceil(file.size / partSize);

    // Step 2: Upload each part
    for (
      let partNumber = 1, start = 0;
      start < file.size;
      start += partSize, partNumber++
    ) {
      const end = Math.min(start + partSize, file.size);
      const part = file.slice(start, end);

      const uploadPartParams = {
        bucket,
        key,
        uploadId,
        partNumber,
        isMultiPart: true,
        totalParts,
        contentLength: part.size,
      };

      const eTag = await uploadPartWithProgress(
        uploadPartParams,
        part,
        onProgress,
        { partNumber, totalParts }
      );

      parts.push({ ETag: eTag, PartNumber: partNumber });

      console.debug(`[Upload] Part ${partNumber} uploaded successfully`);
    }

    // Step 3: Complete the multipart upload
    const completeMultipart = {
      bucket,
      key,
      uploadId,
      completedParts: parts,
      isMultiPart: true,
      totalParts,
    };

    const {
      data: { data: completeData },
    } = await UploaderApiHandler.getUploadUrl(completeMultipart);

    return completeData?.location || `${bucket}/${key}`;
  } catch (error) {
    console.error("[Upload] Error during multipart upload:", error);

    // Step 4: Abort the multipart upload if there's an error
    if (uploadId) {
      const {
        data: { data },
      } = await UploaderApiHandler.abortUpload({ bucket, key, uploadId });
    }
    throw error;
  }
};

const isVideo = (uri) => {
  if (!uri) return false;

  const extension = uri.split(".").pop().toLowerCase();
  const videoExtensions = [
    "mp4",
    "avi",
    "mov",
    "wmv",
    "flv",
    "mkv",
    "webm",
    "mpeg",
  ];

  return videoExtensions.includes(extension);
};

export const uploadS3 = async (
  uri,
  fileName,
  width,
  height,
  duration,
  onSuccess,
  onError,
  onProgress,
  onCancel
) => {
  console.debug("[Upload] Starting upload...", {
    fileName,
    uri,
    fileName,
    width,
    height,
    duration,
  });

  // Add a cancellation token
  const cancelToken = { isCancelled: false };

  // Store the cancel token in the function
  uploadS3.cancelToken = cancelToken;

  // Add a cancel method
  uploadS3.cancel = () => {
    cancelToken.isCancelled = true;
    if (typeof onCancel === "function") {
      onCancel();
    }
  };

  try {
    // Add cancellation check at the start
    if (cancelToken.isCancelled) {
      return; // Just return without throwing error
    }

    let compressedUri = uri;

    // Check file size (200MB = 200 * 1024 * 1024 bytes)
    const MAX_FILE_SIZE = 200 * 1024 * 1024;
    const MAX_DURATION = 180; // 3 minutes in seconds

    if (isVideo(compressedUri)) {
      console.debug(`[Upload] Video Dimensions: ${width}x${height}`);

      if (width > MAX_VIDEO_WIDTH || height > MAX_VIDEO_HEIGHT) {
        console.debug(
          "[Upload] Video is larger than accepted dimensions, compressing..."
        );
        compressedUri = await compressVideo(uri, onError);
        console.debug("[Upload] Compression complete. New URI:", compressedUri);
      } else {
        console.debug(
          "[Upload] Video is within acceptable dimensions. Skipping compression."
        );
      }
    }

    // Fetch the compressed video URI and create a Blob object
    const fetchResponse = await fetch(compressedUri);
    const file = await fetchResponse.blob();

    // Add cancellation check after file fetch
    if (cancelToken.isCancelled) {
      return; // Just return without throwing error
    }

    // Check file size and duration
    if (file._data.size > MAX_FILE_SIZE || duration > MAX_DURATION) {
      console.error("[Upload] File size or duration exceeds limits");
      const fileSize = (file._data.size / (1024 * 1024)).toFixed(1);
      if (typeof onError === "function") {
        onError({
          type: "FILE_LIMITS",
          message: "OOPS!",
          description: "We couldn't upload your video :(",
          details:
            "It looks like your video file was too large! Please make sure the file is under 200mb and less than 3 mins long. Or try a different video.",
        });
      }
      return;
    }

    try {
      let assetUrl;
      let streamUrls;
      const ext = file._data.name.split(".").pop();
      const timeStamp = Date.now();
      const key = `${Date.now()}.${ext}`;

      const contentType = file.type;

      const isVideoFile =
        contentType.includes("video") || isVideo(compressedUri);
      const bucketName = isVideoFile
        ? Config.S3_VIDEO_UPLOAD_BASE_BUCKET
        : Config.BUCKETNAME;
      const inputKey = isVideoFile ? `videosOnly/--${Date.now()}--${key}` : key;

      if (
        file.type.includes("image") ||
        uri.includes(Config.S3_BASE_PHOTO_URL)
      ) {
        assetUrl = await compressAndUpload(uri, bucketName, inputKey);
        assetUrl = `${assetUrl}?t=${Date.now()}`;
      } else {
        // If the file size is larger than 20MB, use multipart upload
        if (file.size > 20 * 1024 * 1024) {
          console.debug(
            "[Upload] File size exceeds 20MB, using multipart upload..."
          );
          assetUrl = await uploadMultiPart(
            file,
            bucketName,
            inputKey,
            onProgress,
            timeStamp,
            width,
            height,
            duration
          );
        } else {
          console.debug(
            "[Upload] File size is less than 20MB, using regular upload..."
          );

          const uploadParams = {
            bucket: bucketName,
            key: inputKey,
            isMultiPart: false,
            contentType: file.type,
            metaData: {
              width: String(width),
              height: String(height),
              duration: String(duration),
              fileSizeInBits: String(file.size),
              timeStamp: String(timeStamp),
            },
          };

          await uploadPartWithProgress(uploadParams, file, onProgress, {
            partNumber: 1,
            totalParts: 1,
          });

          assetUrl = `${Config.S3_VIDEO_UPLOAD_BASE_URL}/${inputKey}`;
        }
      }

      streamUrls = {
        key: inputKey,
        baseUrl: Config.S3_VIDEO_OUTPUT_BASE_URL,
      };

      // Check for cancellation before success callback
      if (cancelToken.isCancelled) {
        return; // Just return without throwing error
      }

      // Success callback
      if (typeof onSuccess === "function") {
        onSuccess(assetUrl, file._data.name, streamUrls);
      }
    } catch (error) {
      console.error("[Upload] Error during upload:", error.message);
      if (typeof onError === "function") {
        onError(error);
      }
      return; // Just return without throwing error
    }
  } catch (error) {
    console.error("[Upload] Upload cancelled or error:", error.message);
    return; // Just return without throwing error
  }
};
