class BaseViewController: UIViewController {
    // ... existing code ...
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // Ensure heavy operations run on background thread
        DispatchQueue.global(qos: .userInitiated).async {
            // Heavy operations here
            
            DispatchQueue.main.async {
                // UI updates here
            }
        }
    }
    
    // Add touch handling debug
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        super.touchesBegan(touches, with: event)
        print("Touch began at: \(Date())")
    }
}