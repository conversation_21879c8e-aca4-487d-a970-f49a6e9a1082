// Add to your AppDelegate.swift
import Foundation

func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
    // ... existing code ...
    
    // Add memory warning observer
    NotificationCenter.default.addObserver(self, 
        selector: #selector(handleMemoryWarning), 
        name: UIApplication.didReceiveMemoryWarningNotification, 
        object: nil)
    
    return true
}

@objc func handleMemoryWarning() {
    // Clear caches and temporary resources
    URLCache.shared.removeAllCachedResponses()
    // Add your custom cache clearing logic here
}