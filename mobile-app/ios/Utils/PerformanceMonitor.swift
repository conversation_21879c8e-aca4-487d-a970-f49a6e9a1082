import Foundation

class PerformanceMonitor {
    static let shared = PerformanceMonitor()
    
    private var runLoop: CFRunLoop?
    private var observer: CFRunLoopObserver?
    
    func startMonitoring() {
        runLoop = CFRunLoopGetMain()
        let activities = CFRunLoopActivity.allActivities.rawValue
        
        observer = CFRunLoopObserverCreateWithHandler(kCFAllocatorDefault, activities, true, 0) { observer, activity in
            let currentTime = CACurrentMediaTime()
            if currentTime > 0.1 { // Threshold for main thread blocking
                let stackTrace = Thread.callStackSymbols
                print("Main thread blocked: \(currentTime) seconds")
                print("Stack trace: \(stackTrace)")
            }
        }
        
        if let observer = observer {
            CFRunLoopAddObserver(runLoop, observer, .commonModes)
        }
    }
}