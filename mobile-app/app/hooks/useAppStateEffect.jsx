import { useFocusEffect } from "@react-navigation/native";
import { useCallback, useRef } from "react";
import { AppState } from "react-native";

export function useAppStateEffect({ onForeground, onBackground }) {
  const appStateRef = useRef(AppState.currentState);

  useFocusEffect(
    useCallback(() => {
      const handleAppStateChange = (nextAppState) => {
        const isGoingToBackground =
          appStateRef.current === "active" &&
          nextAppState.match(/inactive|background/);
        const isComingToForeground =
          appStateRef.current.match(/inactive|background/) &&
          nextAppState === "active";

        if (isGoingToBackground && onBackground) {
          onBackground();
        } else if (isComingToForeground && onForeground) {
          onForeground();
        }

        appStateRef.current = nextAppState;
      };

      const subscription = AppState.addEventListener(
        "change",
        handleAppStateChange
      );

      return () => {
        subscription.remove();
      };
    }, [onForeground, onBackground])
  );
}
