import { useCallback, useEffect, useState } from "react";
import { notifyError } from "../constants/misc";
import CommentApi from "../services/CommentApi";

export const useComments = (commentBoardId) => {
  const [comments, setComments] = useState([]);
  const [isLoading, setIsLoading] = useState(Boolean(commentBoardId));
  const [nextCursor, setNextCursor] = useState(null);

  const fetchComments = useCallback(
    async (cursor) => {
      if (!commentBoardId) return;

      try {
        setIsLoading(true);
        const { data } = await CommentApi.getComments(commentBoardId, cursor);

        if (cursor) {
          setComments((prev) =>
            [...prev, ...data.comments].sort(
              (a, b) => new Date(b.createdAt) - new Date(a.createdAt)
            )
          );
        } else {
          setComments(data.comments);
        }

        setNextCursor(data.pagination.nextCursor || null);
      } catch (error) {
        console.error("[API] Error fetching comments:", error);
        notifyError("Error fetching comments.");
      } finally {
        setIsLoading(false);
      }
    },
    [commentBoardId]
  );

  const handleLoadMoreComments = useCallback(() => {
    if (!nextCursor || isLoading) return;

    fetchComments(nextCursor);
  }, [nextCursor, isLoading, fetchComments]);

  useEffect(() => {
    fetchComments();
  }, [fetchComments]);

  return { comments, setComments, isLoading, handleLoadMoreComments };
};
