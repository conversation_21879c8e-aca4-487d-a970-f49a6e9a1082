import * as Font from "expo-font";
 
export default useFonts = async () =>
  await Font.loadAsync({
    'Bold': require('../../assets/fonts/Fontspring-DEMO-favela-black.otf'),
    'Regular' : require('../../assets/fonts/Poppins-Regular.ttf'),
    'PoppinsBold' : require('../../assets/fonts/Poppins-Bold.ttf'),
    'PoppinsMedium' : require('../../assets/fonts/Poppins-Medium.ttf'),
    'PoppinsSemiBold' : require('../../assets/fonts/Poppins-SemiBold.ttf'),
    'favelaMedium' : require('../../assets/fonts/Fontspring-DEMO-favela-medium.otf'),
    'favelaSemiBold' : require('../../assets/fonts/Fontspring-DEMO-favela-semibold.otf'),
    'favelaBold' : require('../../assets/fonts/favela-bold.ttf'),
    'favelaBlack' : require('../../assets/fonts/favela-black.ttf'),
  });