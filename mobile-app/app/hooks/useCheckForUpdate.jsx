import { useEffect, useState, useCallback } from "react";
import AuthApi from "../services/AuthApi";
import DeviceInfo from "react-native-device-info";

/**
 * A function to compare the version numbers of the api and the device
 * @param {Array<{number}>} apiVersion the array containing the version number of the api
 * @param {Array<{number}>} deviceVersion  the array containing the version number of the device
 * @returns {number} the difference between the two version numbers, 0 means that the number is equal,
 * 1 means that the api version is greater than the device version and -1 means that the device version is greater than the api version
 */
function compareVersions(apiVersion, deviceVersion) {
  const maxLength = Math.max(apiVersion.length, deviceVersion.length);

  for (let i = 0; i < maxLength; i++) {
    const num1 = apiVersion[i] ?? 0;
    const num2 = deviceVersion[i] ?? 0;

    if (num1 > num2) return 1;
    if (num1 < num2) return -1;
  }

  return 0;
}

/**
 * A hook to check for updates
 * @param {boolean} autoCheck the flag to check for updates automatically
 */
export function useCheckForUpdate(autoCheck = true) {
  const [isUpdateAvailable, setIsUpdateAvailable] = useState(false);
  const [loading, setLoading] = useState(autoCheck);
  const [error, setError] = useState(null);

  const checkForUpdate = useCallback(async () => {
    try {
      setLoading(true);
      const {
        data: { data }
      } = await AuthApi.versioncheck();

      const currentVersion = DeviceInfo.getVersion();
      const apiLatestVersion = data?.version?.split(".").map(Number);
      const deviceVersion = currentVersion?.split(".").map(Number);

      if (apiLatestVersion && deviceVersion) {
        const comparison = compareVersions(apiLatestVersion, deviceVersion);
        setIsUpdateAvailable(comparison === 1);
      } else {
        setIsUpdateAvailable(false);
      }
    } catch (err) {
      console.error("Error checking for update:", err);
      setError(err);
      setIsUpdateAvailable(false);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    if (autoCheck) {
      checkForUpdate();
    }
  }, [autoCheck, checkForUpdate]);

  return { isUpdateAvailable, loading, error, checkForUpdate };
}
