import { useState, useEffect, useCallback, useRef } from 'react';
import { Dimensions } from 'react-native';

const windowHeight = Dimensions.get('window').height;

export const useInView = () => {
  const [isInView, setIsInView] = useState(false);
  const ref = useRef(null);

  const handleLayout = useCallback((event) => {
    const { x, y, width, height } = event.nativeEvent.layout;
    const isInViewPort = 
      y >= 0 && 
      y + height <= windowHeight && 
      y + height / 2 >= 0;
    setIsInView(isInViewPort);
  }, []);

  return {
    isInView,
    ref,
    onLayout: handleLayout
  };
};