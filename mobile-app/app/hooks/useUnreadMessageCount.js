import { useCallback, useRef } from "react";
import { dispatch } from "../redux/store";
import { MESSAGE_REFRESH_INTERVAL } from "../constants/chat";
import { useAppStateEffect } from "./useAppStateEffect";
import { useFocusEffect } from "@react-navigation/native";
import { useSelector } from "react-redux";

const fetchUnreadMessageCount = async () => {
  await dispatch.chat.getUnreadMessageCount();
};

export function useUnreadMessageCount(userId) {
  const unreadMessageCount = useSelector(
    (state) => state.chat.unreadMessageCount
  );
  const intervalRef = useRef(null);

  const startInterval = useCallback(() => {
    if (userId && !intervalRef.current) {
      fetchUnreadMessageCount();

      intervalRef.current = setInterval(() => {
        fetchUnreadMessageCount();
      }, MESSAGE_REFRESH_INTERVAL);
    }
  }, [userId]);
  const stopInterval = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, []);

  useAppStateEffect({
    onForeground: startInterval,
    onBackground: stopInterval,
  });

  useFocusEffect(
    useCallback(() => {
      if (!userId) return;

      startInterval();

      return () => stopInterval();
    }, [userId])
  );

  return unreadMessageCount;
}
