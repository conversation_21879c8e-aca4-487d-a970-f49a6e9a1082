import apiHandler from "./ApiHandler";

export default {
    highlights: (data) => apiHandler.post("/highlights/get", data),
    createHighlight: (data) => apiHandler.post("/highlights", data),
    getUsersHighlight: (id, data) =>
      apiHandler.post(`/highlights/user-highlights/${id}`, data),
    getTeamHighlights: (id) =>
      apiHandler.get(`/highlights/team-highlights/${id}`),
    deleteHighlight: (payload) =>
      apiHandler.delete(`/highlights/${payload.id}?userId=${payload.userId}`),
    fetchHighlight: (payload) =>
      apiHandler.get(
        `/highlights/${payload.highlightId}?userId=${payload.userId}`
      ),
    editHighlight: (data) => apiHandler.put("/highlights/update", data),
  };
  