/* eslint-disable import/no-anonymous-default-export */
/* eslint-disable import/no-cycle */

import apiHand<PERSON> from "./ApiHandler";

export default {
  getChats: () => apiHandler.get("/p2p-chat/v2"),
  getChat: (chatroomId, before) =>
    apiHandler.get(
      `/p2p-chat/v2/${chatroomId}${
        before ? `?before=${encodeURIComponent(before)}` : ""
      }`
    ),
  getChatByRecipient: (recipientId) =>
    apiHandler.get(`/p2p-chat/v2/recipient/${recipientId}`),
  createChat: (recipientId) => apiHandler.post(`/p2p-chat/v2`, { recipientId }),
  createMessage: (chatroomId, data) =>
    apiHandler.post(`/p2p-chat/v2/${chatroomId}/message`, data),
  share: (data) => apiHandler.post(`/p2p-chat/v2/share`, data),
  getUnreadMessageCount: () => apiHandler.get("/p2p-chat/v2/unread"),
};
