/* eslint-disable import/no-anonymous-default-export */
/* eslint-disable import/no-cycle */

import apiHandler from "./ApiHandler";

export default {
  getTeam: (id) => apiHandler.post(`/auth/login?id=${id}`, data),
  getAllTeams: (isPrivate) => apiHandler.get(`/teams/all`),
  teamsByClubId: (id, isPrivate) => apiHandler.get(`/teams?clubId=${id}`),
  getAllTeamPlayers: (id) =>
    apiHandler.get(`/teams/players?teamId=${id}&groupBy=position`),
  getSingleTeam: (teamId, clubId) =>
    apiHandler.get(`/teams/${teamId}?clubId=${clubId}`),
  getTeamsByProjection: () => apiHandler.get("/teams/get-by-projection"),
  teamSearch: (data) => apiHandler.get(`/teams/search?query=${data}`),
};
