import axios from "axios";
import moment from "moment";
import { dispatch } from "../redux/store";
import { navigate, resetNavigation } from "../constants/navigation";
import { notifyError } from "../constants/misc";
import { getSingleModel } from "../constants/Constant";
import Config from "react-native-config";

export const BASE_URL = Config.API_URL;

const now = moment().unix();

async function handleRequest(req) {
  req.headers["Content-Type"] = "application/json";
  req.headers.Accept = "application/json";

  const authModel = getSingleModel("auth");

  if (!authModel?.authUser?.token) {
    console.warn("[API] No access token found");
  } else {
    req.headers.Authorization = `Bearer ${authModel.authUser.token}`;
  }

  return req;
}

async function handleResponseError(error) {
  const status = error?.response?.status;
  const message = error?.response?.data?.message;
  const config = error?.config;

  const method = config?.method?.toUpperCase();
  const url = config?.url;

  console.error(`[API] Error in ${method} ${url}: ${message || error.message}`);

  if (status === 401 && message === "jwt expired") {
    try {
      const newAccessToken = await dispatch.auth.refreshToken();
      if (newAccessToken) {
        config.headers.Authorization = `Bearer ${newAccessToken}`;
        return axios(config);
      }
    } catch (refreshError) {
      console.error("[API] Token refresh failed:", refreshError);
      notifyError("Session expired. Please log in again.");
      resetNavigation(0, "AuthNavigation");
    }
  }

  return Promise.reject(error);
}

axios.interceptors.request.use(
  async (req) => {
    console.debug(`[API] ${req.method.toUpperCase()} ${req.url}`);

    if (Boolean(req.skipAuthAndHeaders)) {
      delete req.skipAuthAndHeaders;
      return req;
    }

    const splitedUrl = req.url.split("/");
    const currentEndpoint = splitedUrl[splitedUrl.length - 1];

    if (currentEndpoint === "refresh-token") {
      const authModel = getSingleModel("auth");
      const refreshToken = authModel?.authUser?.refreshToken;
      req.headers.Authorization = `Bearer ${refreshToken}`;
    } else {
      await handleRequest(req);
    }
    return req;
  },
  (error) => Promise.reject(error)
);

axios.interceptors.response.use(
  (response) => response,
  (error) => handleResponseError(error)
);

const apiHandler = {
  post: async (url, data, options) => {
    try {
      const response = await axios.post(
        options?.fullPath ? url : BASE_URL + url,
        data,
        {
          ...options,
        }
      );
      return response;
    } catch (error) {
      throw error;
    }
  },

  get: async (url, params, options) => {
    const fullUrl = options?.fullPath ? url : BASE_URL + url;

    try {
      const response = await axios.get(fullUrl, {
        params,
        ...options,
        timeout: 10000, // 10 second timeout
      });
      return response;
    } catch (error) {
      throw error;
    }
  },

  put: async (url, data, options) => {
    try {
      const response = await axios.put(
        options?.fullPath ? url : BASE_URL + url,
        data,
        {
          ...options,
          timeout: 10000, // 10 second timeout
        }
      );
      return response;
    } catch (error) {
      throw error;
    }
  },

  patch: async (url, data, options) => {
    try {
      const response = await axios.patch(
        options?.fullPath ? url : BASE_URL + url,
        data,
        {
          ...options,
          timeout: 10000, // 10 second timeout
        }
      );
      return response;
    } catch (error) {
      throw error;
    }
  },

  delete: async (url, data, options) => {
    let modData = data;
    modData = data
      ? data instanceof Object && !Object.keys(data).length
        ? null
        : data
      : null;

    const config = modData ? { headers: options, data } : { headers: options };
    try {
      const response = await axios.delete(
        options?.fullPath ? url : BASE_URL + url,
        {
          ...config,
          timeout: 10000, // 10 second timeout
        }
      );
      return response;
    } catch (error) {
      throw error;
    }
  },
};

export default apiHandler;
