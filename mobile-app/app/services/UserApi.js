/* eslint-disable import/no-anonymous-default-export */
/* eslint-disable import/no-cycle */

import apiHand<PERSON> from "./ApiHandler";

export default {
  update: (data) => apiHandler.put("/users/update", data),
  getUserProfile: (id) => apiHandler.get(`/users/get?id=${id}`),
  getUserPhysicalData: (userId) =>
    apiHandler.get(`/users/physical-data?userId=${userId}`),
  postUserPhysicalData: (data) => apiHandler.post("/users/physical-data", data),
  updateUserPhysicalData: (data) =>
    apiHandler.put("/users/physical-data", data),
  userSearch: (data) => apiHandler.get(`/users/search?query=${data}`),
  userSearchByProjection: () =>
    apiHandler.get(
      "/users/get-by-projection?projectionExpression=liveLikeProfileId,firstName,lastName,teamName,clubName,photoUrl,id"
    ),
  blockUsers: (data) => apiHandler.post("/users/block-user", data),
  blockedUsersList: () => apiHandler.get("/users/list-blocked-users"),
  unblockUsers: (data) => apiHandler.post("/users/unblock-user", data),
  getFollowers: (userId) => apiHandler.get(`/users/${userId}/followers`),
  getFollowing: (userId) => apiHandler.get(`/users/${userId}/following`),
};
