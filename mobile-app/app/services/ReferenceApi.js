import apiHandler from "./ApiHandler";

export default {
  postReferences: (data) => apiHandler.post("/references", data),
  getReferences: (userId) => apiHandler.get(`/references/${userId}`),

  deleteReference: (referenceId) => apiHandler.delete(`/references/${referenceId}`),
  getReferenceById: (referenceId) => apiHandler.get(`/references/single/${referenceId}`),
  updateReference: (referenceId, data) => apiHandler.put(`/references/${referenceId}`, data),
};