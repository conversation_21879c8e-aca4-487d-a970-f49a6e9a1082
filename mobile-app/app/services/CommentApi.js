import apiHandler from "./ApiHandler";

export default {
  getComments: (commentBoardId, cursor) =>
    apiHandler.get(
      `/comments/${commentBoardId}?${
        cursor ? `cursor=${encodeURIComponent(cursor)}` : ""
      }`
    ),
  //   createComment: (commentBoardId) =>
  //     apiHandler.post(`/comments/${commentBoardId}`),
  deleteComment: (commentBoardId, commentId) =>
    apiHandler.delete(`/comments/${commentBoardId}/${commentId}`),
};
