import apiHandler from "./ApiHandler";

export default {
  getComments: (commentBoardId, cursor) =>
    apiHandler.get(
      `/comments/${commentBoardId}?${
        cursor ? `cursor=${encodeURIComponent(cursor)}` : ""
      }`
    ),
  createComment: (commentBoardId, data) =>
    apiHandler.post(`/comments/${commentBoardId}`, data),
  deleteComment: (commentBoardId, commentId) =>
    apiHandler.delete(`/comments/${commentBoardId}/${commentId}`),
};
