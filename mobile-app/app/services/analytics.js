import {
  getAnalytics,
  logEvent,
  setUserId as firebaseSetUserId,
} from "@react-native-firebase/analytics";
import { Platform } from "react-native";

class AnalyticsService {
  // Track Page Views
  logScreen = async (pageName) => {
    try {
      const analytics = getAnalytics();
      await logEvent(analytics, "screen_view", {
        screen_name: pageName,
      });
      console.debug(`[Analytics] Screen view logged: ${pageName}`);
    } catch (error) {
      console.error("[Analytics] Error logging screen view:", error);
    }
  };

  // Set User ID
  setUserId = async (userId) => {
    try {
      const analytics = getAnalytics();
      await firebaseSetUserId(analytics, userId ? String(userId) : null);
      console.debug(`[Analytics] User ID set: ${userId ?? "null"}`);
    } catch (error) {
      console.error("[Analytics] Error setting user ID:", error);
    }
  };

  // Log User Login
  logLogin = async (method, email, userId) => {
    try {
      const analytics = getAnalytics();
      await logEvent(analytics, "user_login", {
        method,
        email,
        userId: String(userId),
      });
      console.debug(
        `[Analytics] Login event logged: method=${method}, email=${email}, userId=${userId}`
      );
    } catch (error) {
      console.error("[Analytics] Error logging login event:", error);
    }
  };

  // Log User Logout
  logLogout = async (userId) => {
    try {
      const analytics = getAnalytics();
      const id = userId ? String(userId) : "unknown_user";
      await logEvent(analytics, "user_logout", {
        timestamp: new Date().toISOString(),
        userId: id,
      });
      console.debug(`[Analytics] Logout event logged: userId=${id}`);
    } catch (error) {
      console.error("[Analytics] Error logging logout event:", error);
    }
  };

  // Track Button Clicks
  logButtonClick = async (buttonName, pageName) => {
    try {
      const analytics = getAnalytics();
      await logEvent(analytics, "button_click", {
        button_name: buttonName,
        page_name: pageName,
      });
      console.debug(
        `[Analytics] Button click logged: button=${buttonName}, page=${pageName}`
      );
    } catch (error) {
      console.error("[Analytics] Error logging button click:", error);
    }
  };

  // Log Comment Added
  logCommentAdded = async (contentId, contentType, commentLength) => {
    try {
      const analytics = getAnalytics();
      await logEvent(analytics, "comment_added", {
        content_id: contentId,
        content_type: contentType,
        comment_length: commentLength,
      });
      console.debug(
        `[Analytics] Comment added: contentId=${contentId}, length=${commentLength}`
      );
    } catch (error) {
      console.error("[Analytics] Error logging comment added:", error);
    }
  };

  // Log Reaction Added
  logReactionAdded = async (contentId, contentType, reactionType) => {
    try {
      const analytics = getAnalytics();
      await logEvent(analytics, "reaction_added", {
        content_id: contentId,
        content_type: contentType,
        reaction_type: reactionType,
      });
      console.debug(
        `[Analytics] Reaction added: contentId=${contentId}, reaction=${reactionType}`
      );
    } catch (error) {
      console.error("[Analytics] Error logging reaction added:", error);
    }
  };

  // Log Reaction Removed
  logReactionRemoved = async (contentId, contentType, reactionType) => {
    try {
      const analytics = getAnalytics();
      await logEvent(analytics, "reaction_removed", {
        content_id: contentId,
        content_type: contentType,
        reaction_type: reactionType,
      });
      console.debug(
        `[Analytics] Reaction removed: contentId=${contentId}, reaction=${reactionType}`
      );
    } catch (error) {
      console.error("[Analytics] Error logging reaction removed:", error);
    }
  };

  // Log Content Share
  logContentShare = async (contentId, contentType, shareMethod) => {
    try {
      const analytics = getAnalytics();
      await logEvent(analytics, "content_shared", {
        content_id: contentId,
        content_type: contentType,
        share_method: shareMethod,
      });
      console.debug(
        `[Analytics] Content shared: contentId=${contentId}, method=${shareMethod}`
      );
    } catch (error) {
      console.error("[Analytics] Error logging content share:", error);
    }
  };

  // Log Search Performed
  logSearchPerformed = async (searchTerm, resultCount) => {
    try {
      const analytics = getAnalytics();
      await logEvent(analytics, "search_performed", {
        search_term: searchTerm,
        result_count: resultCount,
      });
      console.debug(
        `[Analytics] Search performed: term=${searchTerm}, results=${resultCount}`
      );
    } catch (error) {
      console.error("[Analytics] Error logging search performed:", error);
    }
  };

  // Verify If Analytics is Enabled
  isAnalyticsEnabled = async () => {
    try {
      const analytics = getAnalytics();
      const appInstanceId = await analytics.getAppInstanceId();
      const enabled = !!appInstanceId;
      console.debug(`[Analytics] Analytics enabled: ${enabled}`);
      return enabled;
    } catch (error) {
      console.error("[Analytics] Error checking analytics enabled:", error);
      return false;
    }
  };

  // Track Custom Events Safely
  verifyEvent = async (eventName, params = {}) => {
    try {
      const analytics = getAnalytics();
      await logEvent(analytics, eventName, {
        ...params,
        timestamp: new Date().toISOString(),
        platform: Platform.OS,
      });
      console.debug(
        `[Analytics] Event logged: ${eventName}, params=${JSON.stringify(
          params
        )}`
      );
      return true;
    } catch (error) {
      console.error(`[Analytics] Error logging event ${eventName}:`, error);
      return false;
    }
  };
}

export default new AnalyticsService();
