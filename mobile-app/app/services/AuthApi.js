import { Platform } from "react-native";
import apiHandler from "./ApiHandler";

export default {
  login: (data) => apiHandler.post("/auth/login", data),
  signup: (data) => apiHandler.post("/auth/signup", data),
  checkForSignup: (data) => apiHandler.post("/auth/allowed-to-signup", data),
  verifyAccount: (data) => apiHandler.post("/auth/verify", data),
  resendVerifyAccount: (data) =>
    apiHandler.post("/auth/resend-verification", data),
  forgotPassword: (data) => apiHandler.post("/auth/forgot-password", data),
  resetPassword: (data) => apiHandler.post("/auth/reset-password", data),
  changePassword: (data) => apiHandler.post("/auth/change-password", data),
  deleteAccount: (data) => apiHandler.post("/auth/delete-account", data),
  refreshToken: (refreshToken) => apiHandler.post('/auth/refresh-token', { refreshToken }),
  versioncheck: () => apiHandler.get(`/mobile-version-manager/${Platform.OS === 'android' ? 'ANDROID' : 'IOS'}`)
};