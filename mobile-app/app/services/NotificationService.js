// app/services/NotificationService.js
import messaging from '@react-native-firebase/messaging';

export const NotificationService = {
  async requestUserPermission() {
    const authStatus = await messaging().requestPermission();
    const enabled =
      authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
      authStatus === messaging.AuthorizationStatus.PROVISIONAL;

    if (enabled) {
      console.log('Authorization status:', authStatus);
      return true;
    }
    return false;
  },

  async getToken() {
    const token = await messaging().getToken();
    //console.log('FCM Token:', token);
    return token;
  },

  async updateToken(token) {
    // Store token in Firebase Realtime Database or Firestore
    // This will be used by FCM to send notifications
    await messaging().setAutoInitEnabled(true);
    await messaging().setAutoInitEnabled(true);
  },

  setBackgroundMessageHandler() {
    messaging().setBackgroundMessageHandler(async (remoteMessage) => {
      console.log('Message handled in the background!', remoteMessage);
    });
  },

  setupForegroundNotification() {
    messaging().onMessage(async (remoteMessage) => {
      console.log('A new FCM message arrived!', remoteMessage);
    });
  },

  setupNotificationOpened() {
    messaging().onNotificationOpenedApp((remoteMessage) => {
      console.log('Notification caused app to open from background state:', remoteMessage);
      // Handle notification opened
    });

    messaging()
      .getInitialNotification()
      .then((remoteMessage) => {
        if (remoteMessage) {
          console.log('Notification caused app to open from quit state:', remoteMessage);
          // Handle notification opened
        }
      });
  },
};