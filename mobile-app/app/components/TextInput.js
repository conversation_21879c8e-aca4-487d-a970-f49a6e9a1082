import React, { useState } from "react";
import {
  Platform,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import { commonStyles } from "../res/CommonStyles";
import { HeightSize, Size } from "../res/Size";
import { Colors } from "../res/Colors";
import { Ionicons } from "@expo/vector-icons"; // Using Ionicons for eye icon

const CustomTextInput = ({
  label,
  style,
  labelTxt,
  textInputStyle,
  placeholder,
  color,
  buttonInsteadOfTextInput,
  onPress,
  selectedName,
  errorMsg,
  onChangeText,
  value,
  keyboardType,
  secureTextEntry = false,
  maxLength,
  multiline = false,
  onBlur,
  inputContainer,
}) => {
  const name = selectedName !== undefined && selectedName !== "" 
  ? selectedName 
  : label === "Date of Birth" 
    ? "Select Date of Birth" 
    : "Select Club Name";

  // State to toggle password visibility
  const [showPassword, setShowPassword] = useState(false);

  return (
    <View style={{ marginTop: HeightSize(3), ...style }}>
      <Text style={[commonStyles.regularText, labelTxt]}>{label}</Text>
      {buttonInsteadOfTextInput ? (
        <TouchableOpacity
          style={[
            textInputStyle,
            {
              height: HeightSize(3.7),
            },
          ]}
          onPress={onPress}
        >
          <Text
            style={[
              commonStyles.regularText,
              {
                fontSize: Size(3.7),
                color:
                  name === "Select Club Name"
                    ? "rgba(0, 0, 0, 0.25)"
                    : Colors.black,
              },
            ]}
          >
            {name}
          </Text>
        </TouchableOpacity>
      ) : (
        <View
          style={{
            flexDirection: "row",
            alignItems: "center",
            ...inputContainer,
          }}
        >
          <TextInput
            style={[
              textInputStyle,
              {
                flex: 1,
                height: multiline === false ? HeightSize(3.7) : "auto",
              },
            ]}
            onChangeText={onChangeText}
            value={value}
            placeholder={placeholder}
            placeholderTextColor={color}
            keyboardType={keyboardType}
            secureTextEntry={secureTextEntry && !showPassword}
            maxLength={maxLength}
            multiline={multiline}
            onBlur={onBlur}
          />
          {/* Eye Icon for toggling password visibility */}
          {secureTextEntry && (
            <TouchableOpacity
              onPress={() => setShowPassword(!showPassword)}
              style={{ padding: 5 }}
            >
              <Ionicons
                name={showPassword ? "eye-off" : "eye"}
                size={24}
                color={Colors.black}
              />
            </TouchableOpacity>
          )}
        </View>
      )}
      {errorMsg !== "" && <Text style={[commonStyles.errTxt]}>{errorMsg}</Text>}
    </View>
  );
};

export default CustomTextInput;
