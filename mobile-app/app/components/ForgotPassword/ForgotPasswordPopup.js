// import React, { useState } from 'react';
// import { View, Text, TextInput, TouchableOpacity, Modal, StyleSheet } from 'react-native';
// import styles from './ForgotPasswordPopupStyles';
// import Button from '../Button/Button';
// import { Ionicons } from "@expo/vector-icons";
// import { HeightSize, Size } from '../../res/Size';
// import { commonStyles } from '../../res/CommonStyles';
// import * as yup from 'yup';
// import { useFormik } from "formik";
// import { Colors } from '../../res/Colors';
// import Client from '../../res/Client';
// import analytics from "@react-native-firebase/analytics";

// const ForgotPasswordPopup = ({ visible, onClose }) => {
//   const [email, setEmail] = useState('');
//   const [showFields, setShowFields] = useState(false);
//   const [loading, setLoading] = useState(false);

//   const initialValues1 = { email: '' };
//   const initialValues2 = { verificationCode: '', password: '' };

//   const emailSchema = yup.object().shape({
//     email: yup.string().email('Invalid email').required('Please enter your email'),
//   });

//   const codeSchema = yup.object().shape({
//     verificationCode: yup.string().required('Please enter the verification code'),
//     password: yup.string().min(6, 'Password must be at least 6 characters').required('Please enter a new password'),
//   });

//   const submitEmail = async (values) => {
//     try {
//       await analytics().logEvent("forgot_password_click", {
//         action: "email_submission",
//         timestamp: new Date().toISOString(),
//       });
//       setLoading(true);
//       const response = await Client.post('auth/forgot-password', values);
//       if (response.data.status === 1) {
//         setShowFields(true);
//         console.log("submit email",response.data)
//       }
//     } catch (error) {
//       console.error('Error sending email:', error);
//     } finally {
//       setLoading(false);
//     }
//   };

//   const submitCode = async (values) => {
//     try {
//       await analytics().logEvent("forgot_password_click", {
//         action: "password_recovery",
//         timestamp: new Date().toISOString(),
//       });
//       setLoading(true);
//       const response = await Client.post('auth/reset-password', { ...values, email: emailFormik.values.email });
//       if (response.data) {
//         onClose();
//       }
//     } catch (error) {
//       console.error('Error resetting password:', error);
//     } finally {
//       setLoading(false);
//     }
//   };

//   const emailFormik = useFormik({
//     initialValues: initialValues1,
//     validationSchema: emailSchema,
//     onSubmit: submitEmail,
//   });

//   const codeFormik = useFormik({
//     initialValues: initialValues2,
//     validationSchema: codeSchema,
//     onSubmit: submitCode,
//   });

//   const handleSendCode = () => {
//     console.log('Send code to: ', email);
//     onClose();
//   };

//   return (
//     <Modal
//       visible={visible}
//       transparent
//       animationType="fade"
//     >
//       <View style={styles.modalContainer}>
//         <View style={styles.modalContent}>
//         <TouchableOpacity style={styles.closeButton} onPress={onClose}>
//         <Ionicons
//               name="close-circle-outline"
//               size={Size(7)}
//               color="black"
//             />
//           </TouchableOpacity>
//           <Text style={styles.title}>Password Recovery</Text>
//           {!showFields ? (
//               <>
//                 <Text style={styles.labelTxt}>Enter the email you used to create an account</Text>
//                 <TextInput
//                   onBlur={emailFormik.handleBlur('email')}
//                   value={emailFormik.values.email}
//                   onChangeText={emailFormik.handleChange('email')}
//                   placeholder="Enter email address"
//                   style={styles.input}
//                 />
//                 {emailFormik.touched.email && emailFormik.errors.email && (
//                   <Text style={commonStyles.errTxt}>{emailFormik.errors.email}</Text>
//                 )}
//               </>
//             ) : (
//               <>
//                 {/* <Text style={styles.labelTxt}>Enter Verification Code</Text> */}
//                 <TextInput
//                   onBlur={codeFormik.handleBlur('verificationCode')}
//                   value={codeFormik.values.verificationCode}
//                   onChangeText={codeFormik.handleChange('verificationCode')}
//                   placeholder="Enter received code"
//                   style={styles.input}
//                 />
//                 {codeFormik.touched.verificationCode && codeFormik.errors.verificationCode && (
//                   <Text style={commonStyles.errTxt}>{codeFormik.errors.verificationCode}</Text>
//                 )}
//                 {/* <Text style={styles.labelTxt}>Enter New Password</Text> */}
//                 <TextInput
//                   onBlur={codeFormik.handleBlur('password')}
//                   value={codeFormik.values.password}
//                   onChangeText={codeFormik.handleChange('password')}
//                   placeholder="Enter new password"
//                   style={styles.input}
//                   secureTextEntry
//                 />
//                 {codeFormik.touched.password && codeFormik.errors.password && (
//                   <Text style={commonStyles.errTxt}>{codeFormik.errors.password}</Text>
//                 )}
//               </>
//             )}

//           {/* <TouchableOpacity style={styles.sendButton} onPress={handleSendCode}>
//             <Text style={styles.sendButtonText}>Send Code</Text>
//           </TouchableOpacity> */}
//           <Button
//           title={showFields ? "RECOVER PASSWORD" : "SEND CODE"}
//           onPress={()=> showFields ? codeFormik.handleSubmit() : emailFormik.handleSubmit()}
//           progress={loading}
//           disable={loading}
//           backgroundColor={Colors.green}
//           containerStyle={styles.containerStyle}
//           textStyleInsideButton={styles.textStyleInsideButton}
//           />
//         </View>
//       </View>
//     </Modal>
//   );
// };

// export default ForgotPasswordPopup;

import React, { useEffect, useState } from "react";
import { useFormik } from "formik";
import { useSelector } from "react-redux";
import {
  View,
  Text,
  TextInput,
  Modal,
  ActivityIndicator,
  TouchableOpacity,
  Image,
  TouchableWithoutFeedback,
  Linking,
} from "react-native";
import Button from "../Button/Button";
import { Colors } from "../../res/Colors";
import { dispatch } from "../../redux/store";
import {
  forgotPassword2Schema,
  forgotPasswordSchema,
} from "../../constants/formSchema";
import styles from "./ForgotPasswordPopupStyles";
import { Ionicons } from "@expo/vector-icons";
import { Size } from "../../res/Size";
import { commonStyles } from "../../res/CommonStyles";
import { Images } from "../../res/Images";
import analytics from "@react-native-firebase/analytics";

const ForgotPassword = () => {
  const [email, setEmail] = useState("");
  const [visible, setVisible] = useState(false);
  const [showFields, setShowFields] = useState(false);

  const sendEmailLoading = useSelector(
    ({ loading }) => loading.effects.auth.forgotPassword
  );

  const sendCodeLoading = useSelector(
    ({ loading }) => loading.effects.auth.resetPassword
  );

  const handleCancel = async () => {
    setVisible(false);
    setEmail("");
    setShowFields(false);
    await dispatch.auth.forgotPasswordModalBox({ visible: false });
  };

  const authModel = useSelector(({ auth }) => auth);

  useEffect(() => {
    setVisible(authModel?.forgotPasswordModal?.visible);
  }, [authModel?.forgotPasswordModal?.visible]);

  const submitCode = async (values) => {
    try {
      await analytics().logEvent("forgot_password_click", {
        action: "password_recovery",
        timestamp: new Date().toISOString(),
      });
      const val = {
        ...values,
        email,
      };
      const res = await dispatch.auth.resetPassword(val);
      if (res === 1) dispatch.auth.forgotPasswordModalBox({ visible: false });
    } catch (error) {
      console.error('Error resetting password:', error);
    }
  };

  const submitEmail = async (values) => {
    try {
      await analytics().logEvent("forgot_password_click", {
        action: "email_submission",
        timestamp: new Date().toISOString(),
      });
      setEmail(values.email);
      const res = await dispatch.auth.forgotPassword(values);
      if (res === 1) setShowFields(true);
    } catch (error) {
      console.error('Error sending email:', error);
    }
  };

  const initialValues1 = {
    email: "",
  };

  const initialValues2 = {
    email: "",
    verificationCode: "",
    password: "",
  };

  const formik1 = useFormik({
    initialValues: initialValues1,
    onSubmit: submitEmail,
    validationSchema: forgotPasswordSchema,
  });

  const formik2 = useFormik({
    initialValues: initialValues2,
    onSubmit: submitCode,
    validationSchema: forgotPassword2Schema,
  });

  return (
    <Modal visible={visible} transparent={true} animationType="slide">
      <TouchableWithoutFeedback onPress={handleCancel}>
        <View style={commonStyles.modalBackdrop}>
          <TouchableWithoutFeedback>
            <View
              style={[commonStyles.modalContainer, styles.forgetPasswordModal]}
            >
              <TouchableOpacity
                style={commonStyles.modalCloseWrapper}
                onPress={handleCancel}
              >
                <Image
                  source={Images.cross}
                  style={commonStyles.modalCloseIcon}
                />
              </TouchableOpacity>
              <Text style={styles.title}>PASSWORD RECOVERY</Text>
              <Text style={styles.description}>
                Reach out to your community manager, or email{" "}
                <Text
                  style={styles.email}
                  onPress={() => Linking.openURL("mailto:<EMAIL>")}
                >
                  <EMAIL>
                </Text>{" "}
                for help.
              </Text>
              <TouchableOpacity
                style={styles.sendButton}
                onPress={handleCancel}
              >
                <Text style={styles.sendButtonText}>CLOSE</Text>
              </TouchableOpacity>
              {/* <View>
            <Text style={styles.title}>Password Recovery</Text>
            {showFields ? (
              <Text style={styles.labelTxt}>
                {" "}
                Enter the code you got in the Email we sent
              </Text>
            ) : (
              <Text style={styles.labelTxt}>
                Enter the email you used to create an account.
              </Text>
            )}
            {showFields ? (
              <View>
                <TextInput
                  style={styles.input}
                  onChangeText={formik2.handleChange("verificationCode")}
                  placeholder="Enter received code"
                  onBlur={formik2.handleBlur("verificationCode")}
                  value={formik2.values.verificationCode}
                />
                {formik2.errors.verificationCode &&
                  formik2.touched.verificationCode && (
                    <Text style={styles.error}>
                      {formik2.errors.verificationCode}
                    </Text>
                  )}
                <TextInput
                  style={styles.input}
                  onChangeText={formik2.handleChange("password")}
                  placeholder="Enter new password"
                  onBlur={formik2.handleBlur("password")}
                  value={formik2.values.password}
                />
                {formik2.errors.password && formik2.touched.password && (
                  <Text style={styles.error}>{formik2.errors.password}</Text>
                )}
                <Button
                  title={"RECOVER PASSWORD"}
                  progress={sendCodeLoading}
                  disable={sendCodeLoading}
                  backgroundColor={Colors.green}
                  containerStyle={styles.containerStyle}
                  textStyleInsideButton={styles.textStyleInsideButton}
                  onPress={formik2.handleSubmit}
                />
              </View>
            ) : (
              <View>
                <TextInput
                  style={styles.input}
                  onChangeText={formik1.handleChange("email")}
                  placeholder="Enter email address"
                  onBlur={formik1.handleBlur("email")}
                  value={formik1.values.email}
                />
                {formik1.errors.email && formik1.touched.email && (
                  <Text style={styles.error}>{formik1.errors.email}</Text>
                )}
                <Button
                  title={"Send Code"}
                  progress={sendEmailLoading}
                  disable={sendEmailLoading}
                  backgroundColor={Colors.green}
                  containerStyle={styles.containerStyle}
                  textStyleInsideButton={styles.textStyleInsideButton}
                  onPress={formik1.handleSubmit}
                />
              </View>
            )}
          </View> */}
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

export default ForgotPassword;
