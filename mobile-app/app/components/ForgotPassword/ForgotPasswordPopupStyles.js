import { StyleSheet } from "react-native";
import { Colors } from "../../res/Colors";
import { HeightSize, Size } from "../../res/Size";

const styles = StyleSheet.create({
  forgetPasswordModal: {
    paddingHorizontal: Size(9),
    paddingVertical: HeightSize(6),
  },
  title: {
    fontSize: Size(4.5),
    fontFamily: "favelaBold",
    marginBottom: HeightSize(2),
    textAlign: "center",
  },
  description: {
    fontSize: Size(4.2),
    fontFamily: "Regular",
    marginBottom: HeightSize(3.5),
    textAlign: "center",
    color: "#7F7F7F",
  },
  email: {
    fontFamily: "PoppinsBold",
  },
  input: {
    borderWidth: 1,
    borderColor: Colors.grey,
    borderRadius: 5,
    padding: Size(2),
    marginBottom: HeightSize(2),
    width: Size(70),
  },
  sendButton: {
    backgroundColor: Colors.black,
    borderRadius: <PERSON>ze(12),
    justifyContent: "center",
    alignItems: "center",
  },
  sendButtonText: {
    fontFamily: "favelaBold",
    color: Colors.white,
    fontSize: Size(4),
    padding: Size(4.5),
  },
  textStyleInsideButton: {
    color: Colors.black,
    paddingHorizontal: Size(7),
  },
  labelTxt: {
    fontSize: Size(4),
    fontFamily: "Regular",
    marginTop: HeightSize(2),
    textAlign: "center",
  },
  containerStyle: {
    width: "90%",
  },
});

export default styles;
