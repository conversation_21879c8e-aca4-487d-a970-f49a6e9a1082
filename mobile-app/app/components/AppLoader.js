// REACT //
import React from "react";

// REACT NATIVE //
import {
  ActivityIndicator,
  Platform,
  StyleSheet,
  Text,
  View,
} from "react-native";

// OTHERS //
import { LogoImg } from "../res/Svg";
import { Size } from "../res/Size";

/** App Loader Component */
const AppLoader = () => {
  // Define States

  // Define Refs

  // Helper Functions

  // Use Effect and Focus Effect

  return (
    <View style={styles.loadingContainer}>
      <LogoImg width={Platform.isPad ? Size(20) : Size(45)} />
      <ActivityIndicator size="large" color="#000000" style={styles.spinner} />
    </View>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    backgroundColor: "#ffffff",
    alignItems: "center",
    justifyContent: "center",
  },
  spinner: {
    marginTop: 50,
  },
});

export default AppLoader;
