import React from "react";
import { StyleSheet, Text, View } from "react-native";
import { Colors } from "../res/Colors";
import { Size } from "../res/Size";

const UnreadMessageCount = ({ count }) => {
  return (
    <View style={styles.unreadMessageCount}>
      <Text style={styles.unreadMessageText}>{count}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  unreadMessageCount: {
    backgroundColor: "#FF0000",
    borderRadius: Size(5),
    width: Size(5),
    height: Size(5),
    alignItems: "center",
    justifyContent: "center",
  },
  unreadMessageText: {
    color: Colors.white,
    fontFamily: "PoppinsBold",
    fontSize: Size(3),
    lineHeight: Size(4),
  },
});

export default UnreadMessageCount;
