import React, { useState } from "react";
import { View, Text, TouchableOpacity, Modal, StyleSheet } from "react-native";
import ShareProfile from "./ShareProfile"
import { WEB_URL } from "../../Config";
import { HeightSize, Size } from "../res/Size";

const VerificationRequest = ({ userDetails, userInfo }) => {
  const [shouldVerify, setShouldVerify] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);

  const verificationHeader = "Share your profile for verification";

  const verificationTitle = `You are invited to verify ${
    userDetails?.id === userInfo?.id ? "my" : `${userDetails?.firstName}'s`
  } PlayerApp profile: \n \n`;

  const verificationUrl = `${WEB_URL}/signup?id=${userDetails?.id}`;
  
  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.verifyButton}
        onPress={() => {
          setShouldVerify(true);
          setModalVisible(true);
        }}
      >
        <Text style={styles.verifyButtonText}>Get verified</Text>
      </TouchableOpacity>

      <Modal
        transparent={true}
        visible={modalVisible}
        animationType="slide"
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <Text style={styles.modalHeaderText}>
              Invite a former coach or colleague onto Playerapp to verify your profile.
            </Text>
            <Text style={styles.modalSubText}>
              Coaches & colleagues can also write a reference on your profile
            </Text>

            <View style={styles.modalContent}>
              {shouldVerify && (
                <ShareProfile
                  title={verificationTitle}
                  header={verificationHeader}
                  url={verificationUrl}
                  id="verification_modal_"
                  Label={() => (
                    <View style={styles.getVerifiedLabel}>
                      <Text style={styles.getVerifiedLabelText}>Get Verified</Text>
                    </View>
                  )}
                />
              )}

              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => setModalVisible(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    alignItems: 'center',
  },
  verifyButton: {
    backgroundColor: '#52FF00',
    borderRadius: Size(10),
    paddingVertical: HeightSize(1),
    paddingHorizontal: Size(5),
    marginVertical: HeightSize(1),
    marginRight: Size(35)
  },
  verifyButtonText: {
    color: 'black',
    fontSize: Size(4),
    fontFamily: "PoppinsMedium"
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContainer: {
    width: '90%',
    backgroundColor: 'white',
    borderRadius: Size(7),
    padding: Size(5),
    alignItems: 'center',
  },
  modalHeaderText: {
    fontFamily: 'PoppinsBold',
    fontSize: Size(6),
    color: 'black',
    textAlign: 'center',
  },
  modalSubText: {
    fontFamily: 'Regular',
    fontSize: Size(5),
    color: 'black',
    opacity: 0.5,
    textAlign: 'center',
    paddingVertical: HeightSize(2),
  },
  modalContent: {
    alignItems: 'center',
  },
  getVerifiedLabel: {
    backgroundColor: '#000',
    borderRadius: Size(5),
    paddingVertical: HeightSize(1.2),
    paddingHorizontal: Size(5),
   // marginVertical: HeightSize(1),
  },
  getVerifiedLabelText: {
    color: 'white',
    fontFamily: "Regular"
  },
  cancelButton: {
    marginTop: HeightSize(1.5),
  },
  cancelButtonText: {
    fontSize: Size(5),
    color: 'black',
    fontFamily: "Regular"
  },
});

export default VerificationRequest;
