import { StyleSheet } from "react-native";
import { HeightSize, Size } from "../../res/Size";
import { Colors } from "../../res/Colors";

const styles = StyleSheet.create({
  resultContainer: {
    width: "100%",
    borderRadius: Size(9),
    overflow: "hidden",
    marginTop: 6,
    aspectRatio: 4 / 5,
  },
  teamStyle: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: HeightSize(2),
    paddingHorizontal: Size(2),
    alignItems: "center"
  },
  scoreStyle: {
    backgroundColor: Colors.green,
    padding: Size(3),
    color: Colors.black,
    fontFamily: "Bold",
    fontSize: Size(4.5),
    borderRadius: Size(2),
    marginHorizontal: Size(1.5)

  },
  clubNameContainer: {
    //flex: 1,
    width: "35%"
  },
});

export default styles;