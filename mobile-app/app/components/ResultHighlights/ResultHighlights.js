import React from "react";
import { commonStyles } from "../../res/CommonStyles";
import styles from "./ResultHighlightsStyles";
import { Images } from "../../res/Images";
import { ImageBackground, Text, View } from "react-native";
import { Size, HeightSize } from "../../res/Size";
import { Colors } from "../../res/Colors";
import { formatDateToWords } from "../../constants/misc";

const ResultHighlights = ({item}) => {
  return (
    <View style={{ 
     }}>
      <ImageBackground source={Images.greenField} style={styles.resultContainer}>
        <Text style={[commonStyles.boldText, { color: Colors.white, textAlign: "center", marginBottom: HeightSize(1) }]}>{item?.type === "RESULT" && formatDateToWords(item?.result?.dateTimePlayed)}</Text>
        <View style={styles.teamStyle}>
          <View style={styles.clubNameContainer}>
            <Text style={commonStyles.normalTxt}>{item?.result?.homeTeam?.data?.clubName}</Text>
          </View>
          <Text style={styles.scoreStyle}>{item?.result?.homeTeam?.score}</Text>
          <Text style={styles.scoreStyle}>{item?.result?.awayTeam?.score}</Text>
          <View style={styles.clubNameContainer}>
            <Text style={commonStyles.normalTxt}>{item?.result?.awayTeam?.data?.clubName}</Text>
          </View>
        </View>
        <View style={styles.teamStyle}>
          {item?.result?.homeTeam?.goalScorers.length > 0 ? (
            item.result.homeTeam.goalScorers.map((goalScorer, idx) => (
              <Text key={idx} style={commonStyles.normalTxt}>
                {goalScorer}
              </Text>
            ))
          ) : (
            <Text style={commonStyles.normalTxt}>No goal scorer</Text>
          )}

          <View>
            {item?.result?.awayTeam?.goalScorers.length > 0 ? (
              item.result.awayTeam.goalScorers.map((goalScorer, idx) => (
                <Text key={idx} style={commonStyles.normalTxt}>
                  {goalScorer}
                </Text>
              ))
            ) : (
              <Text style={commonStyles.normalTxt}>No goal scorer</Text>
            )}
          </View>
        </View>
        <Text style={commonStyles.normalTxt}>Assists</Text>
        <View style={styles.teamStyle}>
          {item?.result?.homeTeam?.assists.length > 0 ? (
            item.result.homeTeam.assists.map((assist, idx) => (
              <Text key={idx} style={commonStyles.normalTxt}>
                {assist}
              </Text>
            ))
          ) : (
            <Text style={commonStyles.normalTxt}>No assist</Text>
          )}
          <View>
            {item?.result?.awayTeam?.assists.length > 0 ? (
              item.result.awayTeam.assists.map((assist, idx) => (
                <Text key={idx} style={commonStyles.normalTxt}>
                  {assist}
                </Text>
              ))
            ) : (
              <Text style={commonStyles.normalTxt}>No assist</Text>
            )}
          </View>
        </View>
        <Text style={commonStyles.normalTxt}>Player of the match</Text>
        <View style={styles.teamStyle}>
          {item?.result?.homeTeam?.playersOfTheMatch.length > 0 ? (
            item.result.homeTeam.playersOfTheMatch.map((pom, idx) => (
              <Text key={idx} style={commonStyles.normalTxt}>
                {pom}
              </Text>
            ))
          ) : (
            <Text style={commonStyles.normalTxt}>No POM</Text>
          )}
          <View>
            {item?.result?.awayTeam?.playersOfTheMatch.length > 0 ? (
              item.result.awayTeam.playersOfTheMatch.map((pom, idx) => (
                <Text key={idx} style={commonStyles.normalTxt}>
                  {pom}
                </Text>
              ))
            ) : (
              <Text style={commonStyles.normalTxt}>No POM</Text>
            )}
          </View>
        </View>
      </ImageBackground>
    </View>
  )
}

export default ResultHighlights;