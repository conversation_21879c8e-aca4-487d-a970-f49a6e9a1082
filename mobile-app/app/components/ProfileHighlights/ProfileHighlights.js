// REACT //
import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";

// PLUGINS //
import { Video } from "expo-av";

// COMPONENTS //
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  ActivityIndicator,
  StyleSheet,
  ScrollView,
  RefreshControl,
} from "react-native";
import { Images } from "../../res/Images";

// NAVIGATION //
import { useNavigation } from "@react-navigation/native";

// OTHERS //
import { PlusIcon } from "../../res/Svg";
import { Colors } from "../../res/Colors";

const ProfileHighlights = ({
  fetchingHighlight,
  fetchingMore,
  profileHighlights,
  userId,
  onLoadMore,
  ownProfile,
  textColor,
  isNonPlayer,
  setVisible,
  setImageViewData,
  videoRef,
  onUploadPress,
  refreshing = false,
  onRefresh,
}) => {
  const navigation = useNavigation();

  // Add debug logging for photo highlights
  //  React.useEffect(() => {
  //   if (profileHighlights?.length > 0) {
  //     const photos = profileHighlights.filter(h => h.type === "PHOTO");
  //     console.log("Photo highlights:", photos.map(p => ({ id: p.id, url: p.url })));
  //   }
  // }, [profileHighlights]);

  // Add this helper function
  const formatImageUrl = (url) => {
    if (!url) return "";
    // Check if URL already has an extension
    if (/\.(jpg|jpeg|png|gif)$/i.test(url)) {
      return url;
    }
    // Add .jpg extension if missing
    return `${url}.jpg`;
  };

  const convertSecondsToMinutes = (seconds) => {
    if (!seconds || seconds <= 0) {
      return "--:--"; // Return placeholder when duration is missing or invalid
    }

    const wholeSeconds = Math.floor(seconds); // Ignore the fractional part
    const minutes = Math.floor(wholeSeconds / 60); // Get the whole minutes part
    const remainingSeconds = wholeSeconds % 60; // Get the remaining seconds
    const formattedMinutes = `${minutes}:${remainingSeconds
      .toString()
      .padStart(2, "0")}`; // Format as MM:SS
    return formattedMinutes;
  };

  const [highlightItems, setHighlightItems] = useState([]);

  useEffect(() => {
    if (profileHighlights) {
      // Sort highlights by creation date in descending order (newest first)
      const sortedHighlights = [...profileHighlights].sort(
        (a, b) => new Date(b.createdAt) - new Date(a.createdAt)
      );

      // Update highlight items with sorted data
      setHighlightItems(sortedHighlights);
    }
  }, [profileHighlights]);

  useEffect(() => {
    if (refreshing) {
      // When refreshing, reset the highlight items
      setHighlightItems([]);
    }
  }, [refreshing]);

  return (
    <ScrollView
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={onRefresh}
          colors={[Colors.primary]}
        />
      }
      style={styles.container}
    >
      {ownProfile && (
        <TouchableOpacity
          onPress={() =>
            navigation.push("UploadHighlights", { fromProfileHighlights: true })
          }
          style={styles.uploadButton}
        >
          <Image
            resizeMode="contain"
            style={{
              maxHeight: 30,
              maxWidth: 30,
              height: 30,
              width: 30,
            }}
            source={isNonPlayer ? Images.plusBlack : Images.plusWhite}
          />
        </TouchableOpacity>
      )}
      {fetchingHighlight && !fetchingMore ? (
        // <ActivityIndicator size="large" color={Colors.green} />
        <View />
      ) : (
        <View style={styles.gridContainer}>
          {highlightItems?.length > 0 ? (
            <>
              {highlightItems.map((highlight, idx) => {
                return (
                  <View
                    key={highlight.id}
                    // ref={idx === highlightItems?.length - 1 ? observe : null}
                    style={styles.highlightContainer}
                  >
                    {highlight.type === "PHOTO" && (
                      <TouchableOpacity
                        style={styles.highlight}
                        onPress={() =>
                          navigation.navigate("HomeScreenStack", {
                            screen: "FeedDetail",
                            params: {
                              highlightId: highlight.id,
                              userId: highlight.userId,
                              fromProfileHighlights: true,
                            },
                          })
                        }
                      >
                        <View style={styles.highLightOverlay}>
                          <Text
                            style={styles.videoText}
                            numberOfLines={3}
                            ellipsizeMode="tail"
                          >
                            {highlight?.comment}
                          </Text>
                        </View>
                        <Image
                          source={{ uri: formatImageUrl(highlight.url) }}
                          style={styles.image}
                          onError={(error) =>
                            console.error(
                              "Image load error:",
                              error.nativeEvent.error
                            )
                          }
                          onLoad={() =>
                            console.debug(
                              "Image loaded successfully:",
                              highlight.url
                            )
                          }
                        />
                      </TouchableOpacity>
                    )}

                    {highlight.type === "VIDEO" && (
                      <TouchableOpacity
                        style={styles.highlight}
                        onPress={() =>
                          navigation.navigate("HomeScreenStack", {
                            screen: "FeedDetail",
                            params: {
                              highlightId: highlight.id,
                              userId: highlight.userId,
                              fromProfileHighlights: true,
                            },
                          })
                        }
                      >
                        <View style={styles.highLightOverlay}>
                          <Text
                            style={styles.videoText}
                            numberOfLines={3}
                            ellipsizeMode="tail"
                          >
                            {highlight?.comment}
                          </Text>

                          {/* Duration */}
                          <Text style={styles.videoDuration}>
                            {convertSecondsToMinutes(
                              highlight?.streamUrl?.duration || 0
                            )}
                          </Text>
                        </View>
                        <View style={styles.videoContainer}>
                          {highlight?.streamUrl ? (
                            <Image
                              alt="thumbnail"
                              style={styles.image}
                              source={{
                                uri: `${highlight?.streamUrl?.baseUrl}/${
                                  highlight?.streamUrl?.key?.split("--")[1]
                                }/thumbnail.png`,
                              }}
                            />
                          ) : (
                            <Video
                              ref={videoRef}
                              style={styles.video}
                              source={{ uri: `${highlight.url}#t=0.001` }}
                              resizeMode="cover"
                            />
                          )}
                        </View>
                      </TouchableOpacity>
                    )}
                  </View>
                );
              })}
              {fetchingMore && <View style={styles.loadingContainer}></View>}
            </>
          ) : (
            <View style={styles.noHighlights}>
              <Text style={[styles.noHighlightsText, { color: textColor }]}>
                {ownProfile
                  ? "Upload football content. Just tap the + button above."
                  : "This player has no highlights yet."}
              </Text>
            </View>
          )}
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingVertical: 10,
    paddingHorizontal: 4,
  },
  uploadButton: {
    borderRadius: 32,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 10,
  },
  plusIcon: {
    width: 40,
    height: 40,
  },
  gridContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
  },
  highlightContainer: {
    width: "32.5%",
    aspectRatio: 1,
    backgroundColor: "white",
    margin: 1.25,
    position: "relative",
    overflow: "hidden",
  },
  highLightOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    zIndex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  videoText: {
    color: Colors.white,
    fontSize: 10,
    textAlign: "center",
    padding: 6,
    fontFamily: "favelaBlack",
    lineHeight: 13,
  },
  videoDuration: {
    fontFamily: "favelaBlack",
    lineHeight: 10,
    color: Colors.white,
    position: "absolute",
    bottom: 4,
    right: 4,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    paddingVertical: 3,
    paddingHorizontal: 7,
    fontSize: 7.5,
    borderRadius: 12,
  },
  highlight: {
    flex: 1,
  },
  image: {
    width: "100%",
    height: "100%",
    resizeMode: "cover",
  },
  videoContainer: {
    flex: 1,
    backgroundColor: "white",
    position: "relative",
  },
  video: {
    position: "absolute",
    top: 0,
    left: 0,
    bottom: 0,
    right: 0,
    width: "100%",
    height: "100%",
  },
  loadingContainer: {
    height: 112,
    backgroundColor: "gray",
    width: "100%",
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 10,
    opacity: 0.5,
  },
  noHighlights: {
    justifyContent: "center",
    alignItems: "center",
  },
  noHighlightsText: {
    color: "white",
    textAlign: "center",
  },
});

ProfileHighlights.propTypes = {
  profileHighlights: PropTypes.array,
  userId: PropTypes.string,
  setVisible: PropTypes.func,
  setImageViewData: PropTypes.func,
  videoRef: PropTypes.object,
  isNonPlayer: PropTypes.bool,
  textColor: PropTypes.string,
  ownProfile: PropTypes.bool,
  refreshing: PropTypes.bool,
  onRefresh: PropTypes.func,
  fetchingHighlight: PropTypes.bool,
  fetchingMore: PropTypes.bool,
};

// ProfileHighlights.defaultProps = {
//   refreshing: false,
//   onRefresh: () => {},
//   profileHighlights: [],
//   isNonPlayer: false,
//   ownProfile: false,
//   fetchingHighlight: false,
//   fetchingMore: false
// };

export default React.memo(ProfileHighlights);
