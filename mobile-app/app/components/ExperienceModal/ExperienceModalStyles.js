import { StyleSheet } from "react-native";
import { HeightSize, Size } from "../../res/Size";
import { Colors } from "react-native/Libraries/NewAppScreen";

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  addButton: {
    borderRadius: Size(11),
    width: Size(11),
    height: Size(11),
    justifyContent: "center",
    alignItems: "center",
    marginVertical: HeightSize(2),
    alignSelf: "center",
    // backgroundColor: "red"
  },
  addButtonText: {
    color: Colors.black,
    fontWeight: "bold",
    fontSize: Size(8),
    textAlign: "center",
  },
  modalWrapper: {
    paddingTop: Size(10),
    maxHeight: "90%",
  },
  modalTitle: {
    fontSize: Size(4),
    fontFamily: "Bold",
    textAlign: "center",
    textTransform: "uppercase",
    marginBottom: HeightSize(2),
    lineHeight: HeightSize(3),
  },
  inputGroup: {
    // marginBottom: HeightSize(1),
    flex: 1,
  },
  textInput: {
    borderBottomWidth: 1,
    borderColor: Colors.black,
    height: HeightSize(4),
    fontFamily: "Regular",
    fontSize: Size(3.5),
  },
  label: {
    fontFamily: "Regular",
    fontSize: Size(3.2),
    color: "gray",
    marginBottom: HeightSize(0.2),
  },
  errorText: {
    color: "red",
    fontFamily: "Regular",
    fontSize: Size(3.2),
    textAlign: "center",
  },
  grid: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  saveButtonText: {
    color: Colors.white,
    fontSize: Size(5.5),
    fontFamily: "Regular",
    textAlign: "center",
  },
  saveButton: {
    backgroundColor: Colors.black,
    paddingVertical: Size(2),
    paddingHorizontal: Size(14),
    borderRadius: 100,
    marginTop: HeightSize(3),
    borderWidth: 1,
    alignSelf: "center",
    marginBottom: HeightSize(3),
  },
  saveButtonDisabled: {
    backgroundColor: "gray",
  },
  dropdown: {
    maxHeight: 100, // Limit dropdown height
    backgroundColor: "#fff",
    borderWidth: 1,
    borderColor: "#ccc",
    borderRadius: 5,
    marginTop: 5,
  },
  dropdownItem: {
    paddingVertical: HeightSize(1),
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginHorizontal: Size(2),
  },
  dropdownText: {
    fontSize: Size(3.5),
  },
  formWrapper: {
    gap: Size(4),
    marginBottom: HeightSize(4),
  },
});

export default styles;
