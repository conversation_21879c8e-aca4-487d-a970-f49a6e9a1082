// REACT //
import React, { useCallback, useEffect, useState } from "react";
import { useSelector } from "react-redux";

// COMPONENTS //
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Modal,
  ActivityIndicator,
  StyleSheet,
  Image,
  FlatList,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from "react-native";
import { Images } from "../../res/Images";

// SERVICES //
import ExperienceApi from "../../services/ExperienceApi";

// CONSTANTS //
import { notifyError, notifySuccess } from "../../constants/misc";

// OTHERS //
import _debounce from "lodash/debounce";
import SelectWithPicture from "../SelectWithPicture";
import { HeightSize, Size } from "../../res/Size";
import styles from "./ExperienceModalStyles";
import { dispatch } from "../../redux/store";
import { PlusIcon } from "../../res/Svg";
import TeamDropdown from "../TeamsDropDown";
import { commonStyles } from "../../res/CommonStyles";

let experience = {
  teamName: "",
  seasonName: "",
  appearances: 0,
  goals: 0,
  assists: 0,
  cleanSheets: 0,
  teamLogo: "",
  minsPlayed: 0,
};

const ExperienceModal = ({ userType, onExperienceUpdate }) => {
  const [seasonNameError, setSeasonNameError] = useState("");
  const [clubNameError, setClubNameError] = useState("");
  const [teamsError, setTeamsError] = useState("");

  const [userExperience, setUserExperience] = useState(experience);
  const [clubs, setClubs] = useState([]);
  const [selectClub, setSelectClub] = useState({});
  const [searchValue, setSearchValue] = useState("");
  const [teamLogo, setTeamLogo] = useState("");
  const [teams, setTeams] = useState([]);
  const [selectTeam, setSelectTeam] = useState({});
  const [modalVisible, setModalVisible] = useState(false);
  const [filteredTeams, setFilteredTeams] = useState([]);

  const currentYear = new Date().getFullYear();
  const century = Math.floor(currentYear / 100) * 100;

  const seasonCheck = /^(\d{2})\/(\d{2})$/;

  const { userInfo } = useSelector((state) => state.auth.authUser);
  const { teamsModel } = useSelector(({ team }) => ({
    teamsModel: team.allTeams,
  }));

  const handleSeasonName = (text) => {
    setUserExperience({ ...userExperience, seasonName: text });

    // Regular expression to check if input is in the "YY/YY" format
    const seasonCheck = /^\d{2}\/\d{2}$/;

    // Check if the format matches
    if (seasonCheck.test(text)) {
      const [start, end] = text.split("/").map(Number);

      // Get the current year
      const currentYear = new Date().getFullYear();

      // Assuming 'century' is 2000, so years 00-49 map to 2000-2049 and 50-99 map to 1950-1999
      const century = 2000;

      // Convert YY to full year, assuming start year < 50 maps to 2000-2049, and > 50 maps to 1900-1999
      const fullStartYear = start < 50 ? century + start : 1900 + start;
      const fullEndYear = end < 50 ? century + end : 1900 + end;

      // Ensure the end year is exactly one year after the start year
      if (fullEndYear === fullStartYear + 1) {
        // Ensure that the fullEndYear is valid (not in the future)
        if (fullStartYear >= 2000 && fullEndYear <= currentYear + 1) {
          setSeasonNameError(""); // No error, valid season name
        } else {
          setSeasonNameError("The season cannot be in the future.");
        }
      } else {
        setSeasonNameError(
          "The second year must be one year after the first year (e.g., 21/22)."
        );
      }
    } else {
      setSeasonNameError("Must be in YY/YY format.");
    }
  };

  const fetchClubs = async () => {
    const res = await dispatch.club.getAllClubs();
    // Update the state with the sorted list
    setClubs(res);
  };

  const handleSearchValueChange = (text) => {
    setSearchValue(text);
    setClubNameError(""); // Clear club name error on text change
  };

  useEffect(() => {
    fetchClubs();
  }, []);

  if (userInfo?.teamName) {
    experience["teamName"] = userInfo.teamName || "";
  }

  const handleClubSelection = async (clubId) => {
    setClubNameError(""); // Clear the error message
    const res = await dispatch.team.getTeamsByClubId(clubId);

    setTeams(res?.data || []);
  };
  const getTeamsBasedOnSelectedClub = useCallback(
    _debounce(handleClubSelection, 100),
    []
  );

  const validateInput = (text) => {
    const regex = /^\d{0,3}$/;
    return regex.test(text);
  };

  const handleAppearance = (text) => {
    if (validateInput(text)) {
      setUserExperience({ ...userExperience, appearances: text });
    }
  };
  const handleGoals = (text) => {
    if (validateInput(text)) {
      setUserExperience({ ...userExperience, goals: text });
    }
  };
  const handleAssist = (text) => {
    if (validateInput(text)) {
      setUserExperience({ ...userExperience, assists: text });
    }
  };
  const handleCleansheet = (text) => {
    if (validateInput(text)) {
      setUserExperience({ ...userExperience, cleanSheets: text });
    }
  };
  const handleMinutesPlayed = (text) => {
    // The regex `/^\d*$/` ensures that the input only contains digits (0-9)
    if (/^\d{0,4}$/.test(text)) {
      setUserExperience({ ...userExperience, minsPlayed: text });
    }
  };

  const handleSubmit = async () => {
    if (!selectClub?.id || !selectClub?.clubName) {
      setClubNameError("Please select a Club/Organisation.");
      return;
    }

    if (teams.length > 0 && (!selectTeam?.id || !selectTeam?.teamName)) {
      setTeamsError("Please select a team for this club.");
      return;
    }

    if (!userExperience.seasonName.trim()) {
      setSeasonNameError("Please enter a season.");
      return;
    }

    const mergedValues = {
      teamName: selectTeam?.teamName || selectClub?.clubName || searchValue,
      teamLogo: selectTeam?.logoUrl || teamLogo,
      userId: userInfo.id,
      appearances: +userExperience.appearances,
      goals: +userExperience.goals,
      assists: +userExperience.assists,
      cleanSheets: +userExperience.cleanSheets,
      seasonName: userExperience.seasonName,
      teamId: selectTeam?.id || "",
      clubId: selectClub?.id || "",
      minsPlayed: +userExperience.minsPlayed,
    };

    try {
      const response = await ExperienceApi.addExperience(mergedValues);

      if (response.data.status === 1) {
        // Fetch the latest profile to ensure updated experience is shown
        await dispatch.user.getUserProfile({ id: userInfo.id });

        // Close modal and reset form
        setModalVisible(false);
        resetUserExperience();
        notifySuccess("Experience created successfully!");
        if (onExperienceUpdate) {
          onExperienceUpdate();
        }
      } else {
        notifyError("Failed to create experience.");
      }
    } catch (error) {
      notifyError(error?.response?.message || error.message);
      console.error(error?.response?.message || error.message);
    }
  };

  const resetUserExperience = () => {
    setUserExperience({
      teamName: "",
      seasonName: "",
      appearances: 0,
      goals: 0,
      assists: 0,
      cleanSheets: 0,
      teamLogo: "",
      minsPlayed: 0,
    });
    setSelectClub({});
    setSearchValue("");
    setSelectTeam({});
    setTeamLogo("");
    setClubNameError("");
    setTeamsError("");
    setTeams([]);
    setSeasonNameError("");
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity
        onPress={() => setModalVisible(true)}
        style={styles.addButton}
      >
        <View>
          <Image
            source={
              userType == "NON_PLAYER" ? Images.plusBlack : Images.plusWhite
            }
            resizeMode="contain"
            style={{
              maxHeight: 30,
              maxWidth: 30,
              height: 30,
              width: 30,
            }}
          />
        </View>
      </TouchableOpacity>

      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={commonStyles.modalBackdrop}>
          <KeyboardAvoidingView
            behavior={Platform.OS === "ios" ? "padding" : undefined} // "padding" for iOS devices to avoid overlap
            style={[commonStyles.modalContainer, styles.modalWrapper]}
          >
            <TouchableOpacity
              onPress={() => {
                resetUserExperience();
                setModalVisible(false);
              }}
              style={commonStyles.modalCloseWrapper}
            >
              <Image
                source={Images.cross}
                style={commonStyles.modalCloseIcon}
              />
            </TouchableOpacity>

            <ScrollView showsVerticalScrollIndicator={false}>
              <Text style={styles.modalTitle}>Add New Experience</Text>

              <View style={styles.formWrapper}>
                <View>
                  <Text style={styles.label}>Club / Organisation</Text>
                  <SelectWithPicture
                    setSelectClub={setSelectClub}
                    selectClub={selectClub}
                    searchValue={searchValue}
                    setSearchValue={handleSearchValueChange}
                    setTeams={setTeams}
                    getTeamsBasedOnSelectedClub={getTeamsBasedOnSelectedClub}
                    setTeamLogo={setTeamLogo}
                    setSelectTeam={setSelectTeam}
                    clubModel={clubs}
                    userInfo={userInfo}
                  />
                  {clubNameError !== "" && (
                    <Text style={styles.errorText}>{clubNameError}</Text>
                  )}
                </View>

                {teams?.length > 0 && (
                  <View>
                    <Text style={styles.label}>Teams</Text>
                    <TeamDropdown
                      teams={teams}
                      selectTeam={selectTeam}
                      setSelectTeam={(team) => {
                        setSelectTeam(team);
                        setTeamsError(""); // Clear error when a team is selected
                      }}
                    />
                    {teamsError !== "" && (
                      <Text style={styles.errorText}>{teamsError}</Text>
                    )}
                  </View>
                )}

                <View>
                  <Text style={styles.label}>Season (YY/YY)</Text>
                  <TextInput
                    onChangeText={handleSeasonName}
                    value={userExperience.seasonName}
                    style={styles.textInput}
                    placeholder="Season (YY/YY)"
                  />
                  {seasonNameError || userExperience.seasonName === "" ? (
                    <Text style={styles.errorText}>{seasonNameError}</Text>
                  ) : null}
                </View>

                {userInfo.userType !== "NON_PLAYER" && (
                  <>
                    <View style={styles.grid}>
                      <View style={styles.inputGroup}>
                        <Text style={styles.label}>Appearance</Text>
                        <TextInput
                          keyboardType="numeric"
                          onChangeText={handleAppearance}
                          value={userExperience.appearances.toString()}
                          style={styles.textInput}
                        />
                      </View>
                      <View
                        style={[styles.inputGroup, { marginLeft: Size(7) }]}
                      >
                        <Text style={styles.label}>Goals</Text>
                        <TextInput
                          keyboardType="numeric"
                          onChangeText={handleGoals}
                          value={userExperience.goals.toString()}
                          style={styles.textInput}
                        />
                      </View>
                    </View>
                    <View style={styles.grid}>
                      <View style={styles.inputGroup}>
                        <Text style={styles.label}>Assists</Text>
                        <TextInput
                          keyboardType="numeric"
                          onChangeText={handleAssist}
                          value={userExperience.assists.toString()}
                          style={styles.textInput}
                        />
                      </View>
                      <View
                        style={[styles.inputGroup, { marginLeft: Size(7) }]}
                      >
                        <Text style={styles.label}>Clean Sheet</Text>
                        <TextInput
                          keyboardType="numeric"
                          onChangeText={handleCleansheet}
                          value={userExperience.cleanSheets.toString()}
                          style={styles.textInput}
                        />
                      </View>
                    </View>

                    {/* Minutes Played */}
                    <View style={styles.grid}>
                      <View style={[styles.inputGroup]}>
                        <Text style={styles.label}>Minutes Played</Text>
                        <TextInput
                          keyboardType="numeric"
                          onChangeText={handleMinutesPlayed}
                          value={String(userExperience.minsPlayed)}
                          style={styles.textInput}
                        />
                      </View>
                      <View
                        style={[styles.inputGroup, { marginLeft: Size(7) }]}
                      />
                    </View>
                  </>
                )}
              </View>

              <TouchableOpacity
                onPress={handleSubmit}
                disabled={!!seasonNameError}
                style={[
                  styles.saveButton,
                  {
                    marginTop:
                      userInfo.userType == "NON_PLAYER" ? HeightSize(2) : 0,
                  },
                ]}
              >
                <Text style={[styles.saveButtonText]}>Save</Text>
              </TouchableOpacity>
            </ScrollView>
          </KeyboardAvoidingView>
        </View>
      </Modal>
    </View>
  );
};

export default ExperienceModal;
