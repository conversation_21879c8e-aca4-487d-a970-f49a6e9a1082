import React, { useEffect, useState } from "react";
import { View, Text, TextInput, TouchableOpacity, StyleSheet, ActivityIndicator, Image } from "react-native";
import { useFormik } from "formik";
import { useSelector } from "react-redux";
import { useNavigation } from "@react-navigation/native";
import ReferenceApi from "../../services/ReferenceApi";
import { notifyError, notifySuccess, referenceFormSchema } from "../../constants/misc";
import RolesPopupModal from "../DropDown/DropDown";
import Button from "../Button/Button";
import { Colors } from "../../res/Colors";
import { HeightSize, Size } from "../../res/Size";
import { commonStyles } from "../../res/CommonStyles";

const EditReferenceForm = ({ referenceId }) => {
    const [submitting, setSubmitting] = useState(false);
    const [selectedRoles, setSelectedRoles] = useState([]);
    const [visible, setVisible] = useState(false);
    const [reference, setReference] = useState(null);

    const navigation = useNavigation();

    const getUserReference = async () => {
        try {
            const {
                data: { data },
            } = await ReferenceApi.getReferenceById(referenceId);
            setReference(data);
        } catch (error) {
      console.error(error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        getUserReference();
    }, []);

    const onSubmit = async (values) => {
        const reference = {
            message: values.message,
            fromRole: values.positionAtTime,
            relationship: values.relationship,
        };

        try {
            setSubmitting(true);
            await ReferenceApi.updateReference(referenceId, reference);
            notifySuccess("Submitted");
            navigation.goBack();
        } catch (error) {
            notifyError(error.message);
        } finally {
            setSubmitting(false);
        }
    };

    useEffect(() => {
        if (reference) {
            // Update form values when reference data is loaded
            setFieldValue('relationship', reference.relationship);
            setFieldValue('message', reference.message);

            // Handle the fromRole array - set both the field value and selected roles state
            if (Array.isArray(reference.fromRole) && reference.fromRole.length > 0) {
                setFieldValue('positionAtTime', reference.fromRole); // Set the whole array
                setSelectedRoles(reference.fromRole); // Update the selectedRoles state as well
            } else if (reference.fromRole) {
                // Handle case where it might be a single string value
                const roleArray = [reference.fromRole];
                setFieldValue('positionAtTime', roleArray);
                setSelectedRoles(roleArray);
            }
        }
    }, [reference]);

    const initialValues = {
        relationship: "",
        positionAtTime: "",
        message: "",
    };

    const {
        values,
        errors,
        touched,
        handleBlur,
        handleChange,
        handleSubmit,
        setFieldValue,
    } = useFormik({
        initialValues,
        onSubmit,
        validationSchema: referenceFormSchema,
    });

    const handleRoleChange = (positionAtTime) => {
        setFieldValue("positionAtTime", positionAtTime);
    };

    const toggleRole = (role) => {
        let updatedRoles = [...selectedRoles];
        if (updatedRoles.includes(role.name)) {
            updatedRoles = updatedRoles.filter((r) => r !== role.name);
        } else {
            updatedRoles.push(role.name);
        }
        setSelectedRoles(updatedRoles);
        handleRoleChange(updatedRoles);
        setVisible(false);
    };

    if (!reference) {
        // Return a loading or fallback UI while data is being fetched
        return <View
            style={{
                height: 300,
                justifyContent: "center",
                alignItems: "center",
            }}
        >
            <ActivityIndicator size="large" color={Colors.green} />
        </View>;
    }

    return (
        <View style={styles.container}>
            <View>
                <Text style={styles.title}>Edit a reference for {reference?.toProfileName}</Text>
                <Text style={styles.subtitle}>
                    This reference will appear on {reference?.toProfileName}'s Profile
                </Text>
            </View>


            <View style={styles.form}>
                <View style={styles.userInfo}>
                    <Image
                        source={{ uri: reference?.toProfilePhoto }}
                        style={styles.userImage}
                    />
                    <Text style={styles.userName}>
                        {reference?.toProfileName}
                    </Text>
                </View>

                <View style={styles.inputGroup}>
                    <Text style={styles.label}>Relationship*</Text>
                    <TextInput
                        style={styles.input}
                        placeholder="E.g. Coach"
                        value={values.relationship}
                        onChangeText={handleChange("relationship")}
                        onBlur={handleBlur("relationship")}
                    />
                    {errors.relationship && touched.relationship && (
                        <Text style={styles.errorText}>{errors.relationship}</Text>
                    )}
                </View>

                <View style={styles.inputGroup}>
                    <RolesPopupModal
                        visible={visible}
                        roles={nonPlayers}
                        toggleRole={toggleRole}
                        selectedRole={selectedRoles}
                        onPress={() => setVisible(false)}
                        showTick={true}
                        onClick={() => setVisible(!visible)}
                        inEditProfile={true}
                        container={{ marginLeft: 0, paddingVertical: HeightSize(1.2) }}
                        line={{ borderBottomColor: Colors.black }}
                        label={"Position at the time*"}
                        itemText={[commonStyles.regularText, { marginLeft: 0, opacity: 0.5 }]}

                    />
                    {errors.positionAtTime && touched.positionAtTime && (
                        <Text style={styles.errorText}>{errors.positionAtTime}</Text>
                    )}
                </View>

                <View style={styles.inputGroup}>
                    <Text style={styles.label}>Add reference*</Text>
                    <TextInput
                        style={styles.input}
                        placeholder="Write your reference here..."
                        value={values.message}
                        onChangeText={handleChange("message")}
                        onBlur={handleBlur("message")}
                        multiline={true}
                    />
                    {errors.message && touched.message && (
                        <Text style={styles.errorText}>{errors.message}</Text>
                    )}
                </View>
            </View>

            <Button
                isLoading={submitting}
                onPress={handleSubmit}
                title={"POST"}
                backgroundColor={Colors.black}
                textStyleInsideButton={{ color: Colors.white }}
                containerStyle={{ marginTop: HeightSize(4) }}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        padding: 16,
        backgroundColor: "#fff",
    },
    title: {
        fontSize: Size(5),
        fontWeight: "bold",
        color: "#000",
    },
    subtitle: {
        fontSize: Size(3.6),
        color: "#000",
        opacity: 0.5,
    },
    form: {
        marginTop: HeightSize(2),
    },
    inputGroup: {
        marginBottom: HeightSize(2.5),
    },
    label: {
        fontSize: Size(4),
        color: "#000",
        opacity: 0.5,
    },
    input: {
        borderBottomWidth: 1,
        borderBottomColor: "#000",
        paddingVertical: 8,
        fontSize: Size(4),
        color: "#333",
    },
    errorText: {
        color: "red",
        fontSize: Size(3.5),
    },
    userInfo: {
        flexDirection: 'row',
        alignItems: 'center',
        marginVertical: HeightSize(2),
    },
    userImage: {
        width: Size(12),
        height: Size(12),
        borderRadius: Size(6),
        marginRight: Size(3),
    },
    userName: {
        fontSize: Size(4.4),
        color: 'black',
        fontFamily: 'PoppinsBold',
    },
});

export default EditReferenceForm;

const nonPlayers = [
    {
        id: 1,
        name: "Agent",
    },
    {
        id: 2,
        name: "Coach",
    },
    {
        id: 3,
        name: "Scout",
    },
    {
        id: 4,
        name: "Academy Manager / Director",
    },
    {
        id: 5,
        name: "Chief Scout",
    },
    {
        id: 6,
        name: "Sporting Director",
    },
    {
        id: 7,
        name: "Player Care",
    },
];