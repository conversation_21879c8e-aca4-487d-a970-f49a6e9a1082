import React, { useState } from "react";
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  Platform,
} from "react-native";
import { HeightSize, Size } from "../../res/Size";
import { Colors } from "../../res/Colors";
import { Images } from "../../res/Images";
import { useNavigation } from "@react-navigation/native";
import { Edit, RedDeleteIcon } from "../../res/Svg";
import { useSelector } from "react-redux";

const ReferenceCard = ({
  name,
  title,
  date,
  message,
  photo,
  relationship,
  userId,
  type,
  onDeletePress,
  referenceId,
  toUserId,
  fromProfileId,
  toProfileId,
}) => {
  const navigation = useNavigation();

  const { loggedUserId } = useSelector(({ auth: { authUser }, user }) => ({
    loggedUserId: authUser?.userInfo?.id || "",
    authUser,
  }));

  return (
    <View style={styles.cardContainer}>
      <View style={styles.card}>
        <TouchableOpacity
          onPress={() => {
            if (!userId) {
              console.error("Error: userId is undefined or null");
              return;
            }
            navigation.navigate("MyProfileScreenStack", {
              screen: "MyProfile",
              params: { userId: userId },
            });
          }}
        >
          {!photo ? (
            <Image source={Images.profile1} style={styles.photo} />
          ) : (
            <Image source={{ uri: photo }} style={styles.photo} />
          )}
        </TouchableOpacity>

        <View style={styles.textContainer}>
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              gap: 10,
            }}
          >
            <Text
              onPress={() => {
                if (!userId) {
                  console.error("Error: userId is undefined or null");
                  return;
                }
                navigation.navigate("MyProfileScreenStack", {
                  screen: "MyProfile",
                  params: { userId: userId },
                });
              }}
              style={styles.name}
            >
              {name}
            </Text>

            {toUserId === loggedUserId && (
              <View
                style={{
                  flexDirection: "row",
                  gap: 16,
                }}
              >
                {type === "given" && (
                  <TouchableOpacity
                    onPress={() =>
                      navigation.navigate("EditReferenceFormPage", {
                        referenceId: referenceId,
                      })
                    }
                  >
                    <Edit
                      width={Platform.isPad ? 40 : 16}
                      height={Platform.isPad ? 40 : 16}
                    />
                  </TouchableOpacity>
                )}

                <TouchableOpacity onPress={onDeletePress}>
                  <RedDeleteIcon
                    width={Platform.isPad ? 40 : 16}
                    height={Platform.isPad ? 40 : 16}
                  />
                </TouchableOpacity>
              </View>
            )}
          </View>
          <Text style={{ fontFamily: "Regular", color: Colors.black }}>
            {relationship} {title ? `/ ${title}` : ""}
          </Text>
          <Text style={styles.date}>{date}</Text>
          <Text style={styles.message}>{message}</Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  cardContainer: {
    paddingTop: HeightSize(1.5),
  },
  card: {
    flexDirection: "row",
    borderBottomWidth: 1,
    borderBottomColor: "#ccc",
    paddingBottom: HeightSize(1),
  },
  photo: {
    width: Size(14),
    height: Size(14),
    borderRadius: Size(7),
  },
  textContainer: {
    paddingLeft: Size(5),
    flex: 1,
  },
  name: {
    fontFamily: "favelaMedium",
    fontSize: Size(4.7),
    flex: 1,
    lineHeight: Size(5.8),
    marginBottom: 4,
  },
  date: {
    color: "rgba(0, 0, 0, 0.4)",
    marginBottom: HeightSize(1),
    fontSize: Size(4.5),
    fontFamily: "Regular",
  },
  message: {
    fontFamily: "Regular",
    fontSize: Size(4.5),
    width: Size(70),
  },
});

export default ReferenceCard;
