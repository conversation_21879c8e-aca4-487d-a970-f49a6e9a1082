import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
} from "react-native";
import { useFormik } from "formik";
import { useSelector } from "react-redux";
import { useNavigation } from "@react-navigation/native";
import ReferenceApi from "../../services/ReferenceApi";
import {
  notifyError,
  notifySuccess,
  referenceFormSchema,
} from "../../constants/misc";
import SearchUserList from "../SearchUserList/SearchUserList";
import RolesPopupModal from "../DropDown/DropDown";
import Button from "../Button/Button";
import { Colors } from "../../res/Colors";
import { HeightSize, Size } from "../../res/Size";
import { commonStyles } from "../../res/CommonStyles";

const ReferenceForm = ({ toUserDetails, isSameUser }) => {
  const [submitting, setSubmitting] = useState(false);
  const [toSelectedUser, setToSelectedUser] = useState(toUserDetails);
  const [selectedRoles, setSelectedRoles] = useState([]);
  const [visible, setVisible] = useState(false);

  const navigation = useNavigation();
  const { userInfo } = useSelector((state) => state.auth.authUser);

  const onSubmit = async (values) => {
    const reference = {
      fromProfileName: `${userInfo.firstName} ${userInfo.lastName}`,
      fromProfilePhoto: userInfo.photoUrl || "",
      fromProfileId: userInfo.id,
      toProfileName: `${toSelectedUser?.firstName} ${toSelectedUser?.lastName}`,
      toProfileId: toSelectedUser?.id,
      toProfilePhoto: toSelectedUser.photoUrl || "",
      message: values.message,
      toRole: toSelectedUser?.nonPlayerRole || [],
      fromRole: values.positionAtTime,
      relationship: values.relationship,
    };

    try {
      setSubmitting(true);
      await ReferenceApi.postReferences(reference);
      notifySuccess("Submitted");
      navigation.goBack();
    } catch (error) {
      notifyError(error.message);
    } finally {
      setSubmitting(false);
    }
  };

  const initialValues = {
    relationship: "",
    positionAtTime: "",
    message: "",
    toUserId: "",
  };

  const {
    values,
    errors,
    touched,
    handleBlur,
    handleChange,
    handleSubmit,
    setFieldValue,
  } = useFormik({
    initialValues,
    onSubmit,
    validationSchema: referenceFormSchema,
  });

  const handleRoleChange = (positionAtTime) => {
    setFieldValue("positionAtTime", positionAtTime);
  };

  const onSelectUser = (userData) => {
    setToSelectedUser(userData);
    setFieldValue("toUserId", userData.id);
  };

  useEffect(() => {
    if (!isSameUser) {
      setToSelectedUser(toUserDetails);
      setFieldValue("toUserId", toUserDetails?.id);
    }
  }, [toUserDetails, isSameUser]);

  const toggleRole = (role) => {
    let updatedRoles = [...selectedRoles];
    if (updatedRoles.includes(role.name)) {
      updatedRoles = updatedRoles.filter((r) => r !== role.name);
    } else {
      updatedRoles.push(role.name);
    }
    setSelectedRoles(updatedRoles);
    handleRoleChange(updatedRoles);
    setVisible(false);
  };

  return (
    <View style={styles.container}>
      {isSameUser && (
        <View>
          <Text style={styles.title}>Write a reference for someone</Text>
          <Text style={styles.subtitle}>
            This reference will appear on their Profile
          </Text>
        </View>
      )}

      <View style={styles.form}>
        {isSameUser && (
          <View>
            <Text style={styles.label}>To:</Text>
            <SearchUserList
              selectedUser={toSelectedUser}
              onSelectUser={onSelectUser}
            />
            {errors.toUserId && touched.toUserId && (
              <Text style={styles.errorText}>{errors.toUserId}</Text>
            )}
          </View>
        )}

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Relationship*</Text>
          <TextInput
            style={styles.input}
            placeholder="E.g. Coach"
            value={values.relationship}
            onChangeText={handleChange("relationship")}
            onBlur={handleBlur("relationship")}
          />
          {errors.relationship && touched.relationship && (
            <Text style={styles.errorText}>{errors.relationship}</Text>
          )}
        </View>

        <View style={styles.inputGroup}>
          <RolesPopupModal
            visible={visible}
            roles={nonPlayers}
            toggleRole={toggleRole}
            selectedRole={selectedRoles}
            onPress={() => setVisible(false)}
            showTick={true}
            onClick={() => setVisible(!visible)}
            inEditProfile={true}
            container={{ marginLeft: 0, paddingVertical: HeightSize(1.2) }}
            line={{ borderBottomColor: Colors.black }}
            label={"Position at the time*"}
            itemText={[
              commonStyles.regularText,
              { marginLeft: 0, opacity: 0.5 },
            ]}
          />
          {errors.positionAtTime && touched.positionAtTime && (
            <Text style={styles.errorText}>{errors.positionAtTime}</Text>
          )}
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Add reference*</Text>
          <TextInput
            style={styles.input}
            placeholder="Write your reference here..."
            value={values.message}
            onChangeText={handleChange("message")}
            onBlur={handleBlur("message")}
            multiline={true}
          />
          {errors.message && touched.message && (
            <Text style={styles.errorText}>{errors.message}</Text>
          )}
        </View>
      </View>

      <Button
        isLoading={submitting}
        onPress={handleSubmit}
        title={"POST"}
        backgroundColor={Colors.black}
        textStyleInsideButton={{ color: Colors.white }}
        containerStyle={{ marginTop: HeightSize(4) }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: "#fff",
  },
  title: {
    fontSize: Size(5),
    fontWeight: "bold",
    color: "#000",
  },
  subtitle: {
    fontSize: Size(3.6),
    color: "#000",
    opacity: 0.5,
  },
  form: {
    marginTop: HeightSize(2),
  },
  inputGroup: {
    marginBottom: HeightSize(2.5),
  },
  label: {
    fontSize: Size(4),
    color: "#000",
    opacity: 0.5,
  },
  input: {
    borderBottomWidth: 1,
    borderBottomColor: "#000",
    paddingVertical: 8,
    fontSize: Size(4),
    color: "#333",
  },
  errorText: {
    color: "red",
    fontSize: Size(3.5),
  },
});

export default ReferenceForm;

const nonPlayers = [
  {
    id: 1,
    name: "Agent",
  },
  {
    id: 2,
    name: "Coach",
  },
  {
    id: 3,
    name: "Scout",
  },
  {
    id: 4,
    name: "Academy Manager / Director",
  },
  {
    id: 5,
    name: "Chief Scout",
  },
  {
    id: 6,
    name: "Sporting Director",
  },
  {
    id: 7,
    name: "Player Care",
},
];
