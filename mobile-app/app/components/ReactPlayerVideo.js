import { useCallback, useEffect, useRef, useState, useMemo, memo } from "react";
import {
  View,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  Platform,
  Animated,
  Text,
} from "react-native";
import { Mute, Volume } from "../res/Svg";
import { HeightSize, Size } from "../res/Size";
import { Ionicons } from "@expo/vector-icons";
import { useSelector } from "react-redux";
import { dispatch } from "../redux/store";
import { uploadedStreamsBaseUrl } from "../constants/misc";
import Video from "react-native-video";
import { useFocusEffect } from "@react-navigation/native";
import Slider from "@react-native-community/slider";
import FastImage from "react-native-fast-image";
import { HIGHLIGHT_MEDIA_HEIGHT } from "../screens/Feed/FeedStyle";
import { useAppStateEffect } from "../hooks/useAppStateEffect";
import { useIsFocused } from '@react-navigation/native';

export const videoContext = {
  Feed: "Feed",
  Detail: "Detail",
};

function getVideoSettingsForContext(context) {
  switch (context) {
    case videoContext.Feed:
      return {
        minBufferMs: 5000,
        maxBufferMs: 15000,
        bufferForPlaybackMs: 1000,
        bufferForPlaybackAfterRebufferMs: 2000,
        backBufferDurationMs: 10000,
      };
    case videoContext.Detail:
      return {
        minBufferMs: 10000,
        maxBufferMs: 30000,
        bufferForPlaybackMs: 1000,
        bufferForPlaybackAfterRebufferMs: 2000,
        backBufferDurationMs: 20000,
      };
    default:
      throw new Error(`Invalid video context: ${context}`);
  }
}

const formatTime = (seconds) => {
  const minutes = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${minutes}:${secs < 10 ? "0" : ""}${secs}`;
};

const formatVideoUrl = (highlight) => {
  if (highlight.id && highlight.streamUrl?.baseUrl) {
    return `${uploadedStreamsBaseUrl}/${
      highlight.streamUrl.key.split("--")[1]
    }/index.m3u8`;
  }
  return highlight.url || highlight.assetUrl || "";
};

const formatThumbnailUrl = (highlight) => {
  if (highlight.id && highlight.streamUrl?.baseUrl) {
    return `${uploadedStreamsBaseUrl}/${
      highlight.streamUrl.key.split("--")[1]
    }/thumbnail.png`;
  }
  return "";
};

const SHOW_CONTROLS_INITIAL_VALUE = true;

const ReactPlayerVideo = ({ highlight, isActive, context }) => {
  const videoRef = useRef(null);
  const fadeAnimRef = useRef(
    new Animated.Value(SHOW_CONTROLS_INITIAL_VALUE ? 1 : 0)
  ).current;
  const hideControlsTimeoutRef = useRef(null);
  const wasPlayingBeforeScrubbingRef = useRef(null);
  const volume = useSelector((state) => state.volume.volume);

  const [duration, setDuration] = useState(0);
  const roundedDuration = useMemo(() => Math.floor(duration), [duration]);
  const [currentTime, setCurrentTime] = useState(0);
  const [isLoaded, setIsLoaded] = useState(false);
  const [isPaused, setIsPaused] = useState(!isActive);
  const [isScrubbing, setIsScrubbing] = useState(false);
  const [isLoadingThumbnail, setIsLoadingThumbnail] = useState(true);
  const isMuted = useMemo(() => volume === 0, [volume]);
  const [showControls, setShowControls] = useState(SHOW_CONTROLS_INITIAL_VALUE);

  const isFocused = useIsFocused();

  const videoUrl = useMemo(() => formatVideoUrl(highlight), [highlight]);
  const thumbnailUrl = useMemo(
    () => formatThumbnailUrl(highlight),
    [highlight]
  );
  const videoSource = useMemo(
    () => ({
      uri: videoUrl,
      bufferConfig: getVideoSettingsForContext(context),
    }),
    [videoUrl, context]
  );

  useEffect(() => {
    return () => {
      if (hideControlsTimeoutRef.current) {
        clearTimeout(hideControlsTimeoutRef.current);
      }
    };
  }, []);

  const handlePause = useCallback(() => {
    setIsPaused(true);
  }, []);

  useAppStateEffect({
    onBackground: handlePause,
  });
  useFocusEffect(
    useCallback(() => {
      return handlePause;
    }, [])
  );

useEffect(() => {
  if (isActive && isFocused && isPaused) {
    setIsPaused(false);
  } else if ((!isActive || !isFocused) && !isPaused) {
    setIsPaused(true);
  }
}, [isActive, isFocused]);

  const togglePlayPause = useCallback(() => {
    setShowControls(true);

    Animated.timing(fadeAnimRef, {
      toValue: 1,
      duration: 0,
      useNativeDriver: true,
    }).start();

    if (hideControlsTimeoutRef.current) {
      clearTimeout(hideControlsTimeoutRef.current);
    }
    hideControlsTimeoutRef.current = setTimeout(() => {
      Animated.timing(fadeAnimRef, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }).start(() => setShowControls(false));
    }, 3000);

    setIsPaused((prev) => !prev);
  }, []);

  const handleLoad = useCallback((meta) => {
    setIsLoaded(true);
    setDuration(meta.duration);
  }, []);

  const handleProgress = useCallback(
    (progress) => {
      if (!isScrubbing) {
        const roundedTime = Math.floor(progress.currentTime);
        setCurrentTime((prevTime) => {
          return prevTime !== roundedTime ? roundedTime : prevTime;
        });
      }
    },
    [isScrubbing]
  );

  const handleVolumeToggle = useCallback(() => {
    dispatch.volume.toggleVolume();
  }, []);

  const handleScrubbingStart = useCallback(() => {
    wasPlayingBeforeScrubbingRef.current = !isPaused;
    setIsScrubbing(true);
    setIsPaused(true);

    if (hideControlsTimeoutRef.current) {
      clearTimeout(hideControlsTimeoutRef.current);
    }
  }, [isPaused]);

  const handleScrubbingChange = useCallback((value) => {
    const roundedValue = Math.floor(value);
    setCurrentTime((prevTime) => {
      return prevTime !== roundedValue ? roundedValue : prevTime;
    });
    videoRef?.current?.seek(roundedValue);
  }, []);

  const handleScrubbingComplete = useCallback((value) => {
    setIsScrubbing(false);

    const roundedValue = Math.floor(value);
    videoRef?.current?.seek(roundedValue);

    if (wasPlayingBeforeScrubbingRef.current) {
      setIsPaused(false);
    }
  }, []);

  const handleImageLoadStart = useCallback(() => {
    setIsLoadingThumbnail(true);
  }, []);

  const handleImageLoadEnd = useCallback(() => {
    setIsLoadingThumbnail(false);
  }, []);

  return (
    <View style={styles.container}>
      {!isLoaded && (
        <View style={styles.thumbnailContainer}>
          {thumbnailUrl ? (
            <FastImage
              style={styles.thumbnail}
              source={{
                uri: thumbnailUrl,
                priority: FastImage.priority.high,
                cache: FastImage.cacheControl.immutable,
              }}
              resizeMode={FastImage.resizeMode.cover}
              onLoadStart={handleImageLoadStart}
              onLoadEnd={handleImageLoadEnd}
            />
          ) : (
            <View style={styles.thumbnailPlaceholder} />
          )}
          {isLoadingThumbnail && (
            <View style={styles.spinnerContainer}>
              <ActivityIndicator color="#fff" />
            </View>
          )}
        </View>
      )}
      <TouchableOpacity
        onPress={togglePlayPause}
        style={styles.videoContainer}
        activeOpacity={0.9}
      >
        <Video
          ref={videoRef}
          ignoreSilentSwitch="ignore"
          muted={isMuted || isPaused}
          onLoad={handleLoad}
          onProgress={handleProgress}
          progressUpdateInterval={1000}
          paused={isPaused}
          repeat={true}
          resizeMode="contain"
          source={videoSource}
          style={[styles.video, { opacity: isLoaded ? 1 : 0 }]}
          volume={volume}
        />
        {isLoaded &&
          (!isPaused ? (
            <TouchableOpacity
              onPress={togglePlayPause}
              style={styles.overlay}
            />
          ) : (
            !isScrubbing && (
              <TouchableOpacity
                onPress={togglePlayPause}
                style={styles.overlayCenter}
              >
                <View style={styles.playButton}>
                  <Ionicons
                    name="play"
                    size={Size(9)}
                    style={{
                      marginLeft: Size(1),
                    }}
                    color="black"
                  />
                </View>
              </TouchableOpacity>
            )
          ))}
      </TouchableOpacity>
      <Animated.View
        style={[styles.controlsOverlay, { opacity: fadeAnimRef }]}
        pointerEvents={showControls ? "auto" : "none"}
      >
        <View style={styles.progressContainer}>
          <Text style={styles.timeText}>{formatTime(currentTime)}</Text>
          <Slider
            style={styles.progressBar}
            minimumValue={0}
            maximumValue={roundedDuration}
            value={currentTime}
            minimumTrackTintColor="#FFFFFF"
            maximumTrackTintColor="rgba(255, 255, 255, 0.5)"
            tapToSeek
            step={1}
            thumbTintColor="#FFFFFF"
            thumbImage={
              Platform.OS === "ios"
                ? require("../../assets/pointer.png")
                : undefined
            }
            onSlidingStart={handleScrubbingStart}
            onValueChange={handleScrubbingChange}
            onSlidingComplete={handleScrubbingComplete}
          />
          <Text style={[styles.timeText, { textAlign: "right" }]}>
            {formatTime(duration)}
          </Text>
        </View>
      </Animated.View>
      <TouchableOpacity
        onPress={handleVolumeToggle}
        style={styles.volumeButton}
      >
        {isMuted ? (
          <Mute
            width={Platform.isPad ? 27 : 20}
            height={Platform.isPad ? 27 : 18}
          />
        ) : (
          <Volume
            width={Platform.isPad ? 28 : 18}
            height={Platform.isPad ? 28 : 20}
          />
        )}
      </TouchableOpacity>
    </View>
  );
};

const VIDEO_CONTROLS_OVERLAY_HEIGHT = HeightSize(8);

const styles = StyleSheet.create({
  container: {
    width: "100%",
    height: HIGHLIGHT_MEDIA_HEIGHT,
    position: "relative",
    overflow: "hidden",
  },
  spinnerContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "transparent",
    zIndex: 15,
  },
  thumbnail: {
    ...StyleSheet.absoluteFillObject,
    resizeMode: "cover",
  },
  videoContainer: {
    ...StyleSheet.absoluteFillObject,
    overflow: "hidden",
    backgroundColor: "black",
  },
  video: {
    flex: 1,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: "center",
    alignItems: "center",
    zIndex: 40,
  },
  overlayCenter: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: "center",
    alignItems: "center",
  },
  playButton: {
    width: Size(16),
    height: Size(16),
    backgroundColor: "rgba(255, 255, 255, 0.5)",
    borderRadius: Size(16),
    justifyContent: "center",
    alignItems: "center",
  },
  controlsOverlay: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    height: VIDEO_CONTROLS_OVERLAY_HEIGHT,
    zIndex: 50,
    alignItems: "center",
    justifyContent: "center",
  },
  progressContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: Size(4),
    gap: Size(3),
  },
  progressBar: {
    flex: 1,
    height: 40,
  },
  timeText: {
    color: "#FFFFFF",
    fontSize: 12,
    width: Size(9),
  },
  volumeButton: {
    position: "absolute",
    bottom: VIDEO_CONTROLS_OVERLAY_HEIGHT + Size(1),
    right: Size(5),
    zIndex: 40,
    backgroundColor: "#4B5563",
    width: Size(8),
    height: Size(8),
    borderRadius: Size(4),
    justifyContent: "center",
    alignItems: "center",
  },
  thumbnailContainer: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: "#000",
    zIndex: 10,
  },
  thumbnailPlaceholder: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: "#1a1a1a",
  },
});

const arePropsEqual = (prevProps, nextProps) => {
  if (
    prevProps.isActive !== nextProps.isActive ||
    prevProps.highlight.id !== nextProps.highlight.id ||
    prevProps.context !== nextProps.context
  ) {
    return false;
  }

  return true;
};

const memoizedReactPlayerVideo = memo(ReactPlayerVideo, arePropsEqual);
export default memoizedReactPlayerVideo;
