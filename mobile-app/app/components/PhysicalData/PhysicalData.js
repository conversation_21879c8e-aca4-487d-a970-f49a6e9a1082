// REACT //
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";

// REACT NATIVE //
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Modal,
  TouchableWithoutFeedback,
  Image,
} from "react-native";

// SERVICES //
import UserApi from "../../services/UserApi";

// CONSTANTS //
import { notifyError, notifySuccess, removeEmpty } from "../../constants/misc";

// OTHERS //
import _debounce from "lodash/debounce";
import isEqual from "lodash/isEqual";
import { HeightSize, Size } from "../../res/Size";
import { Colors } from "../../res/Colors";
import { commonStyles } from "../../res/CommonStyles";
import { max } from "lodash";
import { Images } from "../../res/Images";

const PhysicalData = ({ userInfo }) => {
  // Define States
  const [existintPhysicalData, setExistintPhysicalData] = useState(null);
  const loggedUserInfo = useSelector(({ auth }) => auth.authUser.userInfo);
  const [data, setData] = useState(null);
  const [focusedInput, setFocusedInput] = useState(null);
  const [showPhysicalData, setShowPhysicalData] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);

  // UI Variables
  const isSameUser = userInfo?.id === loggedUserInfo?.id;

  // Styles
  const inputStyle = (inputName) => [
    styles.input,
    focusedInput === inputName && {
      borderColor: Colors.green,
      borderBottomWidth: 2,
      borderRadius: 0,
    },
  ];

  // Helper Functions

  /** Function to Get Player Physical Data */
  const getPlayerPhysicalData = async () => {
    try {
      const {
        data: { data: physicalData },
      } = await UserApi.getUserPhysicalData(userInfo?.id);

      if (physicalData) {
        const { updatedAt, createdAt, id, ...responseData } = physicalData;
        setExistintPhysicalData(responseData);
      }
      setData({
        userId: loggedUserInfo?.id,
        sprintTime5M: physicalData?.sprintTime5M || "",
        sprintTime10M: physicalData?.sprintTime10M || "",
        sprintTime20M: physicalData?.sprintTime20M || "",
        maxSpeed: physicalData?.maxSpeed || "",
        runTime1Kmins: physicalData?.runTime1Kmins || "",
        runTime1Ksecs: physicalData?.runTime1Ksecs || "",
        matchAvgDistance: physicalData?.matchAvgDistance || "",
        counterMovementJump: physicalData?.counterMovementJump || "",
      });
    } catch (error) {
      notifyError("An error occured");
    }
  };

  // Helper function to check if all fields are empty
  const areFieldsEmpty = () => {
    if (!data) return true; // If data is null or undefined, return true
    const { userId, ...fields } = data; // Exclude userId from the check
    return Object.values(fields).every((value) => !value); // Check if all fields are empty
  };

  /** Function to Handle Change in Input Fields */
  const handleChange = (name, value) => {
    const isWholeNumberField = ["runTime1Kmins", "runTime1Ksecs"].includes(
      name
    );
    const regex = isWholeNumberField ? /^\d*$/ : /^(\d+)?(\.\d{0,2})?$/;

    const isValid = value === "" || regex.test(value);
    if (!isValid) return;

    if (name === "runTime1Ksecs") {
      // Allow empty value
      if (value !== "") {
        const numericValue = parseInt(value, 10);
        // Allow only values 0 to 59
        if (isNaN(numericValue) || numericValue > 59) return;
      }
    }

    setData((prevData) => ({ ...prevData, [name]: value }));
  };

  /** Function to Handle Submit */
  const handleSubmit = async (type, data) => {
    if (!data) {
      return;
    }

    if (data && isSameUser) {
      // Check for empty fields
      // const { userId, ...fields } = data;
      // for (const key in fields) {
      //   if (
      //     (userInfo.userType !== "NON_PLAYER" && fields[key] === "") ||
      //     fields[key] == null
      //   ) {
      //     notifyError(`Field ${key} cannot be empty`);
      //     return;
      //   }
      // }

      try {
        const requestFn =
          type === "new"
            ? UserApi.postUserPhysicalData
            : UserApi.updateUserPhysicalData;

        await requestFn(data);

        // Show success message based on the type of operation
        if (type === "new") {
          notifySuccess("Data Created Successfully");
          setModalVisible(false);
        } else {
          notifySuccess("Data Updated Successfully");
          setModalVisible(false);
        }
      } catch (error) {
        console.error("API Error:", error);
        notifyError("An error occurred. Please try again.");
      }
    }
  };

  // Use Effects and Focus Effects
  useEffect(() => {
    getPlayerPhysicalData();
  }, [modalVisible]);

  return (
    <View>
      <TouchableOpacity
        onPress={() => {
          setShowPhysicalData(true);
          setModalVisible(true);
        }}
      >
        <Text style={styles.labelText}>PHYSICAL DATA</Text>
        <Text style={styles.addButtonText}>+</Text>
      </TouchableOpacity>

      {showPhysicalData && (
        <Modal
          animationType="slide"
          transparent={true}
          visible={modalVisible}
          onRequestClose={() => setModalVisible(false)}
        >
          <TouchableWithoutFeedback onPress={() => setModalVisible(false)}>
            <View style={commonStyles.modalBackdrop}>
              <TouchableWithoutFeedback>
                <View style={[commonStyles.modalContainer, styles.modalView]}>
                  {/* Close Popup Button */}
                  <TouchableOpacity
                    style={commonStyles.modalCloseWrapper}
                    activeOpacity={0.6}
                    onPress={() => setModalVisible(false)}
                  >
                    <Image
                      source={Images.cross}
                      style={commonStyles.modalCloseIcon}
                    />
                  </TouchableOpacity>

                  {/* Title */}
                  <Text style={styles.title}>PHYSICAL DATA</Text>

                  {/* Table */}
                  <View style={styles.table}>
                    <View style={styles.seperator} />
                    {/* 5m Sprint Time */}
                    <View style={styles.tableRow}>
                      <Text
                        style={[
                          styles.tableCell,
                          styles.tableTitle,
                          {
                            paddingVertical: isSameUser ? 8 : 16,
                          },
                        ]}
                      >
                        5m Sprint Time
                      </Text>

                      <View style={styles.tableCell}>
                        {isSameUser ? (
                          <View style={styles.inputCellWrapper}>
                            <TextInput
                              style={inputStyle("sprintTime5M")}
                              keyboardType="numeric"
                              onChangeText={(value) =>
                                handleChange("sprintTime5M", value)
                              }
                              value={data?.sprintTime5M}
                              onFocus={() => setFocusedInput("sprintTime5M")}
                              onBlur={() => setFocusedInput(null)}
                            />
                            <Text>s</Text>
                          </View>
                        ) : (
                          <Text style={styles.contentValue}>
                            {existintPhysicalData?.sprintTime5M}
                            {existintPhysicalData?.sprintTime5M ? "s" : ""}
                          </Text>
                        )}
                      </View>
                    </View>

                    {/* 10m Sprint Time */}
                    <View style={styles.tableRow}>
                      <Text
                        style={[
                          styles.tableCell,
                          styles.tableTitle,
                          {
                            paddingVertical: isSameUser ? 8 : 16,
                          },
                        ]}
                      >
                        10m Sprint Time
                      </Text>

                      <View style={styles.tableCell}>
                        {isSameUser ? (
                          <View style={styles.inputCellWrapper}>
                            <TextInput
                              style={inputStyle("sprintTime10M")}
                              keyboardType="numeric"
                              onChangeText={(value) =>
                                handleChange("sprintTime10M", value)
                              }
                              value={data?.sprintTime10M}
                              onFocus={() => setFocusedInput("sprintTime10M")}
                              onBlur={() => setFocusedInput(null)}
                            />
                            <Text>s</Text>
                          </View>
                        ) : (
                          <Text style={styles.contentValue}>
                            {existintPhysicalData?.sprintTime10M}
                            {existintPhysicalData?.sprintTime10M ? "s" : ""}
                          </Text>
                        )}
                      </View>
                    </View>

                    {/* 20m Sprint Time */}
                    <View style={styles.tableRow}>
                      <Text
                        style={[
                          styles.tableCell,
                          styles.tableTitle,
                          {
                            paddingVertical: isSameUser ? 8 : 16,
                          },
                        ]}
                      >
                        20m Sprint Time
                      </Text>

                      <View style={styles.tableCell}>
                        {isSameUser ? (
                          <View style={styles.inputCellWrapper}>
                            <TextInput
                              style={inputStyle("sprintTime20M")}
                              keyboardType="numeric"
                              onChangeText={(value) =>
                                handleChange("sprintTime20M", value)
                              }
                              value={data?.sprintTime20M}
                              onFocus={() => setFocusedInput("sprintTime20M")}
                              onBlur={() => setFocusedInput(null)}
                            />
                            <Text>s</Text>
                          </View>
                        ) : (
                          <Text style={styles.contentValue}>
                            {existintPhysicalData?.sprintTime20M}
                            {existintPhysicalData?.sprintTime20M ? "s" : ""}
                          </Text>
                        )}
                      </View>
                    </View>

                    {/* Max. Speed */}
                    <View style={styles.tableRow}>
                      <Text
                        style={[
                          styles.tableCell,
                          styles.tableTitle,
                          {
                            paddingVertical: isSameUser ? 8 : 16,
                          },
                        ]}
                      >
                        Max. Speed
                      </Text>

                      <View style={styles.tableCell}>
                        {isSameUser ? (
                          <View style={styles.inputCellWrapper}>
                            <TextInput
                              style={inputStyle("maxSpeed")}
                              keyboardType="numeric"
                              onChangeText={(value) =>
                                handleChange("maxSpeed", value)
                              }
                              value={data?.maxSpeed}
                              onFocus={() => setFocusedInput("maxSpeed")}
                              onBlur={() => setFocusedInput(null)}
                            />
                            <Text>m/s</Text>
                          </View>
                        ) : (
                          <Text style={styles.contentValue}>
                            {existintPhysicalData?.maxSpeed}
                            {existintPhysicalData?.maxSpeed ? "m/s" : ""}
                          </Text>
                        )}
                      </View>
                    </View>

                    {/* 1k Run Time */}
                    <View style={[styles.tableRow]}>
                      <Text
                        style={[
                          styles.tableCell,
                          styles.tableTitle,
                          {
                            paddingVertical: isSameUser ? 8 : 16,
                          },
                        ]}
                      >
                        1k Run Time
                      </Text>

                      <View style={styles.tableCell}>
                        {isSameUser ? (
                          <View style={styles.inputCellWrapper}>
                            <View style={styles.inputCellWrapper}>
                              <TextInput
                                style={inputStyle("runTime1Kmins")}
                                keyboardType="numeric"
                                onChangeText={(value) =>
                                  handleChange("runTime1Kmins", value)
                                }
                                value={data?.runTime1Kmins}
                                onFocus={() => setFocusedInput("runTime1Kmins")}
                                onBlur={() => setFocusedInput(null)}
                              />
                              <Text>mins</Text>
                            </View>

                            <View style={styles.inputCellWrapper}>
                              <TextInput
                                style={inputStyle("runTime1Ksecs")}
                                keyboardType="numeric"
                                maxLength={2}
                                onChangeText={(value) =>
                                  handleChange("runTime1Ksecs", value)
                                }
                                value={data?.runTime1Ksecs}
                                onFocus={() => setFocusedInput("runTime1Ksecs")}
                                onBlur={() => setFocusedInput(null)}
                              />
                              <Text>s</Text>
                            </View>
                          </View>
                        ) : (
                          <Text style={styles.contentValue}>
                            {existintPhysicalData?.runTime1Kmins
                              ? `${existintPhysicalData?.runTime1Kmins}m `
                              : ""}
                            {existintPhysicalData?.runTime1Ksecs
                              ? `${existintPhysicalData?.runTime1Ksecs}s`
                              : ""}
                          </Text>
                        )}
                      </View>
                    </View>

                    {/* Match Avg Distance */}
                    <View style={[styles.tableRow]}>
                      <Text
                        style={[
                          styles.tableCell,
                          styles.tableTitle,
                          {
                            paddingVertical: isSameUser ? 8 : 16,
                          },
                        ]}
                      >
                        Match Avg Distance
                      </Text>

                      <View style={styles.tableCell}>
                        {isSameUser ? (
                          <View style={styles.inputCellWrapper}>
                            <TextInput
                              style={inputStyle("matchAvgDistance")}
                              keyboardType="numeric"
                              onChangeText={(value) =>
                                handleChange("matchAvgDistance", value)
                              }
                              value={data?.matchAvgDistance}
                              onFocus={() =>
                                setFocusedInput("matchAvgDistance")
                              }
                              onBlur={() => setFocusedInput(null)}
                            />
                            <Text>m</Text>
                          </View>
                        ) : (
                          <Text style={styles.contentValue}>
                            {existintPhysicalData?.matchAvgDistance}
                            {existintPhysicalData?.matchAvgDistance ? "m" : ""}
                          </Text>
                        )}
                      </View>
                    </View>

                    {/* Counter Movement Jump */}
                    <View style={[styles.tableRow, { borderBottomWidth: 0 }]}>
                      <Text
                        style={[
                          styles.tableCell,
                          styles.tableTitle,
                          {
                            paddingVertical: isSameUser ? 8 : 16,
                          },
                        ]}
                      >
                        Counter Movement Jump
                      </Text>

                      <View style={styles.tableCell}>
                        {isSameUser ? (
                          <View style={styles.inputCellWrapper}>
                            <TextInput
                              style={inputStyle("counterMovementJump")}
                              keyboardType="numeric"
                              onChangeText={(value) =>
                                handleChange("counterMovementJump", value)
                              }
                              value={data?.counterMovementJump}
                              onFocus={() =>
                                setFocusedInput("counterMovementJump")
                              }
                              onBlur={() => setFocusedInput(null)}
                            />
                            <Text>cm</Text>
                          </View>
                        ) : (
                          <Text style={styles.contentValue}>
                            {existintPhysicalData?.counterMovementJump}
                            {existintPhysicalData?.counterMovementJump
                              ? "cm"
                              : ""}
                          </Text>
                        )}
                      </View>
                    </View>
                  </View>

                  {/* Submit Button */}
                  {isSameUser && (
                    <View>
                      <TouchableOpacity
                        style={[
                          styles.saveButton,
                          areFieldsEmpty() && {
                            backgroundColor: Colors.dark_grey,
                          }, // Update button style when disabled
                        ]}
                        disabled={areFieldsEmpty()} // Disable the button when fields are empty
                        onPress={() =>
                          handleSubmit(
                            !existintPhysicalData ? "new" : "update",
                            data
                          )
                        } // Or "update" based on your logic
                      >
                        <Text style={styles.saveButtonText}>Save</Text>
                      </TouchableOpacity>

                      <TouchableOpacity
                        style={styles.cancelButton}
                        onPress={() => setModalVisible(false)}
                      >
                        <Text style={styles.cancelButtonText}>Cancel</Text>
                      </TouchableOpacity>
                    </View>
                  )}
                </View>
              </TouchableWithoutFeedback>
            </View>
          </TouchableWithoutFeedback>
        </Modal>
      )}
    </View>
  );
};

const styles = {
  modalView: {
    paddingHorizontal: Size(4),
    paddingTop: Size(10),
    paddingBottom: Size(7),
    alignItems: "center",
  },
  title: {
    fontSize: Size(4.5),
    fontFamily: "Bold",
    marginBottom: HeightSize(3.5),
    textAlign: "center",
  },
  table: {
    width: "100%",
    position: "relative",
  },
  seperator: {
    height: "100%",
    width: 1,
    backgroundColor: "#ddd",
    position: "absolute",
    left: "50%",
  },
  tableRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    borderBottomWidth: 1,
    borderBottomColor: "#ddd",
    alignItems: "center",
  },
  tableTitle: {
    fontSize: 13,
    textAlign: "center",
    fontFamily: "Regular",
  },
  inputCellWrapper: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  tableCell: {
    width: "50%",
    paddingVertical: 8,
    paddingHorizontal: 10,
    justifyContent: "center",
    alignItems: "center",
  },
  input: {
    width: "40%",
    textAlign: "center",
    fontSize: 13,
    borderColor: "#C8C7C7",
    borderBottomWidth: 1,
    borderRadius: 0,
  },
  labelText: {
    paddingVertical: HeightSize(2),
    fontFamily: "Bold",
    fontSize: Size(4.5),
    color: Colors.white,
  },
  addButtonText: {
    color: "white",
    fontSize: Size(10),
    opacity: 0.4,
  },
  verticalLine: {
    height: "100%",
    width: 1,
    backgroundColor: "gray",
  },
  saveButton: {
    backgroundColor: Colors.black,
    paddingVertical: Size(2),
    paddingHorizontal: Size(14),
    borderRadius: 100,
    marginTop: HeightSize(3),
    borderWidth: 1,
  },
  saveButtonText: {
    color: Colors.white,
    fontSize: Size(5.5),
    fontFamily: "Regular",
    textAlign: "center",
  },
  cancelButton: {
    paddingVertical: Size(2),
    paddingHorizontal: Size(14),
    marginTop: 6,
  },
  cancelButtonText: {
    color: Colors.black,
    fontSize: Size(5.5),
    fontFamily: "Regular",
  },
  contentValue: {
    fontFamily: "PoppinsMedium",
    fontSize: 13,
  },
};

export default React.memo(PhysicalData);
