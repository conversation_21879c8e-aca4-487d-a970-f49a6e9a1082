import { StyleSheet } from "react-native";
import { HeightSize, Size } from "../../res/Size";
import { Colors } from "../../res/Colors";


export const styles = StyleSheet.create({
  iconContainer: {
    height: HeightSize(2),
    justifyContent: "center",
    paddingLeft: Size(5),
  },
  iconStyle: {
    textAlign: "left",
    justifyContent: "flex-start",
    alignSelf: "flex-start",
  },
  screeName: {
    fontFamily: 'favelaBlack',
    fontSize: Size(5),
    color: Colors.black,
    textAlign: "center",
    flex: 1,
    textAlignVertical: "center",
  },
  headerStyle: {
    height: HeightSize(7),
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.white,
    justifyContent: "space-between",
    shadow: {
      shadowColor: Colors.grey,
      shadowOffset: {
        width: 2,
        height: 4,
      },
      shadowOpacity: 0.25,
      shadowRadius: 5,
      elevation: 8,
    },
  },
  leftContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    width: <PERSON>ze(15),
  },
  titleContainer: {
    flex: 1,
  },
  rightContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    width: Size(15),
  },
});
