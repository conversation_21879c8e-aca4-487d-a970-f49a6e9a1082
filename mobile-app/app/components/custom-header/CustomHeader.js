// External dependencies
import React from "react";
import { useNavigation } from "@react-navigation/core";
import { View, Text, TouchableOpacity } from "react-native";
import { BackIcon } from "../../res/Svg";
import { Size } from "../../res/Size";
import { styles } from "./CustomHeaderStyles";


const CustomHeader = ({
  title,
  onPress,
  arrowSize = Size(5),
  buttonText = "",
  buttonOnPress,
  showBackArrow = true,
  btnStyle = {},
  btnTxtStyle = {},
  headerStyle = {},
  headerRight = () => {},
  titleStyle = {},
}) => {
  const navigation = useNavigation();
  return (
    <View style={[styles.headerStyle, headerStyle]}>
      <View style={styles.leftContainer}>
        {showBackArrow && (
          <TouchableOpacity
            style={styles.iconContainer}
            activeOpacity={0.5}
            onPress={() => {
              if (onPress) {
                onPress();
              } else {
                navigation.goBack();
              }
            }}
          >
            <BackIcon/>
          </TouchableOpacity>
        )}
      </View>
      <View style={styles.titleContainer}>
        <Text
          style={[
            styles.screeName,
            titleStyle,
            { marginLeft: Size(showBackArrow ? 0 : 5) },
          ]}
          numberOfLines={1}
          ellipsizeMode={"tail"}
        >
          {title}
        </Text>
      </View>
      {headerRight() ? (
        headerRight()
      ) : (
        <View style={{ width: Size(15) }}></View>
      )}
    </View>
  );
};

export default CustomHeader;
