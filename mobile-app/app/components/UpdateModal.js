import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  ImageBackground,
  Platform,
  Linking,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { PlayerIcon } from '../res/Svg';
import { HeightSize, Size } from '../res/Size';
import { Colors } from '../res/Colors';

const UpdateModal = ({ isVisible, onClose = () => {}, isForced = false }) => {
  const handleUpdate = () => {
    const storeUrl = Platform.select({
      ios: 'https://apps.apple.com/app/id6503473876',
      android: 'https://play.google.com/store/apps/details?id=com.playerapp.playerapp',
    });

    Linking.openURL(storeUrl);
    if (typeof onClose === 'function') {
      onClose();
    }
  };

  if (!isVisible) return null;

  return (
    <Modal transparent visible={isVisible} animationType="fade">
      <View style={styles.overlay}>
        <ImageBackground
          source={require('../../assets/versionUpdateBg.png')}
          style={styles.background}
          resizeMode="cover"
        >
          <LinearGradient
            colors={['transparent', 'rgba(0, 0, 0, 0.5)']}
            style={styles.gradient}
          />

          <View style={styles.modal}>
            <PlayerIcon />
            <Text style={styles.title}>
              Update your application to the latest version
            </Text>
            <Text style={styles.message}>
              A brand new version of the <Text style={{ color: 'white' }}>PLAYER</Text> app is available.
            </Text>
          
            <TouchableOpacity onPress={handleUpdate} style={styles.updateButton}>
              <Text style={styles.updateButtonText}>UPDATE NOW</Text>
            </TouchableOpacity>

            
          </View>
        </ImageBackground>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'flex-end',
  },
  background: {
    flex: 1,
    width: '100%',
    justifyContent: 'flex-end',
  },
  gradient: {
    ...StyleSheet.absoluteFillObject,
  },
  modal: {
    backgroundColor: 'transparent',
    padding: 10,
    borderRadius: 15,
    width: '90%',
    alignSelf: 'center',
    marginBottom: 30,
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontFamily: 'PoppinsBold',
    color: 'white',
    textAlign: 'center',
    marginBottom: 10,
  },
  message: {
    color: '#BBBBBB',
    marginBottom: 20,
    width: '100%',
    textAlign: 'center',
    fontFamily: 'Regular',
    fontSize: Size(3.5),
  },
  updateButton: {
    backgroundColor: Colors.green,
    borderRadius: Size(40),
    justifyContent: 'center',
    width: '100%',
    alignItems: 'center',
    height: HeightSize(9),
  },
  updateButtonText: {
    color: Colors.black,
    fontFamily: 'Bold',
    fontSize: Size(3.7),
  },
  cancelButton: {
    marginTop: 15,
  },
  cancelButtonText: {
    color: 'white',
    fontSize: Size(3.5),
    fontFamily: 'Regular',
    textDecorationLine: 'underline',
  },
});

export default UpdateModal;
