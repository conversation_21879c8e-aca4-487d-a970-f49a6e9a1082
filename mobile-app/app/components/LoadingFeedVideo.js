import React from "react";
import { View, Text, StyleSheet, TouchableOpacity } from "react-native";
import { FailUploadIcon, HourGlassIcon } from "../res/Svg";
import { HeightSize, Size } from "../res/Size";

const LoadingFeedVideo = ({
  isError,
  text = "Please wait while we prepare\nthis highlight, or check back soon...",
  errorText = "Unfortunately, there was an error\nuploading this highlight. Please try again."
}) => {
  return (
    <View style={styles.container}>
      {isError ? (
        <>
          <FailUploadIcon />
          <Text style={styles.text}>
            {errorText}
          </Text>
        </>
      ) : (
        <>
          <HourGlassIcon />
          <Text style={styles.text}>
            {text}
          </Text>
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: "100%",
    aspectRatio: 3 / 4,
    backgroundColor: "#F8F8F8",
    alignItems: "center",
    justifyContent: "center",
  },
  icon: {
    width: Size(20),
    height: Size(20),
    resizeMode: "contain",
  },
  text: {
    paddingVertical: HeightSize(3),
    textAlign: "center",
    fontSize: Size(3.4),
    lineHeight: 18,
    color: "black",
    fontFamily: "Regular",
  },
});

export default LoadingFeedVideo;
