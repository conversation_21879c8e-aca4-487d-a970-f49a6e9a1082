import { StyleSheet } from "react-native";
import { HeightSize, Size } from "../../res/Size";
import { Colors } from "../../res/Colors";

const styles = StyleSheet.create({
  inputGroup: {
    flex: 1,
  },
  textInput: {
    borderBottomWidth: 1,
    borderColor: Colors.black,
    height: HeightSize(4),
    fontFamily: "Regular",
    fontSize: Size(3.5),
  },
  label: {
    fontFamily: "Regular",
    fontSize: Size(3.2),
    color: "gray",
    marginBottom: HeightSize(0.2),
  },
  errorText: {
    color: "red",
    fontSize: 12,
    textAlign: "center",
  },
  grid: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  modalWrapper: {
    paddingTop: Size(10),
  },
  teamNameText: {
    fontFamily: "PoppinsBold",
    fontSize: Size(5),
  },
  teamContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: HeightSize(3),
  },
  errorText: {
    color: "red",
    fontFamily: "Regular",
    fontSize: Size(3.2),
    textAlign: "center",
  },
  saveButton: {
    backgroundColor: Colors.black,
    paddingVertical: Size(2),
    paddingHorizontal: Size(14),
    borderRadius: 100,
    marginTop: HeightSize(3),
    borderWidth: 1,
    alignSelf: "center",
    marginBottom: HeightSize(3),
  },
  saveButtonText: {
    color: Colors.white,
    fontSize: Size(5.5),
    fontFamily: "Regular",
    textAlign: "center",
  },
  formWrapper: {
    gap: Size(4),
  },
});

export default styles;
