// REACT //
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";

// COMPONENTS //
import {
  Text,
  TextInput,
  TouchableOpacity,
  View,
  Image,
  Modal,
  KeyboardAvoidingView,
  Platform,
} from "react-native";
import { Images } from "../../res/Images";

// SERVICES //
import ExperienceApi from "../../services/ExperienceApi";

// CONSTANTS //
import { notifySuccess, notifyError, validateURL } from "../../constants/misc";

// NAVIGATION //
import { useIsFocused } from "@react-navigation/native";

// OTHERS //
import { HeightSize, Size } from "../../res/Size";
import styles from "./EditExperienceStyles";
import { dispatch } from "../../redux/store";
import { commonStyles } from "../../res/CommonStyles";

const EditExperience = ({ shouldShowModal, data, handleCloseModal, onExperienceUpdate }) => {
  const currentYear = new Date().getFullYear();
  const century = Math.floor(currentYear / 100) * 100;

  const seasonCheck = /^(\d{2})\/(\d{2})$/;

  const [seasonNameError, setSeasonNameError] = useState("");
  const [userExperience, setUserExperience] = useState({
    teamName: data?.teamName || "",
    seasonName: data?.seasonName || "",
    appearances: data?.appearances?.toString() || "",
    goals: data?.goals?.toString() || "",
    assists: data?.assists?.toString() || "",
    cleanSheets: data?.cleanSheets?.toString() || "",
    teamLogo: data?.teamLogo || "",
    minsPlayed: data?.minsPlayed?.toString() || "",
  });
  const [modalVisible, setModalVisible] = useState(false);

  const focused = useIsFocused();

  const { userInfo } = useSelector((state) => state.auth.authUser);
  console.log("user exp", data)

  useEffect(() => {
    if (shouldShowModal && data) {
      setUserExperience({
        teamName: data?.teamName || "",
        seasonName: data?.seasonName || "",
        appearances: data?.appearances?.toString() || "",
        goals: data?.goals?.toString() || "",
        assists: data?.assists?.toString() || "",
        cleanSheets: data?.cleanSheets?.toString() || "",
        teamLogo: data?.teamLogo || "",
        minsPlayed: data?.minsPlayed?.toString() || "0",
      });
      setModalVisible(true);
    }
  }, [shouldShowModal, data, focused]);

  // const handleSeasonName = (text) => {
  //   setUserExperience({ ...userExperience, seasonName: text });

  //   if (!text) {
  //     setSeasonNameError("");
  //     return;
  //   }

  //   if (seasonCheck.test(text)) {
  //     const [start, end] = text.split('/').map(Number);
  //     const fullStartYear = start < 50 ? century + start : century - 100 + start;
  //     const fullEndYear = end < 50 ? century + end : century - 100 + end;

  //     if (
  //       fullStartYear >= 2000 &&
  //       fullEndYear <= currentYear &&
  //       fullStartYear < fullEndYear
  //     ) {
  //       setSeasonNameError("");
  //     } else {
  //       setSeasonNameError("Invalid season range. Ensure it is within 2000 to current year and is a continuous year range.");
  //     }
  //   } else {
  //     setSeasonNameError("Must be in YY/YY format.");
  //   }
  // };

  const handleSeasonName = (text) => {
    setUserExperience({ ...userExperience, seasonName: text });

    // Regular expression to check if input is in the "YY/YY" format
    const seasonCheck = /^\d{2}\/\d{2}$/;

    // Check if the format matches
    if (seasonCheck.test(text)) {
      const [start, end] = text.split("/").map(Number);

      // Get the current year
      const currentYear = new Date().getFullYear();

      // Assuming 'century' is 2000, so years 00-49 map to 2000-2049 and 50-99 map to 1950-1999
      const century = 2000;

      // Convert YY to full year, assuming start year < 50 maps to 2000-2049, and > 50 maps to 1900-1999
      const fullStartYear = start < 50 ? century + start : 1900 + start;
      const fullEndYear = end < 50 ? century + end : 1900 + end;

      // Ensure the end year is exactly one year after the start year
      if (fullEndYear === fullStartYear + 1) {
        // Ensure that the fullEndYear is valid (not in the future)
        if (fullStartYear >= 2000 && fullEndYear <= currentYear + 1) {
          setSeasonNameError(""); // No error, valid season name
        } else {
          setSeasonNameError("The season cannot be in the future.");
        }
      } else {
        setSeasonNameError(
          "The second year must be one year after the first year (e.g., 21/22)."
        );
      }
    } else {
      setSeasonNameError("Must be in YY/YY format.");
    }
  };
  const isDisabled = userExperience.seasonName && !seasonNameError;

  const handleBlur = () => {
    if (userExperience.seasonName && seasonNameError) {
      setSeasonNameError("Must be in YY/YY");
    }
  };

  const validateInput = (text) => {
    const regex = /^\d{0,3}$/;
    return regex.test(text);
  };

  const handleAppearance = (text) => {
    if (validateInput(text)) {
      setUserExperience({ ...userExperience, appearances: text });
    }
  };
  const handleGoals = (text) => {
    if (validateInput(text)) {
      setUserExperience({ ...userExperience, goals: text });
    }
  };
  const handleAssist = (text) => {
    if (validateInput(text)) {
      setUserExperience({ ...userExperience, assists: text });
    }
  };
  const handleCleansheet = (text) => {
    if (validateInput(text)) {
      setUserExperience({ ...userExperience, cleanSheets: text });
    }
  };
  const handleMinutesPlayed = (text) => {
    if (/^\d{0,4}$/.test(text)) {
      setUserExperience({ ...userExperience, minsPlayed: text });
    }
  };

  const handleSubmit = async () => {
    if (!userExperience.seasonName.trim()) {
      setSeasonNameError("Please enter a season.");
      return;
    }
  
   const mergedValues = {
  teamName: userExperience.teamName,
  teamLogo: userExperience.teamLogo,
  appearances: +userExperience.appearances,
  goals: +userExperience.goals,
  assists: +userExperience.assists,
  cleanSheets: +userExperience.cleanSheets,
  seasonName: userExperience.seasonName,
  minsPlayed: +userExperience.minsPlayed,
  userId: userExperience.userId || userInfo.id, 
};
  
    try {
      // Update experience first
      const response = await ExperienceApi.updateExperience({
        id: data.id,
        data: mergedValues,
      });
  
      if (response.data.status === 1) {
        notifySuccess(response.data.message);
  
        // Fetch the updated profile after a successful experience update
        await dispatch.user.getUserProfile({ id: userInfo.id });
  
        // Close modal and trigger parent update
        setModalVisible(false);
        handleCloseModal({ 
          visible: false, 
          data: mergedValues,
        });
         // Notify parent to refresh experience
      if (onExperienceUpdate) {
        onExperienceUpdate();
      }
      } else {
        notifyError("Failed to update experience.");
      }
    } catch (error) {
      notifyError(error?.response?.message || error.message);
      console.error("Error updating experience:", error);
    }
  };
  

  return (
    <>
      {modalVisible && (
        <Modal
          animationType="slide"
          transparent={true}
          visible={modalVisible}
          onRequestClose={() => {
            setModalVisible(false);
            handleCloseModal({ visible: false, data: null });
          }}
        >
          <View style={commonStyles.modalBackdrop}>
            <KeyboardAvoidingView
              behavior={Platform.OS === "ios" ? "padding" : undefined} // "padding" for iOS devices to avoid overlap
              style={[commonStyles.modalContainer, styles.modalWrapper]}
            >
              <TouchableOpacity
                onPress={() => {
                  setModalVisible(false);
                  handleCloseModal({ visible: false, data: null });
                }}
                style={commonStyles.modalCloseWrapper}
              >
                <Image
                  source={Images.cross}
                  style={commonStyles.modalCloseIcon}
                />
              </TouchableOpacity>

              <View style={styles.teamContainer}>
                {/* Team Logo */}
                <Image
                  source={{
                    uri: validateURL(data.teamLogo)
                      ? data.teamLogo
                      : Images.ball,
                  }}
                  style={{
                    width: Size(6),
                    height: Size(6),
                    borderRadius: Size(3.5),
                    marginRight: Size(2),
                  }}
                />
                <Text style={styles.teamNameText}>{data.teamName}</Text>
              </View>

              <View style={styles.formWrapper}>
                <View>
                  <Text style={[styles.label]}>Season (YY/YY)</Text>
                  <TextInput
                    style={styles.textInput}
                    onBlur={handleBlur}
                    onChangeText={handleSeasonName}
                    value={userExperience.seasonName}
                    placeholder="Season Name (YY/YY)"
                  />
                  {seasonNameError || userExperience.seasonName === "" ? (
                    <Text style={styles.errorText}>{seasonNameError}</Text>
                  ) : null}
                </View>

                <View style={styles.grid}>
                  <View style={styles.inputGroup}>
                    <Text style={styles.label}>Appearance</Text>
                    <TextInput
                      keyboardType="numeric"
                      onChangeText={handleAppearance}
                      value={userExperience.appearances.toString()}
                      style={styles.textInput}
                    />
                  </View>
                  <View style={[styles.inputGroup, { marginLeft: Size(7) }]}>
                    <Text style={styles.label}>Goals</Text>
                    <TextInput
                      keyboardType="numeric"
                      onChangeText={handleGoals}
                      value={userExperience.goals.toString()}
                      style={styles.textInput}
                    />
                  </View>
                </View>
                <View style={styles.grid}>
                  <View style={styles.inputGroup}>
                    <Text style={styles.label}>Assists</Text>
                    <TextInput
                      keyboardType="numeric"
                      onChangeText={handleAssist}
                      value={userExperience.assists.toString()}
                      style={styles.textInput}
                    />
                  </View>
                  <View style={[styles.inputGroup, { marginLeft: Size(7) }]}>
                    <Text style={styles.label}>Clean Sheet</Text>
                    <TextInput
                      keyboardType="numeric"
                      onChangeText={handleCleansheet}
                      value={userExperience.cleanSheets.toString()}
                      style={styles.textInput}
                    />
                  </View>
                </View>

                <View style={styles.grid}>
                  <View style={styles.inputGroup}>
                    <Text style={styles.label}>Minutes Played</Text>
                    <TextInput
                      keyboardType="numeric"
                      onChangeText={handleMinutesPlayed}
                      value={userExperience.minsPlayed.toString()}
                      style={styles.textInput}
                    />
                  </View>
                  <View
                    style={[styles.inputGroup, { marginLeft: Size(7) }]}
                  ></View>
                </View>
              </View>

              <TouchableOpacity
                onPress={() =>handleSubmit()}
                style={styles.saveButton}
                disabled={!isDisabled}
              >
                <Text style={[styles.saveButtonText]}>Save</Text>
              </TouchableOpacity>
            </KeyboardAvoidingView>
          </View>
        </Modal>
      )}
    </>
  );
};

export default EditExperience;
