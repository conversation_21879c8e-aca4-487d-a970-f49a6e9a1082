import React, { useCallback, useEffect, useRef, useState } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from "react-native";
import { useFormik } from "formik";
import debounce from "lodash/debounce";
import PhoneInput from "react-native-phone-number-input";
import { useSelector } from "react-redux";
import { useNavigation } from "@react-navigation/native";
import AuthApi from "../../services/AuthApi";
import UserApi from "../../services/UserApi";
import { signupSchema } from "../../constants/formSchema";
import VerificationConfirmModal from "./VerificationConfirmModal";
import { Colors } from "../../res/Colors";
import { Ionicons } from "@expo/vector-icons";
import { dispatch } from "../../redux/store";

const SignupForm = () => {
  const [phoneNumber, setPhoneNumber] = useState("");
  const [phoneError, setPhoneError] = useState("");
  const phoneInputRef = useRef(null);
  const [visible, setVisible] = useState(false);
  const navigation = useNavigation();

  const signupLoading = useSelector(
    ({ loading }) => loading.effects.auth.userSignup
  );

  const shouldAllowSignup = async (email) => {
    try {
      const response = await AuthApi.checkForSignup({ email });
      return response.data.status === 1;
    } catch (error) {
      return false;
    }
  };

  const checkEmailEligibility = useCallback(
    debounce((email) => {
      if (email) {
        shouldAllowSignup(email).then((status) => {
          setEmailEligible(status);
        });
      }
    }, 500),
    []
  );

  const onSubmit = async (values) => {
    const formattedPhoneNumber =
      phoneInputRef.current?.getNumberAfterPossiblyEliminatingZero()
        ?.formattedNumber;

    if (!phoneInputRef.current?.isValidNumber(phoneNumber)) {
      setPhoneError("Please enter a valid phone number");
      return;
    }

    setPhoneError("");

    const newValues = {
      ...values,
      phoneNumber: formattedPhoneNumber,
      email: values.email.toLowerCase(),
    };

    const response = await dispatch.auth.userSignup({
      user: newValues,
    });

    if (response?.status === 1) {
      navigation.navigate("Login"); // Navigate here instead
    }
  };

  const handleOnBlur = () => {
    if (phoneNumber && !phoneInputRef.current?.isValidNumber(phoneNumber)) {
      setPhoneError("Please enter a valid phone number");
    } else {
      setPhoneError("");
    }
  };

  const initialValues = {
    firstName: "",
    lastName: "",
    email: "",
    password: "",
  };

  const { values, errors, touched, handleBlur, handleChange, handleSubmit } =
    useFormik({
      initialValues,
      onSubmit,
      //validationSchema: signupSchema,
    });

  return (
    <ScrollView style={styles.container}>
      <View style={styles.form}>
        <TextInput
          style={[
            styles.input,
            errors.firstName && touched.firstName ? styles.errorBorder : {},
          ]}
          placeholder="First Name"
          onChangeText={handleChange("firstName")}
          onBlur={handleBlur("firstName")}
          value={values.firstName}
        />
        {errors.firstName && touched.firstName && (
          <Text style={styles.errorText}>{errors.firstName}</Text>
        )}

        <TextInput
          style={[
            styles.input,
            errors.lastName && touched.lastName ? styles.errorBorder : {},
          ]}
          placeholder="Last Name"
          onChangeText={handleChange("lastName")}
          onBlur={handleBlur("lastName")}
          value={values.lastName}
        />
        {errors.lastName && touched.lastName && (
          <Text style={styles.errorText}>{errors.lastName}</Text>
        )}

        <TextInput
          style={[
            styles.input,
            errors.email && touched.email ? styles.errorBorder : {},
          ]}
          placeholder="Email"
          onChangeText={(value) => {
            handleChange("email")(value);
          }}
          onBlur={handleBlur("email")}
          value={values.email}
        />
        {errors.email && touched.email && (
          <Text style={styles.errorText}>{errors.email}</Text>
        )}
        <View>
          <TextInput
            style={[
              styles.input,
              errors.password && touched.password ? styles.errorBorder : {},
            ]}
            placeholder="Password"
            secureTextEntry={!visible}
            onChangeText={handleChange("password")}
            onBlur={handleBlur("password")}
            value={values.password}
          />
          <TouchableOpacity
            onPress={() => setVisible(!visible)}
            style={styles.eyeIcon}
          >
            <Ionicons
              name={visible ? "eye-off" : "eye"}
              size={24}
              color={Colors.black}
            />
          </TouchableOpacity>
          {errors.password && touched.password && (
            <Text style={styles.errorText}>{errors.password}</Text>
          )}
        </View>
        <PhoneInput
          ref={phoneInputRef}
          defaultValue={phoneNumber}
          defaultCode="US"
          layout="first"
          onChangeText={(text) => setPhoneNumber(text)}
          onChangeFormattedText={(text) => setPhoneNumber(text)}
          containerStyle={styles.phoneContainer}
          textContainerStyle={styles.phoneTextContainer}
          textInputStyle={styles.phoneTextInput}
        />
        {phoneError ? <Text style={styles.errorText}>{phoneError}</Text> : null}

        <TouchableOpacity
          style={styles.submitButton}
          onPress={handleSubmit}
          disabled={signupLoading}
        >
          <Text style={styles.submitText}>
            {signupLoading ? "Submitting..." : "Submit"}
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: "#fff",
  },
  form: {
    marginVertical: 20,
  },
  input: {
    borderBottomWidth: 2,
    borderColor: "#ccc",
    paddingVertical: 8,
    marginBottom: 20,
    fontSize: 16,
  },
  errorBorder: {
    borderColor: "red",
  },
  errorText: {
    color: "red",
    fontSize: 12,
  },
  phoneContainer: {
    height: 50,
    borderWidth: 1,
    borderColor: "#ccc",
    borderRadius: 5,
    marginVertical: 10,
  },
  phoneTextContainer: {
    paddingVertical: 0,
    backgroundColor: "transparent",
  },
  phoneTextInput: {
    fontSize: 16,
    padding: 0,
  },
  submitButton: {
    backgroundColor: "#007BFF",
    padding: 12,
    borderRadius: 8,
    alignItems: "center",
  },
  submitText: {
    color: "#fff",
    fontSize: 16,
  },
  eyeIcon: {
    position: "absolute",
    right: 10,
    top: 14,
  },
});

export default SignupForm;
