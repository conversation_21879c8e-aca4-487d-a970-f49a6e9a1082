import React, { forwardRef } from "react";
import { Modal, View, Text, StyleSheet, TouchableOpacity } from "react-native";
import { LogoIcon } from "../../res/Svg";


const VerificationConfirmModal = forwardRef(({ playerName, isError, visible, onClose }, ref) => {
  return (
    <Modal
      ref={ref}
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalBackdrop}>
        <View style={styles.modalBox}>
          {/* Logo Section */}
          <View style={styles.logoContainer}>
            <LogoIcon/>
          </View>

          {/* Content Section */}
          <View style={styles.contentContainer}>
            <Text style={styles.headerText}>VERIFY A PLAYER’S PROFILE</Text>
            <Text style={styles.playerRequestText}>
              {playerName} has requested you to verify their profile.
            </Text>
            <Text style={styles.instructionText}>
              To verify, please visit their profile here, and click the green Verify button (with visual representation of the toggle / button) to confirm their statistics and club tenures.
            </Text>
            <Text style={styles.instructionText}>
              You can add a footballing & character reference on their behalf, which will aid their career.
            </Text>
          </View>

          {/* Error Message */}
          {isError && (
            <Text style={styles.errorText}>
              Please reload to load players data
            </Text>
          )}

          {/* Close Button */}
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Text style={styles.closeButtonText}>Close</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
});

const styles = StyleSheet.create({
  modalBackdrop: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.6)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalBox: {
    backgroundColor: "white",
    width: "90%",
    borderRadius: 10,
    padding: 20,
    alignItems: "center",
  },
  logoContainer: {
    marginBottom: 20,
  },
  contentContainer: {
    alignItems: "center",
  },
  headerText: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 15,
    textAlign: "center",
  },
  playerRequestText: {
    fontSize: 18,
    textAlign: "center",
    marginBottom: 15,
  },
  instructionText: {
    fontSize: 16,
    color: "rgba(0, 0, 0, 0.5)",
    textAlign: "center",
    marginBottom: 10,
    lineHeight: 24,
  },
  errorText: {
    color: "red",
    fontSize: 16,
    textAlign: "center",
    marginTop: 15,
  },
  closeButton: {
    backgroundColor: "transparent",
    borderColor: "#ccc",
    borderWidth: 1,
    borderRadius: 5,
    paddingVertical: 10,
    paddingHorizontal: 20,
    marginTop: 20,
  },
  closeButtonText: {
    color: "#000",
    fontSize: 16,
  },
});

export default VerificationConfirmModal;
