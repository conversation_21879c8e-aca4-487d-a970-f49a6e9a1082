import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  TextInput,
  Modal,
  StyleSheet,
  TouchableOpacity,
} from "react-native";
import { useNavigation } from "@react-navigation/native";
import { useSelector } from "react-redux";
import { dispatch } from "../../redux/store";

const ConfirmAccount = () => {
  const [code, setCode] = useState("");
  const [visible, setVisible] = useState(false);
  const [email, setEmail] = useState("");
  const navigation = useNavigation();

  const authModel = useSelector((state) => state.auth);

  useEffect(() => {
    setVisible(authModel?.confirmSignUpModal?.visible);
    setEmail(authModel?.confirmSignUpModal?.email);
  }, [
    authModel?.confirmSignUpModal?.email,
    authModel?.confirmSignUpModal?.visible,
  ]);

  const handleCancel = () => {
    dispatch.auth.confirmSignUpModalBox({ visible: false, email: null });
  };

  const handleSubmit = () => {
    dispatch.auth.confirmSignUp({
      code,
      email,
      navigate: navigation.navigate,
    });
  };

  const handleResend = () => {
    dispatch.auth.resendVerifyCode({
      email,
    });
  };

  return (
    <Modal visible={visible} transparent>
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={handleCancel}
          >
            <Text style={styles.closeButtonText}>X</Text>
          </TouchableOpacity>
          <View style={styles.content}>
            <Text style={styles.heading}>Account Created!</Text>
            <Text style={styles.subheading}>
              One more step, check your email and enter the 6-digit code we sent
              you to confirm your account
            </Text>
            <TextInput
              style={styles.input}
              onChangeText={setCode}
              placeholder="Enter Confirm Code"
              secureTextEntry
            />
            <View style={styles.buttonContainer}>
              <TouchableOpacity
                style={[styles.button, styles.greenButton]}
                onPress={handleSubmit}
              >
                <Text style={styles.buttonText}>CONFIRM ACCOUNT</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.button, styles.blackButton]}
                onPress={handleResend}
              >
                <Text style={styles.buttonText}>RESEND CODE</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.6)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalContainer: {
    width: "90%",
    maxWidth: 400,
    backgroundColor: "white",
    borderRadius: 10,
    paddingVertical: 20,
    paddingHorizontal: 15,
    alignItems: "center",
  },
  closeButton: {
    position: "absolute",
    top: 10,
    right: 10,
    backgroundColor: "black",
    borderRadius: 15,
    width: 30,
    height: 30,
    justifyContent: "center",
    alignItems: "center",
  },
  closeButtonText: {
    color: "white",
    fontSize: 16,
  },
  content: {
    marginTop: 20,
    alignItems: "center",
  },
  heading: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#091E42",
    textAlign: "center",
  },
  subheading: {
    marginTop: 10,
    fontSize: 16,
    color: "black",
    textAlign: "center",
  },
  input: {
    borderWidth: 1,
    borderColor: "gray",
    borderRadius: 8,
    width: "100%",
    padding: 10,
    marginTop: 20,
  },
  buttonContainer: {
    marginTop: 20,
    width: "100%",
    alignItems: "center",
  },
  button: {
    width: "100%",
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: "center",
    marginBottom: 10,
  },
  greenButton: {
    backgroundColor: "green",
  },
  blackButton: {
    backgroundColor: "black",
  },
  buttonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "bold",
  },
});

export default ConfirmAccount;
