import React, { useState } from 'react';
import { FlatList, Image, TouchableOpacity } from 'react-native';
import styles from './ReactionsStyles';
import { ClapIcon, FireIcon, HeartIcon, LaughIcon, ShockIcon, ThumbsUpIcon } from '../../res/Svg';

const Reactions = ({ onSelectReaction,size,extraStyle,contentContainerStyle }) => {
  const [selectedReactionId, setSelectedReactionId] = useState(null);

 const emojis =[
    {
      id: "f895b9ba-8d86-455f-9a7d-ce87d7504e0b",
      name: "Cry",
      file: "https://cf-blast-storage.livelikecdn.com/assets/902513b1-3f16-4d96-a36f-b9832c31e4f4.png",
      mimetype: "image/png"
    },
    {
      id: "3eb68561-4ab3-4d9d-a5c8-c8b26230867c",
      name: "<PERSON><PERSON>",
      file: "https://cf-blast-storage.livelikecdn.com/assets/7c26355b-e012-48cc-8f45-3fac124c9b12.png",
      mimetype: "image/png"
    },
    {
      id: "1ea8f088-0a2e-4287-926e-3de10464b9c9",
      name: "Love",
      file: "https://cf-blast-storage.livelikecdn.com/assets/7599e585-9ba4-4066-97b7-108d1281d3fb.png",
      mimetype: "image/png"
    },
    {
      id: "dad55363-f65f-45ba-b998-2e1ad57f2870",
      name: "Shock",
      file: "https://cf-blast-storage.livelikecdn.com/assets/4a3a0f42-ae8c-4d2d-ae75-0876d48ad081.png",
      mimetype: "image/png"
    },
    {
      id: "e1963cee-4fc4-4060-8cd2-3e681b6b568f",
      name: "Wow",
      file: "https://cf-blast-storage.livelikecdn.com/assets/7a407ebd-ba69-4271-8f16-7beffd4b16a6.png",
      mimetype: "image/png"
    }
  ]

  const handleReactionPress = (reactionId) => {
    if (selectedReactionId === reactionId) {
      // Deselect the reaction if it's already selected
      setSelectedReactionId(null);
      onSelectReaction(null);
    } else {
      // Select the reaction
      setSelectedReactionId(reactionId);
      onSelectReaction(emojis.find((emoji) => emoji.id === reactionId)?.name,emojis.find((emoji) => emoji.id === reactionId)?.file);
    }
  };

  const renderReactionIcons = ({ item }) => {
    const isSelected = selectedReactionId === item.id;
    return (
      <TouchableOpacity
        style={[styles.icon, isSelected && styles.selectedIcon]}
        onPress={() => handleReactionPress(item.id,item?.name)}
      >
       <Image source={{ uri: item.file }} style={{ width: size, height: size }} />
      </TouchableOpacity>
    );
  };

  return (
    <FlatList
      data={emojis}
      renderItem={renderReactionIcons}
      keyExtractor={(item) => item.id.toString()}
      horizontal={true}
      style={[styles.reactionListStyle, {...extraStyle}]}
      contentContainerStyle= {contentContainerStyle}
    />
  );
}

export default Reactions;
