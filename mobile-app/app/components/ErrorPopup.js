import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Image } from 'react-native';
import { Colors } from '../res/Colors';
import { HeightSize, Size } from '../res/Size';
import { commonStyles } from '../res/CommonStyles';
import { Images } from '../res/Images';


const ErrorPopup = ({ error, onClose }) => {
  return (
  <View style={[commonStyles.modalBackdrop, styles.container]}>
      <View style={[commonStyles.modalContainer, styles.popup]}>
        <TouchableOpacity 
          onPress={onClose}
          style={commonStyles.modalCloseWrapper}
          activeOpacity={0.8}
          hitSlop={{ top: 40, bottom: 40, left: 40, right: 40 }}
        >
          <Image
            source={Images.cross}
            style={commonStyles.modalCloseIcon}
          />
        </TouchableOpacity>
        <Text style={styles.title}>{error.message}</Text>
        <Text style={styles.description}>{error.description}</Text>
        <Text style={styles.details}>{error.details}</Text>
        {/* <Text style={styles.fileSize}>File: {error.fileSize} MB</Text> */}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
  },
  popup: {
  paddingVertical: HeightSize(4),
  paddingHorizontal: Size(5)
  },
  closeButton: {
    position: 'absolute',
    right: Size(3),
    top: Size(2),
  },
  closeText: {
    fontSize: Size(6),
    color: Colors.black,
  },
  title: {
    fontSize: Size(6.2),
    fontFamily: "favelaBold",
    marginBottom: HeightSize(2),
    color: Colors.black,
    textAlign: "center"
  },
  description: {
    fontSize: Size(4),
    marginBottom: HeightSize(2),
    fontFamily: "PoppinsSemiBold",
    textAlign: 'center',
    color: Colors.black,
  },
  details: {
    fontSize: Size(3.7),
    textAlign: 'center',
    fontFamily:"Regular",
    lineHeight: 25,
    marginBottom: HeightSize(2),
    color: Colors.black,
  },
  fileSize: {
    fontSize: Size(3.5),
    color: Colors.black,
  },
});

export default ErrorPopup;