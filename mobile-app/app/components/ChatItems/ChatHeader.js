import { useState } from "react";
import {
  Modal,
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { BackIcon, DottedMore } from "../../res/Svg";
import { HeightSize, Size } from "../../res/Size";
import { useNavigation } from "@react-navigation/native";
import { Colors } from "../../res/Colors";
import BlockReportModal from "../BlockReportModal";
import Button from "../Button/Button";
import { dispatch } from "../../redux/store";
import { shallowEqual, useSelector } from "react-redux";
import { selectUserInfo } from "../../redux/selectors/authSelectors";
import UserAvatar from "../UserAvatar";

const ChatHeader = ({ userDetails, fromMyProfileContent, userId }) => {
  const navigation = useNavigation();
  const [blockModalVisible, setBlockModalVisible] = useState(false);
  const [confirmationVisible, setConfirmationVisible] = useState(false);
  const [actionType, setActionType] = useState(null);
  const [modalPosition, setModalPosition] = useState({ top: 0, right: 0 });

  const userInfo = useSelector(selectUserInfo, shallowEqual);

  const closeBlockReportModal = () => {
    setBlockModalVisible(false);
  };

  const openBlockReportModal = (nativeEvent) => {
    setModalPosition({
      top: nativeEvent.pageY,
      right: Size(10),
    });
    setBlockModalVisible(true);
  };

  const handleOpenConfirmation = (type) => {
    closeBlockReportModal();
    setActionType(type);
    setConfirmationVisible(true);
  };

  const handleConfirmAction = () => {
    if (actionType === "block") {
      handleBlockUser();
    } else if (actionType === "report") {
      // handleReportComment();
    }
    setConfirmationVisible(false);
    closeBlockReportModal();
  };

  const handleBlockUser = async () => {
    try {
      if (!userDetails.id) {
        console.error("No user selected to block");
        return;
      }

      const payload = {
        blockedUserId: userDetails.id,
        reason: "", // Optionally include a reason for blocking
      };

      // Dispatch the blockUser action
      const status = await dispatch.user.blockUser(payload);

      if (status.status === 1) {
        // Fetch the updated userInfo before navigating
        await dispatch.user.fetchUserDetails(userInfo.id);
        // Navigate back to MessageList and pass the blocked user ID
        navigation.navigate("MessageList", { blockedUserId: userDetails.id });
      }
    } catch (err) {
      console.error(
        "Error in blocking user:",
        err.response ? err.response.data : err.message
      );
    }
  };

  if (!userDetails) {
    return null;
  }

  return (
    <View style={styles.Container}>
      <View style={styles.headerContainer}>
        <TouchableOpacity
          onPress={() =>
            navigation.navigate("MessageList", {
              userId: userId,
              fromMyProfileContent: fromMyProfileContent,
            })
          }
          activeOpacity={9}
        >
          <BackIcon />
        </TouchableOpacity>
        <TouchableOpacity
          style={{
            flex: 1,
            alignItems: "center",
            flexDirection: "row",
            paddingLeft: Size(2),
          }}
          onPress={() => {
            navigation.navigate("MyProfileScreenStack", {
              screen: "MyProfile",
              params: { userId: userDetails?.id },
            });
          }}
        >
          <UserAvatar user={userDetails} />

          <View
            style={{
              marginLeft: Size(3),
              flex: 1,
            }}
          >
            <Text style={styles.nameTxt} numberOfLines={1} ellipsizeMode="tail">
              {`${userDetails?.firstName} ${userDetails?.lastName}`}
            </Text>

            {/* Team Name */}
            {userDetails?.teamName && (
              <Text style={styles.teamTxt}>
                {userDetails?.teamName === "N/A"
                  ? "No Team"
                  : userDetails?.teamName}
              </Text>
            )}
          </View>
        </TouchableOpacity>
        <TouchableOpacity
          style={{ alignItems: "flex-end" }}
          onPress={(event) => openBlockReportModal(event.nativeEvent)}
        >
          <DottedMore />
        </TouchableOpacity>
      </View>
      {blockModalVisible && (
        <BlockReportModal
          visible={blockModalVisible}
          onClose={() => setBlockModalVisible(false)}
          onBlock={() => handleOpenConfirmation("block")}
          onReport={() => handleOpenConfirmation("report")}
          top={modalPosition.top}
          right={modalPosition.right}
          showReportOption={false} // Hide report option on messages screen
        />
      )}

      {/* Confirmation Modal */}
      <Modal
        visible={confirmationVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setConfirmationVisible(false)}
      >
        <View style={styles.confirmationModalContainer}>
          <View style={styles.confirmationModalContent}>
            <Text style={styles.confirmationText}>
              Are you sure you want to{" "}
              <Text style={{ color: Colors.red }}>
                {actionType === "block" ? "block" : "report"}
              </Text>{" "}
              {actionType === "block" ? "this user?" : "this post?"}
            </Text>
            <View style={styles.confirmationButtonContainer}>
              <Button
                title="Yes"
                onPress={() => handleConfirmAction()}
                containerStyle={{ width: "45%" }}
                backgroundColor={Colors.black}
                textStyleInsideButton={{
                  fontSize: Size(4),
                  fontFamily: "Regular",
                }}
                height={HeightSize(5)}
              />
              <Button
                title="No"
                onPress={() => setConfirmationVisible(false)}
                containerStyle={{
                  borderWidth: 1,
                  borderColor: "#A5A5A5",
                  width: "45%",
                }}
                backgroundColor={Colors.white}
                textStyleInsideButton={{
                  color: "#A5A5A5",
                  fontSize: Size(4),
                  fontFamily: "Regular",
                }}
                height={HeightSize(5)}
              />
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default ChatHeader;

const styles = StyleSheet.create({
  Container: {
    //flex: 1,
    // paddingHorizontal: Size(5),
    // backgroundColor: Colors.white,
    paddingTop: Platform.OS === "ios" ? HeightSize(4) : 0,
  },
  imageContainer: {
    width: Size(14),
    height: Size(14),
    borderRadius: Size(7),
    marginLeft: Size(2),
  },
  headerContainer: {
    flexDirection: "row",
    //maxWidth: "75%",
    gap: 7,
    alignItems: "center",
    borderBottomWidth: 1,
    paddingHorizontal: Size(5),
    paddingVertical: HeightSize(2),
    borderBottomColor: "rgba(0, 0, 0, 0.1)",
    marginBottom: HeightSize(2),
  },
  nameTxt: {
    fontSize: Size(4),
    fontFamily: "PoppinsBold",
    lineHeight: Size(5.5),
  },
  teamTxt: {
    fontSize: Size(4),
    fontFamily: "Regular",
  },
  confirmationModalContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  confirmationModalContent: {
    backgroundColor: "white",
    paddingHorizontal: Size(4.5),
    borderRadius: Size(9),
    marginHorizontal: Size(5),
    alignItems: "center",
    paddingVertical: HeightSize(2),
    width: "80%",
  },
  confirmationText: {
    marginBottom: HeightSize(2),
    fontSize: Size(5),
    textAlign: "center",
    fontFamily: "PoppinsMedium",
  },
  confirmationButtonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
    paddingHorizontal: Size(5),
  },
});
