import { View, Text, TouchableOpacity } from "react-native";
import { formatTime } from "../../constants/misc";
import { linkify } from "../../constants/Constant";
import styles from "./ChatMessageStyles";
import { Size } from "../../res/Size";
import { ReportIcon } from "../../res/Svg";
import { useNavigation } from "@react-navigation/native";
import { useMemo } from "react";
import UserAvatar from "../UserAvatar";

const ChatMessageContainer = ({
  onPress,
  onLongPress,
  isFromLoggedInUser,
  isLastMessageInSet,
  isReported,
  message,
  children,
}) => {
  const { created_at, sender_image_url } = message;

  return (
    <TouchableOpacity onPress={onPress} onLongPress={onLongPress}>
      <View
        style={[
          styles.messageContainer,
          isFromLoggedInUser && styles.senderContainer,
        ]}
      >
        {isLastMessageInSet && !isFromLoggedInUser && (
          <UserAvatar
            user={{
              photoUrl: sender_image_url,
            }}
            size={9}
          />
        )}
        <View
          style={[
            styles.messageTextContainer,
            isFromLoggedInUser
              ? styles.senderTextContainer
              : styles.receiverTextContainer,
            !isLastMessageInSet &&
              !isFromLoggedInUser && { marginLeft: Size(11) },
          ]}
        >
          {children}
        </View>
        {isReported && !isFromLoggedInUser && (
          <ReportIcon
            width={Size(5)}
            height={Size(5)}
            style={{
              position: "absolute",
              top: "50%",
              transform: [{ translateY: -Size(2.5) }],
              right: Size(1),
            }}
          />
        )}
      </View>
      <Text
        style={[styles.timestamp, isFromLoggedInUser && styles.timestampSender]}
      >
        {formatTime(created_at)}
      </Text>
    </TouchableOpacity>
  );
};

const ChatMessage = ({
  message,
  isFromLoggedInUser,
  isLastMessageInSet,
  isReported,
  openBlockReportModal,
  fromMyProfileContent,
  recipientId,
}) => {
  const { message: messageText, message_metadata } = message;

  const navigation = useNavigation();

  const handleShareNavigation = (type, id, userId) => {
    return () => {
      console.log("handleShareNavigation", type, id, userId);
      switch (type) {
        case "SHARE_PROFILE":
          navigation.navigate("MyProfileScreenStack", {
            screen: "MyProfile",
            params: { userId: userId },
          });
          break;
        case "SHARE_ANNOUNCEMENT":
        case "PINNED_POST":
          navigation.navigate("AnnouncementPage", {
            announcementId: id,
          });
          break;
        case "SHARE_HIGHLIGHT":
          navigation.navigate("FeedDetail", {
            highlightId: id,
            userId: userId,
            fromShareHighlight: true,
            fromMyProfileContent: fromMyProfileContent,
            recipientId: recipientId,
          });
          break;
        default:
          break;
      }
    };
  };

  const sharingType = useMemo(() => {
    if (!message.message_metadata) {
      return null;
    }

    if (typeof message_metadata === "string") {
      try {
        const metadata = JSON.parse(message_metadata);
        return metadata?.sharingType || null;
      } catch (error) {
        console.error("Failed to parse message_metadata:", error);
        return null;
      }
    }

    return message_metadata?.sharingType || null;
  }, [message.message_metadata]);

  const shareContentDetails = useMemo(() => {
    if (!message.message_metadata) {
      return null;
    }

    if (typeof message_metadata === "string") {
      try {
        const metadata = JSON.parse(message_metadata);
        return metadata?.shareContentDetails || null;
      } catch (error) {
        console.error("Failed to parse message_metadata:", error);
        return null;
      }
    }

    return message_metadata?.shareContentDetails || null;
  }, [message.message_metadata]);

  if (!sharingType) {
    return (
      <ChatMessageContainer
        onLongPress={
          !isFromLoggedInUser
            ? (e) => openBlockReportModal(message, e.nativeEvent)
            : undefined
        }
        isFromLoggedInUser={isFromLoggedInUser}
        isLastMessageInSet={isLastMessageInSet}
        isReported={isReported}
        message={message}
      >
        {linkify(messageText)}
      </ChatMessageContainer>
    );
  }

  return (
    <>
      <ChatMessageContainer
        onPress={handleShareNavigation(
          sharingType,
          shareContentDetails?.id,
          shareContentDetails?.userId
        )}
        onLongPress={
          !isFromLoggedInUser
            ? (e) => openBlockReportModal(message, e.nativeEvent)
            : undefined
        }
        isFromLoggedInUser={isFromLoggedInUser}
        isLastMessageInSet={isLastMessageInSet}
        isReported={isReported}
        message={message}
      >
        <View style={{ flex: 1 }}>
          {messageText && (
            <Text style={styles.shareMessageText}>{messageText}</Text>
          )}
          <View style={styles.shareMessageDetail}>
            <UserAvatar
              user={{
                photoUrl: shareContentDetails?.mediaUrl,
              }}
            />
            <View style={styles.shareMessageDetailContainer}>
              {sharingType === "SHARE_PROFILE" ? (
                <>
                  <Text
                    style={styles.sharedMessageTitleText}
                    numberOfLines={2}
                    ellipsizeMode="tail"
                  >
                    {shareContentDetails?.text}
                  </Text>
                  {shareContentDetails.teamName && (
                    <Text
                      style={styles.sharedMessageTeamText}
                      numberOfLines={2}
                      ellipsizeMode="tail"
                    >
                      {shareContentDetails?.teamName}
                    </Text>
                  )}
                </>
              ) : (
                <Text
                  style={styles.sharedMessageTeamText}
                  numberOfLines={2}
                  ellipsizeMode="tail"
                >
                  {shareContentDetails?.text}
                </Text>
              )}
            </View>
          </View>
          <View style={styles.shareMessageLink}>
            <Text style={styles.shareMessageLinkText}>
              {sharingType === "SHARE_PROFILE"
                ? "View Profile"
                : sharingType === "SHARE_ANNOUNCEMENT"
                ? "View Competition"
                : "View Post"}
            </Text>
          </View>
        </View>
      </ChatMessageContainer>
    </>
  );
};

export default ChatMessage;
