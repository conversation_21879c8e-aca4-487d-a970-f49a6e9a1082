import { Platform, StyleSheet } from "react-native";
import { Colors } from "../../res/Colors";
import { HeightSize, Size } from "../../res/Size";

const styles = StyleSheet.create({
  container: {
    borderTopWidth: 1,
    borderTopColor: "rgba(0,0,0,0.1)",
    paddingTop: 16,
    paddingBottom: Platform.OS === "ios" ? 28 : 20,
    paddingHorizontal: 16,
    backgroundColor: "#FAFAFA",
  },
  sendMessageInputWrap: {
    flexDirection: "row",
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.1)",
    borderRadius: 12,
    padding: 8,
    backgroundColor: Colors.white,
  },
  sendContent: {
    justifyContent: "space-between",
    alignItems: "flex-end",
  },
  textInputStyle: {
    flex: 1,
    maxHeight: 260,
  },
  textCountWapper: {
    flexDirection: "row",
    alignItems: "center",
    gap: 2,
  },
  textCount: {
    fontSize: 11,
    color: Colors.dark_grey,
  },
  longTextError: {
    fontFamily: "Regular",
    color: Colors.red,
    fontSize: 11,
    marginTop: 4,
  },
  sendIconStyle: {
    width: Size(5),
    height: Size(5),
  },
  iconContainer: {
    height: 32,
    width: 32,
    borderRadius: 8,
    backgroundColor: Colors.green,
    alignItems: "center",
    justifyContent: "center",
    marginLeft: 12,
  },
  imageContainer: {
    width: Size(30),
    height: Size(30),
  },
  modalContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  closeButton: {
    width: "100%",
    alignItems: "flex-end",
  },
  modalContent: {
    backgroundColor: "white",
    paddingVertical: HeightSize(1),
    borderRadius: Size(7),
    elevation: 5,
    width: "90%",
    marginTop: HeightSize(10),
    paddingHorizontal: Size(3),
    marginBottom: HeightSize(5),
    justifyContent: "center",
    alignItems: "center",
  },
  previewImage: {
    width: "90%",
    height: HeightSize(30),
    resizeMode: "contain",
    marginBottom: 20,
  },
  sendButton: {
    fontSize: 16,
    color: "blue",
    fontWeight: "bold",
  },
  sendCancelBtn: {
    flexDirection: "row",
    justifyContent: "space-between",
    //borderWidth: 1,
    width: "90%",
  },
});

export default styles;
