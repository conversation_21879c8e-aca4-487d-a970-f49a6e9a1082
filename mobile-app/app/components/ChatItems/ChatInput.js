import React, { useState, useEffect, useRef } from "react";
import {
  Image,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  Modal,
  KeyboardAvoidingView,
  Platform,
} from "react-native";
import { AlertIcon, CameraIcon, Paperclip } from "../../res/Svg";
import { Images } from "../../res/Images";
import styles from "./ChatInputStyles";
import * as ImagePicker from "expo-image-picker";
import { notifyError } from "../../constants/misc";
import { uploadS3 } from "../../../s3.config";
import { ResizeMode, Video } from "expo-av";
import { Size } from "../../res/Size";

const ChatInput = ({ sendTextMessage, sendImageMessage, sendVideoMessage }) => {
  const [value, setValue] = useState("");
  const [media, setMedia] = useState(null);
  const [mediaFileName, setMediaFileName] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [streamUrls, setStreamUrls] = useState(null);
  const videoRef = useRef(null);

  const handleSubmitTextMessage = () => {
    sendTextMessage(value);
    setValue("");
  };

  const pickMedia = async () => {
    let result = await ImagePicker.launchImageLibraryAsync({
      //mediaTypes: ImagePicker.MediaTypeOptions.All, // Allow images and videos
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
    });

    if (!result.canceled) {
      setMedia(result.assets[0].uri);
      setMediaFileName(`media_${Date.now()}`);

      setModalVisible(true);
    }
  };

  const AddMediaByCamera = async () => {
    let { granted, status } = await ImagePicker.getCameraPermissionsAsync();
    let finalStatus = status;
    if (!granted) {
      let res = await ImagePicker.requestCameraPermissionsAsync();
      finalStatus = res.status;
    }
    if (finalStatus === "granted") {
      let result = await ImagePicker.launchCameraAsync({
        // mediaTypes: ImagePicker.MediaTypeOptions.All, // Allow images and videos
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
      });
      if (!result.canceled) {
        setMedia(result.assets[0].uri);
        setMediaFileName(Date.now());
        setModalVisible(true);
      }
    } else {
      alert("Please grant camera permission from settings.");
    }
  };

  const isVideo = (uri) => {
    if (uri && uri.includes(".")) {
      const extension = uri.split(".").pop().toLowerCase();
      const videoExtensions = [
        "mp4",
        "avi",
        "mov",
        "wmv",
        "flv",
        "mkv",
        "webm",
        "mpeg",
      ];
      return videoExtensions.includes(extension);
    }
    return false;
  };

  const uploadS3Function = async () => {
    if (!media) {
      return;
    }

    uploadS3(media, mediaFileName, (assetUrl, fileName, urls) => {
      setStreamUrls(urls);

      // Call the appropriate function to handle sending the media message
      if (isVideo(media)) {
        //  uploadVideo(assetUrl);
      } else {
        uploadImage(assetUrl);
      }
    });
  };

  const uploadImage = async (file) => {
    sendImageMessage(file);
  };

  const uploadVideo = async (file) => {
    sendVideoMessage(file);
  };

  const handleSendMedia = () => {
    setModalVisible(false);
    uploadS3Function();
  };

  const handleCancel = () => {
    setModalVisible(false);
    setMedia(null);
  };

  return (
    <KeyboardAvoidingView behavior={Platform.OS === "ios" ? "padding" : null}>
      <View style={styles.container}>
        <View style={styles.sendMessageInputWrap}>
          <TextInput
            style={[styles.textInputStyle]}
            placeholder="Write a message..."
            placeholderTextColor="#e0e0e0"
            value={value}
            onChangeText={(text) => setValue(text)}
            multiline={true}
          />

          {/* <TouchableOpacity style={styles.iconContainer} onPress={pickMedia}>
          <Paperclip />
        </TouchableOpacity>
        <TouchableOpacity style={styles.iconContainer} onPress={AddMediaByCamera}>
          <CameraIcon />
        </TouchableOpacity> */}

          <View style={styles.sendContent}>
            {value.length > 100 && (
              <View style={styles.textCountWapper}>
                {value.length > 200 && <AlertIcon />}
                <Text
                  style={[
                    styles.textCount,
                    value.length > 200 && { color: "red" },
                  ]}
                >
                  {value.length}/200
                </Text>
              </View>
            )}

            <TouchableOpacity
              style={[
                styles.iconContainer,
                (value.trim().length === 0 || value.length > 200) && {
                  opacity: 0.4,
                },
              ]}
              onPress={handleSubmitTextMessage}
              disabled={value.trim().length === 0 || value.length > 200}
            >
              <Image source={Images.send_icon} style={styles.sendIconStyle} />
            </TouchableOpacity>
          </View>
        </View>

        {value.length > 200 && (
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              gap: 2,
            }}
          >
            <AlertIcon />
            <Text style={styles.longTextError}>Your message is too long.</Text>
          </View>
        )}

        <Modal
          animationType="slide"
          transparent={true}
          visible={modalVisible}
          onRequestClose={() => {
            setModalVisible(false);
          }}
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              {isVideo(media) ? (
                <Video
                  ref={videoRef}
                  source={{ uri: media }}
                  style={styles.previewImage}
                  useNativeControls={true}
                  resizeMode={ResizeMode.COVER}
                  shouldPlay={true}
                />
              ) : (
                <Image source={{ uri: media }} style={styles.previewImage} />
              )}
              <View style={styles.sendCancelBtn}>
                <TouchableOpacity onPress={handleSendMedia}>
                  <Text style={styles.sendButton}>Send</Text>
                </TouchableOpacity>
                <TouchableOpacity onPress={handleCancel}>
                  <Text style={styles.cancelButton}>Cancel</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      </View>
    </KeyboardAvoidingView>
  );
};

export default ChatInput;
