import { useEffect, useRef, useState } from "react";
import { View, Text, ScrollView, Modal } from "react-native";
import { useSelector } from "react-redux";
import styles from "./ChatMessageStyles";
import { Colors } from "../../res/Colors";
import { HeightSize, Size } from "../../res/Size";
import BlockReportModal from "../BlockReportModal";
import Button from "../Button/Button";
import { dispatch } from "../../redux/store";
import ChatMessage from "./ChatMessage";
import { useMemo } from "react";
import { useCallback } from "react";
import { formatDate } from "../../constants/misc";

const ChatMessageList = ({
  messages,
  receiverDetails,
  fromMyProfileContent,
  recipientId,
}) => {
  const scrollViewRef = useRef(null);

  const { userInfo } = useSelector((state) => state.auth.authUser);

  const [reportModalVisible, setReportModalVisible] = useState(false);
  const [confirmationVisible, setConfirmationVisible] = useState(false);
  const [actionType, setActionType] = useState(null);
  const [modalPosition, setModalPosition] = useState({ top: 0, right: 0 });
  const [selectedMessage, setSelectedMessage] = useState(null);

  const messagesByDate = useMemo(() => {
    return Object.entries(
      messages.reduce((acc, message) => {
        const date = new Date(message.created_at).toDateString();
        if (!acc[date]) {
          acc[date] = [];
        }
        acc[date].push(message);
        return acc;
      }, {})
    ).map(([date, messages]) => ({
      date,
      messages,
    }));
  }, [messages]);

  useEffect(() => {
    if (scrollViewRef.current) {
      scrollViewRef.current.scrollToEnd({ animated: true });
    }
  }, []);

  const openBlockReportModal = useCallback((message, nativeEvent) => {
    setSelectedMessage(message);
    setModalPosition({
      top: nativeEvent.pageY,
      right: Size(5),
    });
    setReportModalVisible(true);
  }, []);

  const closeBlockReportModal = useCallback(() => {
    setReportModalVisible(false);
  }, []);

  const handleOpenConfirmation = useCallback(
    (type) => {
      closeBlockReportModal();
      setActionType(type);
      setConfirmationVisible(true);
    },
    [closeBlockReportModal]
  );

  const handleConfirmAction = useCallback(() => {
    if (actionType === "report") {
      handleReportComment(selectedMessage);
    }
    setConfirmationVisible(false);
    closeBlockReportModal();
  }, [handleReportComment, closeBlockReportModal]);

  const handleReportComment = useCallback(
    async (message) => {
      const reporterFullName = `${userInfo?.firstName || "Unknown"} ${
        userInfo?.lastName || "User"
      }`;
      const reportedFullName = `${receiverDetails?.firstName || "Unknown"} ${
        receiverDetails?.lastName || "User"
      }`;
      const reportedUserId = recipientId || "UnknownUserId";
      const reportedContent = message?.message || "No content";

      const payload = {
        reporterUserId: userInfo?.id || "UnknownUserId",
        reportedUserId: reportedUserId,
        reporterFullName: reporterFullName,
        reportedFullName: reportedFullName,
        reason: "",
        reportedContent: reportedContent,
        reportedPhotoUrl: "", // Optional
        reporterPhotoUrl: userInfo?.photoUrl || "",
        reportType: "MESSAGE",
        contendId: message?.id,
      };

      try {
        const status = await dispatch.user.submitReport(payload);
        if (status === 1) {
          console.debug("Report submitted successfully");
          await dispatch.user.fetchUserDetails(userInfo.id);
        } else {
          console.error("Report submission failed");
        }
      } catch (error) {
        console.error("Error in submitting report:", error.message);
      }
      closeBlockReportModal();
    },
    [closeBlockReportModal, userInfo]
  );

  return (
    <>
      <ScrollView
        ref={scrollViewRef}
        contentContainerStyle={styles.container}
        onContentSizeChange={() =>
          scrollViewRef.current.scrollToEnd({ animated: true })
        }
      >
        {messagesByDate.map((dateGroup) => (
          <View key={dateGroup.date}>
            <Text style={styles.dateHeader}>{formatDate(dateGroup.date)}</Text>
            {dateGroup.messages.map((message, index) => {
              const nextMessage = dateGroup.messages[index + 1];
              const isLastMessageInSet =
                !nextMessage || nextMessage.sender_id !== message.sender_id;

              return (
                <ChatMessage
                  key={message.id}
                  message={message}
                  isFromLoggedInUser={
                    message.sender_id === userInfo?.liveLikeProfileId
                  }
                  isLastMessageInSet={isLastMessageInSet}
                  isReported={userInfo?.reports?.some(
                    (report) => report.contendId === message.id
                  )}
                  showTimestamp={isLastMessageInSet}
                  openBlockReportModal={openBlockReportModal}
                  fromMyProfileContent={fromMyProfileContent}
                  recipientId={recipientId}
                />
              );
            })}
          </View>
        ))}
      </ScrollView>
      {reportModalVisible && (
        <BlockReportModal
          visible={reportModalVisible}
          onClose={() => setReportModalVisible(false)}
          onBlock={() => handleOpenConfirmation("block")}
          onReport={() => handleOpenConfirmation("report")}
          top={modalPosition.top}
          right={modalPosition.right}
          showBlockOption={false}
          text={"Message"}
          pointedLeft={true}
          hugContent={true}
        />
      )}
      <Modal
        visible={confirmationVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setConfirmationVisible(false)}
      >
        <View style={styles.confirmationModalContainer}>
          <View style={styles.confirmationModalContent}>
            <Text style={styles.confirmationText}>
              Are you sure you want to{" "}
              <Text style={{ color: Colors.red }}>report</Text> this message?
            </Text>
            <View style={styles.confirmationButtonContainer}>
              <Button
                title="Yes"
                onPress={() => handleConfirmAction()}
                containerStyle={{ width: "45%" }}
                backgroundColor={Colors.black}
                textStyleInsideButton={{
                  fontSize: Size(4),
                  fontFamily: "Regular",
                }}
                height={HeightSize(5)}
              />
              <Button
                title="No"
                onPress={() => setConfirmationVisible(false)}
                containerStyle={{
                  borderWidth: 1,
                  borderColor: "#A5A5A5",
                  width: "45%",
                }}
                backgroundColor={Colors.white}
                textStyleInsideButton={{
                  color: "#A5A5A5",
                  fontSize: Size(4),
                  fontFamily: "Regular",
                }}
                height={HeightSize(5)}
              />
            </View>
          </View>
        </View>
      </Modal>
    </>
  );
};

export default ChatMessageList;
