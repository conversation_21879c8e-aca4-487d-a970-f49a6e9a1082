import { StyleSheet } from "react-native";
import { HeightSize, Size } from "../../res/Size";
import { Colors } from "../../res/Colors";

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  // closeButton: {
  //   width: '100%',
  //   alignItems: "flex-end",
  // },
  modalContent: {
    backgroundColor: "white",
    paddingVertical: HeightSize(1),
    borderRadius: Size(7),
    elevation: 5,
    width: "90%",
    marginTop: HeightSize(20),
    paddingHorizontal: Size(3),
    marginBottom: HeightSize(5),
  },
  crossStyle: {
    width: Size(4),
    height: Size(4),
  },
  crossBtn: {
    backgroundColor: "#e5e7eb",
    width: Size(7),
    height: <PERSON>ze(7),
    borderRadius: <PERSON>ze(3.5),
    justifyContent: "center",
    alignItems: "center",
    position: "absolute",
    top: HeightSize(1),
    right: <PERSON>ze(2),
    zIndex: 1,
  },
  selectedImgContainer: {
    position: "relative",
    width: "100%",
    height: HeightSize(35),
    padding: Size(2),
  },
  selectedImgStyle: {
    width: "100%",
    height: "100%",
  },
  sendImgStyle: {
    width: "100%",
    height: "100%",
    resizeMode: "cover",
  },
  container: {
    flexGrow: 1,
    backgroundColor: Colors.white,
    paddingHorizontal: Size(5),
    paddingBottom: HeightSize(2),
  },
  chatContainer: {
    flexDirection: "column",
    gap: 5,
  },
  messageContainer: {
    flexDirection: "row",
    alignItems: "flex-end",
    gap: Size(2),
    marginTop: HeightSize(1),
    marginBottom: HeightSize(0.6),
  },
  senderContainer: {
    justifyContent: "flex-end",
  },
  messageTextContainer: {
    maxWidth: Size(75),
    minHeight: Size(10),
    flexDirection: "row",
    flexWrap: "wrap",
    borderWidth: 1,
    borderRadius: Size(4),
    paddingHorizontal: Size(3),
    paddingVertical: HeightSize(1.5),
  },
  senderTextContainer: {
    backgroundColor: "#F1F3F5",
    borderColor: "#F1F3F5",
    borderBottomRightRadius: 0,
  },
  receiverTextContainer: {
    backgroundColor: "white",
    borderColor: Colors.lightest_grey,
    borderBottomLeftRadius: 0,
  },
  timestamp: {
    color: Colors.light_grey,
    fontSize: Size(3.2),
    fontFamily: "Regular",
    marginLeft: Size(11),
  },
  timestampSender: {
    marginLeft: 0,
    alignSelf: "flex-end",
  },
  modalView: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.8)",
  },
  modalImage: {
    width: "90%",
    height: "90%",
    resizeMode: "contain",
  },
  closeButton: {
    position: "absolute",
    top: 20,
    right: 20,
    zIndex: 1,
  },
  closeButtonText: {
    fontSize: Size(5),
    color: "white",
  },
  hidden: {
    height: 0,
    overflow: "hidden",
  },
  confirmationModalContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  confirmationModalContent: {
    backgroundColor: "white",
    paddingHorizontal: Size(4.5),
    borderRadius: Size(9),
    marginHorizontal: Size(5),
    alignItems: "center",
    paddingVertical: HeightSize(2),
    width: "80%",
  },
  confirmationText: {
    marginBottom: HeightSize(2),
    fontSize: Size(5),
    textAlign: "center",
    fontFamily: "PoppinsMedium",
  },
  confirmationButtonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
    paddingHorizontal: Size(5),
  },
  dateHeader: {
    fontSize: Size(3.5),
    color: Colors.black,
    textAlign: "center",
    marginTop: HeightSize(2),
    marginBottom: HeightSize(1),
  },
  shareMessageText: {
    fontFamily: "Poppins",
    marginBottom: HeightSize(1),
    color: Colors.black,
  },
  shareMessageDetail: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    flex: 1,
  },
  shareMessageDetailContainer: {
    flexDirection: "column",
    justifyContent: "center",
    gap: 3,
    flex: 1,
  },
  sharedMessageTitleText: {
    fontFamily: "PoppinsSemiBold",
  },
  sharedMessageTeamText: {
    fontFamily: "Poppins",
    color: Colors.dark_grey,
  },
  shareMessageLink: {
    borderTopWidth: 1,
    borderColor: Colors.lighter_grey,
    marginTop: HeightSize(1.5),
    paddingTop: HeightSize(1.5),
  },
  shareMessageLinkText: {
    color: Colors.dark_grey,
    fontSize: Size(3.5),
    fontFamily: "Regular",
    textAlign: "center",
    textDecorationLine: "underline",
  },
  userProfileImage: {
    width: 42,
    height: 42,
    borderRadius: 21,
    objectFit: "cover",
  },
  profileContent: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  postImage: {
    width: Size(13),
    height: Size(13),
    borderRadius: Size(1),
    objectFit: "cover",
  },
  video: {
    width: "100%",
    borderRadius: 0,
    overflow: "hidden",
    aspectRatio: 3 / 4,
  },
});

export default styles;
