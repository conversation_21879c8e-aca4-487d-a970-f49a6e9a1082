import React, { useEffect, useRef, useState } from "react";
import { View, StyleSheet, TouchableOpacity, Image } from "react-native";
import { Video } from "expo-av";
import { Ionicons } from "@expo/vector-icons";
import { Size } from "../../res/Size";
import { Colors } from "../../res/Colors";


const VideoUI = ({ videoUrl }) => {
  const videoRef = useRef(null);
  const [playing, setPlaying] = useState(false);

  const videoHandler = (control) => {
    if (control === "play") {
      videoRef.current.playAsync();
      setPlaying(true);
    } else if (control === "pause") {
      videoRef.current.pauseAsync();
      setPlaying(false);
    }
  };

  useEffect(() => {
    if (videoRef.current && !playing) {
      videoRef.current.loadAsync();
    }

    const loadVideo = async () => {
      if (videoRef.current) {
        await videoRef.current.playAsync();
        setPlaying(true);
      }
    };

    if (videoRef.current) {
      videoRef.current.setOnPlaybackStatusUpdate((status) => {
        if (status.didJustFinish) {
          setPlaying(false);
        }
      });
      loadVideo();
    }

    return () => {
      if (videoRef.current) {
        videoRef.current.unloadAsync();
      }
    };
  }, []);

  return (
    <View style={styles.container}>
      <View style={styles.videoContainer}>
        <Video
          ref={videoRef}
          source={{ uri: videoUrl }}
          style={styles.video}
          resizeMode="cover"
          useNativeControls={false}
          shouldPlay={false}
          isLooping
        />
      </View>
      {playing ? (
        <TouchableOpacity
          style={styles.overlay}
          onPress={() => videoHandler("pause")}
        />
      ) : (
        <TouchableOpacity
          style={styles.overlay}
          onPress={() => videoHandler("play")}
        >
          <View style={styles.playButton}>
          <Ionicons name="play" size={Size(7)} color={Colors.black} />
          </View>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: "100%",
    height: "100%",
    position: "relative",
  },
  videoContainer: {
    width: "100%",
    height: 300,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "black",
    borderRadius: 32,
    overflow: "hidden",
    padding: 3,
  },
  video: {
    width: "100%",
    height: "100%",
    borderRadius: 32,
  },
  overlay: {
    position: "absolute",
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
    justifyContent: "center",
    alignItems: "center",
  },
  playButton: {
    width: 50,
    height: 50,
    backgroundColor: "rgba(255, 255, 255, 0.8)",
    borderRadius: 25,
    justifyContent: "center",
    alignItems: "center",
  },
});

export default VideoUI;
