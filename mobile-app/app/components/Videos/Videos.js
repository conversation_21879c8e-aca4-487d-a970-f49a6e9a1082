import React, { useRef } from 'react';
import { View } from 'react-native';
import { Video } from 'expo-av';

const Videos = ({ setCurrentVideo, style, url, ...others }) => {
  const videoRef = useRef(null);

  return (
    <View ref={others.observe}>
      <Video
        ref={videoRef}
        source={{ uri: `${url}#t=0.003` }}
        style={style}
        resizeMode="cover"
        useNativeControls 
        onPlaybackStatusUpdate={status => {
          // Handle playback status update
          if (status.isPlaying) {
            setCurrentVideo({ Url: url, playing: true });
          } else {
            setCurrentVideo({ Url: url, playing: false });
          }
        }}
      />
    </View>
  );
};

export default Videos;
