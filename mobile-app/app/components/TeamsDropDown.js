// REACT //
import React, { useState, useEffect } from "react";

// REACT NATIVE //
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  StyleSheet,
} from "react-native";

// OTHERS //
import { Colors } from "../res/Colors";
import { HeightSize, Size } from "../res/Size";

const TeamDropdown = ({ teams, selectTeam, setSelectTeam }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedValue, setSelectedValue] = useState("");

  useEffect(() => {
    if (teams?.length > 0) {
      if (selectTeam?.teamName) {
        setSelectedValue(selectTeam.teamName);
      } 
    }
  }, [teams, selectTeam]);

  const handleSelect = (team) => {
    setSelectedValue(team.teamName);
    setSelectTeam(team);
    setIsOpen(false);
  };

  return (
    <View>
      {/* Selected Team View */}
      <TouchableOpacity
        style={styles.dropdown}
        onPress={() => setIsOpen((prev) => !prev)}
      >
        <Text style={styles.selectedText}>
          {selectedValue || "Select a Team"}
        </Text>
      </TouchableOpacity>

      {/* Dropdown List */}
      {isOpen && (
        <FlatList
          data={[...teams]?.sort((a, b) => b.index - a.index)}
          keyExtractor={(item) => item.teamName}
          style={styles.dropdownList}
          showsVerticalScrollIndicator={false}
          nestedScrollEnabled={true}
          renderItem={({ item, index }) => {
            const isLastItem = index === teams.length - 1;

            return (
              <TouchableOpacity
                style={[
                  styles.listItem,
                  isLastItem && { borderBottomWidth: 0 },
                ]}
                onPress={() => handleSelect(item)}
              >
                <Text style={styles.listItemText}>{item.teamName}</Text>
              </TouchableOpacity>
            );
          }}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  dropdown: {
    borderColor: Colors.black,
    borderBottomWidth: 1,
    height: HeightSize(4),
    justifyContent: "center",
  },
  selectedText: {
    fontSize: Size(3.5),
    color: Colors.black,
  },
  dropdownList: {
    backgroundColor: Colors.white,
    width: "100%",
    borderBottomLeftRadius: Size(4),
    borderBottomRightRadius: Size(4),
    borderWidth: 1,
    borderTopWidth: 0,
    borderColor: Colors.black,
    maxHeight: HeightSize(14),
  },
  listItem: {
    paddingVertical: HeightSize(1.5),
    marginHorizontal: Size(2),
    borderBottomWidth: 1,
    borderColor: Colors.grey,
  },
  listItemText: {
    fontSize: Size(3.5),
    color: Colors.black,
  },
});

export default TeamDropdown;
