import { Dimensions, Image, StyleSheet, Text, View } from "react-native";
import { Images } from "../res/Images";

const windowHeight = Dimensions.get("window").height;

const NotFound = () => {
  return (
    <View style={styles.announcementNotFoundWrapper}>
      <View>
        <Image
          source={Images.notFound}
          style={styles.announcementNotFoundImage}
          resizeMode="contain"
        />
        <Text style={styles.notFoundText}>Post not found...</Text>
        <Text style={styles.notFoundDescription}>
          Oh no! It looks like the post that you are looking for is no longer
          available.
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  announcementNotFoundWrapper: {
    height: windowHeight - 100,
    alignItems: "center",
    justifyContent: "center",
  },
  announcementNotFoundImage: {
    width: 319,
    margin: "auto",
  },
  notFoundText: {
    fontFamily: "PoppinsSemiBold",
    fontSize: 20,
    textAlign: "center",
    marginTop: 20,
    marginBottom: 4,
  },
  notFoundDescription: {
    textAlign: "center",
    fontFamily: "Regular",
    maxWidth: "60%",
    margin: "auto",
    fontSize: 12,
  },
});

export default NotFound;
