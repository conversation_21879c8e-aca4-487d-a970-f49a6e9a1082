import React, { useEffect, useState } from 'react';
import { Text, View, StyleSheet } from 'react-native';

const LoadingDots = ({ style }) => {
  const [dots, setDots] = useState('');

  useEffect(() => {
    const interval = setInterval(() => {
      setDots((prev) => (prev.length < 3 ? prev + '.' : ''));
    }, 500); // Adjust the speed as necessary

    return () => clearInterval(interval);
  }, []);

  return (
    <View style={[styles.container, style]}>
      <Text style={styles.text}>{dots}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  text: {
    fontSize: 24,
    color: 'white',
  },
});

export default LoadingDots;
