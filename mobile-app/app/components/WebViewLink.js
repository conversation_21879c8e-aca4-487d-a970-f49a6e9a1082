import React, { useEffect } from 'react';
import { WebView } from 'react-native-webview';
import { SafeAreaView, StatusBar, BackHandler } from 'react-native';
import { Colors } from '../res/Colors';
import { useNavigation } from '@react-navigation/native';

const PrivacyPolicyWebView = ({ route }) => {
  const navigation = useNavigation();
  const { url } = route.params;

  useEffect(() => {
    // Function to handle back button press
    const handleBackPress = () => {
      navigation.navigate('Login'); 
      return true; 
    };

    // Add event listener for hardware back button
    BackHandler.addEventListener('hardwareBackPress', handleBackPress);

    // Cleanup function to remove event listener
    return () => {
      BackHandler.removeEventListener('hardwareBackPress', handleBackPress);
    };
  }, [navigation]);

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <StatusBar barStyle="dark-content" hidden={false} backgroundColor={Colors.white} translucent={false} />
      <WebView source={{ uri: url }} />
    </SafeAreaView>
  );
};

export default PrivacyPolicyWebView;
