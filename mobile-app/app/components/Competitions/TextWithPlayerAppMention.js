import React, { Fragment } from 'react';
import { Text, Linking, StyleSheet } from 'react-native';

const TextWithPlayerAppMention = ({ text, stringToReplace }) => {

  const replacePlayerWithLink = (text) => {
    const playerMention = stringToReplace;

    const handleMailto = () => {
      Linking.openURL('mailto:<EMAIL>');
    };

    const mailtoLink = (
      <Text style={styles.link} onPress={handleMailto}>
        {stringToReplace}
      </Text>
    );

    const parts = text.split(playerMention);

    return (
      <>
        {parts.map((part, index) => (
          <Fragment key={index}>
            <Text>{part}</Text>
            {index < parts.length - 1 && mailtoLink}
          </Fragment>
        ))}
      </>
    );
  };

  return <Text>{replacePlayerWithLink(text)}</Text>;
};

const styles = StyleSheet.create({
  link: {
    color: 'blue',
    textDecorationLine: 'underline',
  },
});

export default TextWithPlayerAppMention;
