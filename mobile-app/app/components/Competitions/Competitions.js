import { useMemo, memo } from "react";
import { View, Text, Image, TouchableOpacity, StyleSheet } from "react-native";
import moment from "moment";
import { useNavigation } from "@react-navigation/native";
import { PIcon } from "../../res/Svg";
import VideoCarousel from "./VideoCarousel";
import { Size } from "../../res/Size";
import { Colors } from "../../res/Colors";
import ReactPlayerVideo, { videoContext } from "../ReactPlayerVideo";
import { getVideoStatus } from "../../constants/misc";
import LoadingFeedVideo from "../LoadingFeedVideo";
import { isEmpty } from "lodash";
import {
  HIGHLIGHT_BANNER_HEIGHT,
  HIGHLIGHT_MEDIA_HEIGHT,
} from "../../screens/Feed/FeedStyle";
import { FeedHighlightReactions } from "../../screens/Feed/FeedHiglightReactions";
import { FeedHighlightActions } from "../../screens/Feed/FeedHighlightActions";

const Competition = ({
  announcement,
  loggedUserId,
  isDetailPage = "false",
  isActive,
  shouldShowEmojiPanel,
  setShouldShowEmojiPanel,
  addUserReaction,
  setShowLoginPopup,
}) => {
  const navigation = useNavigation();

  function getEndDateTime(timestamp) {
    return moment(timestamp).format("DD/MM/YY [at] hh:mm A");
  }

  const videoToDisplay = useMemo(() => {
    if (!announcement) {
      return null;
    }

    const currentStatus = getVideoStatus(announcement, "minutes", 20);

    switch (currentStatus) {
      case "PROCESSING":
        return <LoadingFeedVideo />;
      case "SUCCESSFUL":
        return (
          <View>
            <>
              {announcement?.announcementType === "VOTE" ? (
                <VideoCarousel
                  voteSubmittedAssets={announcement.voteSubmittedAssets}
                />
              ) : (
                <ReactPlayerVideo
                  highlight={announcement}
                  isActive={isActive}
                  context={videoContext.Feed}
                />
              )}
            </>
          </View>
        );
      case "FAILED":
        return <LoadingFeedVideo isError={true} />;
      default:
        break;
    }
  }, [announcement, isActive, videoContext]);

  const handleOpenProfile = (id) => {
    if (id) {
      navigation.navigate("MyProfileScreenStack", {
        screen: "MyProfile",
        params: { id: id },
      });
    } else {
      navigation.navigate("Login");
    }
  };

  if (isEmpty(announcement)) {
    return null;
  }

  return (
    <View style={isDetailPage ? styles.detailPage : styles.container}>
      {announcement.announcementType === "PINNED_POST" ? (
        <View style={styles.nameImageContainer}>
          <PIcon width={Size(12)} height={Size(12)} />
          <View>
            <Text style={styles.nameTxt}>PLAYER</Text>
            <Text style={styles.titleTxt}>{announcement.title}</Text>
          </View>
          <View style={{ flex: 1, alignItems: "flex-end" }}>
            <Image
              source={require("../../../assets/pinGreen.png")}
              style={styles.pinIcon}
            />
          </View>
        </View>
      ) : (
        <View style={styles.header}>
          <View style={styles.titleContainer}>
            <Text style={styles.title} numberOfLines={2} ellipsizeMode="tail">
              {announcement.title}
            </Text>
            {announcement.announcementType !== "WINNER" && (
              <Text style={styles.submissionText}>
                Submissions close {getEndDateTime(announcement.endDateTime)}
              </Text>
            )}
          </View>
          <Image
            source={require("../../../assets/pinGreen.png")}
            style={styles.pinIcon}
          />
        </View>
      )}

      {announcement.type === "PHOTO" && (
        <TouchableOpacity
          style={styles.imageContainer}
          onPress={() =>
            !loggedUserId
              ? setShowLoginPopup(true)
              : navigation.navigate("AnnouncementPage", {
                  announcementId: announcement.id,
                })
          }
        >
          <Image source={{ uri: announcement.assetUrl }} style={styles.image} />
        </TouchableOpacity>
      )}

      {announcement.type === "VIDEO" && (
        <View style={styles.videoContainer}>{videoToDisplay}</View>
      )}

      <View style={styles.contentBottomWrapper}>
        {Boolean(loggedUserId) && (
          <FeedHighlightReactions
            highlight={announcement}
            onReactionsPress={() =>
              navigation.navigate("FeedDetail", {
                highlightId: announcement.id,
                userId: announcement.userId,
                type: "reactions",
              })
            }
          />
        )}
        <FeedHighlightActions
          highlight={announcement}
          shouldShowEmojiPanel={shouldShowEmojiPanel}
          setShouldShowEmojiPanel={setShouldShowEmojiPanel}
          navigation={navigation}
          addUserReaction={addUserReaction}
          loggedUserId={loggedUserId}
          setShowLoginPopup={setShowLoginPopup}
        />
        {isDetailPage ? (
          <>
            {announcement.announcementType === "WINNER" && (
              <Text
                style={{
                  marginTop: 12,
                }}
              >
                Your competition winner is{" "}
                <Text
                  onPress={() =>
                    handleOpenProfile(announcement.winnerHandle?.userId)
                  }
                  style={{
                    color: "#35498F",
                    textDecorationLine: "underline",
                  }}
                >
                  {announcement.winnerHandle?.userName}{" "}
                </Text>
              </Text>
            )}

            <Text style={styles.subtitle}>
              {announcement.announcementType === "PINNED_POST" ? (
                <Text style={styles.nameTxt}>PLAYER{"  "}</Text>
              ) : null}
              {announcement.subtitle}
            </Text>
          </>
        ) : (
          <TouchableOpacity
            style={styles.seeMoreContainer}
            onPress={() => {
              !loggedUserId
                ? setShowLoginPopup(true)
                : navigation.navigate("AnnouncementPage", {
                    announcementId: announcement.id,
                  });
            }}
          >
            <Text style={styles.seeMoreText}>
              {announcement.announcementType === "PINNED_POST" ? (
                <Text style={styles.nameTxt}>PLAYER{"  "}</Text>
              ) : null}
              {announcement.announcementType === "WINNER" ? (
                <Text>
                  Your competition winner is{" "}
                  <Text
                    onPress={() =>
                      handleOpenProfile(announcement.winnerHandle?.userId)
                    }
                    style={{
                      color: "#35498F",
                      textDecorationLine: "underline",
                    }}
                  >
                    {announcement.winnerHandle?.userName}
                  </Text>
                </Text>
              ) : (
                <Text>{announcement.subtitle?.slice(0, 70)}</Text>
              )}{" "}
              <Text style={styles.seeMore}>See more</Text>
            </Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

export const COMPETITION_BACKGROUND_COLOR = "#F2F2F2";

const styles = StyleSheet.create({
  container: {
    backgroundColor: COMPETITION_BACKGROUND_COLOR,
  },
  detailPage: {
    paddingTop: 15,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 10,
    alignItems: "center",
    height: HIGHLIGHT_BANNER_HEIGHT,
    gap: 6,
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontFamily: "favelaBlack",
    fontSize: 20,
    lineHeight: 26,
  },
  submissionText: {
    fontFamily: "Regular",
    fontSize: 12,
    marginTop: 2,
  },
  pinIcon: {
    width: 32,
    height: 32,
  },
  imageContainer: {
    width: "100%",
    position: "relative",
  },
  photoOverlay: {
    position: "absolute",
    height: "100%",
    width: "100%",
    zIndex: 10,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.4)",
  },
  photoTitle: {
    paddingHorizontal: 16,
    fontFamily: "favelaBlack",
    color: "#67FF5A",
    fontSize: 24,
    textAlign: "center",
    lineHeight: 28,
  },
  image: {
    width: "100%",
    height: HIGHLIGHT_MEDIA_HEIGHT,
    resizeMode: "cover",
  },
  videoContainer: {
    overflow: "hidden",
    width: "100%",
    height: HIGHLIGHT_MEDIA_HEIGHT,
  },
  emojiPanel: {
    flexDirection: "row",
    alignItems: "center",
  },
  emojiButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: "#A0A0A0",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 5,
  },
  emojiOptionsContainer: {
    position: "absolute",
    left: 35,
    backgroundColor: "#e6e6e6",
    borderRadius: 10,
    paddingHorizontal: 5,
    paddingVertical: 10,
    flexDirection: "row",
  },
  emojiImage: {
    width: 20,
    height: 20,
    marginHorizontal: 5,
  },
  subtitle: {
    marginTop: 12,
    color: "#000",
    fontSize: 12,
    fontFamily: "Regular",
  },
  contentBottomWrapper: {
    paddingHorizontal: Size(4),
  },
  seeMoreContainer: {
    paddingTop: Size(2.5),
    paddingBottom: Size(5),
  },
  seeMoreText: {
    fontFamily: "Regular",
    color: "#000",
  },
  seeMore: {
    marginLeft: 5,
    color: "#00000066",
    textDecorationLine: "underline",
  },
  nameTxt: {
    fontFamily: "PoppinsBold",
    fontSize: Size(3.7),
  },
  nameImageContainer: {
    flexDirection: "row",
    gap: Size(3),
    alignItems: "center",
    paddingHorizontal: Size(5),
    height: HIGHLIGHT_BANNER_HEIGHT,
  },
  titleTxt: {
    fontFamily: "PoppinsMedium",
    fontSize: Size(3.5),
    color: Colors.grey,
  },
});

export default Competition;
