import React, { useState, useRef } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  FlatList,
  Text,
} from "react-native";
import AnnouncementPlayerVideo from "./AnnouncementPlayerVideo";
import { ChevronLeft, ChevronRight } from "../../res/Svg";
import LoadingFeedVideo from "../LoadingFeedVideo";
import { VIDEO_STATUS } from "../../constants/Constant";

const { width } = Dimensions.get("window");

const VideoCarousel = ({ voteSubmittedAssets }) => {
  const [togglePlay, setTogglePlay] = useState(0);
  const flatListRef = useRef(null);
  const sortedAssets = voteSubmittedAssets.sort(
    (a, b) => a.orderIndex - b.orderIndex
  );

  const renderItem = ({ item, index }) => (
    <View style={{ width }}>
      {item.videoStatus === VIDEO_STATUS.SUCCESSFUL ? (
        <AnnouncementPlayerVideo
          index={index}
          currentViewIndex={togglePlay}
          streamUrl={item.streamUrl}
          assetUrl={item.assetUrl}
        />
      ) : item.videoStatus === VIDEO_STATUS.PROCESSING ? (
        <LoadingFeedVideo />
      ) : item.videoStatus === VIDEO_STATUS.FAILED ? (
        <LoadingFeedVideo isError={true} />
      ) : null}
    </View>
  );

  const onViewRef = useRef(({ viewableItems }) => {
    if (viewableItems.length > 0) {
      setTogglePlay(viewableItems[0].index);
    }
  });

  const viewConfigRef = useRef({ viewAreaCoveragePercentThreshold: 50 });

  const handleNext = () => {
    if (flatListRef.current && togglePlay < sortedAssets.length - 1) {
      flatListRef.current.scrollToIndex({
        index: togglePlay + 1,
        animated: true,
      });
    }
  };

  const handlePrev = () => {
    if (flatListRef.current && togglePlay > 0) {
      flatListRef.current.scrollToIndex({
        index: togglePlay - 1,
        animated: true,
      });
    }
  };

  const renderDots = () => (
    <View style={styles.dotsContainer}>
      {sortedAssets.map((_, index) => (
        <View
          key={index}
          style={[styles.dot, { opacity: togglePlay === index ? 1 : 0.4 }]}
        />
      ))}
    </View>
  );

  return (
    <View style={styles.carouselContainer}>
      <FlatList
        ref={flatListRef}
        data={sortedAssets}
        renderItem={renderItem}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        keyExtractor={(item, index) => `${item.id}-${index}`}
        onViewableItemsChanged={onViewRef.current}
        viewabilityConfig={viewConfigRef.current}
        getItemLayout={(data, index) => ({
          length: width,
          offset: width * index,
          index,
        })}
        snapToInterval={width}
        decelerationRate="fast"
        snapToAlignment="center"
      />

      {/* Pagination Dots */}
      {renderDots()}

      {/* Custom navigation buttons */}
      <View style={styles.navButtonsContainer}>
        <TouchableOpacity
          onPress={handlePrev}
          style={[
            styles.navButtonWrapper,
            { opacity: togglePlay === 0 ? 0.2 : 1 },
          ]}
          disabled={togglePlay === 0}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <ChevronLeft />
        </TouchableOpacity>
        <TouchableOpacity
          onPress={handleNext}
          style={[
            styles.navButtonWrapper,
            { opacity: togglePlay === sortedAssets.length - 1 ? 0.2 : 1 },
          ]}
          disabled={togglePlay === sortedAssets.length - 1}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <ChevronRight />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  carouselContainer: {
    // flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  navButtonsContainer: {
    position: "absolute",
    top: 210,
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
  },
  navButtonWrapper: {
    height: 40,
    width: 40,
    alignItems: "center",
    justifyContent: "center",
  },
  dotsContainer: {
    position: "absolute",
    bottom: 20,
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    width: "100%",
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 5,
    backgroundColor: "#52FF00",
  },
});

export default VideoCarousel;
