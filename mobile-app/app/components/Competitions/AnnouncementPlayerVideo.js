import React, { memo, useState, useRef, useCallback } from "react";
import {
  View,
  Image,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  Dimensions,
} from "react-native";
import { PlayIcon2 } from "../../res/Svg";
import { uploadedStreamsBaseUrl } from "../../constants/misc";
import { Colors } from "../../res/Colors";
import Video from "react-native-video";
import { Size } from "../../res/Size";
import { Ionicons } from "@expo/vector-icons";

const { width } = Dimensions.get("window");

const AnnouncementPlayerVideo = memo(
  ({ streamUrl, currentViewIndex, index }) => {
    const [isReady, setIsReady] = useState(false);
    const [playing, setPlaying] = useState(false);
    const [isVideoEnded, setIsVideoEnded] = useState(false);
    const videoRef = useRef(null); // Reference to the video player

    const getVideoUrl = (type) => {
      if (streamUrl?.baseUrl) {
        return `${uploadedStreamsBaseUrl}/${
          streamUrl?.key?.split("--")[1]
        }/${type}`;
      }
      return "";
    };

    const thumbnail = getVideoUrl("thumbnail.png");
    const videoUrl = getVideoUrl("index.m3u8");

    const handleReady = (meta) => {
      setIsReady(true);
    };

    const togglePlayPause = useCallback(() => {
      if (isVideoEnded) {
        videoRef.current.seek(0);
        setIsVideoEnded(false);
        setPlaying(true);
      } else {
        setPlaying((prev) => !prev);
      }
    }, [isVideoEnded]);

    const handleEnd = () => {
      setIsVideoEnded(true);
      setPlaying(false);
    };

    return (
      <View style={styles.container}>
        {!isReady && (
          <View style={styles.spinnerContainer}>
            <ActivityIndicator
              size="large"
              style={styles.videoOverlayLoader}
              color={Colors.green}
            />
            <Image style={styles.thumbnail} source={{ uri: thumbnail }} />
          </View>
        )}

        <TouchableOpacity
          style={styles.videoContainer}
          onPress={togglePlayPause}
          activeOpacity={0.6}
        >
          <Video
            ref={videoRef}
            source={{ uri: videoUrl }}
            style={styles.video}
            resizeMode="contain"
            onLoad={handleReady}
            isMuted={false}
            paused={!playing || currentViewIndex !== index}
            volume={1.0}
            poster={thumbnail}
            onError={(error) => console.error("err", error)}
            posterStyle={styles.thumbnail}
            posterResizeMode="cover"
            useNativeControls={false}
            onEnd={handleEnd}
            useTextureView={true}
            playInBackground={false}
            playWhenInactive={false}
          />
        </TouchableOpacity>

        {playing && isReady ? (
          <TouchableOpacity onPress={() => setPlaying(false)} />
        ) : (
          isReady && (
            <TouchableOpacity
              onPress={togglePlayPause}
              style={styles.overlayCenter}
            >
              <View style={styles.playButton}>
                <Ionicons
                  name="play"
                  size={Size(9)}
                  style={{
                    marginLeft: Size(1),
                  }}
                  color="black"
                />
              </View>
            </TouchableOpacity>
          )
        )}
      </View>
    );
  }
);

const styles = StyleSheet.create({
  container: {
    width: "100%",
    aspectRatio: 3 / 4,
    position: "relative",
    backgroundColor: "black",
  },
  spinnerContainer: {
    justifyContent: "center",
    alignItems: "center",
    position: "absolute",
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 10,
    backgroundColor: "rgba(0, 0, 0, 0.4)",
  },
  thumbnail: {
    width: "100%",
    height: "100%",
    resizeMode: "cover",
  },
  videoContainer: {
    justifyContent: "center",
    alignItems: "center",
  },
  video: {
    width: "100%",
    height: "100%",
  },
  overlay: {
    position: "absolute",
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    justifyContent: "center",
    alignItems: "center",
    zIndex: 20,
  },
  videoOverlayLoader: {
    position: "absolute",
    zIndex: 20,
  },
  playButton: {
    width: Size(16),
    height: Size(16),
    backgroundColor: "rgba(255, 255, 255, 0.5)",
    borderRadius: Size(16),
    justifyContent: "center",
    alignItems: "center",
  },
  overlayCenter: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: "center",
    alignItems: "center",
  },
});

export default AnnouncementPlayerVideo;
