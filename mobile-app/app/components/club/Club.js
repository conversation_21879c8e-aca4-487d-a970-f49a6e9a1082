// REACT //
import React, { useEffect, useState } from "react";

// PLUGINS //
import { SvgUri } from "react-native-svg";

// COMPONENTS //
import {
  FlatList,
  Image,
  Text,
  TouchableOpacity,
  View,
  ActivityIndicator,
  TextInput,
} from "react-native";

// SERVICES //
import ClubApi from "../../services/ClubApi";

// OTHERS //
import styles from "./ClubsStyles";
import { commonStyles } from "../../res/CommonStyles";
import { Size } from "../../res/Size";
import { Colors } from "../../res/Colors";

const ClubList = ({ onSelect }) => {
  const [clubs, setClubs] = useState([]);
  const [filteredClubs, setFilteredClubs] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchClubs();
  }, []);

  useEffect(() => {
    filterClubs();
  }, [searchQuery]);

  const fetchClubs = async () => {
    try {
      const response = await ClubApi.allClubs("club");
      if (
        response &&
        response.data &&
        response.data.data &&
        response.data.data.Items
      ) {
        setClubs(response.data.data.Items);
        setFilteredClubs(response.data.data.Items); // Initially, show all clubs
      } else {
        setError("Invalid response format");
      }
    } catch (err) {
      setError("Failed to fetch clubs");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const filterClubs = () => {
    if (!searchQuery) {
      setFilteredClubs(clubs);
    } else {
      const filtered = clubs.filter((club) =>
        club.clubName.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredClubs(filtered);
    }
  };

  const renderClubLogo = (url) => {
    if (url.includes(".svg")) {
      return <SvgUri uri={url} width={30} height={30} />;
    } else {
      return <Image source={{ uri: url }} style={{ width: 30, height: 30 }} />;
    }
  };

  if (loading) {
    return <ActivityIndicator size="large" color={Colors.green} />;
  }

  if (error) {
    return <Text>Error: {error}</Text>;
  }

  return (
    <View>
      <TextInput
        style={styles.searchInput} // You can define this style for the input
        placeholder="Search clubs..."
        value={searchQuery}
        onChangeText={setSearchQuery}
      />

      {/* Club List */}
      <FlatList
        data={filteredClubs}
        renderItem={({ item, index }) => {
          return (
            <TouchableOpacity
              onPress={() => onSelect(item.clubName, item.clubLogoUrl, item.id)}
            >
              <View
                style={[
                  styles.clubContainer,
                  {
                    borderBottomWidth:
                      index === filteredClubs.length - 1 ? 0 : 1,
                  },
                ]}
              >
                {renderClubLogo(item.clubLogoUrl)}
                <Text
                  style={[
                    commonStyles.regularText,
                    { paddingHorizontal: Size(3) },
                  ]}
                >
                  {item.clubName}
                </Text>
              </View>
            </TouchableOpacity>
          );
        }}
        keyExtractor={(item, index) => index.toString()}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

export default ClubList;
