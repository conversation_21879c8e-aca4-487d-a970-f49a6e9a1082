import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from "react-native";
import { useNavigation } from "@react-navigation/native";
import { useSelector } from "react-redux";
import { dispatch } from "../../redux/store";
import { getUniqueEmojis } from "../../constants/Constant";
import { CommentIcon, EmojiIcon, Pin, PlayIcon2 } from "../../res/Svg";
import Videos from "../Videos/Videos";
import VideoUI from "../Videos/VideoUI";
import styles from "./AnnouncementStyle";
import { selectUserInfo } from "../../redux/selectors/authSelectors";
import { shallowEqual } from "react-redux";

const Announcements = ({ loggedUserId, reactionPacks }) => {
  const [openFeed, setOpenFeed] = useState("");
  const [announcements, setAnnouncements] = useState({});
  const [shouldShowEmojiPanel, setShouldShowEmojiPanel] = useState({
    visible: false,
    highlightId: "",
  });

  const { activeAnnouncements } = useSelector(
    ({ announcement }) => announcement
  );
  const userInfo = useSelector(selectUserInfo, shallowEqual);
  const navigation = useNavigation();

  const handleClose = () => {
    setOpenFeed("");
  };

  const addUserReaction = async (announcement, reaction) => {
    setShouldShowEmojiPanel({ visible: false, highlightId: "" });
    function filterById(reactedBy, userId) {
      return reactedBy.filter((item) => item.userId !== userId);
    }

    try {
      const existingAnnouncement = activeAnnouncements?.find(
        (item) => item.id === announcement.id
      );

      if (existingAnnouncement) {
        const filteredReactions = filterById(
          existingAnnouncement.reactedByUsers,
          userInfo.id
        );
        const reactedByUsersWithNewReaction = [
          {
            userId: userInfo.id,
            others: {
              user: {
                firstName: userInfo.firstName,
                lastName: userInfo.lastName,
                photoUrl: userInfo.photoUrl,
                cludId: userInfo.cludId,
                gender: userInfo.gender,
              },
              emoji: reaction.file,
              emojiName: reaction.name,
            },
          },
          ...filteredReactions,
        ];
        await dispatch.announcement.updateAnnouncement({
          id: announcement.id,
          reactedByUsers: reactedByUsersWithNewReaction,
        });
      }
    } catch (error) {
      console.error("AN ERROR OCCURED", error);
    }
  };

  const uniqueEmojis = getUniqueEmojis(
    activeAnnouncements[0]?.reactedByUsers || []
  );

  useEffect(() => {
    if (activeAnnouncements?.length > 0) {
      const latestAnnouncement = activeAnnouncements.sort(
        (a, b) => new Date(b.createdAt) - new Date(a.createdAt)
      )[0];
      setAnnouncements(latestAnnouncement);
    }
  }, [activeAnnouncements]);

  return (
    announcements &&
    Object.keys(announcements).length > 0 && (
      <ScrollView style={styles.container}>
        <View style={styles.header}>
          <View style={styles.profileSection}>
            <Image
              source={{ uri: announcements.logoUrl }}
              style={styles.profileImage}
            />
            <View>
              <Text style={styles.title}>{announcements?.title}</Text>
              <Text style={styles.subtitle}>{announcements?.subtitle}</Text>
            </View>
          </View>
          <Pin />
        </View>
        {announcements.type === "PHOTO" && (
          <View style={styles.photoContainer}>
            <TouchableOpacity
              onPress={() =>
                navigation.navigate("FeedDetail", {
                  announcementId: announcements.id,
                })
              }
            >
              <Image
                source={{ uri: announcements.assetUrl }}
                style={styles.photo}
              />
            </TouchableOpacity>
            {openFeed === announcements.assetUrl && (
              //   <OpenFeed
              //     isAnnouncement={true}
              //     highlight={announcements}
              //     closeModal={handleClose}
              //   >
              <TouchableOpacity
                onPress={() =>
                  navigation.navigate("FeedDetail", {
                    announcementId: announcements.id,
                  })
                }
              >
                <Image
                  source={{ uri: announcements.assetUrl }}
                  style={styles.photo}
                />
              </TouchableOpacity>
              //   </OpenFeed>
            )}
          </View>
        )}
        {announcements.type === "VIDEO" && (
          <View style={styles.videoContainer}>
            <TouchableOpacity
              onPress={() =>
                setOpenFeed(openFeed === "" ? announcements.assetUrl : "")
              }
            >
              <View style={styles.videoWrapper}>
                <Videos url={announcements.assetUrl} style={styles.video} />
                <View style={styles.playIconContainer}>
                  <PlayIcon2 />
                </View>
              </View>
            </TouchableOpacity>

            {openFeed === announcements.assetUrl && (
              //   <OpenFeed
              //     isAnnouncement={true}
              //     closeModal={handleClose}
              //     highlight={announcements}
              //   >
              <VideoUI videoUrl={announcements.assetUrl} />
              //   </OpenFeed>
            )}
          </View>
        )}
        {loggedUserId && (
          <View style={styles.reactionContainer}>
            <TouchableOpacity
              onPress={() =>
                setShouldShowEmojiPanel({
                  visible:
                    shouldShowEmojiPanel.visible &&
                    announcements.id === shouldShowEmojiPanel.highlightId
                      ? false
                      : true,
                  highlightId: announcements.id,
                })
              }
            >
              <EmojiIcon />
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() =>
                navigation.navigate("FeedDetail", {
                  announcementId: announcements.id,
                })
              }
            >
              <CommentIcon />
            </TouchableOpacity>
            <View style={styles.emojiContainer}>
              {getUniqueEmojis(announcements?.reactedByUsers).length > 0 ? (
                <View
                  onPress={() =>
                    navigation.navigate("FeedDetail", {
                      announcementId: announcements.id,
                      type: "reactions",
                    })
                  }
                  style={styles.emojiDisplay}
                >
                  {getUniqueEmojis(announcements?.reactedByUsers)
                    .slice(0, 5)
                    .map((item, idx) => (
                      <View
                        key={idx}
                        style={[styles.emoji, { left: idx * -10 }]}
                      >
                        <Image
                          source={{ uri: item }}
                          style={styles.emojiImage}
                        />
                      </View>
                    ))}
                  <Text
                    onPress={() =>
                      navigation.navigate("FeedDetail", {
                        announcementId: announcements.id,
                        type: "reactions",
                      })
                    }
                    style={styles.emojiText}
                  >
                    {announcements?.reactedByUsers
                      ? announcements?.reactedByUsers?.length
                      : ""}
                  </Text>
                </View>
              ) : (
                <Text
                  onPress={() =>
                    navigation.navigate("FeedDetail", {
                      announcementId: announcements.id,
                      type: "reactions",
                    })
                  }
                  style={styles.emojiText}
                >
                  0 reactions
                </Text>
              )}
              <TouchableOpacity
                style={styles.commentCount}
                onPress={() =>
                  navigation.navigate("FeedDetail", {
                    announcementId: announcements.id,
                  })
                }
              >
                <Text>
                  {Number.isInteger(announcements?.totalCommentCount)
                    ? announcements.totalCommentCount
                    : 0}{" "}
                  {Number(announcements?.totalCommentCount) === 0 ||
                  Number(announcements?.totalCommentCount) > 1
                    ? "comments"
                    : "comment"}
                </Text>
              </TouchableOpacity>
            </View>
            {shouldShowEmojiPanel.visible &&
              shouldShowEmojiPanel.highlightId === announcements.id && (
                <View style={styles.emojiPanel}>
                  {reactionPacks?.emojis.map((item, idx) => (
                    <TouchableOpacity
                      key={idx}
                      onPress={() => addUserReaction(announcements, item)}
                      style={styles.emojiButton}
                    >
                      <Image
                        source={{ uri: item.file }}
                        style={styles.emojiImage}
                      />
                    </TouchableOpacity>
                  ))}
                </View>
              )}
          </View>
        )}
      </ScrollView>
    )
  );
};

export default Announcements;
