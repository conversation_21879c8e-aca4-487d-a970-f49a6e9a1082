import { StyleSheet } from "react-native";
import { HeightSize, Size } from "../../res/Size";
import { Colors } from "../../res/Colors";

const styles = StyleSheet.create({
  container: {
    paddingBottom: 32,
    marginBottom: 10,
    paddingTop: 7,
    backgroundColor: "#F4F3F3",
    paddingHorizontal: 15,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
    alignItems: "center",
  },
  profileSection: {
    flexDirection: "row",
    gap: 10,
    alignItems: "center",
  },
  profileImage: {
    width: <PERSON>ze(15),
    height: Size(15),
    borderRadius: Size(15),
  },
  title: {
    fontFamily: "PoppinsBold",
    fontSize: Size(3.7),
  },
  subtitle: {
    fontFamily: "PoppinsMedium",
    fontSize: Size(3.7),
    color: Colors.grey,
  },
  photoContainer: {
    width: "100%",
    borderRadius: Size(9),
    overflow: "hidden",
    marginTop: 6,
    aspectRatio: 4 / 5,
  },
  photo: {
    backgroundColor: Colors.white,
    // resizeMode: "contain",
    width: "100%",
    height: HeightSize(55),
  },
  reactionContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: HeightSize(2),
    paddingRight: Size(64),
    alignItems: "center",
    width: "100%",
    position: "relative",
    paddingTop: HeightSize(4),
  },
  emojiPanel: {
    position: "absolute",
    left: Size(9),
    backgroundColor: Colors.lightest_grey,
    borderRadius: 10,
    flexDirection: "row",
    padding: Size(3),
    top: HeightSize(3),
  },
  emoji: {
    borderRadius: 10,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#D9D9D9",
    height: Size(6),
    width: Size(6),
    // marginRight: -12
  },
  emojiImage: {
    height: Size(6),
    width: Size(6),
  },
  videoContainer: {
    width: "100%",
    borderRadius: 32,
    marginTop: 6,
    overflow: "hidden",
    aspectRatio: 16 / 9,
  },
  videoWrapper: {
    width: "100%",
    height: "100%",
    // backgroundColor: "black",
    borderRadius: 32,
    overflow: "hidden",
  },
  video: {
    width: "100%",
    height: "100%",
  },
  playIconContainer: {
    position: "absolute",
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
    justifyContent: "center",
    alignItems: "center",
  },
  emojiDisplay: {
    flexDirection: "row",
    width: Size(15),
    paddingVertical: 1,
    alignItems: "center",
    justifyContent: "space-between",
  },
  commentCount: {
    flexDirection: "row",
    alignItems: "center",
    color: "#808080",
    // marginLeft: Size(50)
  },
  emojiText: {
    fontSize: 14,
    color: "#808080",
    marginLeft: Size(1),
    textAlign: "center",
  },
  emojiContainer: {
    position: "absolute",
    top: -20,
    //   flex: 1,
    width: Size(80),
    //   left: 0,
    flexDirection: "row",
    justifyContent: "space-between",
    color: "#808080",
    marginVertical: HeightSize(2),
  },
  emojiButton: {
    marginHorizontal: Size(1),
    backgroundColor: "transparent",
    border: "none",
  },
});

export default styles;
