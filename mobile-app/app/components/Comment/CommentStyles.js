import { StyleSheet } from "react-native";
import { HeightSize, Size } from "../../res/Size";
import { Colors } from "../../res/Colors";

const styles = StyleSheet.create({
  commentContentContainer: {
    flexDirection: "column",
    marginTop: HeightSize(2),
    marginBottom: HeightSize(3),
    paddingHorizontal: Size(5),
  },
  commentContainer: {
    flexDirection: "row",
    gap: Size(3),
    flex: 1,
    paddingVertical: Size(4),
    borderBottomWidth: 1,
    borderBottomColor: Colors.lighter_grey,
  },
  userAvatarContainer: {
    paddingTop: Size(0.75),
  },
  userContainer: {
    flex: 1,
    paddingRight: Size(6),
    gap: 2,
  },
  userNameContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: Size(2),
  },
  userName: {
    fontFamily: "PoppinsSemiBold",
    fontSize: Size(3.5),
  },
  timeText: {
    color: Colors.grey,
    fontSize: Size(3.5),
    fontFamily: "Regular",
  },
  commentText: {
    fontFamily: "Regular",
    fontSize: Size(3.5),
  },
});

export default styles;
