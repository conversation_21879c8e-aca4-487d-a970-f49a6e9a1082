import { useCallback } from "react";
import styles from "./CommentStyles";
import { useNavigation } from "@react-navigation/native";
import { Text, TouchableOpacity, View } from "react-native";
import { MenuIcon, ReportIcon } from "../../res/Svg";
import UserAvatar from "../../components/UserAvatar";
import { getShorthandTimestamp } from "../../utils/date";

const Comment = ({ comment, isReported }) => {
  const navigation = useNavigation();

  const handleNavigateToProfile = useCallback(() => {
    navigation.navigate("MyProfileScreenStack", {
      screen: "MyProfile",
      params: { userId: comment.custom_data?.userId },
    });
  }, [comment.custom_data?.userId]);

  const openBlockReportModal = () => {};

  return (
    <View style={styles.commentContainer}>
      <View style={styles.userAvatarContainer}>
        <UserAvatar
          user={comment.user}
          onPress={handleNavigateToProfile}
          size={9}
        />
      </View>
      <View style={styles.userContainer}>
        <View style={styles.userNameContainer}>
          <TouchableOpacity onPress={handleNavigateToProfile}>
            <Text
              style={styles.userName}
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {comment.user.firstName
                ? `${comment.user.firstName}${
                    comment.user.lastName ? ` ${comment.user.lastName}` : ""
                  }`
                : "Anonymous"}
            </Text>
          </TouchableOpacity>
          <Text style={styles.timeText}>
            {getShorthandTimestamp(comment.createdAt)}
          </Text>
        </View>
        <Text style={styles.commentText}>{comment.text}</Text>
      </View>
      {isReported && <ReportIcon />}
      <TouchableOpacity
        onPress={(event) =>
          openBlockReportModal(index, event.nativeEvent, commentUserID)
        }
      >
        <MenuIcon />
      </TouchableOpacity>
      {/* {blockReportModalVisible && selectedCommentId === index && (
        <BlockReportModal
          visible={blockReportModalVisible}
          onClose={closeBlockReportModal}
          //onBlock={() => handleOpenConfirmation("block")}
          onReport={() => handleOpenConfirmation("report", "COMMENT")}
          top={modalPosition.top}
          right={modalPosition.right}
          text={"Comment"}
          showBlockOption={false}
          showDeleteComment={
            commentUserID === userInfo?.id ||
            loggedUserId === highlight?.user?.id
          }
          showReportOption={commentUserID !== userInfo?.id}
          onDelete={() => {
            handleOpenConfirmation("delete", "delete");
            setCommentId(commentId);
            setCommentBoardId(commentBoardId);
          }}
        />
      )} */}
      {/* <ConfirmationModal
        visible={confirmationVisible}
        onClose={() => setConfirmationVisible(false)}
        onConfirm={handleConfirmAction}
        actionType={actionType}
        reportType={reportType}
      /> */}
    </View>
  );
};

export default Comment;
