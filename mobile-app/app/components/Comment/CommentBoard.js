import { useCallback } from "react";
import { ActivityIndicator, FlatList, View } from "react-native";
import Comment from "./Comment";
import { useState } from "react";
import { useEffect } from "react";
import styles from "./CommentStyles";
import CommentApi from "../../services/CommentApi";
import { useSelector } from "react-redux";
import { selectUserInfo } from "../../redux/selectors/authSelectors";
import { shallowEqual } from "react-redux";

// const handleConfirmAction = () => {
//   setConfirmationVisible(false);

//   if (actionType === "block") {
//     handleBlockUser();
//   } else if (actionType === "report") {
//     // Pass the reportType to handleReportComment if needed
//     handleReportComment(reportType);
//   } else if (actionType === "delete") {
//     handleDelete();
//   }
//   closeBlockReportModal();
// };

//   const handleBlockUser = async () => {
//     try {
//       if (!selectedUserId) {
//         console.error("No user selected to block");
//         return;
//       }

//       const payload = {
//         blockedUserId: selectedUserId,
//         reason: "", // Optionally include a reason for blocking
//       };

//       // Dispatch the blockUser action
//       const status = await dispatch.user.blockUser(payload);

//       if (status.status === 1) {
//         // Fetch the updated userInfo before navigating
//         await dispatch.user.fetchUserDetails(userInfo.id);
//         // Navigate back to MessageList and pass the blocked user ID
//         navigation.navigate("Feed");
//       }
//     } catch (err) {
//       console.error(
//         "Error in blocking user:",
//         err.response ? err.response.data : err.message
//       );
//     }
//   };

//   // Report comment function
//   const handleReportComment = async (reportType) => {
//     // Get reporter's full name
//     const reporterFullName = `${userInfo?.firstName || "Unknown"} ${
//       userInfo?.lastName || "User"
//     }`;

//     // Determine reported user's full name and ID based on reportType
//     let reportedFullName = "Unknown User";
//     let reportedUserId = "UnknownUserId";
//     let reportedContent = "No content";

//     if (reportType === "POST") {
//       // Report type is POST
//       reportedFullName = `${highlight?.user?.firstName || "Unknown"} ${
//         highlight?.user?.lastName || "User"
//       }`;
//       reportedUserId = highlight?.userId || "UnknownUserId";
//       reportedContent = highlight?.comment || "No content";
//     } else if (reportType === "COMMENT") {
//       // Report type is COMMENT
//       const authorData = JSON.parse(selectedComment?.custom_data || "{}");
//       reportedFullName = `${authorData.firstName || "Unknown"} ${
//         authorData.lastName || "User"
//       }`;
//       reportedUserId = authorData.id || "UnknownUserId";
//       reportedContent = selectedComment?.text || "No content";
//     }

//     // Prepare the payload for the report
//     const payload = {
//       reporterUserId: loggedUserId || "UnknownUserId",
//       reportedUserId: reportedUserId,
//       reporterFullName: reporterFullName,
//       reportedFullName: reportedFullName,
//       reason: "",
//       reportedContent: reportedContent,
//       reportedPhotoUrl: "", // Optional
//       reporterPhotoUrl: userInfo?.photoUrl || "",
//       reportType: reportType,
//       contendId: reportType === "POST" ? highlightId : selectedComment?.id,
//       ...(reportType === "COMMENT" && {
//         metadata: {
//           highlightId: highlightId || highlight?.id,
//           userId: userId,
//         },
//       }),
//     };

//     // Submit the report
//     try {
//       const status = await dispatch.user.submitReport(payload);
//       if (status === 1) {
//         await dispatch.user.fetchUserDetails(userInfo.id);
//       } else {
//         console.error("Report submission failed");
//       }
//     } catch (error) {
//       console.error("Error in submitting report:", error.message);
//     }

//     // Close the modal
//     closeBlockReportModal();
//   };

// const handleDelete = async () => {
//   if (!commentId || !commentBoardId) {
//     console.error(
//       "Invalid parameters: commentId or commentBoardId is missing."
//     );
//     return;
//   }

//   try {
//     // Calling deleteComment with proper parameters
//     const response = await LiveLike.deleteComment({
//       commentBoardId: commentBoardId || highlight.commentBoardId,
//       commentId: commentId,
//     });

//     // Check if the response is successful (status code 204)
//     if (response.status === 204) {
//       notifySuccess("Comment deleted successfully.");

//       setCommentId(null);
//       setCommentBoardId(null);

//       setFetchedComments((prevComments) => {
//         const updatedComments = prevComments.filter((comment) => {
//           const isDeleted =
//             comment.id === commentId || comment.comment_id === commentId;
//           return !isDeleted;
//         });
//         return updatedComments;
//       });
//     } else {
//       console.error("Failed to delete comment. Response:", response);
//       notifyError("Failed to delete comment. Please try again.");
//     }
//   } catch (error) {
//     console.error("Error deleting comment:", error);
//     notifyError("Error deleting comment. Please try again.");
//   }
// };

// const openBlockReportModal = (idx, nativeEvent, userId) => {
//   setSelectedCommentId(idx);
//   setSelectedComment(fetchedComments[idx]);
//   setModalPosition({
//     top: nativeEvent.pageY,
//     right: Size(5), // Adjust the 'right' value as needed, since 'pageX' gives absolute X coordinate from the left
//   });
//   setSelectedUserId(userId);
//   setBlockReportModalVisible(true);
// };

const CommentBoard = ({ commentBoardId }) => {
  const userInfo = useSelector(selectUserInfo, shallowEqual);

  const [comments, setComments] = useState([]);
  const [nextCursor, setNextCursor] = useState(null);
  const [isLoading, setIsLoading] = useState(Boolean(commentBoardId));

  const fetchComments = useCallback(
    async (cursor) => {
      if (!commentBoardId) return;

      try {
        setIsLoading(true);
        const { data } = await CommentApi.getComments(commentBoardId, cursor);
        setComments((prev) => [...prev, ...data.comments]);
        setNextCursor(data.pagination.nextCursor || null);
      } catch (error) {
        console.error("[API] Error fetching comments:", error);
      } finally {
        setIsLoading(false);
      }
    },
    [commentBoardId]
  );

  const renderComment = useCallback(
    ({ item }) => {
      const isReported = userInfo.reports?.some(
        (report) => report.contendId === item.id
      );
      return <Comment comment={item} isReported={isReported} />;
    },
    [userInfo]
  );

  const handleLoadMoreComments = useCallback(() => {
    console.log("Attempt to load more", comments.length, nextCursor);
    if (!nextCursor || isLoading) return;

    console.log("Do load more", comments.length, nextCursor);

    fetchComments(nextCursor);
  }, [nextCursor, isLoading, fetchComments]);

  const getCommentKey = useCallback((item) => item.id, []);

  useEffect(() => {
    console.log("&&&&&");
    fetchComments();
  }, [fetchComments]);

  return (
    <View>
      <View style={styles.commentContentContainer}>
        <FlatList
          data={comments}
          keyExtractor={getCommentKey}
          renderItem={renderComment}
          //onEndReached={handleLoadMoreComments}
          onEndReachedThreshold={0.5}
          ListFooterComponent={isLoading ? <ActivityIndicator /> : null}
        />
      </View>
    </View>
  );
};

export default CommentBoard;
