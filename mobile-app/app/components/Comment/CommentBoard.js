import { useCallback } from "react";
import { ActivityIndicator, FlatList, View } from "react-native";
import Comment from "./Comment";
import { useState } from "react";
import { useEffect } from "react";
import styles from "./CommentStyles";
import CommentApi from "../../services/CommentApi";
import { useSelector } from "react-redux";
import { selectUserInfo } from "../../redux/selectors/authSelectors";
import { shallowEqual } from "react-redux";
import ConfirmationModal from "../../screens/FeedDetail/ConfirmationModal";
import BlockReportModal from "../BlockReportModal";
import { Size } from "../../res/Size";

//   // Report comment function
//   const handleReportComment = async (reportType) => {
//     // Get reporter's full name
//     const reporterFullName = `${userInfo?.firstName || "Unknown"} ${
//       userInfo?.lastName || "User"
//     }`;

//     // Determine reported user's full name and ID based on reportType
//     let reportedFullName = "Unknown User";
//     let reportedUserId = "UnknownUserId";
//     let reportedContent = "No content";

//     if (reportType === "POST") {
//       // Report type is POST
//       reportedFullName = `${highlight?.user?.firstName || "Unknown"} ${
//         highlight?.user?.lastName || "User"
//       }`;
//       reportedUserId = highlight?.userId || "UnknownUserId";
//       reportedContent = highlight?.comment || "No content";
//     } else if (reportType === "COMMENT") {
//       // Report type is COMMENT
//       const authorData = JSON.parse(selectedComment?.custom_data || "{}");
//       reportedFullName = `${authorData.firstName || "Unknown"} ${
//         authorData.lastName || "User"
//       }`;
//       reportedUserId = authorData.id || "UnknownUserId";
//       reportedContent = selectedComment?.text || "No content";
//     }

//     // Prepare the payload for the report
//     const payload = {
//       reporterUserId: loggedUserId || "UnknownUserId",
//       reportedUserId: reportedUserId,
//       reporterFullName: reporterFullName,
//       reportedFullName: reportedFullName,
//       reason: "",
//       reportedContent: reportedContent,
//       reportedPhotoUrl: "", // Optional
//       reporterPhotoUrl: userInfo?.photoUrl || "",
//       reportType: reportType,
//       contendId: reportType === "POST" ? highlightId : selectedComment?.id,
//       ...(reportType === "COMMENT" && {
//         metadata: {
//           highlightId: highlightId || highlight?.id,
//           userId: userId,
//         },
//       }),
//     };

//     // Submit the report
//     try {
//       const status = await dispatch.user.submitReport(payload);
//       if (status === 1) {
//         await dispatch.user.fetchUserDetails(userInfo.id);
//       } else {
//         console.error("Report submission failed");
//       }
//     } catch (error) {
//       console.error("Error in submitting report:", error.message);
//     }

//     // Close the modal
//     closeBlockReportModal();
//   };

const CommentBoard = ({ commentBoardId }) => {
  const userInfo = useSelector(selectUserInfo, shallowEqual);

  const [comments, setComments] = useState([]);
  const [nextCursor, setNextCursor] = useState(null);
  const [isLoading, setIsLoading] = useState(Boolean(commentBoardId));
  const [reportCommentModalVisible, setReportCommentModalVisible] =
    useState(false);
  const [confirmationModalVisible, setConfirmationModalVisible] =
    useState(false);
  const [actionType, setActionType] = useState(null);
  const [reportType, setReportType] = useState(null);
  const [modalPosition, setModalPosition] = useState({ top: 0, right: 0 });
  const [selectedUserId, setSelectedUserId] = useState(null);
  const [selectedComment, setSelectedComment] = useState(null);

  const fetchComments = useCallback(
    async (cursor) => {
      if (!commentBoardId) return;

      try {
        setIsLoading(true);
        const { data } = await CommentApi.getComments(commentBoardId, cursor);
        setComments((prev) =>
          [...prev, ...data.comments].sort(
            (a, b) => new Date(b.createdAt) - new Date(a.createdAt)
          )
        );
        setNextCursor(data.pagination.nextCursor || null);
      } catch (error) {
        console.error("[API] Error fetching comments:", error);
        notifyError("Error fetching comments.");
      } finally {
        setIsLoading(false);
      }
    },
    [commentBoardId]
  );

  const deleteComment = useCallback(async () => {
    try {
      const response = await CommentApi.deleteComment(
        commentBoardId,
        selectedComment.id
      );
      setComments((prev) => prev.filter((c) => c.id !== comment.id));
    } catch (error) {
      console.error("[API] Error deleting comment:", error);
      notifyError("Error deleting comment.");
    }
  }, [selectedComment]);

  const handleLoadMoreComments = useCallback(() => {
    if (!nextCursor || isLoading) return;

    fetchComments(nextCursor);
  }, [nextCursor, isLoading, fetchComments]);

  const getCommentKey = useCallback((item) => item.id, []);

  useEffect(() => {
    fetchComments();
  }, [fetchComments]);

  const openReportCommentModal = useCallback((nativeEvent, comment) => {
    setModalPosition({
      top: nativeEvent.pageY,
      right: Size(5),
    });
    setSelectedComment(comment);
    setReportCommentModalVisible(true);
  }, []);

  const closeReportCommentModal = useCallback(() => {
    setReportCommentModalVisible(false);
    // setSelectedComment(null);
  }, []);

  const openConfirmationModal = useCallback((action, type) => {
    closeReportCommentModal();
    setConfirmationModalVisible(true);
    setActionType(action);
    setReportType(type);
  }, []);

  const handleConfirmAction = useCallback(() => {
    setConfirmationModalVisible(false);

    if (actionType === "report") {
      handleReportComment(reportType);
    } else if (actionType === "delete") {
      deleteComment();
    }

    closeReportCommentModal();
  }, []);

  const renderComment = useCallback(
    ({ item }) => {
      const isReported = userInfo.reports?.some(
        (report) => report.contendId === item.id
      );
      return (
        <Comment
          comment={item}
          isReported={isReported}
          onMenuPress={(event) =>
            openReportCommentModal(event.nativeEvent, item)
          }
        />
      );
    },
    [userInfo]
  );

  console.log("***", selectedComment);

  return (
    <View>
      <View style={styles.commentContentContainer}>
        <FlatList
          data={comments}
          keyExtractor={getCommentKey}
          renderItem={renderComment}
          // onEndReached={handleLoadMoreComments}
          onEndReachedThreshold={0.5}
          initialNumToRender={10}
          maxToRenderPerBatch={10}
          windowSize={2}
          removeClippedSubviews
          ListFooterComponent={isLoading ? <ActivityIndicator /> : null}
        />
      </View>
      <BlockReportModal
        visible={reportCommentModalVisible}
        onClose={closeReportCommentModal}
        onReport={() => openConfirmationModal("report", "COMMENT")}
        onDelete={() => openConfirmationModal("delete", "delete")}
        top={modalPosition.top}
        right={modalPosition.right}
        text="comment"
        showBlockOption={false}
        showDeleteComment={selectedComment?.user.id === userInfo?.id}
        showReportOption={selectedComment?.user.id !== userInfo?.id}
      />
      <ConfirmationModal
        visible={confirmationModalVisible}
        onClose={() => setConfirmationModalVisible(false)}
        onConfirm={handleConfirmAction}
        actionType={actionType}
        reportType={reportType}
      />
    </View>
  );
};

export default CommentBoard;
