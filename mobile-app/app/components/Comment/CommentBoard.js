import { useCallback } from "react";
import { ActivityIndicator, FlatList, View } from "react-native";
import Comment from "./Comment";
import { useState } from "react";
import styles from "./CommentStyles";
import CommentApi from "../../services/CommentApi";
import { useSelector } from "react-redux";
import { selectUserInfo } from "../../redux/selectors/authSelectors";
import { shallowEqual } from "react-redux";
import ConfirmationModal from "../../screens/FeedDetail/ConfirmationModal";
import BlockReportModal from "../BlockReportModal";
import { Size } from "../../res/Size";
import { notifyError, notifySuccess } from "../../constants/misc";
import { dispatch } from "../../redux/store";

const CommentBoard = ({
  commentBoardId,
  comments,
  setComments,
  isLoading,
  highlightId,
}) => {
  const userInfo = useSelector(selectUserInfo, shallowEqual);

  const [selectedComment, setSelectedComment] = useState(null);
  const [reportModalPosition, setReportModalPosition] = useState({
    top: 0,
    right: 0,
  });
  const [reportCommentModalVisible, setReportCommentModalVisible] =
    useState(false);
  const [confirmationModalVisible, setConfirmationModalVisible] =
    useState(false);
  const [actionType, setActionType] = useState(null);
  const [reportType, setReportType] = useState(null);

  const deleteComment = useCallback(async () => {
    try {
      await CommentApi.deleteComment(commentBoardId, selectedComment.id);
      setComments((prev) => prev.filter((c) => c.id !== selectedComment.id));
      setSelectedComment(null);
      notifySuccess("Comment deleted");
    } catch (error) {
      console.error("[API] Error deleting comment:", error);
      notifyError("Error deleting comment. Please try again.");
    }
  }, [selectedComment, commentBoardId]);

  const handleReportComment = useCallback(
    async (reportType) => {
      const reporterFullName = `${userInfo?.firstName}${
        userInfo?.lastName ? ` ${userInfo?.lastName}` : ""
      }`;
      const reportedFullName = `${selectedComment.user.firstName}${
        selectedComment.user.lastName ? ` ${selectedComment.user.lastName}` : ""
      }`;
      const payload = {
        reporterUserId: userInfo?.id,
        reportedUserId: selectedComment.user.id,
        reporterFullName,
        reportedFullName,
        reason: "",
        reportedContent: selectedComment.text,
        reportedPhotoUrl: "",
        reporterPhotoUrl: userInfo?.photoUrl || "",
        reportType,
        contendId: selectedComment.id,
        metadata: {
          ...(highlightId && { highlightId }),
          commentBoardId: commentBoardId,
          userId: selectedComment.user.id,
        },
      };

      await dispatch.user.submitReport(payload);

      notifySuccess("Comment reported.");
    },
    [userInfo, highlightId, selectedComment, reportType]
  );

  const openReportCommentModal = useCallback((nativeEvent, comment) => {
    setReportModalPosition({
      top: nativeEvent.pageY,
      right: Size(5),
    });
    setSelectedComment(comment);
    setReportCommentModalVisible(true);
  }, []);

  const closeReportCommentModal = useCallback(() => {
    setReportCommentModalVisible(false);
  }, []);

  const openConfirmationModal = useCallback(
    (action, type) => {
      closeReportCommentModal();
      setConfirmationModalVisible(true);
      setActionType(action);
      setReportType(type);
    },
    [closeReportCommentModal]
  );

  const handleConfirmAction = useCallback(async () => {
    setConfirmationModalVisible(false);

    try {
      if (actionType === "report") {
        await handleReportComment(reportType);
      } else if (actionType === "delete") {
        await deleteComment();
      }
    } catch (error) {
      console.error("Error in confirm action:", error);
    } finally {
      setConfirmationModalVisible(false);
    }
  }, [actionType, reportType, handleReportComment, deleteComment]);

  const getCommentKey = useCallback((item) => item.id, []);

  const renderComment = useCallback(
    ({ item }) => {
      const isReported = userInfo.reports?.some(
        (report) => report.contendId === item.id
      );
      return (
        <Comment
          comment={item}
          isReported={isReported}
          onMenuPress={(event) =>
            openReportCommentModal(event.nativeEvent, item)
          }
        />
      );
    },
    [userInfo]
  );

  return (
    <View>
      <View style={styles.commentContentContainer}>
        <FlatList
          data={comments}
          keyExtractor={getCommentKey}
          renderItem={renderComment}
          // onEndReached={handleLoadMoreComments}
          onEndReachedThreshold={0.5}
          initialNumToRender={10}
          maxToRenderPerBatch={10}
          windowSize={2}
          removeClippedSubviews
          ListFooterComponent={isLoading ? <ActivityIndicator /> : null}
        />
      </View>
      <BlockReportModal
        visible={reportCommentModalVisible}
        onClose={closeReportCommentModal}
        onReport={() => openConfirmationModal("report", "COMMENT")}
        onDelete={() => openConfirmationModal("delete", "delete")}
        top={reportModalPosition.top}
        right={reportModalPosition.right}
        text="comment"
        showBlockOption={false}
        showDeleteComment={selectedComment?.user.id === userInfo?.id}
        showReportOption={selectedComment?.user.id !== userInfo?.id}
      />
      <ConfirmationModal
        visible={confirmationModalVisible}
        onClose={() => {
          setConfirmationModalVisible(false);
          setSelectedComment(null);
        }}
        onConfirm={handleConfirmAction}
        actionType={actionType}
        reportType={reportType}
      />
    </View>
  );
};

export default CommentBoard;
