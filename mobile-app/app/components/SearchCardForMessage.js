import { View, Text, TouchableOpacity } from "react-native";
import {
  CommonActions,
  useNavigation,
  useRoute,
} from "@react-navigation/native";
import UserAvatar from "./UserAvatar";

const SearchCardForMessage = ({ user, setUser }) => {
  const navigation = useNavigation();
  const route = useRoute();

  return (
    <View
      style={{
        width: "100%",
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        paddingVertical: 8,
        marginBottom: 12,
      }}
    >
      <TouchableOpacity
        style={{ flexDirection: "row", alignItems: "center" }}
        onPress={() => {
          setUser(user);
          navigation.dispatch(
            CommonActions.reset({
              index: 0,
              routes: [
                {
                  name: "Message",
                  params: {
                    recipientId: user.id,
                    fromMyProfileContent:
                      route?.params?.fromMyProfileContent || false,
                  },
                },
              ],
            })
          );
        }}
      >
        <UserAvatar user={user} />
        <View style={{ marginLeft: 10 }}>
          <Text style={{ fontSize: 14, fontWeight: "bold" }}>
            {user.firstName} {user.lastName}
          </Text>
          <Text style={{ fontSize: 14, fontWeight: "bold", opacity: 0.5 }}>
            {(user.teamName !== "N/A" && user.teamName) || user.clubName}
          </Text>
        </View>
      </TouchableOpacity>
    </View>
  );
};

export default SearchCardForMessage;
