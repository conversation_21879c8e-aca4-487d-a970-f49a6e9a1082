import React from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { HeightSize, Size } from "../res/Size";

const Tabs = ({ style, tabs, selected, setSelected, color, isConnections, inactiveColor }) => {

  return (
    <View style={{ marginTop: isConnections ? HeightSize(2) : 0 , marginBottom: HeightSize(2) }}>
      <View style={styles.tabsContainer}>
        {tabs.map((tab, i) => {
          const isSelected = isConnections ? (selected.title === tab.title && selected.connectionCount === tab.connectionCount) : selected === tab;
          return (
            <TouchableOpacity
              key={i}
              onPress={() => setSelected(tab)}
              style={[styles.tabTouchable, { 
              //paddingBottom: isConnections ? HeightSize(2) : HeightSize(5),
              borderBottomWidth: isSelected ? 2 : 0,
              borderBottomColor: isSelected ? color : "transparent",
              }, style]}
            >
              <Text
                style={[
                  styles.tabText,
                  {
                    color: isSelected ? color : inactiveColor,
                    textTransform: isConnections ? "capitalize" : "uppercase",
                    fontSize: isConnections ? 12 : 16,
                    fontFamily: isConnections ? "PoppinsMedium" : "Bold",
                    fontSize: Size(3.7),
                  }
                ]}
              >
                {isConnections ? `${tab.connectionCount} ${tab.title}` : tab}
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  tabsContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    flexWrap: "wrap",
  },
  tabTouchable: {
    flex: 1,
    //backgroundColor: "pink"
  },
  tabText: {
    textAlign: "center",
    paddingVertical: HeightSize(2),
  },
});

export default Tabs;
