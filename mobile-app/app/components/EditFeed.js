import React, { useMemo, useRef, useState } from "react";
import {
  View,
  Text,
  TextInput,
  Image,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  ScrollView,
} from "react-native";
import { useSelector } from "react-redux";
import { useNavigation } from "@react-navigation/native";
import { Colors } from "../res/Colors";
import Button from "./Button/Button";
import { dispatch } from "../redux/store";
import { BackIcon } from "../res/Svg";
import { HeightSize, Size } from "../res/Size";
import Video from "react-native-video";
import { uploadedStreamsBaseUrl } from "../constants/misc";

const EditFeed = ({
  highlightObject,
  close,
  fromProfileHighlights,
  selectedTab,
}) => {
  const [comment, setComment] = useState(highlightObject.comment);
  const [commentError, setCommentError] = useState("");

  const navigation = useNavigation();

  const editing = useSelector(
    (state) => state.loading.effects.feed.editHighlights
  );

  const videoRef = useRef(null);

  const videoUrl = useMemo(() => {
    if (highlightObject && highlightObject?.streamUrl?.baseUrl) {
      return `${uploadedStreamsBaseUrl}/${
        highlightObject.streamUrl.key.split("--")[1]
      }/index.m3u8`;
    }
    return highlightObject?.url || highlightObject?.assetUrl || "";
  }, [highlightObject]);

  const thumbnailUrl = useMemo(() => {
    if (highlightObject && highlightObject?.streamUrl?.baseUrl) {
      return `${uploadedStreamsBaseUrl}/${
        highlightObject.streamUrl.key.split("--")[1]
      }/thumbnail.png`;
    }
    return "";
  }, [highlightObject]);

  const handlePlaybackStatusUpdate = (status) => {
    if (videoRef.current) {
      console.debug("Video ended");
      videoRef.current.seek(0); // Set the playback position to 0
    }
  };

  const handleSubmit = async () => {
    if (comment.length < 1) {
      setCommentError("Please add a Comment");
    } else {
      const res = await dispatch.feed.editHighlights({
        userId: highlightObject?.userId,
        id: highlightObject?.id,
        comment: comment,
      });
      if (res.status === 1) {
        close(false);
        if (fromProfileHighlights) {
          navigation.navigate("MyProfileScreenStack", {
            screen: "MyProfile",
            params: {
              selectedTab: "highlights",
              userId: highlightObject?.userId,
            },
          });
        } else {
          navigation.navigate("HomeScreenStack", {
            screen: "Feed",
          });
        }
      }
    }
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity
        onPress={() => close(false)}
        style={{ marginBottom: HeightSize(3), paddingHorizontal: Size(4) }}
      >
        <BackIcon />
      </TouchableOpacity>
      <View style={styles.mediaContainer}>
        {highlightObject?.type === "PHOTO" ? (
          <Image source={{ uri: highlightObject.url }} style={styles.media} />
        ) : (
          <Video
            ref={videoRef}
            source={{ uri: `${videoUrl}#t=0.001` }}
            style={styles.media}
            controls={true}
            onEnd={handlePlaybackStatusUpdate}
            resizeMode="contain"
            poster={thumbnailUrl}
            onError={(error) => console.error("err", error)}
            posterResizeMode="cover"
          />
        )}
      </View>

      <View style={{ padding: Size(4) }}>
        <View style={styles.inputContainer}>
          <TextInput
            onChangeText={setComment}
            style={styles.input}
            value={comment}
            placeholder="Enter Caption"
            placeholderTextColor="#888"
            returnKeyType="default"
            multiline={true}
          />
        </View>
        {commentError ? (
          <Text style={styles.errorText}>{commentError}</Text>
        ) : null}
        <Button
          title={"PUBLISH"}
          onPress={handleSubmit}
          backgroundColor={Colors.green}
          progress={editing}
          disable={editing}
          width={"100%"}
          textStyleInsideButton={{
            color: Colors.black,
            fontFamily: "Bold",
            fontSize: Size(4),
          }}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "white",
    paddingTop: HeightSize(3),
  },
  mediaContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    aspectRatio: 3 / 4,
    backgroundColor: Colors.black,
  },
  media: {
    width: "100%",
    height: "100%",
  },
  inputContainer: {
    paddingBottom: HeightSize(3),
    marginTop: HeightSize(3),
  },
  input: {
    backgroundColor: "transparent",
    borderBottomWidth: 1,
    borderBottomColor: "black",
    width: "100%",
    paddingVertical: 10,
    fontSize: 16,
    color: "gray",
    maxHeight: 120,
  },
  errorText: {
    color: "red",
    fontSize: 12,
    textAlign: "center",
    marginTop: 10,
  },
});

export default EditFeed;
