import React, { useCallback, useEffect, useState } from "react";
import { View, Text, ScrollView, Switch } from "react-native";
// Import your DropDownIcon if necessary

const nonPlayers = [
  "Agent",
  "Coach",
  "Scout",
  "Academy Manager / Director",
  "Chief Scout",
  "Sporting Director",
  "Player Care"
];

const DropdownWithCheckBox = ({
  onChange,
  listOfOptions = nonPlayers,
  defaultOptions = [],
  icon
}) => {
  const [selectedOption, setSelectedOption] = useState(defaultOptions);

  const handleChange = (item) => {
    const isChecked = selectedOption.includes(item);
    if (isChecked) {
      setSelectedOption(selectedOption.filter((option) => option !== item));
    } else {
      setSelectedOption([...selectedOption, item]);
    }
  };

  const handleChangeWithCallback = useCallback(
    (item) => handleChange(item),
    [selectedOption]
  );

  useEffect(() => {
    onChange(selectedOption);
  }, [selectedOption]);

  return (
    <View style={styles.dropdown}>
      <View style={styles.header}>
        {icon || (
          <View style={styles.headerContent}>
            {/* Your DropDownIcon here */}
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              {selectedOption.map((item, idx) => (
                <Text style={styles.selectedOptionText} key={item}>
                  {item} {selectedOption.length - 1 !== idx ? "," : ""}
                </Text>
              ))}
            </ScrollView>
          </View>
        )}
      </View>
      <ScrollView style={styles.dropdownContent}>
        {listOfOptions.map((role, key) => (
          <View style={styles.optionContainer} key={key}>
            <Switch
              value={selectedOption.includes(role)}
              onValueChange={() => handleChangeWithCallback(role)}
            />
            <Text>{role}</Text>
          </View>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = {
  dropdown: {
    // Your dropdown styles
  },
  header: {
    // Your header styles
  },
  headerContent: {
    // Your header content styles
  },
  selectedOptionText: {
    // Your selected option text styles
  },
  dropdownContent: {
    // Your dropdown content styles
  },
  optionContainer: {
    // Your option container styles
    flexDirection: "row",
    alignItems: "center"
  }
};

export default DropdownWithCheckBox;
