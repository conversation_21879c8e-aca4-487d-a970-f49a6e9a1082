import React, { useState } from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../res/Colors';
import { Size } from '../res/Size';

const PlayPauseOverlay = ({ isReady, playing, setPlaying }) => {
  return (
    <>
      {playing && isReady ? (
        <TouchableOpacity
          onPress={() => {
            setPlaying(false);
          }}
          style={styles.overlay}
        />
      ) : isReady && (
        <TouchableOpacity
          onPress={() => {
            setPlaying(true);
          }}
          style={styles.overlayCenter}
        >
          <View style={styles.playButton}>
            <Ionicons name="play" size={Size(6)} color="black" />
          </View>
        </TouchableOpacity>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  overlay: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 40,
  },
  overlayCenter: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
  },
  playButton: {
    width: Size(10),
    height: Size(10),
    backgroundColor: Colors.white,
    borderRadius: Size(5),
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default PlayPauseOverlay;
