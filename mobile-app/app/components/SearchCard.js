import { Image, Text, TouchableOpacity, View } from "react-native";
import { useNavigation } from "@react-navigation/native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { BlockIcon, Close } from "../res/Svg";
import { SvgUri } from "react-native-svg";
import { Size } from "../res/Size";
import UserAvatar from "./UserAvatar";

const SearchCard = ({
  data,
  removeFromRecent,
  isRecent = true,
  blockedUsersList = [],
}) => {
  const navigation = useNavigation();

  const addToRecentSearch = async (currentUser) => {
    try {
      // Get the recent search data from AsyncStorage
      const recentSearchString = await AsyncStorage.getItem("searchResult");
      let recentSearch = JSON.parse(recentSearchString) || [];

      // Check if the current user is already in recent searches
      const userInRecentSearch = recentSearch.find(
        (item) => item.id === currentUser.id
      );
      if (userInRecentSearch) {
        return;
      }

      // Add the current user to recent searches
      recentSearch.push(currentUser);

      // Store the updated recent search data back to AsyncStorage
      await AsyncStorage.setItem("searchResult", JSON.stringify(recentSearch));
    } catch (error) {
      console.error("Error adding to recent search:", error);
    }
  };

  const renderClubLogo = (url) => {
    if (url.includes(".svg")) {
      return (
        <SvgUri uri={url} style={{ width: 36, height: 36, borderRadius: 23 }} />
      );
    } else {
      return (
        <Image
          source={{ uri: url }}
          style={{ width: 36, height: 36, borderRadius: 23 }}
        />
      );
    }
  };

  // Check if the user is blocked
  const isBlocked = blockedUsersList.some((user) => user.id === data.id);

  return (
    <View
      style={{
        width: "100%",
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        paddingVertical: 8,
        marginBottom: 12,
        paddingHorizontal: Size(5),
      }}
    >
      <TouchableOpacity
        onPress={() => {
          addToRecentSearch(data);
          // if (data.userType === 'ADMIN' || 'PLAYER') {
          //   navigation.navigate('Profile', { id: data.id });
          // } else {
          //   navigation.navigate('TeamDashboard', { teamId: data.id, clubId: data.clubId });
          // }
          if (data.type === "user") {
            navigation.navigate("MyProfileScreenStack", {
              screen: "MyProfile",
              params: { userId: data?.id, fromSearch: true, type: data?.type },
            });
          }
        }}
        style={{ flexDirection: "row", alignItems: "center", gap: 12 }}
      >
        {data.type === "user" && <UserAvatar user={data} />}

        <View>
          {data.type === "user" && (
            <>
              <Text style={{ fontSize: Size(4), fontFamily: "PoppinsMedium" }}>
                {data.firstName} {data.lastName}
              </Text>
              <Text
                style={{
                  fontSize: Size(4),
                  fontFamily: "PoppinsMedium",
                  opacity: 0.3,
                }}
              >
                {(data.teamName !== "N/A" && data.teamName) || data?.clubName}
              </Text>
            </>
          )}
        </View>
      </TouchableOpacity>
      {isBlocked && <BlockIcon />}
      {isRecent && (
        <TouchableOpacity
          onPress={() => removeFromRecent(data?.id)}
          style={{ backgroundColor: "transparent" }}
        >
          <Close />
        </TouchableOpacity>
      )}
    </View>
  );
};

export default SearchCard;
