// REACT //
import React, { useCallback, useEffect, useState } from "react";
import { useSelector } from "react-redux";

// COMPONENTS //
import {
  View,
  TextInput,
  Text,
  Image,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  StyleSheet,
} from "react-native";
import { Images } from "../../res/Images";

// CONSTANTS //
import { searchArrayOrMakeCallToAPI } from "../../constants/misc";

// OTHERS //
import _debounce from "lodash/debounce";
import { dispatch } from "../../redux/store";
import { HeightSize, Size } from "../../res/Size";
import { Colors } from "../../res/Colors";
import UserAvatar from "../UserAvatar";

const SearchUserList = ({ selectedUser, onSelectUser }) => {
  const [searchResult, setSearchResult] = useState(null);
  const [inputValue, setInputValue] = useState("");

  const gettingUser = useSelector(
    ({ loading }) => loading.effects.user.userSearch
  );
  const { userList, teamsList } = useSelector(({ user, team }) => ({
    userList: user.usersByProjection,
    teamsList: team.teamsByProjection,
  }));

  const handleSearch = async (query) => {
    const searchResponse = await searchArrayOrMakeCallToAPI({
      searchTerm: query,
      array: [
        ...userList?.map((item) => ({
          ...item,
          type: "user",
          fullname: `${item?.firstName} ${item?.lastName}`,
        })),
      ],
      makeSearchCall: [dispatch.user.userSearch, dispatch.team.teamSearch],
    });

    const sortedResponse =
      searchResponse?.length > 0
        ? searchResponse.sort((a, b) => {
            if (a.type === "user" && b.type === "team") {
              return 1;
            } else if (a.type === "team" && b.type === "user") {
              return -1;
            }
          })
        : null;

    setSearchResult(sortedResponse);
  };

  const handleSearchResult = (user) => {
    onSelectUser(user);
    handleSearch("");
    setInputValue(`@${user?.firstName}${user?.lastName}`);
  };

  const fetchSearchData = useCallback(_debounce(handleSearch, 400), [
    userList,
    teamsList,
  ]);

  const fetchAllUserData = async () => {
    await dispatch.user.userSearchByProjection();
  };

  useEffect(() => {
    if (!Boolean(userList?.length)) {
      fetchAllUserData();
    }
  }, []);

  const renderItem = ({ item }) => (
    <TouchableOpacity
      style={styles.resultItem}
      onPress={() => handleSearchResult(item)}
    >
      <UserAvatar user={item} />

      <View style={styles.resultText}>
        <Text style={styles.resultName}>
          @{item?.firstName}
          {item?.lastName}
        </Text>
        <Text style={styles.resultDetails}>
          {item?.teamName || item?.clubName}
        </Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <TextInput
        style={styles.input}
        placeholder={
          selectedUser
            ? `${selectedUser?.firstName || ""} ${selectedUser?.lastName || ""}`
            : "Type name"
        }
        value={inputValue}
        onChangeText={(text) => {
          setInputValue(text);
          fetchSearchData(text);
        }}
      />
      <View
        style={{
          borderBottomWidth: 1,
          borderBottomColor: "#ccc",
          marginVertical: HeightSize(1),
        }}
      />
      {searchResult && (
        <View style={styles.searchResultContainer}>
          {!gettingUser ? (
            <FlatList
              data={searchResult}
              keyExtractor={(item, index) => index.toString()}
              renderItem={renderItem}
              nestedScrollEnabled={true}
            />
          ) : (
            <ActivityIndicator size="large" color={Colors.green} />
          )}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    // flex: 1,
    padding: HeightSize(1),
  },
  input: {
    // borderBottomWidth: 1,
    // borderBottomColor: "#ccc",
    fontSize: Size(4),
    // marginBottom: HeightSize(1),
    height: HeightSize(3),
  },
  searchResultContainer: {
    maxHeight: HeightSize(30),
    borderRadius: Size(3),
    borderWidth: 1,
    borderColor: "#ccc",
    backgroundColor: "#fff",
    padding: HeightSize(1),
  },
  resultItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  profileImage: {
    width: Size(10),
    height: Size(10),
    borderRadius: Size(5),
  },
  resultText: {
    marginLeft: 10,
  },
  resultName: {
    fontWeight: "bold",
    fontSize: Size(4),
    color: "#000",
  },
  resultDetails: {
    fontSize: Size(3.7),
    color: "#666",
  },
});

export default SearchUserList;
