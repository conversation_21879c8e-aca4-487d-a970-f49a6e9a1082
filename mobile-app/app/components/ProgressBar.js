import React from "react";
import { View } from "react-native";
import { Colors } from "../res/Colors";

const Progress = ({ width, style }) => {
  return (
    <View
      style={{
        width: "100%",
        height: 4,
        backgroundColor: style?.unfilled ?? "rgba(255,255,255,0.2)",
        borderRadius: 10,
        ...style,
      }}
    >
      <View
        style={{
          width: width * 100 + "%",
          height: "100%",
          borderRadius: 10,
          backgroundColor: style?.filled ?? Colors.dark_grey,
        }}
      />
    </View>
  );
};

export default Progress;
