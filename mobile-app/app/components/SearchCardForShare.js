import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { Colors } from "../res/Colors";
import { HeightSize, Size } from "../res/Size";
import UserAvatar from "./UserAvatar";

const SearchCardForShare = ({ user, handleItemSelect, selectedItems }) => {
  return (
    <TouchableOpacity
      style={styles.container}
      onPress={() => handleItemSelect(user.id)}
    >
      <View style={styles.userContainer}>
        <UserAvatar user={user} />
        <View style={styles.userDetailsContainer}>
          <Text style={styles.nameTxt}>
            @{user.firstName} {user.lastName}
          </Text>
          <Text style={[styles.nameTxt, { opacity: 0.5 }]}>
            {(user.teamName !== "N/A" && user.teamName) || user.clubName}
          </Text>
        </View>
      </View>
      <View
        style={[
          styles.squareBox,
          {
            backgroundColor: selectedItems.includes(user.id)
              ? Colors.green
              : "transparent",
          },
        ]}
      >
        {selectedItems.includes(user.id) && <View style={styles.tickMark} />}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: HeightSize(2),
    marginHorizontal: Size(5),
    //backgroundColor: "pink"
  },
  userContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  imageContainer: {
    width: 46,
    height: 46,
    borderRadius: 23,
  },
  userDetailsContainer: {
    marginLeft: 10,
  },
  nameTxt: {
    fontSize: 14,
    fontWeight: "bold",
  },
  squareBox: {
    width: 20,
    height: 20,
    borderWidth: 1,
    borderColor: Colors.grey,
    justifyContent: "center",
    alignItems: "center",
  },
  tickMark: {
    width: 10,
    height: 5,
    borderTopWidth: 2,
    borderRightWidth: 2,
    borderColor: "white",
    transform: [{ rotate: "130deg" }],
  },
});

export default SearchCardForShare;
