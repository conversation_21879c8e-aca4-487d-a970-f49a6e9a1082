import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { Colors } from "../res/Colors";
import { HeightSize, Size } from "../res/Size";
import UserAvatar from "./UserAvatar";
import { CheckMark } from "../res/Svg";

const SearchCardForShare = ({ user, handleItemSelect, selectedItems }) => {
  const isSelected = selectedItems.find((it) => it.id === user.id);

  const handlePress = () => {
    handleItemSelect({
      id: user.id,
      photoUrl: user.photoUrl,
      firstName: user.firstName,
      lastName: user.lastName,
    });
  };

  return (
    <TouchableOpacity style={styles.container} onPress={handlePress}>
      <View style={styles.userContainer}>
        <UserAvatar user={user} />
        <View style={styles.userDetailsContainer}>
          <Text style={styles.nameTxt}>
            {user.firstName} {user.lastName}
          </Text>
          <Text style={[styles.nameTxt, { opacity: 0.5 }]}>
            {(user.teamName !== "N/A" && user.teamName) || user.clubName}
          </Text>
        </View>
      </View>
      {isSelected ? <CheckMark /> : <View style={styles.squareBox} />}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: HeightSize(2),
    marginHorizontal: Size(5),
  },
  userContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  imageContainer: {
    width: 46,
    height: 46,
    borderRadius: 23,
  },
  userDetailsContainer: {
    marginLeft: 10,
  },
  nameTxt: {
    fontSize: 14,
    fontWeight: "bold",
  },
  squareBox: {
    width: 16,
    height: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.lighGrey,
    backgroundColor: Colors.white,
  },
});

export default SearchCardForShare;
