import React from "react";
import {
  View,
  Text,
  Modal,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
} from "react-native";
import { CommonActions, useNavigation } from "@react-navigation/native";
import { useSelector } from "react-redux";
import { dispatch } from "../redux/store";

const ConfirmDelete = ({
  shouldShowModal,
  handleCloseModal,
  data,
  selectedTab,
}) => {
  const navigation = useNavigation();

  const deleting = useSelector(
    (state) => state.loading.effects.feed.deleteHighlights
  );

  const handleDelete = async () => {
    const res = await dispatch.feed.deleteHighlights({
      id: data.id,
      userId: data.userId,
    });

    if (res.status === 1) {
      handleCloseModal(false);
      // if (navigation.canGoBack()) {
      //   navigation.goBack();
      // } else {

      navigation.navigate("MyProfileScreenStack", {
        screen: "MyProfile",
        params: { selectedTab: selectedTab },
      });
      // }
    }
  };

  return (
    <Modal visible={shouldShowModal} transparent={true} animationType="slide">
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          <Text style={styles.modalText}>
            Are you sure you want to delete this?
          </Text>
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.button, styles.yesButton]}
              onPress={handleDelete}
              disabled={deleting}
            >
              {deleting ? (
                <ActivityIndicator color="white" />
              ) : (
                <Text style={styles.buttonText}>Yes</Text>
              )}
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.button, styles.noButton]}
              onPress={() => handleCloseModal(false)}
            >
              <Text style={styles.buttonText}>No</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.9)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalContainer: {
    backgroundColor: "white",
    width: "80%",
    padding: 20,
    borderRadius: 20,
    alignItems: "center",
  },
  modalText: {
    fontSize: 18,
    textAlign: "center",
    marginBottom: 20,
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    width: "100%",
  },
  button: {
    flex: 1,
    padding: 10,
    margin: 5,
    borderRadius: 10,
    alignItems: "center",
  },
  yesButton: {
    backgroundColor: "black",
  },
  noButton: {
    backgroundColor: "gray",
  },
  buttonText: {
    color: "white",
    fontSize: 16,
  },
});

export default ConfirmDelete;
