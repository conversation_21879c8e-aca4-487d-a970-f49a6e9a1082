import React from 'react';
import { View, Text, TouchableOpacity, Image, StyleSheet } from 'react-native';
import { Size } from '../../res/Size';
import { commonStyles } from '../../res/CommonStyles';

const RadioButton = ({ label, selected, onSelect, selectedImage, unselectedImage, style}) => {
  const radioButtonImage = selected ? selectedImage : unselectedImage;

  return (
    <TouchableOpacity style={[styles.radioButton,style]} onPress={onSelect}>
      <Image source={radioButtonImage} style={styles.radioButtonImage} />
      <Text style={commonStyles.regularText}>{label}</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  radioButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: Size(5),
  },
  radioButtonImage: {
    width: Size(5),
    height: Size(5),
    marginRight: Size(3),
  },
  radioButtonLabel: {
    fontSize: 16,
  },
});

export default RadioButton;
