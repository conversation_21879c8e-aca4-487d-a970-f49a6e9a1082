import { View, Text, TouchableOpacity, Linking } from "react-native";
import { CopyIcon, GmailIcon, WhatsAppIcon } from "../../res/Svg";
import { handleCopyToClipBoard } from "../../constants/misc";
import styles from "./ShareStyles";

const ShareModal = ({
  url = "https://playerapp.co/invite",
  header = "Invite your friends",
  title = "Join your team on PlayerApp",
  id,
  showHeader = false,
}) => {
  const link = encodeURI(url);

  const shareViaEmail = async () => {
    try {
      const emailSubject = encodeURIComponent(title);
      const emailBody = encodeURIComponent(link);
      const emailUrl = `mailto:?subject=${emailSubject}&body=${emailBody}`;
      await Linking.openURL(emailUrl);
    } catch (error) {
      console.error("Error sharing via email:", error);
    }
  };

  const shareViaWhatsApp = async () => {
    try {
      const text = `${title}\n${link}`;
      const whatsappUrl = `whatsapp://send?text=${encodeURIComponent(text)}`;
      await Linking.openURL(whatsappUrl);
    } catch (error) {
      console.error("Error sharing via WhatsApp:", error);
    }
  };

  return (
    <View style={styles.container}>
      {showHeader && header && (
        <Text style={styles.header}>
          {header}
        </Text>
      )}
      <View style={styles.iconsContainer}>
        <TouchableOpacity onPress={shareViaWhatsApp}>
          <WhatsAppIcon />
        </TouchableOpacity>
        <TouchableOpacity onPress={shareViaEmail}>
          <GmailIcon />
        </TouchableOpacity>
        <TouchableOpacity onPress={() => handleCopyToClipBoard(link)}>
          <CopyIcon />
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default ShareModal;
