import { View, Text, TouchableOpacity, Linking } from "react-native";
import { CopyIcon, GmailIcon, WhatsAppIcon } from "../../res/Svg";
import { handleCopyToClipBoard } from "../../constants/misc";
import { HeightSize, Size } from "../../res/Size";

const ShareModal = ({
  url = "https://playerapp.co/invite",
  header = "Invite your friends",
  title = "Join your team on PlayerApp",
  id,
  showHeader = false,
}) => {
  const link = encodeURI(url);

  const shareViaEmail = async () => {
    try {
      const emailSubject = encodeURIComponent(title);
      const emailBody = encodeURIComponent(link);
      const emailUrl = `mailto:?subject=${emailSubject}&body=${emailBody}`;
      await Linking.openURL(emailUrl);
    } catch (error) {
      console.error("Error sharing via email:", error);
    }
  };

  const shareViaWhatsApp = async () => {
    try {
      const text = `${title}\n${link}`;
      const whatsappUrl = `whatsapp://send?text=${encodeURIComponent(text)}`;
      await Linking.openURL(whatsappUrl);
    } catch (error) {
      console.error("Error sharing via WhatsApp:", error);
    }
  };

  return (
    <View style={{ alignItems: "center" }}>
      <TouchableOpacity style={{ position: "relative" }}>
        <View style={{ alignItems: "center", backgroundColor: "#FFFFFF" }}>
          <Text
            style={{
              textTransform: "uppercase",
              fontSize: 15,
              fontFamily: "Bold",
              color: "#000000",
              paddingTop: HeightSize(2),
            }}
          >
            {showHeader && header}
          </Text>
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              width: "100%",
              paddingHorizontal: 24,
              paddingVertical: HeightSize(2),
            }}
          >
            <TouchableOpacity onPress={shareViaEmail}>
              <View
                style={{
                  width: Size(15),
                  height: Size(15),
                  borderRadius: 18,
                  borderWidth: 1,
                  borderColor: "#000000",
                  justifyContent: "center",
                  alignItems: "center",
                  marginHorizontal: Size(2),
                }}
              >
                <GmailIcon />
              </View>
            </TouchableOpacity>
            <TouchableOpacity onPress={shareViaWhatsApp}>
              <View
                style={{
                  width: Size(15),
                  height: Size(15),
                  borderRadius: 18,
                  borderWidth: 1,
                  borderColor: "#000000",
                  justifyContent: "center",
                  alignItems: "center",
                  marginHorizontal: Size(2),
                }}
              >
                <WhatsAppIcon />
              </View>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => handleCopyToClipBoard(link)}>
              <View
                style={{
                  width: Size(15),
                  height: Size(15),
                  borderRadius: 18,
                  borderWidth: 1,
                  borderColor: "#000000",
                  justifyContent: "center",
                  alignItems: "center",
                  marginHorizontal: Size(2),
                }}
              >
                <CopyIcon />
              </View>
            </TouchableOpacity>
          </View>
        </View>
      </TouchableOpacity>
    </View>
  );
};

export default ShareModal;
