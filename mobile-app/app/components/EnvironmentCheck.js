// app/components/EnvironmentCheck.js
import React, { useEffect } from 'react';
import { View, ActivityIndicator } from 'react-native';
import {useSelector } from 'react-redux';
import { dispatch } from '../redux/store';
import { resetNavigation } from '../constants/navigation';
import { checkEnvironmentChange } from '../utils/checkEnvironmentChange';


export const EnvironmentCheck = ({ children }) => {
  const isAuthLoading = useSelector(state => state.auth.loading);

  useEffect(() => {
    const checkAndHandle = async () => {
      try {
        const envChanged = await checkEnvironmentChange();
        
        if (envChanged) {
          // First, reset navigation to Auth screen
          resetNavigation(0, "AuthNavigation");
          
          // Then proceed with logout
          await dispatch.auth.logout();
        }
      } catch (error) {
        console.error('Error in environment check:', error);
        // On error, reset navigation and proceed with logout
        resetNavigation(0, "AuthNavigation");
        await dispatch.auth.logout();
      }
    };

    checkAndHandle();
  }, [dispatch]);

  if (isAuthLoading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" />
      </View>
    );
  }

 return <View style={{ flex: 1 }}>{children}</View>;;
};