import React, { memo, useState } from "react";
import { View, TouchableOpacity, StyleSheet, Modal, Image, Platform } from "react-native";
import ShareModal from "./Share/Share";
import { ShareProfileIcon } from "../res/Svg";
import { HeightSize, Size } from "../res/Size";
import { Images } from "../res/Images";
import { Colors } from "../res/Colors";

const ShareProfile = ({ Label, header, title, url, id, userType }) => {
  const [isModalVisible, setIsModalVisible] = useState(false);

  const toggleModalVisibility = () => {
    setIsModalVisible(!isModalVisible);
  };

  return (
    <View style={styles.container}>
      <Modal
        visible={isModalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setIsModalVisible(false)}
      >
        <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
        {/* <TouchableOpacity onPress={setIsModalVisible(false)} style={styles.crossBtn}>
        <Image source={Images.cross} style={styles.crossStyle} />
        </TouchableOpacity> */}
        <TouchableOpacity style={styles.crossBtn} onPress={() => setIsModalVisible(false)}>
        <Image source={Images.cross} style={styles.crossStyle} />
        </TouchableOpacity>
          <ShareModal
            id={id}
            header={header}
            title={title}
            url={url}
            showHeader={true}
            onClose={() => setIsModalVisible(false)}
          />
          </View>
        </View>
      </Modal>
      {Label ? (
         <TouchableOpacity onPress={toggleModalVisibility}>
        <Label />
        </TouchableOpacity>
      ) : (
        <TouchableOpacity onPress={toggleModalVisibility}>
          <View style={styles.labelContainer}>
          <ShareProfileIcon width={Platform.isPad ? 40 : 25} 
          height={Platform.isPad ? 40 : 25}
          color={userType == "NON_PLAYER" ? Colors.black : Colors.white}/>
          </View>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  labelContainer: {
    width: Platform.isPad ? 40 : 30,
    height: Platform.isPad ? 40 : 30,
    borderRadius: 15,
    overflow: 'hidden',
   // marginRight: 20,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: "white",
    paddingVertical: HeightSize(2),
    borderRadius: Size(7),
    elevation: 5,
    width: "85%",
    marginTop: HeightSize(10),
    paddingHorizontal: Size(3),
    marginBottom: HeightSize(5),
    overflow: "hidden",
    justifyContent: 'center',
    alignItems: 'center',
  },
  crossStyle: {
    width: Size(3),
    height: Size(3),
  },
  crossBtn: {
    backgroundColor: "#e5e7eb",
    width: Size(5),
    height: Size(5),
    borderRadius: Size(2.5),
    justifyContent: "center",
    alignItems: "center",
    position: "absolute",
    top: HeightSize(1),
    right: Size(3), 
    zIndex: 1,
  },
});

export default memo(ShareProfile);
