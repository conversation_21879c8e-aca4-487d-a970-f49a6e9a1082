import { StyleSheet } from "react-native";
import { Colors } from "../../res/Colors";
import { Size } from "../../res/Size";


const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.black,
    alignItems: "center",
    justifyContent: "center",
    padding: 0,
    margin: 0,
    alignSelf: "center",
  },
  textStyleInsideButton: {
    fontSize: Size(4.4),
   // alignSelf: "center",
    color: Colors.white,
    fontFamily: "Bold",
    textAlign: "center",
  },
  textContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  iconTxtContainer: {
    flexDirection: "row",
    alignItems: "center",
  }
});

export default styles;
