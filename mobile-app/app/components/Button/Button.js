//External dependencies
import React, { useState } from "react";
import { Text, TouchableOpacity, View } from "react-native";
import { HeightSize, Size } from "../../res/Size";
import styles from "./ButtonStyles";
import { Colors } from "../../res/Colors";
import Loader from "../Loader/Loader";

import { useRoute } from "@react-navigation/native";
import analytics from "../../services/analytics";

const Button = ({
  title,
  onPress,
  height = HeightSize(9),
  width = "90%",
  textStyleInsideButton = {},
  containerStyle = {},
  leftComponent = null,
  textContainer = {},
  rightComponent = null,
  leftIcon = null,
  iconTxtContainer = {},
  progress = false,
  disable = false,
  backgroundColor,
  indicatorSize = "small",
  disableColor = Colors.grey,
  indicatorColor = Colors.white,
  loadingType,
  loadingText = "Loading...",
  disableOpacity = false,
}) => {
  const route = useRoute();
  const [isLogging, setIsLogging] = useState(false); // Prevent multiple clicks

  const handlePress = async () => {
    if (onPress && !progress && !isLogging) {
      setIsLogging(true); // Temporarily disable clicks

      try {
        const isEnabled = await analytics.isAnalyticsEnabled();
        if (isEnabled) {
          await analytics.logButtonClick(title, route?.name || "unknown");
        }
      } catch (error) {
        console.error("Analytics tracking failed:", error);
      } finally {
        setIsLogging(false);
        onPress();
      }
    }
  };

  return (
    <TouchableOpacity
      activeOpacity={0.5}
      style={[
        styles.container,
        {
          height,
          width,
          borderRadius: Size(40),
          justifyContent: "center",
          backgroundColor: disable
            ? disableColor || Colors.grey
            : backgroundColor,
          opacity: disable && disableOpacity ? 0.2 : 1,
        },
        containerStyle,
      ]}
      onPress={handlePress}
      disabled={disable || isLogging} // Prevent multiple clicks
    >
      {progress ? (
        loadingType === "text" ? (
          <Text
            style={{
              color: indicatorColor,
              fontSize: Size(3.8),
              fontFamily: "favelaBold",
              textAlign: "center",
            }}
          >
            {loadingText}
          </Text>
        ) : (
          <Loader size={indicatorSize} color={indicatorColor} />
        )
      ) : (
        <View style={[styles.textContainer, textContainer]}>
          {leftComponent}
          <View style={[styles.iconTxtContainer, iconTxtContainer]}>
            {leftIcon}
            <Text style={[styles.textStyleInsideButton, textStyleInsideButton]}>
              {title}
            </Text>
          </View>
          {rightComponent}
        </View>
      )}
    </TouchableOpacity>
  );
};

export default Button;
