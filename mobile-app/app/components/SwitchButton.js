import React from "react";
import { View, Text, TouchableOpacity, Switch, StyleSheet } from "react-native";

const SwitchButton = ({
  checked,
  handleCheckboxChange,
  small,
  isLoading,
  inActiveBgColor = 'gray',
  activeBgColor = 'black',
  labelPosition = "right",
  text = "publish",
  uppercaseText = true,
  //textFont = "PoppinsBold",
}) => {
  const labelStyle = [styles.label, small ? styles.smallText : styles.normalText, textFont === "font-favela-bold" && styles.boldText];
  const containerStyle = [styles.container, uppercaseText && styles.uppercase];
  const switchStyle = [styles.switch, { backgroundColor: checked ? activeBgColor : inActiveBgColor }];

  return (
    <View style={containerStyle}>
      {labelPosition === "left" && (
        <Text style={labelStyle}>{text}</Text>
      )}
      <TouchableOpacity
        style={switchStyle}
        onPress={() => !isLoading && handleCheckboxChange(!checked)}
        disabled={isLoading}
      >
        <Switch
          value={checked}
          onValueChange={handleCheckboxChange}
          disabled={isLoading}
          style={styles.switchInner}
        />
      </TouchableOpacity>
      {labelPosition === "right" && (
        <Text style={labelStyle}>{text}</Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 4,
    width: '100%',
    textAlign: 'center',
  },
  uppercase: {
    textTransform: 'uppercase',
  },
  label: {
    // Default text styling here
  },
  smallText: {
    fontSize: 14,
  },
  normalText: {
    fontSize: 16,
  },
  boldText: {
    fontFamily: 'favela-bold', // Replace with your actual bold font family
  },
  switch: {
    position: 'relative',
    width: 50, // Adjust sizes as needed
    height: 30,
    borderRadius: 15,
    justifyContent: 'center',
  },
  switchInner: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
});

export default SwitchButton;
