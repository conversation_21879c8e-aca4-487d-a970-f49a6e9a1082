import { Size } from "../res/Size";
import { useMemo } from "react";
import { Images } from "../res/Images";
import { TouchableOpacity } from "react-native";
import FastImage from "react-native-fast-image";

const UserAvatar = ({ user, onPress, size = 12 }) => {
  const imageSource = useMemo(() => {
    return !user?.photoUrl || user.photoUrl === "/images/profile.png"
      ? Images.profile1
      : { uri: user.photoUrl, priority: FastImage.priority.high };
  }, [user.photoUrl]);

  const ImageComponent = useMemo(
    () => (
      <FastImage
        source={imageSource}
        style={{
          width: Size(size),
          height: Size(size),
          borderRadius: Size(size / 2),
        }}
        resizeMode={FastImage.resizeMode.cover}
      />
    ),
    [size, imageSource]
  );

  return onPress ? (
    <TouchableOpacity onPress={onPress}>{ImageComponent}</TouchableOpacity>
  ) : (
    ImageComponent
  );
};

export default UserAvatar;
