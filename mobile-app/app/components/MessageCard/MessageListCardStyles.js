import { StyleSheet } from "react-native";
import { HeightSize, Size } from "../../res/Size";
import { Colors } from "../../res/Colors";

const styles = StyleSheet.create({
  container: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    alignSelf: "center",
    paddingVertical: HeightSize(2),
    paddingHorizontal: Size(5),
    position: "relative"
  },
  borderBottom: {
    width: "100%",
    position: "absolute",
    width: "83%",
    bottom: 0,
    height: 1,
    backgroundColor: "rgba(0, 0, 0, 0.1)",
    right: <PERSON>ze(5)
  },
  imageContainer: {
    width: Size(12),
    height: Size(12),
    borderRadius: Size(6),
  },
  nameTxt: {
    fontSize: Size(4),
    fontFamily: "PoppinsBold",
  },
  msgTxt: {
    fontSize: Size(4),
    fontFamily: "PoppinsMedium",
    opacity: 0.5,
  },
  nameMsgContainer: {
    marginLeft: Size(5),
    paddingRight: Size(5)
  },
  timeTxt: {
    fontFamily: "PoppinsMedium",
    opacity: 0.5,
    fontSize: Size(3.5),
    marginBottom: 2
  },
});

export default styles;
