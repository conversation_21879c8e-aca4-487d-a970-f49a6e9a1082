// REACT //

// COMPONENTS //
import { View, Text, TouchableOpacity } from "react-native";

// CONSTANTS //
import { trim150 } from "../../constants/misc";

// NAVIGATION //
import { useNavigation } from "@react-navigation/native";

// OTHERS //
import moment from "moment";
import styles from "./MessageListCardStyles";
import UnreadMessageCount from "../UnreadMessageCount";
import { Size } from "../../res/Size";
import UserAvatar from "../UserAvatar";

const MessageListCard = ({ chat, fromMyProfileContent }) => {
  const navigation = useNavigation();

  return (
    <View style={styles.container}>
      <View style={styles.borderBottom} />

      <TouchableOpacity
        style={{ flexDirection: "row", alignItems: "center", flex: 1 }}
        onPress={() => {
          navigation.navigate({
            name: "Message",
            params: {
              recipientId: chat.otherUser.id,
              chatroomId: chat.chatroomId,
              fromMessageList: true,
              fromMyProfileContent,
            },
            key: `Message-${chat.otherUser.id}`,
          });
        }}
      >
        <View
          style={{ flexDirection: "row", width: "75%", alignItems: "center" }}
        >
          <UserAvatar user={chat.otherUser} />
          <View style={styles.nameMsgContainer}>
            <Text style={styles.nameTxt} numberOfLines={1} ellipsizeMode="tail">
              {`${chat.otherUser?.firstName || ""}${
                chat.otherUser?.lastName ? ` ${chat.otherUser.lastName}` : ""
              }` || "User"}
            </Text>
            <Text style={styles.msgTxt} numberOfLines={1} ellipsizeMode="tail">
              {chat.lastMessage && trim150(chat.lastMessage, 50)}
            </Text>
          </View>
        </View>

        <View style={{ width: "25%", alignItems: "flex-end" }}>
          <Text style={styles.timeTxt}>
            {chat.lastMessageSentAt &&
              moment(chat.lastMessageSentAt).fromNow(true)}
          </Text>
          {chat.unreadMessageCount > 0 ? (
            <UnreadMessageCount count={chat.unreadMessageCount} />
          ) : (
            <View
              style={{
                width: Size(5),
                height: Size(5),
              }}
            />
          )}
        </View>
      </TouchableOpacity>
    </View>
  );
};

export default MessageListCard;
