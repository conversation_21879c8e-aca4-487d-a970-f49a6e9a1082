import React, { useEffect, useRef, useState } from "react";
import {
  Image,
  Text,
  View,
  TouchableOpacity,
  Modal,
  ScrollView,
  ActivityIndicator,
  Platform,
  Dimensions,
  TouchableWithoutFeedback,
} from "react-native";
import { useSelector } from "react-redux";
import { useNavigation } from "@react-navigation/native";
import { Images } from "../../res/Images";
import { ArrowDown, DeleteIcon, Edit, EditIcon } from "../../res/Svg";
import { notifyError, validateURL } from "../../constants/misc";
import EditExperience from "../EditExperienceModal/EditExperienceModal";
import styles from "./ExperienceCardStyles";
import { HeightSize, Size } from "../../res/Size";
import ExperienceApi from "../../services/ExperienceApi";
import { dispatch } from "../../redux/store";
import { commonStyles } from "../../res/CommonStyles";

// Add this function at the beginning of your component
const ExperienceCard = ({
  data = {},
  shouldEdit,
  userType,
  bgColorAndText,
  textColor,
  onExperienceUpdate, // Add this prop
}) => {
  const [isLoading, setLoading] = useState(false);
  const [editModal, setEditModal] = useState({ visible: false, data: null });
  const [activeDeleteModal, setActiveDeleteModal] = useState(null);
  const [activeEditModal, setActiveEditModal] = useState(null);
  const [seasonModal, setSeasonModal] = useState({
    visible: false,
    data: null,
  });

  const navigation = useNavigation();

  const { userId } = useSelector(({ auth }) => ({
    userId: auth.authUser?.userInfo.id,
    userInfo: auth.authUser?.userInfo,
  }));

  const handleDelete = async (id) => {
    try {
      setLoading(true);
      await ExperienceApi.deleteExperience(id);
      if (onExperienceUpdate) {
        onExperienceUpdate(); // Notify parent of update
      }
      setActiveDeleteModal(null);
    } catch (error) {
      notifyError(error?.response?.data?.message || "Something went wrong");
    } finally {
      setLoading(false);
    }
  };

  const openSeasonModal = (seasonData) => {
    // Set the modal position and visibility
    setSeasonModal({
      visible: true,
      data: seasonData,
    });
  };

  const closeSeasonModal = () => {
    setSeasonModal({
      visible: false,
      data: null,
    });
  };

  if (!Object.keys(data).length) {
    return (
      <View
        style={{
          width: "100%",
          height: 128,
          backgroundColor: bgColorAndText,
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <Text style={{ color: "#fff" }}>Add your club history & stats</Text>
      </View>
    );
  }

  const renderClubLogo = (url) => {
    if (url.includes(".svg")) {
      return <SvgUri uri={url} width={30} height={30} />;
    } else {
      return <Image source={{ uri: url }} style={{ width: 30, height: 30 }} />;
    }
  };

  if (isLoading) {
    return <ActivityIndicator />;
  }

  // Add this function to normalize the data structure
  const normalizeExperienceData = (rawData) => {
    // Check if data is an array (other's profile)
    if (Array.isArray(rawData)) {
      // Group by teamName
      return rawData.reduce((acc, exp) => {
        const teamName = exp.teamName || 'Unknown Team';
        if (!acc[teamName]) {
          acc[teamName] = [];
        }
        acc[teamName].push(exp);
        return acc;
      }, {});
    }
    // If it's already in the correct format (own profile)
    return rawData;
  };

  // Normalize the data before using it
  const normalizedData = normalizeExperienceData(data);

  return (
    <>
      {/* Edit Experience Modal */}
      <Modal
        visible={editModal.visible}
        transparent={true}
        onRequestClose={() => setEditModal({ visible: false, data: null })}
      >
        <EditExperience
          handleCloseModal={setEditModal}
          shouldShowModal={editModal.visible}
          data={editModal.data}
          onExperienceUpdate={onExperienceUpdate}
        />
      </Modal>

      {/* Season Stats Modal */}
      <Modal
        visible={seasonModal.visible}
        transparent={true}
        animationType="slide"
        onRequestClose={closeSeasonModal}
      >
        <TouchableWithoutFeedback onPress={closeSeasonModal}>
          <View style={commonStyles.modalBackdrop}>
            <TouchableWithoutFeedback>
              <View
                style={[
                  commonStyles.modalContainer,
                  {
                    alignItems: "center",
                    paddingTop: Size(8),
                  },
                ]}
              >
                <TouchableOpacity
                  onPress={closeSeasonModal}
                  style={commonStyles.modalCloseWrapper}
                >
                  <Image
                    source={Images.cross}
                    style={commonStyles.modalCloseIcon}
                  />
                </TouchableOpacity>

                <View
                  style={{
                    width: "90%",
                    gap: Size(1.5),
                  }}
                >
                  <Text style={[styles.statsPopupTitle]}>
                    Season{" "}
                    {seasonModal.data?.seasonName || seasonModal.data?.name}
                  </Text>

                  <View style={styles.seasonItemContainer}>
                    <Text style={styles.seasonItemText}>Appearances</Text>
                    <Text style={styles.seasonItemText}>
                      {seasonModal.data?.appearances || 0}
                    </Text>
                  </View>

                  <View style={styles.seasonItemContainer}>
                    <Text style={styles.seasonItemText}>Goals</Text>
                    <Text style={styles.seasonItemText}>
                      {seasonModal.data?.goals || 0}
                    </Text>
                  </View>

                  <View style={styles.seasonItemContainer}>
                    <Text style={styles.seasonItemText}>Assists</Text>
                    <Text style={styles.seasonItemText}>
                      {seasonModal.data?.assists || 0}
                    </Text>
                  </View>

                  <View style={styles.seasonItemContainer}>
                    <Text style={styles.seasonItemText}>Clean Sheets</Text>
                    <Text style={styles.seasonItemText}>
                      {seasonModal.data?.cleanSheets || "N/A"}
                    </Text>
                  </View>

                  <View style={styles.seasonItemContainer}>
                    <Text style={styles.seasonItemText}>Minutes Played</Text>
                    <Text style={styles.seasonItemText}>
                      {seasonModal.data?.minsPlayed || 0}
                    </Text>
                  </View>
                </View>
              </View>
            </TouchableWithoutFeedback>
          </View>
        </TouchableWithoutFeedback>
      </Modal>

      <View>
        {Object.entries(normalizedData)
          .sort((a, b) => {
            const seasonA = parseInt(a[1][0]?.seasonName) || 0;
            const seasonB = parseInt(b[1][0]?.seasonName) || 0;
            return seasonB - seasonA; // Sort by latest season year first
          })
          .map((club, key) => {
            const teamData = club[1][0] || {};
            return (
              <View
                key={key + club[0] + teamData?.id}
                style={{ marginVertical: 10 }}
              >
                <TouchableOpacity
                  style={styles.teamNameContainer}
                  onPress={() => {
                    if (teamData?.clubId && teamData?.teamId) {
                      navigation.navigate("MyTeamScreenStack", {
                        screen: "MyTeam",
                        params: {
                          clubId: teamData.clubId,
                          teamId: teamData.teamId,
                        },
                      });
                    }
                  }}
                >
                  {validateURL(teamData?.teamLogo) ? (
                    <Image
                      source={{
                        uri: teamData?.teamLogo.endsWith(".svg")
                          ? teamData?.teamLogo.replace(".svg", ".png") // Replace .svg with .png
                          : teamData?.teamLogo
                      }}
                      style={styles.teamLogo}
                      defaultSource={Images.ball}
                    />
                  ) : (
                    <Image source={Images.ball} style={styles.teamLogo} />
                  )}

                  <Text style={[styles.teamName, { color: textColor }]}>
                    {club[0] !== "undefined" ? club[0] : teamData?.teamName || "No name provided"}
                  </Text>
                </TouchableOpacity>

                {shouldEdit && (
                  <View style={styles.editButtonsContainer}>
                    {/* Start Drop down for delete items */}
                    <View style={styles.relative}>
                      <TouchableOpacity
                        onPress={() => setActiveDeleteModal(club[0])}
                      >
                        <DeleteIcon
                          width={Platform.isPad ? 40 : 25}
                          height={Platform.isPad ? 40 : 25}
                        />
                      </TouchableOpacity>

                      {/* Delete Modal */}
                      <Modal
                        visible={activeDeleteModal === club[0]} // Check if the active modal matches the current experience
                        transparent={true}
                        animationType="slide"
                        onRequestClose={() => setActiveDeleteModal(null)}
                      >
                        <TouchableWithoutFeedback
                          onPress={() => setActiveDeleteModal(null)}
                        >
                          <View style={commonStyles.modalBackdrop}>
                            <TouchableWithoutFeedback>
                              <View
                                style={[
                                  commonStyles.modalContainer,
                                  {
                                    paddingTop: Size(8),
                                  },
                                ]}
                              >
                                <TouchableOpacity
                                  onPress={() => setActiveDeleteModal(null)}
                                  style={commonStyles.modalCloseWrapper}
                                >
                                  <Image
                                    source={Images.cross}
                                    style={commonStyles.modalCloseIcon}
                                  />
                                </TouchableOpacity>

                                <Text style={styles.actionPopupTitle}>
                                  {club[0] || "No name provided"}
                                </Text>

                                <Text style={styles.seasonItemTitle}>
                                  Current Seasons:
                                </Text>

                                <View style={styles.actionPopupWrapper}>
                                  {Array.isArray(club[1]) &&
                                    club[1]?.map((item, idx) => (
                                      <TouchableOpacity
                                        key={idx}
                                        onPress={() => handleDelete(item.id)}
                                        style={[styles.dropdownItem]}
                                      >
                                        <Text style={styles.popupText}>
                                          {item?.seasonName}{" "}
                                        </Text>
                                        <Text style={styles.deleteText}>x</Text>
                                      </TouchableOpacity>
                                    ))}
                                </View>
                              </View>
                            </TouchableWithoutFeedback>
                          </View>
                        </TouchableWithoutFeedback>
                      </Modal>
                    </View>
                    {/*End of Drop down for delete items */}

                    {/* Start Drop down for edit items */}
                    {userType !== "NON_PLAYER" && (
                      <View>
                        <TouchableOpacity
                          onPress={() => setActiveEditModal(club[0])}
                        >
                          <EditIcon
                            width={Platform.isPad ? 40 : 25}
                            height={Platform.isPad ? 40 : 25}
                          />
                        </TouchableOpacity>

                        {/* Edit Modal */}
                        <Modal
                          visible={activeEditModal === club[0]}
                          transparent={true}
                          animationType="slide"
                          onRequestClose={() => setActiveEditModal(null)}
                        >
                          <TouchableWithoutFeedback
                            onPress={() => {
                              setActiveEditModal(null);
                            }}
                          >
                            <View style={commonStyles.modalBackdrop}>
                              <TouchableWithoutFeedback>
                                <View
                                  style={[
                                    commonStyles.modalContainer,
                                    {
                                      paddingTop: Size(8),
                                    },
                                  ]}
                                >
                                  <TouchableOpacity
                                    onPress={() => setActiveEditModal(null)}
                                    style={commonStyles.modalCloseWrapper}
                                  >
                                    <Image
                                      source={Images.cross}
                                      style={commonStyles.modalCloseIcon}
                                    />
                                  </TouchableOpacity>

                                  <Text style={styles.actionPopupTitle}>
                                    {club[0] || "No name provided"}
                                  </Text>

                                  <Text style={styles.seasonItemTitle}>
                                    Current Seasons:
                                  </Text>

                                  <View style={styles.actionPopupWrapper}>
                                    {Array.isArray(club[1]) && club[1]?.map((item, idx) => (
                                      <TouchableOpacity
                                        key={idx + item.id}
                                        onPress={() => {
                                          setEditModal({
                                            visible: true,
                                            data: item,
                                          });
                                          setActiveEditModal(null);
                                        }}
                                        style={[styles.dropdownItem]}
                                      >
                                        <Text style={styles.popupText}>
                                          {item?.seasonName}
                                        </Text>
                                        <Edit
                                          width={Platform.isPad ? 20 : 10}
                                          height={Platform.isPad ? 20 : 10}
                                          style={{ marginTop: 3 }}
                                        />
                                      </TouchableOpacity>
                                    ))}
                                  </View>
                                </View>
                              </TouchableWithoutFeedback>
                            </View>
                          </TouchableWithoutFeedback>
                        </Modal>
                      </View>
                    )}
                    {/* End Drop down for edit items */}
                  </View>
                )}

                <View style={styles.seasonStatsContainer}>
                  <Text style={[styles.statsTitle, { color: textColor }]}>
                    {userType !== "NON_PLAYER" ? "Stats" : "Seasons"}
                  </Text>
                  <View style={styles.seasonsContainer}>
                    {Array.isArray(club[1]) &&
                      club[1]
                        .sort((a, b) => {
                          const startYearA =
                            parseInt(a.seasonName?.split("/")[0]) || 0;
                          const startYearB =
                            parseInt(b.seasonName?.split("/")[0]) || 0;
                          return startYearB - startYearA; // Sort in descending order
                        })
                        .map((season, i) => (
                          <View key={i} style={styles.seasonItem}>
                            <TouchableOpacity
                              // ref={arrowRef}
                              disabled={
                                userType !== "NON_PLAYER" ? false : true
                              }
                              onPress={() => openSeasonModal(season)}
                              style={{
                                flexDirection: "row",
                                alignItems: "center",
                              }}
                            >
                              <Text
                                style={[
                                  styles.seasonText,
                                  { color: textColor },
                                ]}
                              >
                                {season?.seasonName || season?.name}
                              </Text>
                              {userType !== "NON_PLAYER" && (
                                <ArrowDown
                                  width={Platform.isPad ? 20 : 10}
                                  height={Platform.isPad ? 20 : 10}
                                />
                              )}
                            </TouchableOpacity>
                          </View>
                        ))}
                  </View>
                </View>
                <View style={styles.divider} />
              </View>
            );
          })}
      </View>
    </>
  );
};

export default ExperienceCard;
