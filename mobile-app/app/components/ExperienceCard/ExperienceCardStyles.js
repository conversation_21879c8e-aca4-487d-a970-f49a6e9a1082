// REACT NATIVE //
import { Platform, StyleSheet } from "react-native";

// OTHERS //
import { Size, HeightSize } from "../../res/Size";
import { Colors } from "../../res/Colors";

const styles = StyleSheet.create({
  teamNameContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: HeightSize(1),
    width: "90%",
  },
  teamLogo: {
    width: Platform.isPad ? 35 : 28,
    height: Platform.isPad ? 35 : 28,
    borderRadius: 14,
    marginRight: 10,
  },
  teamName: {
    textTransform: "uppercase",
    fontSize: Size(4),
    fontFamily: "favelaBlack",
    color: "white",
  },
  editButtonsContainer: {
    flexDirection: "row",
    justifyContent: "flex-end",
    gap: Size(5),
  },
  relative: {
    position: "relative",
    zIndex: 999,
  },
  dropdownContent: {
    backgroundColor: "#FFF",
    paddingVertical: HeightSize(3),
    borderRadius: Size(5),
    flexDirection: "row",
    flexWrap: "wrap",
    alignItems: "center",
    justifyContent: "space-between",
    position: "absolute",
    zIndex: 1000,
    elevation: 10,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    right: Platform.OS === "ios" ? -45 : -30,
    top: Platform.OS === "ios" ? 10 : 0,
  },
  actionPopupWrapper: {
    flexDirection: "row",
    flexWrap: "wrap",
  },
  actionPopupTitle: {
    color: "black",
    fontSize: Size(4.5),
    fontFamily: "favelaBold",
    textTransform: "uppercase",
    textAlign: "center",
    marginBottom: HeightSize(3),
  },
  seasonItemTitle: {
    fontSize: Size(4),
    fontFamily: "PoppinsSemiBold",
  },
  dropdownItem: {
    paddingVertical: Size(3),
    alignItems: "flex-start",
    flexDirection: "row",
    gap: Size(1),
    width: "33%",
  },
  deleteText: {
    color: "red",
    fontSize: Size(4),
    fontFamily: "Regular",
  },
  popupText: {
    fontSize: Size(4),
    fontFamily: "Regular",
    color: "black",
  },
  seasonStatsContainer: {
    flexDirection: "row",
    gap: Size(5),
    marginVertical: HeightSize(2),
    alignItems: "center",
    flex: 1,
  },
  statsTitle: {
    color: "white",
    fontSize: Size(5),
    fontFamily: "PoppinsMedium",
  },
  statsPopupTitle: {
    color: "black",
    fontSize: Size(6),
    fontFamily: "Regular",
    textAlign: "center",
  },
  seasonsContainer: {
    flexDirection: "row",
    gap: Size(6),
    flex: 1,
    flexWrap: "wrap",
  },
  seasonItem: {
    flexDirection: "row",
    alignItems: "center",
    position: "relative",
  },
  seasonText: {
    fontSize: Size(4),
    color: "white",
  },
  divider: {
    borderBottomWidth: 2,
    borderBottomColor: "#FFFFFF33",
    width: "60%",
    alignSelf: "center",
    marginTop: 10,
  },
  seasonItemText: {
    fontSize: Size(3.5),
    fontFamily: "Regular",
    textAlign: "left",
    paddingRight: Size(1),
  },
  seasonItemContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  modalContainer: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalContent: {
    width: "50%",
    padding: Size(5),
    backgroundColor: "white",
    borderRadius: Size(5),
    alignItems: "center",
    justifyContent: "center",
  },
});

export default styles;
