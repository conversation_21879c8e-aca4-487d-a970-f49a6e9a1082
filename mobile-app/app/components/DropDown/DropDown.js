import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  StyleSheet,
  TextInput,
  FlatList,
} from "react-native";
import { commonStyles } from "../../res/CommonStyles";
import { HeightSize, Size } from "../../res/Size";
import { Ionicons } from "@expo/vector-icons";
import { Colors } from "../../res/Colors";

const RolesPopupModal = ({
  style,
  roles,
  visible,
  toggleRole,
  selectedRole,
  onPress,
  showTick,
  label,
  onClick,
  inEditProfile,
  itemText,
  container,
  line,
}) => {
  const [searchQuery, setSearchQuery] = useState("");

  // Filter roles based on search query
  const filteredRoles = roles.filter((role) =>
    role.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <View style={style}>
      <Text style={[commonStyles.regularText, itemText]}>{label}</Text>
      <TouchableOpacity onPress={onClick}>
        <View
          style={[
            styles.container,
            {
              paddingVertical: inEditProfile ? HeightSize(1) : HeightSize(0.5),
              marginLeft: inEditProfile ? Size(5) : 0,
            },
            container,
          ]}
        >
          {selectedRole && selectedRole.length > 0 ? (
            <Text style={styles.selectedRole}>
              {Array.isArray(selectedRole)
                ? selectedRole.join(", ")
                : selectedRole}
            </Text>
          ) : inEditProfile ? (
            <Text style={commonStyles.regularText}> -- </Text>
          ) : null}
          {inEditProfile && (
            <TouchableOpacity style={styles.iconContainer} onPress={onClick}>
              <Ionicons
                name="chevron-down-outline"
                size={Size(5)}
                color="black"
              />
            </TouchableOpacity>
          )}
        </View>
      </TouchableOpacity>
      <View
        style={[
          styles.line,
          {
            borderBottomColor: inEditProfile
              ? "rgba(0, 0, 0, 0.1)"
              : Colors.black,
          },
          line,
        ]}
      />

      <Modal visible={visible} transparent>
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            {/* Search Input */}
            <TextInput
              placeholder="Search..."
              placeholderTextColor="rgba(0,0,0,0.5)"
              style={styles.searchInput}
              value={searchQuery}
              onChangeText={(text) => setSearchQuery(text)}
            />

            {/* Roles List */}
            <FlatList
              data={filteredRoles}
              keyExtractor={(item) => item.id.toString()}
              renderItem={({ item: role }) => {
                // Check if role is selected
                const isSelected = selectedRole.includes(role.name);

                return (
                  <TouchableOpacity
                    key={role.id}
                    style={[
                      styles.roleItem,
                      {
                        backgroundColor:
                          selectedRole?.id === role.id
                            ? Colors.green
                            : Colors.white,
                      },
                    ]}
                    onPress={() => {
                      toggleRole(role);
                      onPress();
                    }}
                  >
                    {showTick && (
                      <View
                        style={[
                          styles.checkbox,
                          {
                            backgroundColor:
                              selectedRole?.id === role.id
                                ? Colors.black
                                : Colors.white,
                          },
                        ]}
                      >
                        {isSelected && (
                          <Text style={{ color: "black" }}>✓</Text>
                        )}
                      </View>
                    )}
                    <Text
                      style={[
                        commonStyles.regularText,
                        { paddingHorizontal: Size(2) },
                      ]}
                    >
                      {role.name}
                    </Text>
                  </TouchableOpacity>
                );
              }}
              ListEmptyComponent={
                <Text style={commonStyles.regularText}>No roles found</Text>
              }
            />
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  modalContent: {
    backgroundColor: "white",
    padding: 20,
    borderRadius: 10,
    elevation: 5,
    width: "90%",
  },
  roleItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: HeightSize(2),
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0, 0, 0, 0.1)",
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 1,
    borderColor: "black",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 10,
  },
  itemText: {
    fontFamily: "Bold",
    fontSize: Size(4),
    marginLeft: Size(5),
  },
  container: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: HeightSize(2.5),
    marginLeft: Size(5),
  },
  iconContainer: {
    marginRight: Size(5),
  },
  line: {
    borderBottomWidth: 1,
  },
  selectedRole: {
    marginRight: Size(2),
    color: "black",
  },
});

export default RolesPopupModal;
