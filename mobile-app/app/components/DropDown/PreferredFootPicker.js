import React from 'react';
import { View, Text, TextInput, Picker, StyleSheet } from 'react-native';

const PreferredFootPicker = ({ userInfo, values, handleChange, handleBlur, errors, touched, PreferredFoot }) => {
  if (userInfo.userType === 'NON_PLAYER') return null;

  return (
    <View style={styles.container}>
      <View style={styles.inputGroup}>
        <Picker
          selectedValue={values?.preferredFoot}
          onValueChange={(itemValue) => handleChange('preferredFoot')(itemValue)}
          onBlur={handleBlur('preferredFoot')}
          style={styles.picker}
        >
          <Picker.Item label="--" value="" enabled={false} />
          {Object.keys(PreferredFoot).map((key) => (
            <Picker.Item key={key} label={PreferredFoot[key]} value={key} />
          ))}
        </Picker>
        <Text style={styles.label}>Preferred Foot</Text>
        {errors.preferredFoot && touched.preferredFoot && (
          <Text style={styles.errorText}>{errors.preferredFoot}</Text>
        )}
      </View>

      <View style={styles.inputGroup}>
        <TextInput
          value={values?.height}
          onChangeText={handleChange('height')}
          onBlur={handleBlur('height')}
          style={styles.input}
          placeholder="Metres"
        />
        <Text style={styles.label}>Height</Text>
        {errors.height && touched.height && (
          <Text style={styles.errorText}>{errors.height}</Text>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  picker: {
    height: 50,
    width: '100%',
    borderColor: 'gray',
    borderBottomWidth: 2,
  },
  input: {
    height: 40,
    borderColor: 'gray',
    borderBottomWidth: 2,
    marginBottom: 5,
  },
  label: {
    position: 'absolute',
    left: 0,
    top: -10,
    fontSize: 12,
    color: 'gray',
  },
  errorText: {
    color: 'red',
    fontSize: 12,
    textAlign: 'center',
  },
});

export default PreferredFootPicker;
