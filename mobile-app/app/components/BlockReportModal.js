import { Modal, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { BlockIcon, RedDeleteIcon, ReportIcon } from "../res/Svg";
import { Size } from "../res/Size";
import { Colors } from "../res/Colors";

const BlockReportModal = ({
  visible,
  onClose,
  onBlock,
  onReport,
  top,
  right,
  showReportOption = true,
  text,
  showBlockOption = true,
  showDeleteComment = false,
  onDelete,
  pointedLeft = false,
  hugContent = false,
}) => {
  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <TouchableOpacity
        style={styles.modalContainer}
        activeOpacity={1}
        onPressOut={onClose}
      >
        <View
          style={[
            styles.modalContent,
            { top, right },
            pointedLeft
              ? {
                  borderTopLeftRadius: 0,
                  borderTopRightRadius: Size(3),
                }
              : {
                  borderTopLeftRadius: Size(3),
                  borderTopRightRadius: 0,
                },
            hugContent === false ? { minWidth: 200 } : null,
          ]}
        >
          {showReportOption && (
            <TouchableOpacity style={styles.menuOption} onPress={onReport}>
              <View style={styles.optionContainer}>
                <ReportIcon width={Size(4)} height={Size(4)} />
                <Text style={styles.text}>{`Report ${text}`}</Text>
              </View>
            </TouchableOpacity>
          )}
          {showBlockOption && (
            <TouchableOpacity style={styles.menuOption} onPress={onBlock}>
              <View style={styles.optionContainer}>
                <BlockIcon width={Size(4)} height={Size(4)} />
                <Text style={styles.text}>Block user</Text>
              </View>
            </TouchableOpacity>
          )}
          {showDeleteComment && (
            <TouchableOpacity style={styles.menuOption} onPress={onDelete}>
              <View style={styles.optionContainer}>
                <RedDeleteIcon width={Size(4)} height={Size(4)} />
                <Text style={styles.text}>Delete comment</Text>
              </View>
            </TouchableOpacity>
          )}
        </View>
      </TouchableOpacity>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  modalContent: {
    backgroundColor: "white",
    borderBottomEndRadius: Size(3),
    borderBottomLeftRadius: Size(3),
    paddingVertical: Size(3),
    paddingHorizontal: Size(5),
    position: "absolute", // Use absolute positioning to allow dynamic placement
  },
  menuOption: {
    paddingVertical: 10,
  },
  optionContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  text: {
    marginLeft: Size(3),
    fontSize: Size(4),
    color: Colors.red,
  },
});

export default BlockReportModal;
