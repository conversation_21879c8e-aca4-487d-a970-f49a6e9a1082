import { StyleSheet } from "react-native";
import { HeightSize, Size } from "../../res/Size";
import { Colors } from "../../res/Colors";

const styles = StyleSheet.create({
  card: {
    flexDirection: "row",
    //paddingVertical: HeightSize(1),
    justifyContent: "space-between",
    alignItems: "center",
    width: "100%",
    flex: 1,
   
  },
  cardContent: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  profileImage: {
    width: Size(10),
    height: Size(10),
    borderRadius: Size(5),
    
  },
  profileInfo: {
    marginLeft: Size(3),
    flex: 1,
  },
  nickname: {
    fontFamily: "Regular",
    fontSize: Size(4),
    color: Colors.white,
    marginTop: HeightSize(2)
  },
  teamName: {
    //marginTop: -5,
    opacity: 0.5,
    fontFamily: "Regular",
    fontSize: Size(4),
    color: Colors.white
  },
  dropdown: {
    position: "relative",
  },
  dropdownButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: <PERSON><PERSON>(1.5),
    backgroundColor: "#ffffff8a",
    borderRadius: 20,
    paddingHorizontal: Size(3)
  },
  followingText: {
    color: "white",
  },
  dropdownIcon: {
    marginLeft: 5,
  },
  dropdownContent: {
   // position: "absolute",
   // top: 30,
    width: "100%",
    backgroundColor: "white",
    borderRadius: 10,
    marginTop: HeightSize(0.5),
   // zIndex: 2
  },
  unfollowButton: {
   // padding: 30,
    alignItems: "center",
    //backgroundColor: "pink",
    borderRadius: 10,
    paddingVertical: HeightSize(0.5)
    
  },
  unfollowText: {
    color: "black",
  },
  followButton: {
    backgroundColor: "#52FF00",
    //padding: Size(2),
    borderRadius: 20,
    paddingHorizontal: Size(3),
    paddingVertical: Size(1.5),
    width: "30%"
  },
  followText: {
    color: "black",
    textAlign: "center"
  },
});
 
export default styles;