import { useState } from "react";
import { View, Text, TouchableOpacity } from "react-native";
import { useNavigation } from "@react-navigation/native";
import styles from "./ConnectionCardsStyles";
import { ArrowDown } from "../../res/Svg";
import { useSelector } from "react-redux";
import UserAvatar from "../UserAvatar";

const ConnectionCards = ({
  connection,
  follow,
  unfollow,
  isLoggedUserFollowingProfile,
  isProfileFollowingLoggedUser,
  isOwnConnectionsScreen,
}) => {
  const [dropdownVisible, setDropdownVisible] = useState(false);
  const { userInfo } = useSelector(({ user }) => ({
    userInfo: user.data,
  }));

  const navigation = useNavigation();

  const handleNavigationToProfile = () => {
    navigation.navigate("MyProfile", {
      userId: connection.id,
    });
  };

  const thisUser = connection.id === userInfo?.id;

  return (
    <View style={styles.card}>
      <View style={styles.cardContent}>
        <UserAvatar user={connection} size={10} />
        <TouchableOpacity
          onPress={handleNavigationToProfile}
          style={styles.profileInfo}
        >
          <Text style={styles.nickname}>
            {connection?.firstName} {connection?.lastName}
          </Text>
          <Text style={styles.teamName}>
            {connection.teamName === "N/A"
              ? connection.clubName
              : connection.teamName}
          </Text>
        </TouchableOpacity>
      </View>
      {thisUser ? (
        <View />
      ) : Boolean(isLoggedUserFollowingProfile && isOwnConnectionsScreen) ? (
        <View style={styles.dropdown}>
          <TouchableOpacity
            style={styles.dropdownButton}
            onPress={() => {
              if (isOwnConnectionsScreen) {
                setDropdownVisible(!dropdownVisible);
              }
            }}
          >
            <Text style={styles.followingText}>Following</Text>
            <ArrowDown />
          </TouchableOpacity>

          {dropdownVisible && (
            <View style={styles.dropdownContent}>
              <TouchableOpacity
                onPress={() => {
                  if (isOwnConnectionsScreen) {
                    unfollow(connection.id);
                    setDropdownVisible(false);
                  }
                }}
                style={styles.unfollowButton}
              >
                <Text style={styles.unfollowText}>Unfollow</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      ) : (
        isOwnConnectionsScreen && (
          <TouchableOpacity
            onPress={() => {
              if (isOwnConnectionsScreen) {
                follow(connection.id);
              }
            }}
            style={styles.followButton}
          >
            <Text style={styles.followText}>
              {isProfileFollowingLoggedUser ? "Follow Back" : "Follow"}
            </Text>
          </TouchableOpacity>
        )
      )}
    </View>
  );
};

export default ConnectionCards;
