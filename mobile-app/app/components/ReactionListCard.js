import React from 'react';
import { View, Text, Image, TouchableOpacity, StyleSheet } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Size } from '../res/Size';

const ReactionListCard = ({ reaction, userId }) => {
  const navigation = useNavigation();

  const handleOpenProfile = (id) => {
    if (id) {
      navigation.navigate('MyProfile', { id });
    } else {
      navigation.navigate('Login');
    }
  };

  return (
    <TouchableOpacity onPress={() => handleOpenProfile(userId)} style={styles.card}>
      <Text style={styles.username}>@{reaction.user.firstName}</Text>
      <View style={styles.emojiContainer}>
        <Image source={{ uri: reaction.emoji }} style={styles.emoji} />
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    flexDirection: 'row',
    marginTop: Size(2),
    padding: Size(2),
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#f4f3f3',
    borderRadius: Size(2),
  },
  username: {
    fontSize: <PERSON>ze(4),
    fontFamily: 'Regular',
  },
  emojiContainer: {
    padding: Size(1),
    borderRadius: Size(7),
    backgroundColor: 'white',
  },
  emoji: {
    width: Size(5),
    height: Size(5),
    borderRadius: Size(5),
  },
});

export default ReactionListCard;
