import React from "react";
import { View, Text, Switch } from "react-native";

const SwitchButton = ({
  checked,
  handleCheckboxChange,
  small,
  isLoading,
  inActiveBgColor = '#A9A9A9',
  activeBgColor = '#696969',
  labelPosition = "right",
  text = "publish",
  uppercaseText = true,
  //textFont = { fontFamily: "font-favela-bold" },
}) => {
  return (
    <View style={{ flexDirection: "row", alignItems: "center", justifyContent: "center", width: "100%" }}>
      {labelPosition === "left" && (
        <Text style={{ ...(uppercaseText && { textTransform: "uppercase" }), ...(textFont || {}), ...(small ? { fontSize: 14 } : { fontSize: 16 }) }}>{text}</Text>
      )}
      <Switch
        value={checked}
        onValueChange={handleCheckboxChange}
        disabled={isLoading}
        style={{ transform: [{ scaleX: small ? 0.7 : 1 }, { scaleY: small ? 0.7 : 1 }], opacity: isLoading ? 0.5 : 1 }}
        thumbColor={checked ? "#fff" : "#fff"}
        trackColor={{ false: inActiveBgColor, true: activeBgColor }}
      />
      {labelPosition === "right" && (
        <Text style={{ ...(uppercaseText && { textTransform: "uppercase" }), ...(textFont || {}), ...(small ? {} : { fontSize: 16 }) }}>{text}</Text>
      )}
    </View>
  );
};

export default SwitchButton;
