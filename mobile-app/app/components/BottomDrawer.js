import React, { useRef, useEffect, useCallback } from "react";

// COMPONENTS //
import {
  BottomSheetModal,
  BottomSheetModalProvider,
  BottomSheetScrollView,
  BottomSheetView,
  BottomSheetBackdrop,
  BottomSheetFooter,
} from "@gorhom/bottom-sheet";

/** Bottom Drawer Component */
const BottomDrawer = ({
  children,
  isOpen,
  onClose,
  snapPoints,
  isScrollable,
  index,
  footerComponent,
  shouldRenderBackdrop = true,
  enablePanDownToClose,
  onModalChange,
  onBackdropPress,
  shouldCloseOnBackdropPress = true,
  enableDynamicSizing = true,
}) => {
  // Define States

  // Define Refs
  const bottomSheetModalRef = useRef(null);

  // Helper Functions

  /** Open the Drawer */
  const presentDrawer = () => {
    bottomSheetModalRef.current?.present();
  };

  /** Dismiss the Drawer */
  const dismissDrawer = () => {
    bottomSheetModalRef.current?.dismiss();
  };

  /** Snap to particular index */
  const snapTo = (index) => {
    bottomSheetModalRef.current?.snapToIndex(index);
  };

  // Use Effect and Focus Effect
  useEffect(() => {
    if (isOpen) {
      presentDrawer();
      snapTo(index || 0);
    } else {
      dismissDrawer();
    }
  }, [isOpen]);

  // Backdrop Component
  const renderBackdrop = useCallback(
    (props) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        onPress={onBackdropPress}
        pressBehavior={shouldCloseOnBackdropPress ? "close" : "none"}
      />
    ),
    []
  );

  const renderFooter = useCallback(
    (props) => (
      <BottomSheetFooter {...props}>{footerComponent}</BottomSheetFooter>
    ),
    []
  );

  return (
    <>
      {/**Bottom Sheet Modal Component */}
      <BottomSheetModalProvider>
        <BottomSheetModal
          ref={bottomSheetModalRef}
          onChange={onModalChange}
          snapPoints={snapPoints}
          index={index}
          onDismiss={onClose}
          backdropComponent={shouldRenderBackdrop ? renderBackdrop : undefined}
          footerComponent={renderFooter}
          enablePanDownToClose={enablePanDownToClose}
          containerStyle={{ zIndex: 100 }}
          keyboardBlurBehavior={"restore"}
          keyboardBehavior="interactive"
          android_keyboardInputMode="adjustResize"
          enableDynamicSizing={enableDynamicSizing}
          handleHeight={0}
          handleIndicatorStyle={{ backgroundColor: "tranparent" }}
        >
          {isScrollable ? (
            <BottomSheetScrollView contentContainerStyle={{ flex: 1 }}>
              {children}
            </BottomSheetScrollView>
          ) : (
            <BottomSheetView style={{ flex: 1 }}>{children}</BottomSheetView>
          )}
        </BottomSheetModal>
      </BottomSheetModalProvider>
    </>
  );
};

export default BottomDrawer;