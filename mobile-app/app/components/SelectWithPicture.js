import React, { useState, useEffect } from "react";
import {
  View,
  TextInput,
  TouchableOpacity,
  FlatList,
  Text,
  Image,
} from "react-native";
import { HeightSize, Size } from "../res/Size";
import { Colors } from "../res/Colors";
import { SvgXml } from "react-native-svg";
import { commonStyles } from "../res/CommonStyles";
import { normalizeText } from "../constants/misc";

const SelectWithPicture = ({
  setSelectClub,
  selectClub,
  setTeams,
  setSelectTeam,
  getTeamsBasedOnSelectedClub,
  searchValue,
  setSearchValue,
  setTeamLogo,
  className = "",
  userInfo = {},
  clubModel,
  loading,
}) => {
  const [searchData, setSearchData] = useState([]);
  const [listOfClubs, setListOfClubs] = useState([]);
  const [showClubs, setShowClubs] = useState(false);
  const [svgContent, setSvgContent] = useState(null);


useEffect(() => {
  if (clubModel) {
    setListOfClubs(clubModel);
  }
}, [clubModel]);

useEffect(() => {
  if (searchValue) {
    const filteredData = listOfClubs.filter((club) => {
      const clubNameNorm = club.clubName ? normalizeText(club.clubName) : "";
      return clubNameNorm.includes(normalizeText(searchValue));
    });
    setSearchData(filteredData);
  } else {
    setSearchData([]);
  }
}, [searchValue, listOfClubs]);

  const handleClubChange = (selectedClub) => {
    if (setTeamLogo) {
      setTeamLogo(selectedClub?.clubLogoUrl);
    }

    if (selectedClub?.id) {
      setSelectClub(selectedClub || {});
      setSearchValue(selectedClub.clubName);
      if (getTeamsBasedOnSelectedClub) {
        getTeamsBasedOnSelectedClub(selectedClub.id);
      }
    }

    if (setTeams) {
      setTeams([]);
    }
    if (setSelectTeam) {
      setSelectTeam({});
    }
  };

  // Function to fetch SVG content from the URL
  const fetchSvg = async (url) => {
    try {
      const response = await fetch(url);

      const text = await response.text();
      setSvgContent(text);
    } catch (error) {
      console.error("Error fetching SVG:", error);
    }
  };

  // Function to render club logo
  const renderClubLogo = (url) => {
    // Check if the image URL ends with .svg
    if (url?.endsWith(".svg")) {
      if (!svgContent) {
        fetchSvg(url);
        return (
          <Image
            source={{ uri: "https://path-to-your-placeholder.png" }}
            style={{ width: Size(4), height: Size(4) }}
          />
        );
      }
      // Render the SVG content using SvgXml
      return (
        <SvgXml
          xml={svgContent}
          width={Size(7)}
          height={Size(7)}
          style={{ borderWidth: 2, width: 100, height: 100 }}
        />
      );
    } else {
      return (
        <Image
          source={{ uri: url }}
          style={{
            width: Size(4),
            height: Size(4),
            borderRadius: Size(1),
            marginHorizontal: Size(2),
          }}
        />
      );
    }
  };

  const renderList = ({ item, index }) => {
    const isLastItem = index === searchData.length - 1;

    return (
      <TouchableOpacity
        onPress={() => {
          handleClubChange(item);
          setShowClubs(false);
        }}
        style={{
          paddingVertical: HeightSize(1.5),
          flexDirection: "row",
          alignItems: "center",
          marginHorizontal: Size(2),
          borderBottomWidth: isLastItem ? 0 : 1, // Remove border for the last item
          borderColor: Colors.grey,
        }}
      >
        {renderClubLogo(item?.clubLogoUrl)}
        <Text style={{ fontSize: Size(3.5) }}>{item?.clubName}</Text>
      </TouchableOpacity>
    );
  };

  return (
    <View>
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          borderBottomWidth: 1,
          borderColor: Colors.black,
          height: HeightSize(4),
        }}
      >
        {selectClub?.clubLogoUrl && renderClubLogo(selectClub.clubLogoUrl)}

        <TextInput
          onChangeText={(text) => {
            setSearchValue(text);
            setSelectClub({});
            setShowClubs(true);
          }}
          onBlur={() => {
            if (!searchValue.trim()) {
              setSearchValue("");
            }
          }}
          style={{
            fontFamily: "Regular",
            fontSize: Size(3.5),
            flex: 1,
          }}
          value={searchValue}
          placeholder={userInfo?.clubName || "Search"}
          placeholderTextColor={"gray"}
          autoCorrect={false}
          autoCapitalize="none"
          spellCheck={false}
          autoComplete="off"
        />
      </View>

      {showClubs &&
        (searchData.length > 0 ? (
          <View
            style={{
              backgroundColor: Colors.white,
              width: "100%",
              borderBottomLeftRadius: Size(4),
              borderBottomRightRadius: Size(4),
              borderWidth: 1,
              borderTopWidth: 0,
              borderColor: Colors.black,
            }}
          >
            <FlatList
              data={searchData}
              keyExtractor={(item) => item.id.toString()}
              renderItem={renderList}
              style={{ maxHeight: HeightSize(14) }}
              nestedScrollEnabled={true}
            />
          </View>
        ) : (
          searchValue.trim() !== "" && (
            <>
              <Text style={commonStyles.errTxt}>Team not yet available.</Text>
              <Text style={commonStyles.errTxt}>
                <NAME_EMAIL> to get it added.
              </Text>
            </>
          )
        ))}
    </View>
  );
};

export default SelectWithPicture;
