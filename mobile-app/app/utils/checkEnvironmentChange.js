import AsyncStorage from '@react-native-async-storage/async-storage';
import Config from 'react-native-config'; 

const currentEnv = Config.APP_ENV || (__DEV__ ? 'development' : 'production');
const ENV_KEY = 'lastUsedEnvironment';

export const checkEnvironmentChange = async () => {
  try {
    const storedEnv = await AsyncStorage.getItem(ENV_KEY);

    if (storedEnv !== currentEnv) {
      console.warn(`Environment changed from '${storedEnv}' to '${currentEnv}'. Clearing persisted data.`);
      await AsyncStorage.clear(); // or selectively clear keys
      await AsyncStorage.setItem(ENV_KEY, currentEnv);
      return true; // Trigger logout or reset
    }

    return false;
  } catch (error) {
    console.error('Error during environment check:', error);
    return true; 
  }
};
