import { getSingleModel } from "../constants/Constant";
import { liveLikeClientId } from "../constants/misc";
import moment from "moment";
import { LiveLikeReactionPacks } from "../constants/reactions";
import { Image } from "react-native";
const LiveLike = require("@livelike/javascript");

export async function initializeLiveLike() {
  try {
    const userInfo = getSingleModel("auth")?.authUser.userInfo;

    const liveLikeProfile = await LiveLike.init({
      accessToken: userInfo.liveLikeProfileToken,
      clientId: liveLikeClientId,
    });

    const customData = liveLikeProfile?.custom_data
      ? JSON.parse(liveLikeProfile.custom_data)
      : {};

    if (customData) {
      if (customData.nextUpdate <= moment().unix()) {
        await LiveLike.updateUserProfile({
          accessToken: userInfo.liveLikeProfileToken,
          options: {
            custom_data: JSON.stringify({
              photoUrl: userInfo?.photoUrl || "/images/profile.png",
              firstName: userInfo?.firstName,
              lastName: userInfo?.lastName,
              userId: userInfo?.id,
              clubName: userInfo?.clubName,
              teamName: userInfo?.teamName,
              liveLikeProfileId: userInfo?.liveLikeProfileId,
              nextUpdate: moment().add(5, "days").unix(),
            }),
          },
        });
      }
    } else {
      await LiveLike.updateUserProfile({
        accessToken: userInfo.liveLikeProfileToken,
        options: {
          custom_data: JSON.stringify({
            photoUrl: userInfo.photoUrl || "/images/profile.png",
            firstName: userInfo.firstName,
            lastName: userInfo.lastName,
            userId: userInfo.id,
            clubName: userInfo.clubName,
            teamName: userInfo.teamName,
            liveLikeProfileId: userInfo.liveLikeProfileId,
            nextUpdate: moment().add(5, "days").unix(),
          }),
        },
      });
    }
  } catch (error) {
    console.error("[LiveLike] Error initialising LiveLike: ", error);
  }
}

export async function prefetchLiveLikeEmojis() {
  try {
    const prefetchPromises = LiveLikeReactionPacks?.emojis.map((item) =>
      Image.prefetch(item.file)
    );
    await Promise.all(prefetchPromises);
    return true;
  } catch (error) {
    console.error("[LiveLike] Error prefetching emoji images:", error);
    return false;
  }
}
