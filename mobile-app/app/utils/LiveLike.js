import { getSingleModel } from "../constants/Constant";
import { liveLikeClientId } from "../constants/misc";
import { LiveLikeReactionPacks } from "../constants/reactions";
import { Image } from "react-native";
const LiveLike = require("@livelike/javascript");

export async function initializeLiveLike() {
  try {
    const userInfo = getSingleModel("auth")?.authUser.userInfo;

    await LiveLike.init({
      accessToken: userInfo.liveLikeProfileToken,
      clientId: liveLikeClientId,
    });
  } catch (error) {
    console.error("[LiveLike] Error initialising LiveLike: ", error);
  }
}

export async function prefetchLiveLikeEmojis() {
  try {
    const prefetchPromises = LiveLikeReactionPacks?.emojis.map((item) =>
      Image.prefetch(item.file)
    );
    await Promise.all(prefetchPromises);
    return true;
  } catch (error) {
    console.error("[LiveLike] Error prefetching emoji images:", error);
    return false;
  }
}
