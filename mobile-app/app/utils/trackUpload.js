import axios from "axios";
import Upload<PERSON><PERSON>piHand<PERSON> from "../services/uploader";

export async function uploadPartWithProgress(
  params,
  blob,
  onProgress,
  { partNumber, totalParts }
) {
  const {
    data: { data }
  } = await UploaderApiHandler.getUploadUrl(params);
  try {
    const config = {
      skipAuthAndHeaders: true,
      headers: {
        "Content-Length": params.contentLength
      },
      transformRequest: [(data, headers) => {        
        delete headers.common;
        return data;
      }],

      onUploadProgress: (progressEvent) => {
        const percentCompleted = progressEvent.loaded / progressEvent.total;

        const partsBefore = partNumber - 1;

        const overallProgress =
          ((partsBefore + percentCompleted) / totalParts) * 100;

        onProgress(Math.round(overallProgress));
      }
    };

    const response = await axios.put(data.url, blob, config);
    const eTag = response.headers.etag?.replace(/"/g, "");
    return eTag;
  } catch (error) {
    throw new Error(error.response?.data);
  }
}
