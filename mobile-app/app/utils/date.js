import moment from "moment";

export function getShorthandTimestamp(date) {
  const now = moment();
  const then = moment(date);
  const seconds = now.diff(then, "seconds");
  const minutes = now.diff(then, "minutes");
  const hours = now.diff(then, "hours");
  const days = now.diff(then, "days");
  const weeks = now.diff(then, "weeks");

  if (seconds < 60) return `${seconds}s`;
  if (minutes < 60) return `${minutes}m`;
  if (hours < 24) return `${hours}h`;
  if (days < 7) return `${days}d`;
  return `${weeks}w`;
}
