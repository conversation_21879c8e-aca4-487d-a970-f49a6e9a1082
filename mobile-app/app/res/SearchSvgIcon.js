import React from "react";
import { SvgXml } from "react-native-svg";
import { Colors } from "./Colors";

const SearchIcon = ({ green= Colors.green, ...props }) => {
  return (
    <SvgXml
      xml={`
        <svg
          width="25"
          height="25"
          viewBox="0 0 30 30"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g opacity="0.5">
            <path
              d="M14.5 23C19.1944 23 23 18.9706 23 14C23 9.02944 19.1944 5 14.5 5C9.80558 5 6 9.02944 6 14C6 18.9706 9.80558 23 14.5 23Z"
              stroke="${green ? "green" : "black"}"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M25 25L21 20"
              stroke="${green ? "green" : "black"}"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </g>
        </svg>
      `}
      {...props}
    />
  );
};

// export const SearchIcon2 = () => (
//   <Svg
//     width="20"
//     height="21"
//     viewBox="0 0 20 21"
//     fill="none"
//     xmlns="http://www.w3.org/2000/svg"
//   >
//     <path
//       d="M9.16667 16.3333C12.8486 16.3333 15.8333 13.3486 15.8333 9.66667C15.8333 5.98477 12.8486 3 9.16667 3C5.48477 3 2.5 5.98477 2.5 9.66667C2.5 13.3486 5.48477 16.3333 9.16667 16.3333Z"
//       stroke="#52FF00"
//       strokeWidth="2"
//       strokeLinecap="round"
//       strokeLinejoin="round"
//     />
//     <path
//       d="M17.5 18L13.875 14.375"
//       stroke="#52FF00"
//       strokeWidth="2"
//       strokeLinecap="round"
//       strokeLinejoin="round"
//     />
//   </Svg>
// );

export default SearchIcon;
