import { Platform, StyleSheet } from "react-native";
import { Colors } from "./Colors";
import { HeightSize, Size } from "./Size";

export const commonStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  boldText: {
    color: Colors.black,
    fontSize: Size(5),
    fontFamily: "Bold",
  },
  regularText: {
    color: Colors.black,
    fontSize: Size(3.8),
    fontFamily: "Regular",
  },
  subContainer: {
    paddingHorizontal: Size(5),
    marginTop: HeightSize(7),
  },
  textInputStyle: {
    borderBottomWidth: 1,
    // paddingVertical: Platform.OS === "ios" ? HeightSize(1) : HeightSize(0),
    // paddingBottom: Platform.OS === "ios" ? HeightSize(1) : 0,
    // lineHeight: Platform.OS === "ios" ? HeightSize(4) : undefined,
    fontSize: Size(3.7),
  },
  errTxt: {
    fontSize: Size(3),
    color: Colors.warning,
    fontFamily: "Regular",
    //marginBottom:HeightSize(5),
    // paddingHorizontal: Size(16),
  },
  normalTxt: {
    color: Colors.white,
    fontSize: Size(3.8),
    fontFamily: "Regular",
    textAlign: "center",
  },
  shadow: {
    shadowColor: Colors.grey,
    shadowOffset: {
      width: 2,
      height: 4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 5,
    elevation: 8,
  },
  modalBackdrop: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)", // Semi-transparent black for backdrop
    justifyContent: "center",
    alignItems: "center",
  },
  modalContainer: {
    backgroundColor: Colors.white,
    borderWidth: 6,
    borderColor: Colors.green,
    borderRadius: Size(5),
    padding: Size(5),
    width: "90%",
    position: "relative",
    maxWidth: 500,
  },
  modalCloseWrapper: {
    position: "absolute",
    top: 14,
    right: 14,
    height: 24,
    width: 24,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#EDEDED",
    borderRadius: 12,
    zIndex: 10
  },
  modalCloseIcon: {
    height: 12,
    width: 12,
  },
});
