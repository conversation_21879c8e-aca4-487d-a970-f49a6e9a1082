import { useState, useEffect } from 'react';
import Client from './Client';
import { notifyError, removeDuplicates } from '../constants/misc';

const useFetchHighlights = (userId, requestPayload) => {
  const [profileHighlights, setProfileHighlights] = useState([]);
  const [profileLastEvaluatedKey, setProfileLastEvaluatedKey] = useState(null);
  const [fetchingHighlight, setFetchingHighlight] = useState(true);
  const [fetchingMore, setFetchingMore] = useState(false);
  const [error, setError] = useState(null);

  const fetchHighlightsByUserId = async (id, payload) => {
    setError(null);
    setFetchingHighlight(true);

    try {
      const { data } = await Client.post(`highlights/user-highlights/${id}`, payload);
      
      const mergeNewFeedToOld = [
        ...profileHighlights,
        ...data.data.Items
      ];

      const cleaned = removeDuplicates(mergeNewFeedToOld, "id");
      
      // Sort the cleaned highlights by date in descending order
      const sortedHighlights = cleaned.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

      setProfileHighlights(sortedHighlights);
      setProfileLastEvaluatedKey(data.data.LastEvaluatedKey || null);
      setFetchingHighlight(false);

      return {
        data: sortedHighlights,
        lastEvaluatedKey: data.data.LastEvaluatedKey
      };
    } catch (err) {
      setFetchingHighlight(false);
      notifyError(err.response?.data?.message || err.message);
      setError(err.response?.data?.message || err.message);
    }
  };

  useEffect(() => {
    if (userId) {
      fetchHighlightsByUserId(userId, requestPayload);
    }
  }, [userId, requestPayload]);

  return {
    profileHighlights,
    profileLastEvaluatedKey,
    fetchingHighlight,
    fetchingMore,
    setFetchingMore,
    fetchHighlightsByUserId,
    error
  };
};

export default useFetchHighlights;
