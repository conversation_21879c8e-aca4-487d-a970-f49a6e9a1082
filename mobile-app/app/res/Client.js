import axios from 'axios';
import { getUserData } from '../constants/misc';
import { resetNavigation } from '../constants/navigation';

// Base URL for the API
//export const baseURL = "https://rlml11w5wi.execute-api.us-east-1.amazonaws.com/dev/api/";
export const baseURL = "https://prod-api.playerapp.co/prod/api/";

const Client = axios.create({ baseURL });

// Add an interceptor to modify requests
Client.interceptors.request.use(
  async (config) => {
    const contentType = config.headers?.['Content-Type'] || 'application/json';
    config.headers = {
      "Content-Type": contentType,
      Accept: "application/json",
    };

    try {
      // Retrieve access token and user data
      const { accessToken, userData } = await getUserData();
      // Add the access token to the request headers if it exists
      if (accessToken) {
        config.headers.Authorization = `Bearer ${accessToken}`;
      }
      
      // Optionally, you can also access user data here if needed
      // console.log("User data:", userData);
    } catch (error) {
      console.error('Error retrieving user data:', error);
    }

    return config;
  },
  (error) => Promise.reject(error)
);

// Add an interceptor to handle responses
Client.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle response errors
    if (error?.response?.status === 401) {
      // Navigate to the login screen on a 401 error
      resetNavigation(0, 'AuthNavigation');
    }
    return Promise.reject(error);
  }
);

export default Client;
