import * as yup from "yup";

const passwordCheck = /^[\s\S]{8,32}$/;
const seasonCheck = /[0-9][1-9]\/[0-9][1-9]/i;

export const signupSchema = yup.object().shape({
  firstName: yup.string().min(2).required("Firstname is required"),
  lastName: yup.string().min(2).required("Lastname is required"),
  userType: yup.string().required("User type is required"),
  email: yup
    .string()
    .email("Please enter a valid email")
    .required("Email is required"),
  password: yup
    .string()
    .min(8)
    .matches(passwordCheck, {
      message: "Must contain at least one number and alphabet"
    })
    .required("Password field is empty"),
  terms: yup.boolean().oneOf([true], "You must accept the terms and conditions")
});

export const forgotPassword2Schema = yup.object().shape({
  verificationCode: yup
    .number()
    .integer("Must be a number")
    .min(8)
    .required("Code cannot be empty"),
  password: yup.string().min(8).matches(passwordCheck, {
    message: "Must contain at least one number and alphabet"
  })
});

export const forgotPasswordSchema = yup.object().shape({
  email: yup
    .string()
    .email("Please enter a valid email")
    .required("Email is required")
});

export const loginSchema = yup.object().shape({
  email: yup
    .string()
    .email("Please enter a valid email")
    .required("Email is required"),
  password: yup
    .string()
    .min(8)
    .matches(passwordCheck, {
      message: "Must contain at least one number and alphabet"
    })
    .required("Password field is empty")
});

export const createProfile1 = yup.object().shape({
  userType: yup.string().required("User type is required"), // Add required
  position: yup.string().when("userType", {
    is: "PLAYER",
    then: () => yup.string().required("Position is required for players")
  }),
  location: yup.string().required("Location is required"),
  nonPlayerRole: yup.array().when("userType", {
    is: "NON_PLAYER",
    then: () =>
      yup
        .array()
        .min(1, "Select at least one role")
        .of(yup.string())
        .required("Role is required")
  }),
  club: yup.object().when("userType", {
    is: "NON_PLAYER",
    then: () => yup.object().required("Select your organisation")
  }),
  gender: yup.string().required("Please pick a gender")
});

export const editStats = yup.object().shape({
  teamName: yup.string(),
  seasonName: yup.string().matches(seasonCheck, {
    message: "Must follow the pattern YY/YY"
  }),
  goals: yup.string(),
  teamLogo: yup.string(),
  assists: yup.string(),
  appearances: yup.string(),
  cleanSheets: yup.string()
});

export const radioButtons = yup.object().shape({
  clubAdmin: yup.boolean(),
  haveAuthority: yup.boolean()
});

export const editProfile = yup.object().shape({
  firstName: yup.string(),
  lastName: yup.string(),
  clubName: yup.string(),
  location: yup.string(),
  position: yup.string(),
  bio: yup
    .string()
    .min(5, "Must be up to 5 characters")
    .max(150, "Must not exceed 150 characters")
});

export const experienceScheme = yup.object().shape({
  teamName: yup.string(),

  name: yup.string().matches(seasonCheck, {
    message: "Must follow the pattern YY/YY"
  }),
  goals: yup.string(),
  assists: yup.string(),
  apperance: yup.string(),
  cleanSheets: yup.string()
});

export const changePasswordScheme = yup.object().shape({
  oldPassword: yup.string().min(8).required("Password field is empty"),

  newPassword: yup
    .string()
    .min(8)
    .matches(passwordCheck, {
      message: "Must contain at least one number and alphabet"
    })
    .required("Password field is empty"),

  confirmPassword: yup
    .string()
    .oneOf([yup.ref("newPassword"), null], "New Passwords do not match")
    .required("Confirm password")
});

export const referenceFormSchema = yup.object().shape({
  relationship: yup.string().min(3).required("Relationship is required"),
  positionAtTime: yup
    .array()
    .min(1, "Select at least one position")
    .of(yup.string())
    .required("Your position at the time is required"),

  message: yup.string().required("Message for the reference")
});
