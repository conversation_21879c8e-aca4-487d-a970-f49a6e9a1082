// REACT //
import React from "react";

// REACT NATIVE //
import { Text, Linking, View, Clipboard } from "react-native";

// PLUGINS //
import AsyncStorage from "@react-native-async-storage/async-storage";

// OTHERS //
import store, { dispatch } from "../redux/store";
import moment from "moment";
import { notifySuccess } from "./misc";

export function linkify(text) {
  // First decode any HTML entities in the text
  const decodedText = decodeHTMLEntities(text);

  // More comprehensive URL regex to better match various URL formats
  const urlRegex =
    /(https?:\/\/|www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)/gi;

  // Preprocess text to identify URLs
  const segments = [];
  let lastIndex = 0;
  let match;

  while ((match = urlRegex.exec(decodedText)) !== null) {
    // Add text segment before the URL if it exists
    if (match.index > lastIndex) {
      segments.push({
        text: decodedText.substring(lastIndex, match.index),
        isLink: false,
      });
    }

    // Add the URL segment
    const url = match[0];
    segments.push({
      text: url,
      isLink: true,
      // Ensure URL has a protocol
      fullUrl: url.startsWith("http") ? url : `https://${url}`,
    });

    lastIndex = match.index + url.length;
  }

  // Add any remaining text after the last URL
  if (lastIndex < decodedText.length) {
    segments.push({
      text: decodedText.substring(lastIndex),
      isLink: false,
    });
  }

  // Render with optimizations to prevent reformatting delays
  return (
    <View style={{ flexDirection: "row", flexWrap: "wrap" }}>
      {segments.map((segment, index) =>
        segment.isLink ? (
          <Text
            key={`link-${index}`}
            accessible={true}
            accessibilityRole="link"
            style={{
              color: "#0000EE",
              textDecorationLine: "underline",
              paddingVertical: 2,
              flexShrink: 1,
              lineHeight: 18,
              fontFamily: "Poppins",
              fontSize: 14,
            }}
            onPress={() => {
              Linking.openURL(segment.fullUrl);
            }}
            onLongPress={() => {
              Clipboard.setString(segment.fullUrl);
              notifySuccess("URL copied to clipboard");
            }}
          >
            {segment.text}
          </Text>
        ) : (
          <Text
            key={`text-${index}`}
            style={{
              color: "#000",
              flexShrink: 1,
              lineHeight: 18,
              fontFamily: "Poppins",
              fontSize: 14,
            }}
          >
            {segment.text}
          </Text>
        )
      )}
    </View>
  );
}

// Helper function to decode HTML entities
function decodeHTMLEntities(text) {
  const entities = {
    "&amp;": "&",
    "&lt;": "<",
    "&gt;": ">",
    "&quot;": '"',
    "&#39;": "'",
    "&#47;": "/",
    "&#58;": ":",
    "&#x2F;": "/",
    // Add any other HTML entities you need to handle
  };

  return text.replace(/&(amp|lt|gt|quot|#39|#47|#58|#x2F);/g, (match) => {
    return entities[match] || match;
  });
}

export const getAllModels = () => {
  return store.getState();
};

export const getSingleModel = (model) => {
  const currentState = store.getState();
  return currentState?.[model];
};

export const reduxDispatch = () => store.dispatch;

export const getUniqueEmojis = (arr) => {
  const array = Array.isArray(arr) ? arr : [];

  const uniqueEmojisSet = new Set();

  array.forEach((item) => {
    if (item.others && item.others.emoji) {
      uniqueEmojisSet.add(item.others.emoji);
    }
  });

  const uniqueEmojisArray = Array.from(uniqueEmojisSet);
  return uniqueEmojisArray;
};

export const clearAsyncStorage = async () => {
  try {
    const rememberedEmail = await AsyncStorage.getItem("rememberedEmail"); // Retrieve the remembered email

    const allKeys = await AsyncStorage.getAllKeys();
    if (allKeys.length > 0) {
      await AsyncStorage.multiRemove(allKeys);
    }

    // Restore the remembered email after clearing
    if (rememberedEmail) {
      await AsyncStorage.setItem("rememberedEmail", rememberedEmail);
    }
  } catch (error) {
    console.error("Failed to clear AsyncStorage:", error);
  }
};

// allows the show of waiting icon if the highlight is less than 3 hours old and not processed
export const shouldShowWaitIcon = (highlight) => {
  const diffInTime = moment().diff(moment(highlight.createdAt), "hours");
  if (
    (!highlight.queueProcessed && diffInTime >= 3) ||
    highlight.queueProcessed
  ) {
    return false;
  }
  return true;
};

export const VIDEO_STATUS = {
  PROCESSING: "PROCESSING",
  SUCCESSFUL: "SUCCESSFUL",
  FAILED: "FAILED",
};
