import { getFocusedRouteNameFromRoute } from "@react-navigation/native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import Toast from "react-native-toast-message";
import Clipboard from "@react-native-clipboard/clipboard";
import { WEB_URL } from "../../Config";
import moment from "moment";
import * as yup from "yup";
import { useFormik } from "formik";
import { dispatch } from "../redux/store";
import { getStatusBarHeight } from "react-native-status-bar-height";
import Config from "react-native-config";

const statusBarHeight = getStatusBarHeight();
export const liveLikeClientId = Config.CLIENTID;

export const uploadedStreamsBaseUrl = Config.S3_VIDEO_OUTPUT_BASE_URL;
// export const uploadedStreamsBaseUrl =
// "https://playerapp-streaming-assets-dev.global.ssl.fastly.net";

export const convertSecondsToMinutes = (seconds) => {
  if (!seconds || seconds <= 0) {
    return "--:--"; // Return placeholder when duration is missing or invalid
  }

  const wholeSeconds = Math.floor(seconds); // Ignore the fractional part
  const minutes = Math.floor(wholeSeconds / 60); // Get the whole minutes part
  const remainingSeconds = wholeSeconds % 60; // Get the remaining seconds
  const formattedMinutes = `${minutes}:${remainingSeconds
    .toString()
    .padStart(2, "0")}`; // Format as MM:SS
  return formattedMinutes;
};

export const nonPlayers = [
  {
    id: 1,
    name: "Agent",
  },
  {
    id: 2,
    name: "Coach",
  },
  {
    id: 3,
    name: "Scout",
  },
  {
    id: 4,
    name: "Academy Manager / Director",
  },
  {
    id: 5,
    name: "Chief Scout",
  },
  {
    id: 6,
    name: "Sporting Director",
  },
  {
    id: 7,
    name: "Player Care",
},
];

export const PlayerPostion = {
  GOAL_KEEPERS: "Goalkeeper",
  CENTRE_BACK: "Centre Back",
  LEFT_BACK: "Left Back",
  RIGHT_BACK: "Right Back",
  WING_BACK: "Wing Back",
  DEFENSIVE_MIDFIELD: "Defensive Midfield",
  CENTRAL_MIDFIELD: "Central Midfield",
  ATTACKING_MIDFIELD: "Attacking Midfield",
  WINGER: "Winger",
  FORWARD: "Forward",
  STRIKER: "Striker",
};

export const PreferredFoot = {
  RIGHT_FOOTED: "Right Footed",
  LEFT_FOOTED: "Left Footed",
  BOTH_FOOTED: "Both Footed",
};

export const readablePosition = (position) => {
  if (PlayerPostion[position]) {
    return PlayerPostion[position];
  }
  return position?.split("_").join(" ").toLowerCase();
};

export const readableFoot = (position) => {
  if (PreferredFoot[position]) {
    return PreferredFoot[position];
  }
  return position?.split("_").join(" ").toLowerCase();
};

export const trim150 = (str) => {
  return str?.length > 150 ? str.substring(0, 150) + "..." : str;
};

export const getTabBarStyle = (route) => {
  const hiddenTabRouteList = [
    "FeedDetail",
    "ShareFeed",
    "UploadHighlights",
    "Security",
    "EditProfile",
    "Account",
    "Settings",
    "BlockedUsers",
    "AnnouncementPage",
    "MessageList",
    "Message",
    "WebViewScreen",
    "PrivacySecurity",
  ];

  const currentRoute = getFocusedRouteNameFromRoute(route) || "";

  return hiddenTabRouteList.includes(currentRoute) ? "none" : "flex";
};

export const getUserData = async () => {
  try {
    // Retrieve access token and user data from AsyncStorage
    const accessToken = await AsyncStorage.getItem("accessToken");
    const userData = await AsyncStorage.getItem("userData");

    // Parse user data from JSON format
    const parsedUserData = JSON.parse(userData);

    return { accessToken, userData: parsedUserData };
  } catch (error) {
    console.error("Error retrieving user data:", error);
    throw error;
  }
};

export const getVideoUrl = (highlight) => {
  if (highlight.id) {
    if (highlight?.streamUrl) {
      return `${highlight?.streamUrl?.baseUrl}/${
        highlight?.streamUrl?.key?.split("--")[1]?.split("-")[0]
      }/index.m3u8`;
    } else {
      return highlight.id ? highlight?.url : highlight?.assetUrl;
    }
  }
  return "";
};

export const getVideoPosterUrl = (highlight) => {
  return (
    highlight?.streamUrl?.baseUrl +
    `/${highlight?.streamUrl?.key?.split("--")[1]}/thumbnail.png`
  );
};

export const removeDuplicates = (array, property) => {
  const uniqueIds = [];

  const unique = array.filter((element) => {
    const isDuplicate = uniqueIds.includes(element[property]);

    if (!isDuplicate) {
      uniqueIds.push(element[property]);

      return true;
    }

    return false;
  });

  return unique;
};

export const trimString = (text) => {
  return text.trim();
};

export const formatDateToWords = (dateString) => {
  function getDaySuffix(day) {
    if (day >= 11 && day <= 13) {
      return "TH";
    }
    switch (day % 10) {
      case 1:
        return "ST";
      case 2:
        return "ND";
      case 3:
        return "RD";
      default:
        return "TH";
    }
  }
  const months = [
    "JANUARY",
    "FEBRUARY",
    "MARCH",
    "APRIL",
    "MAY",
    "JUNE",
    "JULY",
    "AUGUST",
    "SEPTEMBER",
    "OCTOBER",
    "NOVEMBER",
    "DECEMBER",
  ];

  const dateParts = dateString.split("/");
  const day = parseInt(dateParts[0], 10);
  const month = parseInt(dateParts[1], 10);
  const year = parseInt(dateParts[2], 10);

  const formattedDate = `${day}${getDaySuffix(day)} ${
    months[month - 1]
  } ${year}`;

  return formattedDate;
};

export const calculateAge = (dateOfBirth) => {
  if (!dateOfBirth) {
    return "";
  }

  // Parse the date using a wide range of formats
  const parsedDate = moment(
    dateOfBirth,
    [
      "DD/MM/YYYY",
      "D/M/YYYY",
      "DD.MM.YYYY",
      "D.M.YYYY",
      "DD. MM. YYYY",
      "D. M. YYYY",
      "YYYY-MM-DD",
      "DD-MM-YYYY",
      "YYYY/MM/DD", // Added ISO format with slashes
    ],
    true
  ); // strict mode

  // Check if the date is valid
  if (!parsedDate.isValid()) {
    console.warn("Invalid date format:", dateOfBirth);
    return "";
  }

  const age = Math.floor(moment().diff(parsedDate, "years", true));
  return age < 0 ? 0 : age;
};

export const cleanDate = (dateOfBirth = new Date()) => {
  // const dob = dateOfBirth || "";
  // const splitDate = dob.split("/");
  // const birthdateTimeStamp = new Date(
  //   splitDate[2],
  //   splitDate[1] - 1,
  //   splitDate[0]
  // );

  return moment(dateOfBirth, [
    "DD/MM/YYYY",
    "D/M/YYYY",
    "DD.MM.YYYY",
    "D.M.YYYY",
    "DD. MM. YYYY",
    "D. M. YYYY",
    "YYYY-MM-DD",
    "DD-MM-YYYY",
  ]).format("YYYY-MM-DD");
};

export const notifySuccess = (message) => {
  Toast.show({
    type: "success",
    position: "top",
    text1: message,
    visibilityTime: 2000,
    autoHide: true,
    topOffset: statusBarHeight + 10,
  });
};

export const notifyError = (message) => {
  Toast.show({
    type: "error",
    position: "top",
    text1: message,
    visibilityTime: 2000,
    autoHide: true,
    topOffset: statusBarHeight + 10,
  });
};

export const notifyWarn = (message) => {
  Toast.show({
    type: "warning",
    position: "top",
    text1: message,
    visibilityTime: 2000,
    autoHide: true,
    topOffset: statusBarHeight + 10,
  });
};

export const handleCopyToClipBoard = (text) => {
  try {
    Clipboard.setString(text);
    notifySuccess("URL has been copied to your clipboard");
  } catch (error) {
    console.error("Error copying to clipboard:", error);
  }
};

export const searchArrayOrMakeCallToAPI = async ({
  array = [],
  searchTerm = "",
  makeSearchCall,
}) => {
  // If there's no search term, return an empty array
  if (!Boolean(searchTerm)) {
    return [];
  }

  // Perform local filtering if the array is available
  const searchResults = array?.filter((item) => {
    for (const key in item) {
      if (
        typeof item[key] === "string" &&
        item[key]?.toLowerCase()?.includes(searchTerm?.toLowerCase())
      ) {
        return true;
      }
    }
    return false;
  });

  if (searchResults?.length > 0) {
    // If search results are found locally, return them
    return searchResults;
  } else {
    // If no local results, make API calls
    try {
      // Ensure makeSearchCall is an array of functions
      if (makeSearchCall && Array.isArray(makeSearchCall)) {
        const searchPromises = makeSearchCall?.map((funcs) =>
          funcs(searchTerm)
        );
        const searchResponses = await Promise.all(searchPromises);

        // Merge data from API responses
        const mergedData = [
          ...(searchResponses[0]?.data || []),
          ...(searchResponses[1]?.data || []),
        ]?.map((data) => {
          return {
            ...data,
            type: data?.logoUrl === undefined ? "user" : "team",
          };
        });

        return mergedData;
      } else {
        console.warn("No valid search functions provided.");
        return [];
      }
    } catch (error) {
      console.error("Error fetching data from API:", error);
      return [];
    }
  }
};

export const findMatchingObject = (array1 = [], array2 = []) => {
  for (let obj1 of array1) {
    for (let obj2 of array2) {
      if (obj1.chatroomId === obj2.chatroomId) {
        return obj1;
      }
    }
  }
  return null;
};

export const addChatClass = () => {
  let userMessageCount = 0;
  let guestMessageCount = 0;
  return (user, nextMessage) => {
    if (user) {
      guestMessageCount = 0;
      if (userMessageCount === 0 && nextMessage) {
        userMessageCount++;
        return "user-message user-first-message";
      } else if (userMessageCount === 0 && !nextMessage) {
        userMessageCount++;
        return "user-message user-lone-message";
      }
      if (userMessageCount > 0 && nextMessage) {
        userMessageCount++;
        return "user-message user-middle-message";
      } else {
        userMessageCount = 0;
        return "user-message user-last-message";
      }
    } else {
      userMessageCount = 0;
      if (guestMessageCount === 0 && nextMessage) {
        guestMessageCount++;
        return "guest-message guest-first-message";
      } else if (guestMessageCount === 0 && !nextMessage) {
        guestMessageCount++;
        return "guest-message guest-lone-message";
      }
      if (guestMessageCount > 0 && nextMessage) {
        guestMessageCount++;
        return "guest-message guest-middle-message";
      } else {
        guestMessageCount = 0;
        return "guest-message guest-last-message";
      }
    }
  };
};

export const getNextMessage = (id, ind, chatLen, arr) => {
  const index = ind + 1;

  if (index >= chatLen) {
    return false;
  }
  return id === arr[index]["sender_id"];
};

const pluralize = (word, count) => {
  return count === 1 ? word : `${word}s`;
};

export const getTimeAgo = (timestamp) => {
  const currentTime = new Date();
  const targetTime = new Date(timestamp);

  const timeDiff = currentTime.getTime() - targetTime.getTime();
  const seconds = Math.floor(timeDiff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  const months = Math.floor(days / 30);
  const years = Math.floor(days / 365);

  if (seconds < 60) {
    return "just now";
  } else if (minutes < 60) {
    return `${minutes} ${pluralize("minute", minutes)} ago`;
  } else if (hours < 24) {
    return `${hours} ${pluralize("hour", hours)} ago`;
  } else if (days < 30) {
    return `${days} ${pluralize("day", days)} ago`;
  } else if (days < 365) {
    return `${months} ${pluralize("month", months)} ago`;
  } else {
    return `${years} ${pluralize("year", years)} ago`;
  }
};

export function validateURL(textval) {  
  var urlregex = /^(https?|ftp):\/\/[^\s/$.?#].[^\s]*$/i  
  return urlregex.test(textval);
}

export const removeEmpty = (obj) => {
  return Object.fromEntries(
    Object.entries(obj).filter(
      ([_, v]) => v != "" || v != null || v != undefined
    )
  );
};

export const referenceFormSchema = yup.object().shape({
  relationship: yup.string().min(3).required("Relationship is required"),
  positionAtTime: yup
    .array()
    .min(1, "Select at least one position")
    .of(yup.string())
    .required("Your position at the time is required"),

  message: yup.string().required("Message for the reference"),
});

export const formatDate = (dateString) => {
  const date = new Date(dateString);
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(today.getDate() - 1);

  const isToday =
    date.getFullYear() === today.getFullYear() &&
    date.getMonth() === today.getMonth() &&
    date.getDate() === today.getDate();

  const isYesterday =
    date.getFullYear() === yesterday.getFullYear() &&
    date.getMonth() === yesterday.getMonth() &&
    date.getDate() === yesterday.getDate();

  if (isToday) return "Today";
  if (isYesterday) return "Yesterday";

  const options = { day: "numeric", month: "short" };
  if (date.getFullYear() !== today.getFullYear()) {
    options.year = "numeric";
  }
  return date.toLocaleDateString("en-US", options); // Example: "26 Jun" or "26 Jun 2023"
};

export const formatTime = (timestamp) => {
  const date = new Date(timestamp);
  const hours = date.getHours();
  const minutes = date.getMinutes();
  const ampm = hours >= 12 ? "PM" : "AM";
  const formattedHours = hours % 12 || 12; // Convert to 12-hour format
  const formattedMinutes = minutes < 10 ? `0${minutes}` : minutes;

  return `${formattedHours}:${formattedMinutes} ${ampm}`;
};

// Function to shuffle an array
export const shuffleArray = (array) => {
  console.debug("Shuffling Feed");
  const shuffledArray = [...array];
  for (let i = shuffledArray.length - 1; i > 0; i--) {
    const randomIndex = Math.floor(Math.random() * (i + 1));
    // Swap elements
    [shuffledArray[i], shuffledArray[randomIndex]] = [
      shuffledArray[randomIndex],
      shuffledArray[i],
    ];
  }
  return shuffledArray;
};

// allows the show of waiting icon if the highlight is less than 1 hour old and not processed
export const getVideoStatus = (
  highlight,
  unitOfTime = "hours",
  waitTime = 1
) => {
  const diffInTime = moment().diff(moment(highlight.createdAt), unitOfTime);
  if (highlight.videoStatus === "PROCESSING") {
    return "PROCESSING";
  } else if (
    (!highlight.queueProcessed && diffInTime >= waitTime * 2) ||
    highlight.queueProcessed ||
    highlight.videoStatus === "SUCCESSFUL"
  ) {
    return "SUCCESSFUL";
  } else if (highlight.videoStatus === "FAILED") {
    return "FAILED";
  }
  return "PROCESSING";
};

export const normalizeText = (text) => {
  return text
    ?.normalize("NFD") 
    .replace(/[\u0300-\u036f]/g, "") 
    .toLowerCase(); 
};
