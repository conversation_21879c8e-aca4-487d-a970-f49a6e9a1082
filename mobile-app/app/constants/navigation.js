// External dependencies
import {
    CommonActions,
    createNavigationContainerRef,
  } from "@react-navigation/native";
  
  export const _navigator = createNavigationContainerRef();
  
  export const navigate = (routeName, params = {}) => {
    if (_navigator.current && _navigator.current.isReady()) {
      _navigator.current.navigate(routeName, params);
    } else {
      // wait for navigationRef to be ready
      setTimeout(() => {
        navigate(routeName, params);
      }, 100);
    }
  };
  
  export const resetNavigation = (index = 0, routeName, params = {}) => {
    if (_navigator.isReady()) {
      const resetAction = CommonActions.reset({
        index,
        routes: [
          {
            name: routeName,
            params,
          },
        ],
      });
      _navigator.dispatch(resetAction);
    }
  };
  
  export const navigateBack = () => {
    if (_navigator.isReady()) {
      _navigator.goBack();
    }
  };
  
  export const getNavigator = () => {
    return _navigator;
  };
  