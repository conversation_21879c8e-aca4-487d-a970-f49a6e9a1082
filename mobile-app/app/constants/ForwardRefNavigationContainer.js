import { NavigationContainer } from "@react-navigation/native";
import React, { forwardRef, useRef } from "react";
import * as Linking from "expo-linking";
import AnalyticsService from "../services/analytics";

const ForwardRefNavigationContainer = forwardRef((props, ref) => {
  const routeNameRef = useRef(null);

  const linking = {
    prefixes: [Linking.createURL("/"), "https://playerapp.co", "playerapp://"],
    config: {
      initialRouteName: "BottomStack",
      screens: {
        BottomStack: {
          initialRouteName: "HomeScreenStack",
          screens: {
            HomeScreenStack: {
              initialRouteName: "Feed",
              screens: {
                Feed: "Feed",
                AnnouncementPage: {
                  path: "user/announcement",
                  parse: {
                    announcementId: (id) => id,
                  },
                },
                FeedDetail: {
                  path: "user/comment",
                  parse: {
                    highlightId: (highlightId) => highlightId,
                    userId: (userId) => userId,
                    // highlightId: (_path, queryParams) => {
                    //   console.log('Parsing highlightId:', queryParams?.highlightId);
                    //   return queryParams?.highlightId;
                    // },
                    // userId: (_path, queryParams) => {
                    //   console.log('Parsing userId:', queryParams?.userId);
                    //   return queryParams?.userId;
                    // }
                  },
                },
              },
            },
            MyProfileScreenStack: {
              screens: {
                MyProfile: {
                  path: "profile",
                  parse: {
                    id: (id) => id,
                  },
                },
              },
            },
          },
        },
      },
    },
    getInitialURL: async () => {
      const url = await Linking.getInitialURL();
      if (url) {
        console.debug("[Router] Initial URL in linking config:", url);
      }
      return url;
    },
    subscribe: (listener) => {
      const subscription = Linking.addEventListener("url", ({ url }) => {
        console.debug("[Router] URL in subscription:", url);
        listener(url);
      });
      return () => subscription.remove();
    },
  };

  const onStateChange = async () => {
    const previousRouteName = routeNameRef.current;
    const currentRouteName = ref.current?.getCurrentRoute()?.name;

    if (previousRouteName !== currentRouteName) {
      await AnalyticsService.logScreen(currentRouteName);
    }

    routeNameRef.current = currentRouteName;
  };

  return (
    <NavigationContainer
      ref={ref}
      linking={linking}
      onStateChange={onStateChange}
      {...props}
    >
      {props.children}
    </NavigationContainer>
  );
});

export default ForwardRefNavigationContainer;
