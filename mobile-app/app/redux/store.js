/* eslint-disable import/no-cycle */
import loadingPlugin from "@rematch/loading";
import selectPlugin from "@rematch/select";
import { init } from "@rematch/core"

import * as models from "./models/index";
import { persistStorage } from "./persist";

// Initialize the Redux store using Rematch
const store = init({
  models, 
  plugins: [
    loadingPlugin(), 
    selectPlugin(),  
    persistStorage,  
  ],
});


export const { dispatch, select } = store;


export default store;
