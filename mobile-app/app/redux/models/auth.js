/* eslint-disable import/no-cycle */
import { createModel } from "@rematch/core";
import {
  liveLikeClientId,
  notify<PERSON>rror,
  notifySuc<PERSON>,
} from "../../constants/misc";
import { navigate, resetNavigation } from "../../constants/navigation";
import AuthApi from "../../services/AuthApi";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { clearAsyncStorage } from "../../constants/Constant";
import analytics from "../../services/analytics";
const LiveLike = require("@livelike/javascript");
import { dispatch as globalDispatch } from "../store";
import { initializeLiveLike } from "../../utils/LiveLike";
import { createReducerActions } from "../reducer";

const initialState = {
  confirmSignUpModal: { visible: false, email: "" },
  forgotPasswordModal: { visible: false },
  authUser: {
    token: null,
    userInfo: {},
    authenticated: false,
    refreshToken: null,
    expiresIn: null,
  },
};

export const auth = createModel()({
  state: initialState,
  reducers: createReducerActions(initialState),
  effects: (dispatch) => ({
    loadAuthState: async () => {
      try {
        const savedState = await AsyncStorage.getItem("authState");
        if (savedState) {
          const parsedState = JSON.parse(savedState);
          if (
            parsedState.authUser?.token &&
            parsedState.authUser?.userInfo?.id
          ) {
            dispatch.auth.setState(parsedState);
            return true;
          }
        }
        return false;
      } catch (error) {
        console.error("Error loading auth state:", error);
        return false;
      }
    },

    // Add a new effect to check auth status
    checkAuthStatus: () => {
      const state = getSingleModel("auth");
      return state?.authUser?.authenticated && state?.authUser?.token;
    },

    userLogin: async (payload) => {
      dispatch.auth.setError(null);
      const { navigation, values, from } = payload;

      try {
        await analytics.verifyEvent("login_attempt", { email: values.email });
        const { data } = await AuthApi.login(values);

        const receivedData = data;
        const userId = receivedData?.data?.id;

        if (!receivedData || receivedData.status === 0 || !userId) {
          notifyError(receivedData.message);
          return await dispatch.auth.logout();
        }

        await analytics.logLogin("email", values.email, userId);

        const authState = {
          authUser: {
            token: receivedData?.data.tokens.access,
            userInfo: data.data,
            authenticated: true,
            refreshToken: receivedData?.data.tokens.refresh,
            expiresIn: receivedData?.data.tokens?.expiresIn,
          },
        };

        await AsyncStorage.setItem("authState", JSON.stringify(authState));
        await AsyncStorage.setItem(
          "accessToken",
          receivedData?.data.tokens.access
        );

        dispatch.auth.setState(authState);

        await initializeLiveLike();

        dispatch.club.getAllClubs();
        dispatch.team.getAllTeams();

        if (receivedData?.data?.userType !== "NON_PLAYER") {
          navigation.reset({
            index: 0,
            routes: [{ name: from }],
          });
        } else {
          const playerVerificationId = await AsyncStorage.getItem(
            "verificationPlayerId"
          );

          if (Boolean(playerVerificationId)) {
            navigation.navigate("MyProfileScreenStack", {
              screen: "MyProfile",
              params: { id: playerVerificationId, verify: true },
            });
          } else {
            navigation.reset({
              index: 0,
              routes: [{ name: "BottomStack" }],
            });
          }
        }
      } catch (err) {
        console.error("Error during login:", err);

        await analytics.verifyEvent("login_error", {
          error: err.message || "Unknown error",
        });

        if (err.response) {
          if (err.response.data.data?.name === "UserNotConfirmedException") {
            notifyError("Please confirm your Account");
            dispatch.auth.confirmSignUpModalBox({
              visible: true,
              email: values.email,
            });
          } else {
            notifyError(err.response.data.message);
          }
        }
      }
    },

    async refreshToken(_, rootState) {
      try {
        const authUser = rootState.auth.authUser;
        if (!authUser?.refreshToken) {
          await dispatch.auth.logout();
          return null;
        }

        const { data } = await AuthApi.refreshToken(authUser.refreshToken);
        if (data?.data?.access) {
          dispatch.auth.setState({
            authUser: {
              ...authUser,
              token: data.data.access,
              expiresIn: data.data.expiresIn,
            },
          });
          return data.data.access;
        }
        return null;
      } catch (error) {
        await dispatch.auth.logout();
        return null;
      }
    },

    userSignup: async (payload) => {
      dispatch.auth.setError(false);
      try {
        const { playerId, ...payloadData } = payload.user;
        const { data } = await AuthApi.signup(payload.user);

        if (data.status !== 1) {
          notifyError(data.message);
        }
        if (data.status === 1) {
          if (playerId) {
            await AsyncStorage.setItem("verificationPlayerId", playerId);
          }
          // payload.navigate("Login");
          notifySuccess(data.message);
        }
      } catch (err) {
        notifyError(err?.response?.data?.message || err.message);
      }
    },

    confirmSignUpModalBox: async (payload) => {
      dispatch.auth.setError(null);
      dispatch.auth.setState({
        confirmSignUpModal: { visible: payload.visible, email: payload.email },
      });
    },

    confirmSignUp: async (payload) => {
      dispatch.auth.setError(null);
      const { email, code, navigateTo } = payload;
      const confirmDetails = {
        email,
        verificationCode: code,
      };
      try {
        const { data } = await AuthApi.verifyAccount(confirmDetails);
        const { message, status } = data;
        if (status !== 1) {
          notifyError(message);
        }

        if (status === 1) {
          notifySuccess(message);
          dispatch.auth.confirmSignUpModalBox({ visible: false, email: null });
          navigateTo("Login");
        }
      } catch (error) {
        notifyError(error);
      }
    },

    resendVerifyCode: async (payload) => {
      dispatch.auth.setError(null);
      try {
        const { data } = await AuthApi.resendVerifyAccount(payload);
        const { status, message } = data;
        if (status === 0) {
          notifyError(message);
        }
        if (status === 1) {
          notifySuccess("Confirmation Code Sent");
        }
      } catch (err) {
        notifyError(
          err?.response?.data?.message || err.message || "An error occurred"
        );
      }
    },

    forgotPasswordModalBox: async (payload) => {
      dispatch.auth.setError(null);
      dispatch.auth.setState({
        forgotPasswordModal: { visible: payload.visible },
      });
    },

    forgotPassword: async (payload) => {
      dispatch.auth.setError(null);
      try {
        const { data } = await AuthApi.forgotPassword(payload);
        const { status, message } = data;
        if (status === 0) {
          notifyError(message);
        }
        if (status === 1) {
          notifySuccess(message);
        }
        return status;
      } catch (err) {
        notifyError(err?.response?.data?.message || err.message);
      }
    },

    resetPassword: async (payload) => {
      dispatch.auth.setError(null);
      try {
        const { data } = await AuthApi.resetPassword(payload);
        const { status, message } = data;
        if (status === 0) {
          notifyError(message);
        }
        if (status === 1) {
          notifySuccess(message);
        }
        return status;
      } catch (err) {
        notifyError(err?.response?.data?.message || err.message);
      }
    },

    changePassword: async (payload) => {
      dispatch.auth.setError(null);
      try {
        const { data } = await AuthApi.changePassword(payload);
        const { status, message } = data;
        if (status === 0) {
          notifyError(message);
        }
        if (status === 1) {
          notifySuccess(message);
        }
        return status;
      } catch (err) {
        notifyError(err?.response?.data?.message || err.message);
      }
    },

    async logout(_, rootState) {
      try {
        const userId = rootState.auth.authUser?.userInfo?.id || "unknown_user";

        LiveLike._$$.accessToken = "";
        LiveLike.userProfile.access_token = "";

        await analytics.logLogout(
          rootState.auth.authUser?.userInfo?.id || "unknown_user"
        );

        globalDispatch.announcement.reset();
        globalDispatch.chat.reset();
        globalDispatch.club.reset();
        globalDispatch.feed.reset();
        globalDispatch.team.reset();
        globalDispatch.user.reset();
        globalDispatch.volume.reset();
        globalDispatch.auth.reset();

        await clearAsyncStorage();
      } catch (error) {
        console.error("[Auth] Error logging out user:", error);
        notifyError(error.message);
      }
    },

    async delAccount(payload) {
      try {
        const { data } = await AuthApi.deleteAccount(payload);
        notifySuccess(data.message);
        return data.message;
      } catch (error) {
        notifyError(error.response.data.message || error.message);
      }
    },
  }),
});
