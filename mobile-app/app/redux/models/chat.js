import { createModel } from "@rematch/core";
import { createReducerActions } from "../reducer";
import Chat<PERSON><PERSON> from "../../services/ChatApi";
import { notifyError } from "../../constants/misc";
import UserApi from "../../services/UserApi";
import { mergeChatMessages } from "../../utils/chat";

function sortChats(chats) {
  return chats.sort(
    (a, b) =>
      new Date(b.lastMessageSentAt || b.createdAt || 0).getTime() -
      new Date(a.lastMessageSentAt || a.createdAt || 0).getTime()
  );
}

const initialState = {
  chats: [],
  unreadMessageCount: 0,
};
export const chat = createModel()({
  state: initialState,
  reducers: createReducerActions(initialState),
  effects: (dispatch, rootState) => ({
    async getChats() {
      dispatch.chat.setError(null);
      try {
        const { data } = await ChatApi.getChats();
        dispatch.chat.setState((prevState) => {
          return {
            chats: data.chats.map((chat) => {
              const existingChat = prevState.chats.find(
                (existingChat) => existingChat.chatroomId === chat.chatroomId
              );
              if (existingChat?.messages) {
                return {
                  ...chat,
                  messages: existingChat.messages,
                };
              } else {
                return chat;
              }
            }),
            unreadMessageCount: data.unreadMessageCount,
          };
        });
      } catch (error) {
        notifyError(error.response.data.message || error.message);
      }
    },

    async getChat({ chatroomId }) {
      dispatch.chat.setError(null);
      try {
        const { data } = await ChatApi.getChat(chatroomId);

        dispatch.chat.setState((prevState) => {
          const existingChat = prevState.chats.find(
            (chat) => chat.chatroomId === chatroomId
          );

          const updatedChats = existingChat
            ? prevState.chats.map((chat) => {
                if (chat.chatroomId === chatroomId) {
                  const mergedMessages = mergeChatMessages(
                    chat.messages,
                    data.messages
                  );
                  return {
                    ...chat,
                    lastMessage: mergedMessages.length
                      ? mergedMessages[mergedMessages.length - 1].message
                      : null,
                    lastMessageSentAt: mergedMessages.length
                      ? mergedMessages[mergedMessages.length - 1].created_at
                      : null,
                    messages: mergedMessages,
                    unreadMessageCount: 0,
                  };
                }

                return chat;
              })
            : [
                ...prevState.chats,
                {
                  chatroomId: chatroomId,
                  lastMessage: data.messages.length
                    ? data.messages[data.messages.length - 1].message
                    : null,
                  lastMessageSentAt: data.messages.length
                    ? data.messages[data.messages.length - 1].created_at
                    : null,
                  messages: data.messages,
                  unreadMessageCount: 0,
                },
              ];

          const sortedChats = sortChats(updatedChats);

          return {
            unreadMessageCount: sortedChats.reduce(
              (acc, chat) => acc + (chat.unreadMessageCount || 0),
              0
            ),
            chats: sortedChats,
          };
        });
      } catch (error) {
        notifyError(error.response.data.message || error.message);
      }
    },

    async getChatByRecipient({ recipientId, temporaryChatroomId }) {
      dispatch.chat.setError(null);
      try {
        const { data } = await ChatApi.getChatByRecipient(recipientId);

        dispatch.chat.setState((prevState) => {
          const existingChat = prevState.chats.find((chat) =>
            data?.chatroomId
              ? chat.chatroomId === data.chatroomId
              : chat.temporaryChatroomId === temporaryChatroomId
          );

          const updatedChats = existingChat
            ? prevState.chats.map((chat) =>
                (
                  data?.chatroomId
                    ? chat.chatroomId === data.chatroomId
                    : chat.temporaryChatroomId === temporaryChatroomId
                )
                  ? {
                      ...chat,
                      ...(data?.chatroomId && {
                        chatroomId: data.chatroomId,
                      }),
                      lastMessage: data?.messages?.length
                        ? data.messages[data.messages.length - 1].message
                        : null,
                      lastMessageSentAt: data?.messages?.length
                        ? data.messages[data.messages.length - 1].created_at
                        : null,
                      messages: data?.messages || [],
                      unreadMessageCount: 0,
                    }
                  : chat
              )
            : [
                ...prevState.chats,
                {
                  ...(data?.chatroomId
                    ? { chatroomId: data.chatroomId }
                    : { temporaryChatroomId }),
                  lastMessage: data?.messages?.length
                    ? data.messages[data.messages.length - 1].message
                    : null,
                  lastMessageSentAt: data?.messages?.length
                    ? data.messages[data.messages.length - 1].created_at
                    : null,
                  messages: data?.messages || [],
                  unreadMessageCount: 0,
                },
              ];

          const sortedChats = sortChats(updatedChats);

          return {
            unreadMessageCount: sortedChats.reduce(
              (acc, chat) => acc + (chat.unreadMessageCount || 0),
              0
            ),
            chats: sortedChats,
          };
        });

        return { chatroomId: data.chatroomId };
      } catch (error) {
        console.error("[State] getChatByRecipient error", error);
      }
    },

    async getOtherUser({ chatroomId, temporaryChatroomId, recipientId }) {
      dispatch.chat.setError(null);
      try {
        const { data } = await UserApi.getUserProfile(recipientId);

        dispatch.chat.setState((prevState) => {
          const existingChat = prevState.chats.find((chat) =>
            chatroomId
              ? chat.chatroomId === chatroomId
              : chat.temporaryChatroomId === temporaryChatroomId
          );
          if (existingChat) {
            return {
              ...prevState,
              chats: prevState.chats.map((chat) =>
                (
                  chatroomId
                    ? chat.chatroomId === chatroomId
                    : chat.temporaryChatroomId === temporaryChatroomId
                )
                  ? { ...chat, otherUser: data.data }
                  : chat
              ),
            };
          } else {
            return {
              ...prevState,
              chats: [
                {
                  chatroomId,
                  temporaryChatroomId,
                  otherUser: data.data,
                },
                ...prevState.chats,
              ],
            };
          }
        });
      } catch (error) {
        notifyError(error.response?.data.message || error.message);
      }
    },

    async createChat({ recipientId, temporaryChatroomId }) {
      dispatch.chat.setError(null);
      try {
        const { data } = await ChatApi.createChat(recipientId);
        dispatch.chat.setState((prevState) => {
          const existingChat = prevState.chats.find(
            (chat) => chat.temporaryChatroomId === temporaryChatroomId
          );
          if (existingChat) {
            return {
              ...prevState,
              chats: prevState.chats.map((chat) =>
                chat.temporaryChatroomId === temporaryChatroomId
                  ? {
                      ...chat,
                      chatroomId: data.chatroomId,
                      temporaryChatroomId: null,
                    }
                  : chat
              ),
            };
          } else {
            return {
              ...prevState,
              chats: [
                {
                  chatroomId: data.chatroomId,
                  temporaryChatroomId: null,
                },
                ...prevState.chats,
              ],
            };
          }
        });

        return data.chatroomId;
      } catch (error) {
        notifyError(error.response?.data.message || error.message);
      }
    },

    createTemporaryMessage({
      chatroomId,
      temporaryChatroomId,
      temporaryMessageId,
      userInfo,
      data,
    }) {
      dispatch.chat.setState((prevState) => {
        return {
          ...prevState,
          chats: prevState.chats.map((chat) =>
            (
              chatroomId
                ? chat.chatroomId === chatroomId
                : chat.temporaryChatroomId === temporaryChatroomId
            )
              ? {
                  ...chat,
                  messages: [
                    ...(chat.messages || []),
                    {
                      id: temporaryMessageId,
                      created_at: new Date().toISOString(),
                      sender_id: userInfo.liveLikeProfileId,
                      sender_image_url: userInfo.photoUrl,
                      ...data,
                    },
                  ],
                }
              : chat
          ),
        };
      });
    },

    async createMessage({
      chatroomId,
      temporaryChatroomId,
      temporaryMessageId,
      data,
    }) {
      dispatch.chat.setError(null);
      try {
        const { data: createdMessage } = await ChatApi.createMessage(
          chatroomId,
          data
        );

        dispatch.chat.setState((prevState) => {
          const updatedChats = prevState.chats.map((chat) =>
            (
              chatroomId
                ? chat.chatroomId === chatroomId
                : chat.temporaryChatroomId === temporaryChatroomId
            )
              ? {
                  ...chat,
                  lastMessage: createdMessage.message,
                  lastMessageSentAt: createdMessage.created_at,
                  messages: chat.messages.length
                    ? chat.messages.map((message) =>
                        message.id === temporaryMessageId
                          ? createdMessage
                          : message
                      )
                    : [createdMessage],
                  unreadMessageCount: 0,
                }
              : chat
          );

          const sortedChats = sortChats(updatedChats);

          return {
            unreadMessageCount: sortedChats.reduce(
              (acc, chat) => acc + (chat.unreadMessageCount || 0),
              0
            ),
            chats: sortedChats,
          };
        });
      } catch (error) {
        notifyError(error.response?.data.message || error.message);
        dispatch.chat.setState((prevState) => {
          return {
            ...prevState,
            chats: prevState.chats.map((chat) =>
              (
                chatroomId
                  ? chat.chatroomId === chatroomId
                  : chat.temporaryChatroomId === temporaryChatroomId
              )
                ? {
                    ...chat,
                    messages: chat.messages.length
                      ? chat.messages.filter(
                          (message) => message.id !== temporaryMessageId
                        )
                      : [],
                  }
                : chat
            ),
          };
        });
      }
    },

    async clearTemporaryChats() {
      dispatch.chat.setError(null);
      try {
        dispatch.chat.setState((prevState) => {
          return {
            ...prevState,
            chats: prevState.chats.filter((chat) => chat.chatroomId),
          };
        });
      } catch (error) {
        notifyError(error.response?.data.message || error.message);
      }
    },

    async getUnreadMessageCount() {
      dispatch.chat.setError(null);
      try {
        const { data } = await ChatApi.getUnreadMessageCount();
        dispatch.chat.setState((prevState) => ({
          ...prevState,
          unreadMessageCount: data,
        }));
      } catch (error) {
        const status = error?.response?.status;
        const message = error?.response?.data?.message || error.message;

        // Skip toast for server errors (500–599)
        if (!(status >= 500 && status < 600)) {
          notifyError(message);
        } else {
          console.error(
            `[UnreadMessageCount] Server error (${status}):`,
            message
          );
        }
      }
    },
  }),
});
