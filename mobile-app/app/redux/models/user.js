import { createModel } from "@rematch/core";
import moment from "moment";
import _groupBy from "lodash/groupBy";
import { notifyError, notifySuccess } from "../../constants/misc";
import UserApi from "../../services/UserApi";
import { createReducerActions } from "../reducer";
import { getSingleModel } from "../../constants/Constant";
import ReportApi from "../../services/ReportApi";

const now = moment().unix();

const initialState = {
  loaded: false,
  data: {
    photoURL: null, // Ensure photoURL is initialized
    experiences: [],
  },
  guest: null,
  usersByProjection: [],
  blockedUsers: [],
};

export const user = createModel()({
  state: initialState,
  reducers: createReducerActions(initialState),
  effects: (dispatch, rootState) => ({
    async updateUser(payload) {
      dispatch.user.setError(null);
      const { shouldNotShowNotification, ...others } = payload;

      try {
        const { data } = await UserApi.update(others);
        const auth = getSingleModel("auth");
        const receivedData = data;
        if (data.status === 1) {
          if (data.data.id === auth.authUser.userInfo.id) {
            await dispatch.user.getUserProfile({ id: data.data.id });
            await dispatch.auth.setState({
              authUser: {
                token: auth.authUser.token,
                userInfo: { ...auth.authUser.userInfo, ...receivedData.data },
                authenticated: auth.authUser.authenticated,
                refreshToken: auth.authUser.refreshToken,
              },
            });
          }
          if (!shouldNotShowNotification) {
            notifySuccess(receivedData.message);
          }
        } else {
          notifyError(receivedData.message);
        }

        return data.status;
      } catch (err) {
        console.error("Error updating user:", err);

        const isArray = Array.isArray(err?.response?.data?.message);
        const message = isArray
          ? err.response.data.message.join(" - ")
          : err?.response?.data?.message;

        notifyError(message || "An unexpected error occurred");
      }
    },

    async blockUser(payload) {
      dispatch.user.setError(null); // Reset any previous errors
      try {
        const { blockedUserId, reason } = payload;
        const data = { blockedUserId, reason };

        // Call API to block the user
        const response = await UserApi.blockUsers(data);

        if (response.data.status === 1) {
          const auth = getSingleModel("auth");

          if (auth?.authUser?.userInfo) {
            const updatedBlockedUsers = [
              ...(auth.authUser.userInfo.blockedUsers || []),
              blockedUserId,
            ];

            // Update blocked users list in auth without changing the structure
            await dispatch.auth.setState({
              authUser: {
                ...auth.authUser,
                userInfo: {
                  ...auth.authUser.userInfo,
                  blockedUsers: updatedBlockedUsers,
                },
              },
            });

            // Synchronize the user model without changing its structure
            await dispatch.user.setState((state) => ({
              data: {
                ...state.data,
                blockedUsers: updatedBlockedUsers,
              },
            }));
          } else {
            console.error("Auth model or user info not found");
          }

          notifySuccess(response.data.message);
          return { status: 1, message: response.data.message };
        } else {
          notifyError(response.data.message);
          return { status: 0, message: response.data.message };
        }
      } catch (error) {
        console.error("Error blocking user:", error);
        const message = Array.isArray(error?.response?.data?.message)
          ? error.response.data.message.join(" - ")
          : error?.response?.data?.message;
        notifyError(message || "An unexpected error occurred");
      }
    },
    async unblockUser(payload) {
      dispatch.user.setError(null);
      try {
        const { blockedUserId } = payload;
        const data = { blockedUserId };

        const response = await UserApi.unblockUsers(data);

        if (response.data.status === 1) {
          const auth = getSingleModel("auth");

          if (auth?.authUser?.userInfo) {
            const updatedBlockedUsers =
              auth.authUser.userInfo.blockedUsers?.filter(
                (id) => id !== blockedUserId
              ) || [];

            await dispatch.auth.setState({
              authUser: {
                ...auth.authUser,
                userInfo: {
                  ...auth.authUser.userInfo,
                  blockedUsers: updatedBlockedUsers,
                },
              },
            });

            await dispatch.user.setState((state) => ({
              data: {
                ...state.data,
                blockedUsers: updatedBlockedUsers,
              },
            }));
          } else {
            console.error("Auth model or user info not found");
          }

          notifySuccess(response.data.message);
        } else {
          notifyError(response.data.message);
        }
      } catch (error) {
        console.error("Error unblocking user:", error);
        const message = Array.isArray(error?.response?.data?.message)
          ? error.response.data.message.join(" - ")
          : error?.response?.data?.message;
        notifyError(message || "An unexpected error occurred");
      }
    },
    async submitReport(payload) {
      dispatch.user.setError(null);

      try {
        const {
          reportedUserId,
          reporterUserId,
          reporterFullName,
          reportedFullName,
          reason,
          reportedContent,
          reportedPhotoUrl,
          reporterPhotoUrl,
          reportType,
          contendId,
          metadata,
        } = payload;

        const reportData = {
          reportedUserId,
          reporterUserId,
          reporterFullName,
          reportedFullName,
          reason,
          reportedContent,
          reportedPhotoUrl,
          reporterPhotoUrl,
          reportType,
          contendId,
          metadata,
        };

        // Submit the report
        const { data } = await ReportApi.reports(reportData);

        if (data.status === 1) {
          notifySuccess("Report submitted successfully");

          // Access auth model
          const auth = getSingleModel("auth");

          if (auth?.authUser?.userInfo) {
            const existingReports = auth.authUser.userInfo.reports || [];
            const updatedReports = [...existingReports, reportData];

            // Update auth model with new report
            await dispatch.auth.setState({
              authUser: {
                ...auth.authUser,
                userInfo: {
                  ...auth.authUser.userInfo,
                  reports: updatedReports,
                },
              },
            });

            // Also update user model with new report
            await dispatch.user.setState((state) => ({
              data: {
                ...state.data,
                reports: updatedReports,
              },
            }));
          } else {
            console.error("Auth model or user info not found");
          }

          return data.status;
        } else {
          notifyError(
            data.message || "Error occurred while submitting the report"
          );
          return data.status;
        }
      } catch (error) {
        console.error("Error submitting report:", error);
        const isArray = Array.isArray(error?.response?.data?.message);
        const message = isArray
          ? error.response.data.message.join(" - ")
          : error?.response?.data?.message;

        notifyError(message || "An unexpected error occurred");
        return 0;
      }
    },

    async fetchBlockedUsersList() {
      try {
        const { data } = await UserApi.blockedUsersList();

        return data.data;
      } catch (error) {
        notifyError(error.response?.data?.message || error.message);
      }
    },
    async fetchUserDetails(payload) {
      try {
        const { data } = await UserApi.getUserProfile(payload);
        return data.data;
      } catch (error) {
        notifyError(error.response?.data?.message || error.message);
      }
    },

    async getUserProfile(payload) {
      const auth = getSingleModel("auth");
      if (!auth.authUser.authenticated) {
        return;
      }

      try {
        const { data } = await UserApi.getUserProfile(payload.id);
        if (!data?.data) {
          throw new Error("Invalid profile data received");
        }

        const sortedExperiences = data?.data?.experiences?.sort(
          (a, b) =>
            Number(b.seasonName.split("/")[1]) -
            Number(a.seasonName.split("/")[1])
        );

        const groupedData = _groupBy(
          sortedExperiences,
          (item) => item.teamName
        );
        const profileData = {
          ...data.data,
          experiences: groupedData,
          photoURL: data?.data?.photoURL || initialState.data.photoURL,
        };

        // Always update state for logged-in user
        await dispatch.user.setState({
          loaded: true,
          data: profileData,
          guest: null, // Clear guest data when viewing own profile
        });

        return { data: { data: profileData } };
      } catch (error) {
        console.error("Error in getUserProfile:", error);
        notifyError(error.response?.data?.message || "Failed to load profile");
        if (error?.response?.status === 401) {
          await dispatch.auth.logout();
        }
        throw error; // Propagate error to handle in component
      }
    },

    async getUserProfileForGuest(userId, rootState) {
      try {
        const auth = getSingleModel("auth");
        const isAuthenticated = auth?.authUser?.authenticated;

        // If not authenticated, try to refresh token
        if (!isAuthenticated) {
          const newToken = await dispatch.auth.refreshToken();
          if (!newToken) {
            throw new Error("Authentication required");
          }
        }

        const { data } = await UserApi.getUserProfile(userId);
        const profileData = data?.data;

        if (!profileData) {
          throw new Error("Profile not found");
        }

        await dispatch.user.setState((state) => ({
          loaded: true,
          guest: profileData,
          data: state.data,
        }));

        return profileData;
      } catch (error) {
        console.error("Error in getUserProfileForGuest:", error);
        if (error?.response?.status === 401) {
          await dispatch.auth.logout();
        }
        throw error;
      }
    },

    async userSearch(payload) {
      try {
        const { data } = await UserApi.userSearch(payload);
        return data;
      } catch (error) {
        notifyError(error.response?.data?.message || error.message);
      }
    },

    async userSearchByProjection() {
      try {
        const { data } = await UserApi.userSearchByProjection();
        await dispatch.user.setState({
          usersByProjection: data.data.Items,
        });
        return data.data;
      } catch (error) {
        notifyError(error.response?.data?.message || error.message);
      }
    },
  }),
});
