/* eslint-disable import/no-cycle */
import { createModel } from "@rematch/core";
import moment from "moment";
import { createReducerActions } from "../reducer";
import Club<PERSON>pi from "../../services/ClubApi";
import { notifyError } from "../../constants/misc";

const now = moment().unix();

const initialState = {
  allClubs: []
};

export const club = createModel()({
  state: initialState,
  reducers: createReducerActions(initialState),
  effects: (dispatch) => ({
    async getAllClubs(payload, rootState) {
      dispatch.user.setError(null);
      const { auth, user } = rootState;
      const isPrivate =
        auth.authUser.userInfo?.allowPrivateClubsTeams ||
        user.data?.allowPrivateClubsTeams;
        
      const userType =
        auth.authUser.userInfo?.userType ||
        user.data?.userType;  
      try {
        const { data } = await ClubApi.allClubs(isPrivate, userType);

        dispatch.club.setState({
          allClubs: data.data.Items
        });
        return data.data.Items;
      } catch (err) {
        notifyError(err.response.data.message);
      }
    },
  }),
});
