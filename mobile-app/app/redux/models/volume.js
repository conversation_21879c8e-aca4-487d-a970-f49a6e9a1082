import { createModel } from "@rematch/core";
import { createReducerActions } from "../reducer";

const initialState = {
  volume: 0,
};

export const volume = createModel()({
  state: initialState,
  reducers: {
    ...createReducerActions(initialState),
    setVolume(state, volume) {
      return { ...state, volume };
    },
  },
  effects: (dispatch) => ({
    toggleVolume(payload, rootState) {
      const currentVolume = rootState.volume.volume;
      const newVolume = currentVolume === 0 ? 1 : 0;
      dispatch.volume.setVolume(newVolume);
    },
  }),
});
