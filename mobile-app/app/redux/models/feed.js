// SERVICES //
import FeedApi from "../../services/FeedApi";

// CONSTANTS //
import { notifyError, notify<PERSON>uc<PERSON>, removeDuplicates, shuffleArray } from "../../constants/misc";
import { getSingleModel } from "../../constants/Constant";

// OTHERS //
import { createModel } from "@rematch/core";
import moment from "moment";
import { createReducerActions } from "../reducer";
import AsyncStorage from "@react-native-async-storage/async-storage";

const initialState = {
  highlights: [],
  profileHighlights: [],
  teamHighlights: [],
  lastEvaluatedKey: null,
  profileLastEvaluatedKey: null,
  currentIndex: 0,
  data: [],
  uploadProgress: 0
};

export const feed = createModel()({
  state: initialState,
  reducers: {
    ...createReducerActions(initialState),
    setHighlights(state, payload) {
      return {
        ...state,
        highlights: payload
      };
    },
    updateHighlightUserPhotoUrl(state, payload) {
      const { userId, newPhotoUrl } = payload;
      const updatedHighlights = state.highlights.map((highlight) =>
        highlight.user.id === userId
          ? { ...highlight, user: { ...highlight.user, photoUrl: newPhotoUrl } }
          : highlight
      );
      return {
        ...state,
        highlights: updatedHighlights
      };
    },
    setUploadProgress(state, progress) {
      return {
        ...state,
        uploadProgress: progress
      };
    }
  },
  effects: (dispatch) => ({
    async fetchHighlights(queryData) {
      dispatch.feed.setError(null);

      try {
        const now = moment().unix();
        const feed = getSingleModel("feed");
        let dataItems = feed.data;

        // Load cached data from AsyncStorage first**
        const cachedData = await AsyncStorage.getItem("cachedHighlights");
        if (cachedData) {
          const parsedData = JSON.parse(cachedData);
          if (parsedData.length > 0) {
            dispatch.feed.setState({ highlights: parsedData });
          }
        }

        //  Fetch new data from API if cache is empty or expired**
        if (!dataItems?.length || feed?.expiredTimestamp < now) {
          const { data } = await FeedApi.highlights({
            limit: queryData?.limit || 1000,
            lastEvaluatedKey: queryData?.lastEvaluatedKey || undefined,
          });

          dataItems = data?.data?.Items;

          dispatch.feed.setState({
            ...feed,
            data: data.data.Items,
            lastEvaluatedKey: data?.data?.LastEvaluatedKey,
            expiredTimestamp: moment().add(1, "hour").unix(),
          });
        }

        // Process and sort highlights**
        const newHighlights = [...dataItems].sort(
          (a, b) => new Date(b.createdAt) - new Date(a.createdAt)
        );

        let newIndex = feed?.currentIndex;
        const nextHighlights = newHighlights.slice(feed?.currentIndex, feed?.currentIndex + 10);
        newIndex += 10;

        // Merge new highlights with existing ones**
        const mergeNewFeedToOld =
          feed.highlights.length > 0 ? [...feed.highlights, ...nextHighlights] : nextHighlights;
        const cleaned = removeDuplicates(mergeNewFeedToOld, "id");

        // Store updated highlights in AsyncStorage**
        await AsyncStorage.setItem("cachedHighlights", JSON.stringify(cleaned));

        // Update the state with new highlights**
        dispatch.feed.setState({
          highlights: queryData?.id ? [queryData, ...cleaned] : [...cleaned],
          currentIndex: newIndex,
        });

        if (queryData?.cb) {
          queryData.cb(false);
        }

        return cleaned;
      } catch (err) {
        notifyError(err?.response?.data?.message || err.message);
        if (err?.response?.status === 401) {
          await dispatch.auth.logout();
        }
      }
    },

    async fetchHighlightsByUserId({ id, requestPayload }) {

      dispatch.feed.setError(null);

      try {
        const { data } = await FeedApi.getUsersHighlight(id, requestPayload);
        const feedState = getSingleModel("feed");

        const mergeNewFeedToOld = [
          ...feedState.profileHighlights,
          ...data.data.Items
        ];

        const cleaned = removeDuplicates(mergeNewFeedToOld, "id");
        // dispatch.feed.setState({
        //   profileHighlights: cleaned,
        //   profileLastEvaluatedKey: data?.data?.LastEvaluatedKey
        // });

        return {
          data: data?.data?.Items,
          lastEvaluatedKey: data?.data?.LastEvaluatedKey
        };
      } catch (err) {
        notifyError(err?.response?.data?.message || err.message);
      }
    },

    async createHighlights(payload) {
      dispatch.feed.setError(null);

      try {
        const { data } = await FeedApi.createHighlight(payload);

        if (data.status === 1) {
          if (payload.type === "VIDEO") {
            notifySuccess("Your video is being processed");
          } else {
            notifySuccess(data?.message || "Highlight created successfully");
            await dispatch.feed.fetchHighlights(data?.data?.newHighlight);
          }
        }
        return data;
      } catch (err) {
        notifyError(err?.response?.data?.message || err.message);
      }
    },

    async deleteHighlights(payload, getState) {
      dispatch.feed.setError(null);
      const state = getState;
      try {
        const { data } = await FeedApi.deleteHighlight(payload);
        if (data.status === 1) {
          notifySuccess(data?.message);

          // Remove the deleted highlight from the current state
          const currentHighlights = state.feed.highlights;
          const updatedHighlights = currentHighlights.filter(
            (highlight) => highlight.id !== payload.id
          );

          // Update state
          dispatch.feed.setState({ highlights: updatedHighlights });

          // Update AsyncStorage
          await AsyncStorage.setItem("cachedHighlights", JSON.stringify(updatedHighlights));

          // Optionally, re-fetch highlights to ensure up-to-date data
          await dispatch.feed.fetchHighlights();
          return data;
        }
      } catch (err) {
        dispatch.feed.setError(err?.response?.data?.message || err.message);
        notifyError(err?.response?.data?.message || err.message);
      }
    },

    async fetchHighlight(payload) {
      dispatch.feed.setError(null);

      try {
        const { data } = await FeedApi.fetchHighlight(payload);

        return data;
      } catch (err) {
        dispatch.feed.setError(err?.response?.data?.message || err.message);
        notifyError(err?.response?.data?.message || err.message);
      }
    },

    async editHighlights(payload, getState) {
      const { noNotification, isEmojiUpdate, ...others } = payload;
      dispatch.feed.setError(null);
      const state = getState;
      const highlights = state.feed.highlights;
      if (isEmojiUpdate) {
        const newHighlights = highlights.map((item) => {
          if (item.id === payload.id) {
            return {
              ...item,
              reactedByUsers: payload.reactedByUsers,
            };
          }
          return item;
        });
        dispatch.feed.setState({
          highlights: newHighlights,
        });
      }
      try {
        const { data } = await FeedApi.editHighlight(others);
        if (data.status === 1) {
          !noNotification && notifySuccess(data?.message);
          // Update highlights with new data
          const updatedHighlights = highlights.map((item) => {
            if (item.id === payload.id) {
              return {
                ...item,
                ...data.data, // assuming the updated highlight data is in data.data
              };
            }
            return item;
          });
          dispatch.feed.setState({
            highlights: updatedHighlights,
          });
        } else {
          if (isEmojiUpdate) {
            dispatch.feed.setState({
              highlights: state.feed.highlights,
            });
          }
        }
        return data;
      } catch (err) {
        dispatch.feed.setError(err?.response?.data?.message || err.message);
        !noNotification && notifyError(err?.response?.data?.message || err.message);
        if (isEmojiUpdate) {
          dispatch.feed.setState({
            highlights: state.feed.highlights,
          });
        }
      }
    },

    // GET TEAM HIGHLIGHTS BY ID
    async getTeamHighlights(payload) {
      dispatch.feed.setError(null);

      try {
        const res = await FeedApi.getTeamHighlights(payload);

        if (res.status === 200) {
          dispatch.feed.setState({
            teamHighlights: res.data.data
          });
        }
      } catch (err) {
        notifyError(err?.response?.data?.message || err.message);
      }
    },
  }),
});
