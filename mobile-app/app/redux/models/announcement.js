/* eslint-disable import/no-cycle */
import { createModel } from "@rematch/core";
import moment from "moment";

import _groupBy from "lodash/groupBy";
import AnnouncementApi from "../../services/AnnouncementApi";
import { createReducerActions } from "../reducer";

const initialState = {
  announcements: [],
  activeAnnouncements: [],
};
export const announcement = createModel()({
  state: initialState,
  reducers: createReducerActions(initialState),
  effects: (dispatch, rootState) => ({
    async getAllAnnouncements() {
      dispatch.announcement.setError(null);
      try {
        const payload = { limit: 1000 };
        const { data } = await AnnouncementApi.getAnnouncements(payload);
        //console.log("announcement data", data.data);

        if (data.status === 1) {
          const activeAnnouncements = data?.data?.Items.filter((item) =>
            Boolean(item.isActive)
          ).sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

          dispatch.announcement.setState({
            announcements: data.data.Items,
          });

          dispatch.announcement.setState({
            activeAnnouncements: activeAnnouncements,
          });
        }
        return data.data.Items;
      } catch (err) {
        console.error("ERROR", err);
      }
    },

    async getOneAnnouncement(payload) {
      dispatch.announcement.setError(null);
      try {
        const { data } = await AnnouncementApi.getOneAnnouncement(payload);
        return data.data.Item;
      } catch (err) {
        console.error("ERROR", err);
      }
    },

    async updateAnnouncement(payload, rootState) {
      dispatch.announcement.setError(null);
      // update the state immediately
      const activeAnnouncements = rootState?.announcement?.activeAnnouncements;
      const newActiveAnnouncements = activeAnnouncements.map((item) => {
        if (item.id === payload.id) {
          return {
            ...item,
            reactedByUsers: payload.reactedByUsers,
          };
        }
        return item;
      });
      dispatch.announcement.setState({
        activeAnnouncements: newActiveAnnouncements,
        announcements: rootState.announcement.announcements,
      });

      try {
        // then make the API call
        const { data } = await AnnouncementApi.updateAnnouncement(payload);

        if (data.status !== 1) {
          // set the state to be what it was before
          dispatch.announcement.setState({
            ...rootState?.announcement,
          });
        }
        return data.data.Attributes;
      } catch (err) {
        // set the state to be what it was before
        dispatch.announcement.setState({
          ...rootState?.announcement,
        });
        console.error("ERROR", err);
      }
    },
  }),
});
