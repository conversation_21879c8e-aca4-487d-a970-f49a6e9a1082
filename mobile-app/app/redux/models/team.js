/* eslint-disable import/no-cycle */
import { createModel } from "@rematch/core";
import { createReducerActions } from "../reducer";
import Team<PERSON><PERSON> from "../../services/TeamApi";
import { notifyError } from "../../constants/misc";

const initialState = {
  allTeams: { loaded: false, data: {}, currentTeamId: "", currentClubId: "" },
  allTeamPlayers: [],
  singleTeam: {},
  clubData: {},
  teamsByProjection: []
};

export const team = createModel()({
  state: initialState,
  reducers: createReducerActions(initialState),

  effects: (dispatch) => ({
    async getTeam(payload) {
      try {
        const res = await TeamApi.getTeam(payload.id);
      } catch (e) {
        console.log(e);
      }
    },

    // GET ALL TEAMS
    async getAllTeams(payload, rootState) {
      const { auth, user, team } = rootState;
      const isPrivate =
        auth.authUser.userInfo?.allowPrivateClubsTeams ||
        user.data?.allowPrivateClubsTeams;

      try {
        const res = await TeamApi.getAllTeams(isPrivate);

        if (res.status === 200) {
          dispatch.team.setState({
            allTeams: { ...team.allTeams, loaded: true, data: res.data.data }
          });
        }
        return res.data.data.Items;
      } catch (e) {
        dispatch.team.setState({
          allTeams: { loaded: false, data: {} }
        });
        notifyError(e.response?.data?.message || "Failed to load teams");
      }
    },

    // GET TEAMS BY CLUBID
    async getTeamsByClubId(payload, rootState) {
      const { auth, user } = rootState;
      const isPrivate =
        auth.authUser.userInfo?.allowPrivateClubsTeams ||
        user.data?.allowPrivateClubsTeams;

      try {
        const { data } = await TeamApi.teamsByClubId(payload, isPrivate);
        return data;
      } catch (err) {
        notifyError(err.response.data.message);
      }
    },

    // GET ALL TEAM PLAYERS
    async getAllTeamsPlayers(payload) {
      try {
        const res = await TeamApi.getAllTeamPlayers(payload);
        if (res.status === 200) {
          dispatch.team.setState({
            allTeamPlayers: res.data.data
          });
        }
      } catch (err) {
        notifyError(err.response.data.message);
      }
    },

    // GET SINGLE TEAM
    async getSingleTeam({ teamId, clubId }) {
      try {
        const res = await TeamApi.getSingleTeam(teamId, clubId);

        if (res.status === 200) {
          dispatch.team.setState({
            singleTeam: res.data.data.Item,
            clubData: res.data.data.clubData
          });
        }
      } catch (err) {
        notifyError(err.response.data.message);
      }
    },

    async getTeamsByProjection() {
      try {
        const { data } = await TeamApi.getTeamsByProjection();
        await dispatch.team.setState({
          teamsByProjection: data.data.Items
        });
        return data.data;
      } catch (error) {
        notifyError(error.response.data.message || error.message);
      }
    },

    async teamSearch(payload) {
      try {
        const { data } = await TeamApi.teamSearch(payload);
        return data;
      } catch (error) {
        notifyError(error.response.data.message || error.message);
      }
    },
  }),
});
