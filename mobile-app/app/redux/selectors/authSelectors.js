import { createSelector } from "reselect";
import isEqual from "lodash/isEqual";

const getAuthState = (state) => state.auth;

// Cached empty objects to maintain reference equality
const EMPTY_AUTH_USER = null;
const EMPTY_STATUS = Object.freeze({
  authenticated: false,
  token: null,
  refreshToken: null,
  expiresIn: null,
});
const EMPTY_USER_INFO = Object.freeze({
  id: null,
  email: null,
  firstName: null,
  lastName: null,
  photoUrl: null,
  liveLikeProfileId: null,
  liveLikeProfileToken: null,
  experiences: [],
  followingsIDs: [],
  blockedUsers: [],
  reports: [],
});

export const selectAuthUser = createSelector(
  [getAuthState],
  (auth) => auth.authUser || EMPTY_AUTH_USER,
  {
    memoizeOptions: {
      maxSize: 1,
      equalityCheck: isEqual,
    },
  }
);

export const selectAuthStatus = createSelector(
  [selectAuthUser],
  (authUser) => {
    if (!authUser?.authenticated && !authUser?.token) return EMPTY_STATUS;

    const newStatus = {
      authenticated: Boolean(authUser.authenticated),
      token: authUser.token || null,
      refreshToken: authUser.refreshToken || null,
      expiresIn: authUser.expiresIn || null,
    };

    return isEqual(newStatus, EMPTY_STATUS) ? EMPTY_STATUS : newStatus;
  },
  {
    memoizeOptions: {
      maxSize: 1,
      equalityCheck: isEqual,
    },
  }
);

export const selectUserInfo = createSelector(
  [selectAuthUser],
  (authUser) => {
    if (!authUser?.userInfo) return EMPTY_USER_INFO;

    const userInfo = authUser.userInfo;
    const newUserInfo = {};

    Object.keys(EMPTY_USER_INFO).forEach((key) => {
      if (Array.isArray(EMPTY_USER_INFO[key])) {
        newUserInfo[key] = userInfo[key] || [];
      } else {
        newUserInfo[key] = userInfo[key] ?? EMPTY_USER_INFO[key];
      }
    });

    return isEqual(newUserInfo, EMPTY_USER_INFO)
      ? EMPTY_USER_INFO
      : newUserInfo;
  },
  {
    memoizeOptions: {
      maxSize: 1,
      equalityCheck: isEqual,
    },
  }
);

const EMPTY_MODAL_STATE = Object.freeze({
  visible: false,
  email: null,
});

export const selectConfirmSignUpModal = createSelector(
  [getAuthState],
  (auth) => auth.confirmSignUpModal || EMPTY_MODAL_STATE
);

export const selectForgotPasswordModal = createSelector(
  [getAuthState],
  (auth) => auth.forgotPasswordModal || EMPTY_MODAL_STATE
);
