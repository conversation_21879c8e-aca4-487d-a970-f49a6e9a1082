// REACT //
import React, { useRef, useState, useEffect } from "react";
import { useSelector } from "react-redux";

// PLUGINS //
import AsyncStorage from "@react-native-async-storage/async-storage";

// COMPONENTS //
import {
  ActivityIndicator,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  ScrollView,
  StatusBar,
  TouchableOpacity,
  View,
  Image,
  Text,
  TextInput,
  BackHandler,
  Modal,
  FlatList,
} from "react-native";
import { Images } from "../../res/Images";
import ReactPlayerVideo, {
  videoContext,
} from "../../components/ReactPlayerVideo";
import ResultHighlights from "../../components/ResultHighlights/ResultHighlights";
import ReactionListCard from "../../components/ReactionListCard";
import ConfirmDelete from "../../components/ConfirmDelete";
import EditFeed from "../../components/EditFeed";
import BlockReportModal from "../../components/BlockReportModal";
import Button from "../../components/Button/Button";

// SERVICES //
import UserApi from "../../services/UserApi";

// CONSTANTS //
import { notifyError, notifySuccess } from "../../constants/misc";
import { LiveLikeReactionPacks } from "../../constants/reactions";
import { shouldShowWaitIcon } from "../../constants/Constant";

// NAVIGATION //
import { useIsFocused } from "@react-navigation/native";

// OTHERS //
import { dispatch } from "../../redux/store";
const LiveLike = require("@livelike/javascript");
import {
  BackIcon,
  BlockIcon,
  CommentIcon,
  DeleteIcon,
  Edit,
  EmojiIcon,
  MenuIcon,
  MessageIcon,
  RedDeleteIcon,
  ReportIcon,
  SendMsgIcon,
  ShareIcon,
} from "../../res/Svg";
import styles from "./FeedDetailStyles";
import { commonStyles } from "../../res/CommonStyles";
import { Colors } from "../../res/Colors";
import { HeightSize, Size } from "../../res/Size";
import moment from "moment";
import LoadingFeedVideo from "../../components/LoadingFeedVideo";
import analytics from "../../services/analytics";
import ConfirmationModal from "./ConfirmationModal";
import NotFound from "../../components/NotFound";
import { FeedHighlightReactions } from "../Feed/FeedHiglightReactions";
import UserAvatar from "../../components/UserAvatar";

const FeedDetail = ({ navigation, route, recipientId }) => {
  const [fetchedComments, setFetchedComments] = useState([]);
  const [commentsCount, setCommentsCount] = useState();
  const [commentsLoading, setCommentsLoading] = useState(false);
  const [sendLoading, setSendLoading] = useState(false);
  const [comment, setComment] = useState("");
  const [highlight, setHighlight] = useState(null);
  const [isPanelOpen, setIsPanelOpen] = useState(false);
  const [editModal, setEditModal] = useState(false);
  const [editFeedModal, setEditFeedModal] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [blockReportModalVisible, setBlockReportModalVisible] = useState(false);
  const [selectedCommentId, setSelectedCommentId] = useState(null);
  const [modalPosition, setModalPosition] = useState({ top: 0, right: 0 });
  const [selectedUserId, setSelectedUserId] = useState(null);
  const [confirmationVisible, setConfirmationVisible] = useState(false);
  const [actionType, setActionType] = useState(null);
  const [blockModalVisible, setBlockModalVisible] = useState(false);
  const [selectedComment, setSelectedComment] = useState(null);
  const [reportType, setReportType] = useState(null);
  const [shouldFetchMore, setNextCusor] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [commentId, setCommentId] = useState(null);
  const [commentBoardId, setCommentBoardId] = useState(null);

  const { userInfo } = useSelector((state) => state.auth.authUser);

  const focused = useIsFocused();

  const highlightId = route?.params?.highlightId;
  const userId = route?.params?.userId;
  const announcementId = route?.params?.announcementId;

  const emojiPanelRef = useRef(null);

  const commentInputRef = useRef(null);

  const { loggedUserId } = useSelector(({ auth: { authUser }, user }) => ({
    loggedUserId: authUser?.userInfo?.id || "",
    authUser,
  }));
  const liveLikeProfileToken = userInfo?.liveLikeProfileToken || "";

  const getHighlight = async () => {
    setIsLoading(true);
    setHighlight(null);
    try {
      if (highlightId && userId) {
        const res = await dispatch.feed.fetchHighlight({ highlightId, userId });
        setHighlight(res.data);
      } else if (highlightId && !userId) {
        // const res = await dispatch.announcement.getOneAnnouncement({ highlightId });
        // setHighlight(res.data);
      } else if (announcementId) {
        // const res = await dispatch.announcement.getOneAnnouncement({ announcementId });
        // setHighlight(res.data);
      }
    } catch (error) {
      console.error("AN ERROR OCCURRED", error);
      // Handle error as needed
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    getHighlight();
  }, [focused]);

  const openModal = () => {
    setModalVisible(true);
  };

  const closeModal = () => {
    setModalVisible(false);
  };

  const openBlockReportModal = (idx, nativeEvent, userId) => {
    setSelectedCommentId(idx);
    setSelectedComment(fetchedComments[idx]);
    setModalPosition({
      top: nativeEvent.pageY,
      right: Size(5), // Adjust the 'right' value as needed, since 'pageX' gives absolute X coordinate from the left
    });
    setSelectedUserId(userId);
    setBlockReportModalVisible(true);
  };

  const openProfileBlockReportModal = (nativeEvent, userId) => {
    setModalPosition({
      top: nativeEvent.pageY,
      right: Size(5), // Adjust the position if needed
    });
    setSelectedUserId(userId);
    setBlockModalVisible(true);
  };

  const closeBlockReportModal = () => {
    setBlockReportModalVisible(false);
    setSelectedCommentId(null);
    setBlockModalVisible(false);
  };

  const handleOpenConfirmation = (action, type) => {
    closeBlockReportModal();
    setConfirmationVisible(true);
    setActionType(action); // 'block' or 'report'
    setReportType(type); // 'COMMENT' or 'POST'
  };

  const handleDelete = async () => {
    if (!commentId || !commentBoardId) {
      console.error(
        "Invalid parameters: commentId or commentBoardId is missing."
      );
      return;
    }

    try {
      // Calling deleteComment with proper parameters
      const response = await LiveLike.deleteComment({
        commentBoardId: commentBoardId || highlight.commentBoardId,
        commentId: commentId,
      });

      // Check if the response is successful (status code 204)
      if (response.status === 204) {
        notifySuccess("Comment deleted successfully.");

        setCommentId(null);
        setCommentBoardId(null);

        setFetchedComments((prevComments) => {
          const updatedComments = prevComments.filter((comment) => {
            const isDeleted =
              comment.id === commentId || comment.comment_id === commentId;
            return !isDeleted;
          });
          return updatedComments;
        });
      } else {
        console.error("Failed to delete comment. Response:", response);
        notifyError("Failed to delete comment. Please try again.");
      }
    } catch (error) {
      console.error("Error deleting comment:", error);
      notifyError("Error deleting comment. Please try again.");
    }
  };

  const handleConfirmAction = () => {
    setConfirmationVisible(false);

    if (actionType === "block") {
      handleBlockUser();
    } else if (actionType === "report") {
      // Pass the reportType to handleReportComment if needed
      handleReportComment(reportType);
    } else if (actionType === "delete") {
      handleDelete();
    }
    closeBlockReportModal();
  };

  const handleBlockUser = async () => {
    try {
      if (!selectedUserId) {
        console.error("No user selected to block");
        return;
      }

      const payload = {
        blockedUserId: selectedUserId,
        reason: "", // Optionally include a reason for blocking
      };

      // Dispatch the blockUser action
      const status = await dispatch.user.blockUser(payload);

      if (status.status === 1) {
        // Fetch the updated userInfo before navigating
        await dispatch.user.fetchUserDetails(userInfo.id);
        // Navigate back to MessageList and pass the blocked user ID
        navigation.navigate("Feed");
      }
    } catch (err) {
      console.error(
        "Error in blocking user:",
        err.response ? err.response.data : err.message
      );
    }
  };

  // Report comment function
  const handleReportComment = async (reportType) => {
    // Get reporter's full name
    const reporterFullName = `${userInfo?.firstName || "Unknown"} ${
      userInfo?.lastName || "User"
    }`;

    // Determine reported user's full name and ID based on reportType
    let reportedFullName = "Unknown User";
    let reportedUserId = "UnknownUserId";
    let reportedContent = "No content";

    if (reportType === "POST") {
      // Report type is POST
      reportedFullName = `${highlight?.user?.firstName || "Unknown"} ${
        highlight?.user?.lastName || "User"
      }`;
      reportedUserId = highlight?.userId || "UnknownUserId";
      reportedContent = highlight?.comment || "No content";
    } else if (reportType === "COMMENT") {
      // Report type is COMMENT
      const authorData = JSON.parse(selectedComment?.custom_data || "{}");
      reportedFullName = `${authorData.firstName || "Unknown"} ${
        authorData.lastName || "User"
      }`;
      reportedUserId = authorData.id || "UnknownUserId";
      reportedContent = selectedComment?.text || "No content";
    }

    // Prepare the payload for the report
    const payload = {
      reporterUserId: loggedUserId || "UnknownUserId",
      reportedUserId: reportedUserId,
      reporterFullName: reporterFullName,
      reportedFullName: reportedFullName,
      reason: "",
      reportedContent: reportedContent,
      reportedPhotoUrl: "", // Optional
      reporterPhotoUrl: userInfo?.photoUrl || "",
      reportType: reportType,
      contendId: reportType === "POST" ? highlightId : selectedComment?.id,
      ...(reportType === "COMMENT" && {
        metadata: {
          highlightId: highlightId || highlight?.id,
          userId: userId,
        },
      }),
    };

    // Submit the report
    try {
      const status = await dispatch.user.submitReport(payload);
      if (status === 1) {
        await dispatch.user.fetchUserDetails(userInfo.id);
      } else {
        console.error("Report submission failed");
      }
    } catch (error) {
      console.error("Error in submitting report:", error.message);
    }

    // Close the modal
    closeBlockReportModal();
  };

  const handleEditPress = () => {
    setEditFeedModal(true);
    closeModal();
  };

  const handleDeletePress = () => {
    setEditModal(true);
    closeModal();
  };

  const togglePanel = () => {
    setIsPanelOpen(!isPanelOpen);
  };

  useEffect(() => {
    const backAction = () => {
      if (route?.params?.fromProfileHighlights) {
        navigation.navigate("MyProfileScreenStack", {
          screen: "MyProfile",
          params: { selectedTab: "highlights", userId: userId },
        });
      } else {
        navigation.navigate("HomeScreenStack", {
          screen: "Feed",
        });
      }
      return true;
    };

    const backHandler = BackHandler.addEventListener(
      "hardwareBackPress",
      backAction
    );

    const unsubscribe = navigation.addListener("beforeRemove", (e) => {
      // Prevent the default back behavior (especially important on iOS)
      e.preventDefault();

      // Delay navigation to avoid iOS crashing or inconsistent behavior
      setTimeout(() => {
        if (route?.params?.fromProfileHighlights) {
          navigation.navigate("MyProfileScreenStack", {
            screen: "MyProfile",
            params: { selectedTab: "highlights", userId: userId },
          });
        } else {
          navigation.navigate("HomeScreenStack", {
            screen: "Feed",
          });
        }
      }, 0);
    });

    return () => {
      backHandler.remove();
      unsubscribe();
    };
  }, [navigation, route?.params?.fromProfileHighlights, userId]);

  const handleButtonClick = () => {
    togglePanel();
  };

  const handleClickOutside = () => {
    setIsPanelOpen(false);
  };

  const addUserReaction = async (highlight, reaction) => {
    if (!loggedUserId) {
      notifyWarn("Please login to react");
      return;
    }
  
    setIsPanelOpen(false);
  
    const filterById = (reactedBy, userId) => {
      if (reactedBy) {
        return reactedBy.filter((item) => item.userId !== userId);
      }
      return [];
    };
  
    try {
      if (highlight) {
        const reactedByUsers = highlight.reactedByUsers;
        // Find if user already reacted and with which emoji
        const existingUserReaction = reactedByUsers?.find(
          (item) => item.userId === userInfo?.id
        );
        const isSameReaction =
          existingUserReaction?.others?.emoji === reaction?.file;
  
        let reactedByUsersWithNewReaction;
  
        if (isSameReaction) {
          // Remove reaction
          reactedByUsersWithNewReaction = filterById(
            reactedByUsers,
            userInfo?.id
          );
        } else {
          // Change/add reaction
          const filteredReactions = filterById(
            reactedByUsers,
            userInfo?.id
          );
          reactedByUsersWithNewReaction = [
            ...filteredReactions,
            {
              userId: userInfo?.id,
              others: {
                user: {
                  firstName: userInfo?.firstName,
                  lastName: userInfo?.lastName,
                  photoUrl: userInfo?.photoUrl,
                  cludId: userInfo?.cludId,
                  clubId: userInfo?.clubId,
                  gender: userInfo?.gender,
                },
                emoji: reaction?.file,
                emojiName: reaction?.name,
              },
            },
          ];
        }
  
        // Update the state immediately
        setHighlight((prevHighlight) => ({
          ...prevHighlight,
          reactedByUsers: reactedByUsersWithNewReaction,
        }));
  
        // Log reaction before updating state
        await analytics.logReactionAdded(
          highlight.id,
          "highlight",
          isSameReaction ? "removed" : reaction.name
        );
  
        // Make the API call to update the reaction
        if (highlight && userId) {
          dispatch.feed.editHighlights({
            userId: highlight.userId,
            id: highlight.id,
            reactedByUsers: reactedByUsersWithNewReaction,
            noNotification: true,
            isEmojiUpdate: true,
          });
        }
        // if (highlight && !userId) {
        //   dispatch.announcement.updateAnnouncement({
        //     id: highlight.id,
        //     reactedByUsers: reactedByUsersWithNewReaction,
        //     noNotification: true,
        //     isEmojiUpdate: true
        //   });
        // }
      }
    } catch (error) {
      console.error("AN ERROR OCCURED", error);
      setHighlight({
        ...highlight,
        reactedByUsers: highlight.reactedByUsers,
      });
    }
  };

  const sendCommentsCount = async (commentBoardId, count) => {
    if (commentBoardId) {
      if (highlightId) {
        try {
          dispatch.feed.editHighlights({
            noNotification: true,
            id: highlight?.id,
            userId: highlight?.user?.id,
            totalCommentCount: count,
          });
        } catch (error) {
          console.error(error);
        }
      }

      // if (announcementId) {
      //   try {
      //     const comment = await getComments({
      //       commentBoardId
      //     });
      //     dispatch.announcement.updateAnnouncement({
      //       id: highlight.id,
      //       totalCommentCount: comment.count
      //     });
      //   } catch (error) {
      //     console.log(error);
      //   }
    }
  };
  // };

  const createcommentBoard = async () => {
    try {
      const commentBoard = await LiveLike.createCommentBoard({
        title: highlightId || highlight?.id,
        customId: highlightId,
        repliesDepth: 2,
        allowComments: true,
        customData: `created by ${
          highlight?.user?.id || highlight?.title
        } on ${Date.now()}`,
      });
      if (highlightId) {
        dispatch.feed.editHighlights({
          noNotification: true,
          commentBoardId: commentBoard.id,
          id: highlightId,
          userId: userId,
          totalCommentCount: 0,
        });
      }
      // if (announcementId) {
      //   dispatch.announcement.updateAnnouncement({
      //     id: highlight.id,
      //     commentBoardId: commentBoard.id,
      //     totalCommentCount: 0
      //   });
      // }
      return commentBoard;
    } catch (error) {
      console.error(error);
    }
  };

  const fetchComments = async (cursor) => {
    if (loggedUserId) {
      setCommentsLoading(true);

      // ✅ Clear previous state if this is a fresh fetch (not a paginated call)
      if (!cursor) {
        setFetchedComments([]);
        setNextCusor(null);
      }

      let commentBoardId = "";

      try {
        if (!highlight?.commentBoardId) {
          const getCommentBoardDetail = await LiveLike.getCommentBoardDetails({
            customId: highlightId || highlight?.id,
          });

          if (getCommentBoardDetail.id) {
            commentBoardId = getCommentBoardDetail.id;
            if (highlightId) {
              // dispatch.announcement.updateAnnouncement({
              //   id: highlightId,
              //   commentBoardId: getCommentBoardDetail.id,
              // });
            }
          }
        } else {
          commentBoardId = highlight?.commentBoardId || "";
        }
      } catch (error) {
        setCommentsLoading(false);
        if (error === "Resource not found") {
          createcommentBoard();
        }
      }

      // Check if the comment board id has been created or fetched
      if (commentBoardId) {
        try {
          const getCommentPayload = {
            commentBoardId,
            sorting: LiveLike.CommentSort.NEWEST,
          };

          let commentResponse;

          if (cursor && !cursor.done) {
            commentResponse = await Promise.resolve(cursor.next());
            // Filter out deleted comments (is_deleted: true) before setting them
            const filteredComments = Array.isArray(
              commentResponse?.value?.results
            )
              ? commentResponse.value.results.filter(
                  (comment) => comment.is_deleted !== true
                )
              : [];

            // Set the filtered comments by merging with the existing fetchedComments
            const updatedComments = [...fetchedComments, ...filteredComments];
            setFetchedComments(updatedComments);

            // Only reset the cursor if there are no more comments to fetch
            if (commentResponse.done) {
              setNextCusor(!commentResponse.done);
            }
          } else {
            commentResponse = await LiveLike.getComments(getCommentPayload);

            // Ensure that commentResponse?.results is an array and filter out deleted comments
            const filteredComments = Array.isArray(commentResponse?.results)
              ? commentResponse.results.filter(
                  (comment) => comment.is_deleted !== true
                )
              : [];

            // Set the filtered comments to the state
            setFetchedComments(filteredComments);

            if (!commentResponse.done) {
              setNextCusor(commentResponse);
            }
          }

          setCommentsCount(commentResponse.count);
          setCommentsLoading(false);
        } catch (error) {
          console.error(error, "Error");
          setCommentsLoading(false);
        }
      }
    }
  };

  // Keep your useEffect as it is
  useEffect(() => {
    fetchComments(false);
  }, [highlightId, highlight?.commentBoardId, highlight?.id]);

  const handleLoadMore = () => {
    if (shouldFetchMore && !commentsLoading) {
      fetchComments(shouldFetchMore);
    }
  };

  const addUserComment = async () => {
    //If comment is not added show toast
    if (comment === "") {
      notifyError("Please add the comment");
      return;
    }

    if (!highlight.id) {
      console.warn("No highlight ID found, exiting function.");
      return;
    }

    setSendLoading(true);

    let commentBoardId = "";

    try {
      if (!highlight.commentBoardId) {
        if (!highlightId) {
          console.error(
            "highlightId is undefined, unable to fetch comment board details."
          );
          throw new Error("highlightId is required but is undefined.");
        }

        const getCommentBoardDetail = await LiveLike.getCommentBoardDetails({
          customId: highlightId || highlight?.id,
        });

        if (getCommentBoardDetail?.id) {
          commentBoardId = getCommentBoardDetail.id;
        } else {
          console.warn("No valid comment board ID returned.");
        }
      } else {
        commentBoardId = highlight.commentBoardId || "";
      }

      if (commentBoardId) {
        const newComment = {
          id: userInfo.id,
          firstName: userInfo.firstName,
          lastName: userInfo.lastName,
          clubName: userInfo.clubName || "",
          teamName: userInfo.teamName || "",
          photoUrl: userInfo.photoUrl || "/images/profile.png",
        };

        const addComments = await LiveLike.addComment({
          text: comment,
          customData: JSON.stringify(newComment),
          commentBoardId,
        });

        if (addComments) {
          const currentComments = [...fetchedComments];
          const currentCount = commentsCount || 0;

          // Update local state with the new comment
          setFetchedComments([
            {
              custom_data: JSON.stringify(newComment),
              text: comment,
              comment_board_id: addComments.comment_board_id,
              comment_id: addComments.id,
            },
            ...currentComments,
          ]);
          setCommentsCount(currentCount + 1); // Increment the local comment count

          notifySuccess("Comment Added");
          await analytics.logCommentAdded(
            highlight.id,
            "highlight",
            comment?.length || 0
          );
          setSendLoading(false);
          sendCommentsCount(commentBoardId, currentCount + 1);
          if (commentInputRef.current) {
            commentInputRef.current.value = "";
          }
          setComment("");
        } else {
          console.warn("Failed to add comment.");
        }
      } else {
        console.error("No valid commentBoardId found, comment not added.");
      }
    } catch (error) {
      setSendLoading(false);
      console.error("Error occurred while adding comment:", error);

      const currentComments = [...fetchedComments];

      // Revert state changes if error occurs
      setFetchedComments(currentComments);
      setCommentsCount(currentCount);
      sendCommentsCount(commentBoardId, currentCount);
    }
  };

  // const handleAddComment = async () => {
  //   if (!comment.trim()) {
  //     return;
  //   }

  //   try {
  //     await addUserComment(comment);
  //     commentInputRef.current.clear();
  //     setComment("");
  //   } catch (error) {
  //     console.error("Error adding comment:", error);
  //   }
  // };

  const handleSearch = async (data) => {
    const splitName = data.split(" ");
    const filteredNames = splitName.filter((name) => name !== "");
    const checkThirdName = filteredNames[2]
      ? !filteredNames[2].includes("(")
      : false;
    const fullName = `${filteredNames[0]} ${filteredNames[1]}${
      checkThirdName ? ` ${filteredNames[2]}` : ""
    }`;

    const searchResult = await handleSearch(fullName);
    if (searchResult.length > 0) {
      handleOpenProfile(searchResult[0].id);
    } else {
      notifyError("User profile not found");
    }
  };

  const parseString = (data, purpose = "userName") => {
    const rawData = data.split("/");
    const output = purpose === "userName" ? rawData[0] : rawData[1];
    return output.replace(/\s/g, "");
  };

  const getAuthorDataFromCommentCustomData = (customData) => {
    try {
      // Handle new case of the customData for comments
      return JSON.parse(customData);
    } catch (error) {
      // Handle old case of the customData for comments
      console.error("Invalid JSON in custom_data:", error);
      const firstName = parseString(customData, "userName");
      const id = parseString(customData, "id");
      return { firstName, id };
    }
  };

  const handleOpenProfile = (id) => {
    if (id) {
      navigation.navigate("MyProfileScreenStack", {
        screen: "MyProfile",
        params: { userId: id },
      });
    } else {
      navigation.navigate("Login");
    }
  };

  const handleLoginNavigation = () => {
    navigation.navigate("Login");
  };

  const isPostReported = (postId) => {
    // Assuming userInfo.reports contains the list of reported posts
    return userInfo?.reports?.some(
      (report) => report.contendId === postId && report.reportType === "POST"
    );
  };

  return (
    <SafeAreaView style={commonStyles.container}>
      <StatusBar
        barStyle="dark-content"
        hidden={false}
        backgroundColor={Colors.white}
        translucent={false}
      />
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.green} />
        </View>
      ) : (
        <KeyboardAvoidingView
          style={highlight ? { flex: 1 } : {}}
          behavior={Platform.OS === "ios" ? "padding" : null}
        >
          <ScrollView>
            <View
              style={[
                commonStyles.subContainer,
                { marginTop: HeightSize(4), paddingHorizontal: 0 },
              ]}
            >
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                  alignItems: "center",
                  paddingHorizontal: Size(5),
                }}
              >
                {!editFeedModal && (
                  <TouchableOpacity
                    onPress={() => {
                      // Force cleanup before navigation
                      if (highlight?.type === "VIDEO") {
                        setTimeout(() => {
                          setHighlight(null);
                        }, 100);
                      }
                      route?.params?.fromProfileHighlights
                        ? navigation.navigate("MyProfileScreenStack", {
                            screen: "MyProfile",
                            params: {
                              selectedTab: "highlights",
                              userId: userId,
                            },
                          })
                        : route?.params?.fromShareHighlight
                        ? navigation.navigate("HomeScreenStack", {
                            screen: "Message",
                            params: {
                              recipientId: route?.params?.recipientId,
                              fromMyProfileContent:
                                route?.params?.fromMyProfileContent || false,
                            },
                          })
                        : navigation.goBack();
                    }}
                    activeOpacity={9}
                  >
                    <BackIcon />
                  </TouchableOpacity>
                )}
              </View>

              {editModal ? (
                <ConfirmDelete
                  handleCloseModal={setEditModal}
                  shouldShowModal={editModal}
                  data={highlight}
                  selectedTab={"highlights"}
                />
              ) : (
                ""
              )}
              {editFeedModal ? (
                <EditFeed
                  close={setEditFeedModal}
                  highlightObject={highlight}
                  selectedTab={"highlights"}
                  fromProfileHighlights={route?.params?.fromProfileHighlights}
                />
              ) : (
                ""
              )}

              {highlight && !editFeedModal ? (
                <View key={highlight.id}>
                  <View
                    style={{
                      flexDirection: "row",
                      justifyContent: "space-between",
                      alignItems: "center",
                      paddingHorizontal: 16,
                      paddingBottom: 12,
                    }}
                  >
                    {highlight.type !== "RESULT" && (
                      <TouchableOpacity
                        style={styles.nameImageContainer}
                        onPress={() =>
                          handleOpenProfile(highlight?.user?.id, "id")
                        }
                      >
                        {highlight?.user?.photoUrl ? (
                          <Image
                            source={{ uri: highlight?.user?.photoUrl }}
                            style={styles.photoContainer}
                          />
                        ) : (
                          <Image
                            source={Images.profile1}
                            style={styles.photoContainer}
                          />
                        )}

                        <View>
                          <Text style={styles.nameTxt}>
                            {highlight?.user?.firstName
                              ? `${highlight?.user?.firstName} ${highlight?.user?.lastName}`
                              : highlight?.title}
                          </Text>
                          <Text style={styles.teamNameTxt}>
                            {(highlightId &&
                              highlight?.user?.teamName !== "N/A" &&
                              highlight?.user?.teamName) ||
                              highlight?.user?.clubName}
                          </Text>
                        </View>
                      </TouchableOpacity>
                    )}
                    {highlight?.type !== "RESULT" && !editFeedModal && (
                      <View>
                        {isPostReported(highlight?.id) && (
                          <ReportIcon
                            style={{ position: "absolute", right: Size(8) }}
                          />
                        )}
                        <TouchableOpacity
                          onPress={(event) =>
                            userInfo?.id === highlight?.userId
                              ? openModal()
                              : openProfileBlockReportModal(
                                  event.nativeEvent,
                                  highlight?.userId
                                )
                          }
                          style={{ position: "absolute", right: Size(1) }}
                        >
                          <MenuIcon />
                        </TouchableOpacity>
                        {userInfo?.id === highlight?.userId ? (
                          <Modal
                            animationType="fade"
                            transparent={true}
                            visible={modalVisible}
                            onRequestClose={closeModal}
                          >
                            <TouchableOpacity
                              style={styles.modalContainer}
                              activeOpacity={1}
                              onPressOut={closeModal}
                            >
                              <View style={styles.modalContent}>
                                <TouchableOpacity
                                  style={styles.menuOption}
                                  onPress={handleEditPress}
                                >
                                  <View style={styles.optionContainer}>
                                    <Edit />
                                    <Text style={styles.text}>Edit</Text>
                                  </View>
                                </TouchableOpacity>
                                <TouchableOpacity
                                  style={styles.menuOption}
                                  onPress={handleDeletePress}
                                >
                                  <View style={styles.optionContainer}>
                                    <RedDeleteIcon />
                                    <Text
                                      style={[styles.text, styles.deleteText]}
                                    >
                                      Delete
                                    </Text>
                                  </View>
                                </TouchableOpacity>
                              </View>
                            </TouchableOpacity>
                          </Modal>
                        ) : (
                          <BlockReportModal
                            visible={blockModalVisible}
                            onClose={() => setBlockModalVisible(false)}
                            onBlock={() => handleOpenConfirmation("block")}
                            onReport={() =>
                              handleOpenConfirmation("report", "POST")
                            }
                            top={modalPosition.top}
                            right={modalPosition.right}
                            text={"Post"}
                            showDeleteComment={false}
                          />
                        )}
                      </View>
                    )}
                  </View>
                  {highlight.type === "PHOTO" && (
                    <View style={styles.photoStyle}>
                      <Image
                        source={{
                          uri: highlightId ? highlight.url : highlight.assetUrl,
                        }}
                        style={{
                          backgroundColor: Colors.white,
                          width: "100%",
                          height: Platform.isPad
                            ? HeightSize(80)
                            : HeightSize(60),
                        }}
                      />
                    </View>
                  )}

                  {highlight.type === "RESULT" && (
                    <View style={styles.resultContainer}>
                      <ResultHighlights item={highlight} />
                    </View>
                  )}

                  {highlight.type === "VIDEO" &&
                    (shouldShowWaitIcon(highlight) ? (
                      <LoadingFeedVideo
                        isError={highlight?.videoStatus === "FAILED"}
                      />
                    ) : (
                      <ReactPlayerVideo
                        highlight={highlight}
                        isActive={true}
                        context={videoContext.Detail}
                      />
                    ))}

                  <View
                    style={{
                      paddingHorizontal: Size(5),
                    }}
                  >
                    <FeedHighlightReactions highlight={highlight} />

                    {Boolean(loggedUserId && liveLikeProfileToken) && (
                      <>
                        <View
                          style={{
                            marginTop: Platform.isPad
                              ? HeightSize(4)
                              : HeightSize(1),
                            flexDirection: "row",
                          }}
                        >
                          <TouchableOpacity
                            activeOpacity={9}
                            style={styles.button}
                            onPress={() => {
                              togglePanel();
                            }}
                          >
                            <EmojiIcon
                              width={Platform.isPad ? 42 : 28}
                              height={Platform.isPad ? 42 : 28}
                            />
                            {isPanelOpen && (
                              <View
                                ref={emojiPanelRef}
                                style={styles.emojiPanel}
                              >
                                {LiveLikeReactionPacks?.emojis.map(
                                  (item, idx) => (
                                    <TouchableOpacity
                                      key={idx}
                                      onPress={() =>
                                        addUserReaction(highlight, item)
                                      }
                                      style={styles.emojiButton}
                                    >
                                      {/* <Image source={{ uri: item.file }} style={styles.emojiImagePanel} /> */}
                                      <Image
                                        source={{ uri: item.file }}
                                        style={{
                                          height: Size(5),
                                          width: Size(5),
                                        }}
                                      />
                                    </TouchableOpacity>
                                  )
                                )}
                              </View>
                            )}
                          </TouchableOpacity>
                          {!isPanelOpen && (
                            <TouchableOpacity
                              activeOpacity={9}
                              style={styles.button}
                              onPress={() =>
                                navigation.navigate("ShareFeed", {
                                  highlight: highlight,
                                  type: "highlight",
                                  profile: null,
                                })
                              }
                            >
                              <ShareIcon
                                width={Platform.isPad ? 42 : 28}
                                height={Platform.isPad ? 42 : 28}
                              />
                            </TouchableOpacity>
                          )}
                        </View>

                        {/* Comment */}
                        <Text
                          style={{
                            paddingVertical: 12,
                            fontFamily: "Regular",
                          }}
                        >
                          <Text style={{ fontFamily: "PoppinsBold" }}>
                            {`${highlight?.user?.firstName} ${highlight?.user?.lastName}`}{" "}
                          </Text>
                          {highlight?.comment}
                        </Text>
                      </>
                    )}
                  </View>
                  <View>
                    <View style={styles.commentContentContainer}>
                      {fetchedComments.length > 0 && (
                        <FlatList
                          data={fetchedComments}
                          keyExtractor={(item, idx) =>
                            item?.id || idx.toString()
                          }
                          renderItem={({ item, index }) => {
                            // Check if custom_data exists and is a valid string before parsing
                            let parsedData;
                            if (item?.author?.custom_data) {
                              try {
                                parsedData = JSON.parse(
                                  item.author.custom_data
                                );
                              } catch (error) {
                                console.error("Invalid JSON:", error);
                                parsedData = {}; // Fallback to an empty object if parsing fails. It returns null initially
                              }
                            } else {
                              parsedData = getAuthorDataFromCommentCustomData(
                                item.custom_data
                              ); // Fallback to an empty object if custom_data is undefined
                            }

                            const commentUserID =
                              parsedData?.userId || parsedData?.id;

                            const isReported = userInfo?.reports?.some(
                              (report) => report.contendId === item?.id
                            );

                            const commentId = item.id || item.comment_id;

                            const commentBoardId = item.comment_board_id;

                            return (
                              <View style={styles.commentContainer}>
                                <View style={styles.userContainer}>
                                  <View
                                    style={{
                                      flexDirection: "row",
                                      gap: Size(3),
                                    }}
                                  >
                                    <UserAvatar
                                      user={parsedData}
                                      onPress={() =>
                                        handleOpenProfile(commentUserID)
                                      }
                                      size={10}
                                    />
                                    <View style={{ paddingRight: 50 }}>
                                      <View style={styles.userNameContainer}>
                                        <TouchableOpacity
                                          onPress={() =>
                                            handleOpenProfile(commentUserID)
                                          }
                                        >
                                          <Text style={styles.userName}>
                                            {parsedData?.firstName &&
                                            parsedData?.lastName
                                              ? `${parsedData.firstName} ${parsedData.lastName}`
                                              : "Unknown User"}
                                          </Text>
                                        </TouchableOpacity>
                                        <Text style={styles.timeText}>
                                          {moment(item.created_at).fromNow(
                                            true
                                          )}
                                        </Text>
                                      </View>
                                      <Text style={styles.commentText}>
                                        {item.text}
                                      </Text>
                                    </View>
                                  </View>
                                </View>

                                <View
                                  style={{
                                    opacity: 0.4,
                                    flexDirection: "row",
                                    alignItems: "center",
                                    width: 30,
                                  }}
                                >
                                  {isReported && <ReportIcon />}
                                  <TouchableOpacity
                                    onPress={(event) =>
                                      openBlockReportModal(
                                        index,
                                        event.nativeEvent,
                                        commentUserID
                                      )
                                    }
                                  >
                                    <MenuIcon />
                                  </TouchableOpacity>
                                </View>

                                {blockReportModalVisible &&
                                  selectedCommentId === index && (
                                    <BlockReportModal
                                      visible={blockReportModalVisible}
                                      onClose={closeBlockReportModal}
                                      //onBlock={() => handleOpenConfirmation("block")}
                                      onReport={() =>
                                        handleOpenConfirmation(
                                          "report",
                                          "COMMENT"
                                        )
                                      }
                                      top={modalPosition.top}
                                      right={modalPosition.right}
                                      text={"Comment"}
                                      showBlockOption={false}
                                      showDeleteComment={
                                        commentUserID === userInfo?.id ||
                                        loggedUserId === highlight?.user?.id
                                      }
                                      showReportOption={
                                        commentUserID !== userInfo?.id
                                      }
                                      onDelete={() => {
                                        handleOpenConfirmation(
                                          "delete",
                                          "delete"
                                        );
                                        setCommentId(commentId);
                                        setCommentBoardId(commentBoardId);
                                      }}
                                    />
                                  )}

                                {/* Confirmation Modal */}
                                <ConfirmationModal
                                  visible={confirmationVisible}
                                  onClose={() => setConfirmationVisible(false)}
                                  onConfirm={handleConfirmAction}
                                  actionType={actionType}
                                  reportType={reportType}
                                />
                              </View>
                            );
                          }}
                          onEndReached={handleLoadMore} // Trigger when user scrolls to bottom
                          onEndReachedThreshold={0.5} // Fetch more when user reaches half the list
                          ListFooterComponent={
                            commentsLoading ? <ActivityIndicator /> : null
                          }
                        />
                      )}
                    </View>
                  </View>
                </View>
              ) : // (<ActivityIndicator/>)
              null}
            </View>
          </ScrollView>

          {highlight &&
            (loggedUserId ? (
              !editFeedModal && (
                <View style={styles.writeCommentContainer}>
                  <TextInput
                    ref={commentInputRef}
                    style={styles.input}
                    value={comment}
                    onChangeText={(text) => setComment(text)}
                    placeholder={`Add a comment for ${highlight?.user?.firstName}`}
                  />
                  <TouchableOpacity
                    onPress={addUserComment}
                    style={[
                      styles.sendButton,
                      {
                        backgroundColor: sendLoading
                          ? "transparent"
                          : "#14FF00",
                      },
                    ]}
                  >
                    {sendLoading ? <ActivityIndicator /> : <SendMsgIcon />}
                  </TouchableOpacity>
                </View>
              )
            ) : (
              <View style={styles.loginContainer}>
                <TouchableOpacity onPress={handleLoginNavigation}>
                  <Text>Login to view and make comments</Text>
                </TouchableOpacity>
              </View>
            ))}

          {!highlight && !isLoading && <NotFound />}
        </KeyboardAvoidingView>
      )}
    </SafeAreaView>
  );
};

export default FeedDetail;
