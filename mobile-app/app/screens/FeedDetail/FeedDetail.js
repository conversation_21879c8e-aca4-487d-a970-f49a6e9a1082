import { useRef, useState, useEffect, useCallback } from "react";
import { useSelector } from "react-redux";
import {
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  ScrollView,
  StatusBar,
  TouchableOpacity,
  View,
  Image,
  Text,
  TextInput,
  BackHandler,
  Modal,
} from "react-native";
import { Images } from "../../res/Images";
import ReactPlayerVideo, {
  videoContext,
} from "../../components/ReactPlayerVideo";
import ResultHighlights from "../../components/ResultHighlights/ResultHighlights";
import ConfirmDelete from "../../components/ConfirmDelete";
import EditFeed from "../../components/EditFeed";
import BlockReportModal from "../../components/BlockReportModal";
import { notifyError, notifySuccess } from "../../constants/misc";
import { LiveLikeReactionPacks } from "../../constants/reactions";
import { shouldShowWaitIcon } from "../../constants/Constant";
import { useFocusEffect } from "@react-navigation/native";
import { dispatch } from "../../redux/store";
import {
  BackIcon,
  Edit,
  EmojiIcon,
  MenuIcon,
  RedDeleteIcon,
  ReportIcon,
  SendMsgIcon,
  ShareIcon,
} from "../../res/Svg";
import styles from "./FeedDetailStyles";
import { commonStyles } from "../../res/CommonStyles";
import { Colors } from "../../res/Colors";
import { HeightSize, Size } from "../../res/Size";
import LoadingFeedVideo from "../../components/LoadingFeedVideo";
import analytics from "../../services/analytics";
import NotFound from "../../components/NotFound";
import { FeedHighlightReactions } from "../Feed/FeedHiglightReactions";
import CommentBoard from "../../components/Comment/CommentBoard";
import { useComments } from "../../hooks/useComments";
import CommentApi from "../../services/CommentApi";
import { selectUserInfo } from "../../redux/selectors/authSelectors";
import { shallowEqual } from "react-redux";
import ConfirmationModal from "./ConfirmationModal";

const FeedDetail = ({ navigation, route }) => {
  const {
    recipientId,
    highlightId,
    fromProfileHighlights,
    fromShareHighlight,
    userId,
  } = route?.params || {};

  const [highlight, setHighlight] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [comment, setComment] = useState("");
  const [isCreatingComment, setIsCreatingComment] = useState(false);
  const [emojiPanelVisible, setEmojiPanelVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editFeedModalVisible, setEditFeedModalVisible] = useState(false);
  const [editDeleteModalVisible, setEditDeleteModalVisible] = useState(false);
  const [blockReportModalVisible, setBlockReportModalVisible] = useState(false);
  const [blockReportModalPosition, setBlockReportModalPosition] = useState({
    top: 0,
    right: 0,
  });
  const [confirmationModalVisible, setConfirmationModalVisible] =
    useState(false);
  const [actionType, setActionType] = useState(null);
  const [reportType, setReportType] = useState(null);

  const {
    comments,
    setComments,
    isLoading: isLoadingComments,
  } = useComments(highlight?.commentBoardId);

  const userInfo = useSelector(selectUserInfo, shallowEqual);

  const emojiPanelRef = useRef(null);

  const getHighlight = useCallback(async () => {
    if (!highlightId || !userId) return;

    setIsLoading(true);
    setHighlight(null);

    try {
      const res = await dispatch.feed.fetchHighlight({ highlightId, userId });
      setHighlight(res.data);
    } catch (error) {
      console.error("[API] Error fetching highlight:", error);
    } finally {
      setIsLoading(false);
    }
  }, [highlightId, userId]);

  const openModal = useCallback(() => {
    setEditDeleteModalVisible(true);
  }, []);

  const closeModal = useCallback(() => {
    setEditDeleteModalVisible(false);
  }, []);

  const openProfileBlockReportModal = useCallback((nativeEvent, userId) => {
    setBlockReportModalPosition({
      top: nativeEvent.pageY,
      right: Size(5),
    });
    setBlockReportModalVisible(true);
  }, []);

  const closeBlockReportModal = useCallback(() => {
    setBlockReportModalVisible(false);
  }, []);

  const openConfirmationModal = useCallback(
    (action, type) => {
      closeBlockReportModal();
      setConfirmationModalVisible(true);
      setActionType(action);
      setReportType(type);
    },
    [closeBlockReportModal]
  );

  const handleEditPress = useCallback(() => {
    setEditFeedModalVisible(true);
    closeModal();
  }, [closeModal]);

  const handleDeletePress = useCallback(() => {
    setEditModalVisible(true);
    closeModal();
  }, [closeModal]);

  const togglePanel = useCallback(() => {
    setEmojiPanelVisible((prev) => !prev);
  }, []);

  const addUserReaction = useCallback(
    async (highlight, reaction) => {
      if (!userInfo.id) {
        notifyWarn("Please login to react");
        return;
      }

      setEmojiPanelVisible(false);
      function filterById(reactedBy, userId) {
        if (reactedBy) {
          const filteredArray = reactedBy.filter(
            (item) => item.userId !== userId
          );
          return filteredArray;
        }
      }

      try {
        if (highlight) {
          const reactedByUsers = highlight.reactedByUsers;
          const filteredReactions =
            filterById(reactedByUsers, userInfo.id) || [];
          const reactedByUsersWithNewReaction = [
            ...filteredReactions,
            {
              userId: userInfo?.id,
              others: {
                user: {
                  firstName: userInfo?.firstName,
                  lastName: userInfo?.lastName,
                  photoUrl: userInfo?.photoUrl,
                  cludId: userInfo?.cludId,
                  clubId: userInfo?.clubId,
                  gender: userInfo?.gender,
                },
                emoji: reaction?.file,
                emojiName: reaction?.name,
              },
            },
          ];

          // Update the state immediately
          setHighlight((prevHighlight) => ({
            ...prevHighlight,
            reactedByUsers: reactedByUsersWithNewReaction,
          }));

          // Log reaction before updating state
          await analytics.logReactionAdded(
            highlight.id,
            "highlight",
            reaction.name
          );

          if (highlight && userId) {
            dispatch.feed.editHighlights({
              userId: highlight.userId,
              id: highlight.id,
              reactedByUsers: reactedByUsersWithNewReaction,
              noNotification: true,
              isEmojiUpdate: true,
            });
          }
        }
      } catch (error) {
        console.error("AN ERROR OCCURED", error);
        setHighlight({
          ...highlight,
          reactedByUsers: reactedByUsers,
        });
      }
    },
    [highlight, userInfo]
  );

  const createComment = useCallback(async () => {
    try {
      setIsCreatingComment(true);

      const { data: createdComment } = await CommentApi.createComment(
        highlight?.commentBoardId,
        { comment }
      );

      setComments((prev) => [createdComment, ...prev]);
      setComment("");
    } catch (error) {
      console.error("[API] Error creating comment:", error);
      notifyError("Error creating comment. Please try again.");
    } finally {
      setIsCreatingComment(false);
    }
  }, [highlight?.commentBoardId, comment]);

  const handleOpenProfile = useCallback((id) => {
    if (id) {
      navigation.navigate("MyProfileScreenStack", {
        screen: "MyProfile",
        params: { userId: id },
      });
    } else {
      navigation.navigate("Login");
    }
  }, []);

  const handleLoginNavigation = useCallback(
    () => navigation.navigate("Login"),
    []
  );

  const getIsPostReported = useCallback(
    (postId) => {
      return userInfo?.reports?.some(
        (report) => report.contendId === postId && report.reportType === "POST"
      );
    },
    [userInfo?.reports]
  );

  useFocusEffect(
    useCallback(() => {
      getHighlight();
    }, [])
  );

  useEffect(() => {
    const backAction = () => {
      if (fromProfileHighlights) {
        navigation.navigate("MyProfileScreenStack", {
          screen: "MyProfile",
          params: { selectedTab: "highlights", userId: userId },
        });
      } else {
        navigation.navigate("HomeScreenStack", {
          screen: "Feed",
        });
      }
      return true;
    };

    const backHandler = BackHandler.addEventListener(
      "hardwareBackPress",
      backAction
    );

    const unsubscribe = navigation.addListener("beforeRemove", (e) => {
      // Prevent the default back behavior (especially important on iOS)
      e.preventDefault();

      // Delay navigation to avoid iOS crashing or inconsistent behavior
      setTimeout(() => {
        if (fromProfileHighlights) {
          navigation.navigate("MyProfileScreenStack", {
            screen: "MyProfile",
            params: { selectedTab: "highlights", userId: userId },
          });
        } else {
          navigation.navigate("HomeScreenStack", {
            screen: "Feed",
          });
        }
      }, 0);
    });

    return () => {
      backHandler.remove();
      unsubscribe();
    };
  }, [navigation, fromProfileHighlights, userId]);

  const handleBlockUser = useCallback(async () => {
    try {
      const payload = {
        blockedUserId: highlight?.userId,
        reason: "",
      };

      await dispatch.user.blockUser(payload);
    } catch (err) {
      console.error(
        "Error in blocking user:",
        err.response ? err.response.data : err.message
      );
    }
  }, []);

  const handleReportPost = useCallback(async () => {
    const reporterFullName = `${userInfo?.firstName}${
      userInfo?.lastName ? ` ${userInfo?.lastName}` : ""
    }`;
    const reportedFullName = `${highlight?.user.firstName}${
      highlight?.user.lastName ? ` ${highlight?.user.lastName}` : ""
    }`;
    const payload = {
      reporterUserId: userInfo?.id,
      reportedUserId: highlight?.userId,
      reporterFullName,
      reportedFullName,
      reason: "",
      reportedContent: highlight?.comment,
      reportedPhotoUrl: "",
      reporterPhotoUrl: userInfo?.photoUrl || "",
      reportType,
      contendId: highlight.id,
      metadata: {
        highlightId,
        userId: highlight.userId,
      },
    };

    await dispatch.user.submitReport(payload);

    notifySuccess("Comment reported.");
  }, [userInfo, highlight, reportType]);

  const handleConfirmAction = useCallback(async () => {
    setConfirmationModalVisible(false);

    try {
      if (actionType === "report") {
        await handleReportPost(reportType);
      } else if (actionType === "block") {
        await handleBlockUser();
      }
    } catch (error) {
      console.error("Error in confirm action:", error);
    } finally {
      setConfirmationModalVisible(false);
    }
  }, [actionType, reportType, handleReportPost, handleBlockUser]);

  return (
    <SafeAreaView style={commonStyles.container}>
      <StatusBar
        barStyle="dark-content"
        hidden={false}
        backgroundColor={Colors.white}
        translucent={false}
      />
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.green} />
        </View>
      ) : (
        <KeyboardAvoidingView
          style={highlight ? { flex: 1 } : {}}
          behavior={Platform.OS === "ios" ? "padding" : null}
        >
          <ScrollView>
            <View
              style={[
                commonStyles.subContainer,
                { marginTop: HeightSize(4), paddingHorizontal: 0 },
              ]}
            >
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                  alignItems: "center",
                  paddingHorizontal: Size(5),
                }}
              >
                {!editFeedModalVisible && (
                  <TouchableOpacity
                    onPress={() => {
                      // Force cleanup before navigation
                      if (highlight?.type === "VIDEO") {
                        setTimeout(() => {
                          setHighlight(null);
                        }, 100);
                      }
                      fromProfileHighlights
                        ? navigation.navigate("MyProfileScreenStack", {
                            screen: "MyProfile",
                            params: {
                              selectedTab: "highlights",
                              userId: userId,
                            },
                          })
                        : fromShareHighlight
                        ? navigation.navigate("HomeScreenStack", {
                            screen: "Message",
                            params: {
                              recipientId,
                              fromMyProfileContent:
                                fromMyProfileContent || false,
                            },
                          })
                        : navigation.goBack();
                    }}
                    activeOpacity={9}
                  >
                    <BackIcon />
                  </TouchableOpacity>
                )}
              </View>

              {editModalVisible ? (
                <ConfirmDelete
                  handleCloseModal={setEditModalVisible}
                  shouldShowModal={editModalVisible}
                  data={highlight}
                  selectedTab={"highlights"}
                />
              ) : (
                ""
              )}
              {editFeedModalVisible ? (
                <EditFeed
                  close={setEditFeedModalVisible}
                  highlightObject={highlight}
                  selectedTab={"highlights"}
                  fromProfileHighlights={fromProfileHighlights}
                />
              ) : (
                ""
              )}

              {highlight && !editFeedModalVisible ? (
                <View key={highlight.id}>
                  <View
                    style={{
                      flexDirection: "row",
                      justifyContent: "space-between",
                      alignItems: "center",
                      paddingHorizontal: 16,
                      paddingBottom: 12,
                    }}
                  >
                    {highlight.type !== "RESULT" && (
                      <TouchableOpacity
                        style={styles.nameImageContainer}
                        onPress={() =>
                          handleOpenProfile(highlight?.user?.id, "id")
                        }
                      >
                        {highlight?.user?.photoUrl ? (
                          <Image
                            source={{ uri: highlight?.user?.photoUrl }}
                            style={styles.photoContainer}
                          />
                        ) : (
                          <Image
                            source={Images.profile1}
                            style={styles.photoContainer}
                          />
                        )}

                        <View>
                          <Text style={styles.nameTxt}>
                            {highlight?.user?.firstName
                              ? `${highlight?.user?.firstName} ${highlight?.user?.lastName}`
                              : highlight?.title}
                          </Text>
                          <Text style={styles.teamNameTxt}>
                            {(highlightId &&
                              highlight?.user?.teamName !== "N/A" &&
                              highlight?.user?.teamName) ||
                              highlight?.user?.clubName}
                          </Text>
                        </View>
                      </TouchableOpacity>
                    )}
                    {highlight?.type !== "RESULT" && !editFeedModalVisible && (
                      <View>
                        {getIsPostReported(highlight?.id) && (
                          <ReportIcon
                            style={{ position: "absolute", right: Size(8) }}
                          />
                        )}
                        <TouchableOpacity
                          onPress={(event) =>
                            userInfo?.id === highlight?.userId
                              ? openModal()
                              : openProfileBlockReportModal(
                                  event.nativeEvent,
                                  highlight?.userId
                                )
                          }
                          style={{ position: "absolute", right: Size(1) }}
                        >
                          <MenuIcon />
                        </TouchableOpacity>
                        {userInfo?.id === highlight?.userId ? (
                          <Modal
                            animationType="fade"
                            transparent={true}
                            visible={editDeleteModalVisible}
                            onRequestClose={closeModal}
                          >
                            <TouchableOpacity
                              style={styles.modalContainer}
                              activeOpacity={1}
                              onPressOut={closeModal}
                            >
                              <View style={styles.modalContent}>
                                <TouchableOpacity
                                  style={styles.menuOption}
                                  onPress={handleEditPress}
                                >
                                  <View style={styles.optionContainer}>
                                    <Edit />
                                    <Text style={styles.text}>Edit</Text>
                                  </View>
                                </TouchableOpacity>
                                <TouchableOpacity
                                  style={styles.menuOption}
                                  onPress={handleDeletePress}
                                >
                                  <View style={styles.optionContainer}>
                                    <RedDeleteIcon />
                                    <Text
                                      style={[styles.text, styles.deleteText]}
                                    >
                                      Delete
                                    </Text>
                                  </View>
                                </TouchableOpacity>
                              </View>
                            </TouchableOpacity>
                          </Modal>
                        ) : (
                          <BlockReportModal
                            visible={blockReportModalVisible}
                            onClose={closeBlockReportModal}
                            onBlock={() => openConfirmationModal("block")}
                            onReport={() =>
                              openConfirmationModal("report", "POST")
                            }
                            top={blockReportModalPosition.top}
                            right={blockReportModalPosition.right}
                            text="post"
                            showDeleteComment={false}
                          />
                        )}
                      </View>
                    )}
                  </View>
                  {highlight.type === "PHOTO" && (
                    <View style={styles.photoStyle}>
                      <Image
                        source={{
                          uri: highlightId ? highlight.url : highlight.assetUrl,
                        }}
                        style={{
                          backgroundColor: Colors.white,
                          width: "100%",
                          height: Platform.isPad
                            ? HeightSize(80)
                            : HeightSize(60),
                        }}
                      />
                    </View>
                  )}

                  {highlight.type === "RESULT" && (
                    <View style={styles.resultContainer}>
                      <ResultHighlights item={highlight} />
                    </View>
                  )}

                  {highlight.type === "VIDEO" &&
                    (shouldShowWaitIcon(highlight) ? (
                      <LoadingFeedVideo
                        isError={highlight?.videoStatus === "FAILED"}
                      />
                    ) : (
                      <ReactPlayerVideo
                        highlight={highlight}
                        isActive={true}
                        context={videoContext.Detail}
                      />
                    ))}

                  <View
                    style={{
                      paddingHorizontal: Size(5),
                    }}
                  >
                    <FeedHighlightReactions highlight={highlight} />

                    {Boolean(userInfo.id) && (
                      <>
                        <View
                          style={{
                            marginTop: Platform.isPad
                              ? HeightSize(4)
                              : HeightSize(1),
                            flexDirection: "row",
                          }}
                        >
                          <TouchableOpacity
                            activeOpacity={9}
                            style={styles.button}
                            onPress={() => {
                              togglePanel();
                            }}
                          >
                            <EmojiIcon
                              width={Platform.isPad ? 42 : 28}
                              height={Platform.isPad ? 42 : 28}
                            />
                            {emojiPanelVisible && (
                              <View
                                ref={emojiPanelRef}
                                style={styles.emojiPanel}
                              >
                                {LiveLikeReactionPacks?.emojis.map(
                                  (item, idx) => (
                                    <TouchableOpacity
                                      key={idx}
                                      onPress={() =>
                                        addUserReaction(highlight, item)
                                      }
                                      style={styles.emojiButton}
                                    >
                                      <Image
                                        source={{ uri: item.file }}
                                        style={{
                                          height: Size(5),
                                          width: Size(5),
                                        }}
                                      />
                                    </TouchableOpacity>
                                  )
                                )}
                              </View>
                            )}
                          </TouchableOpacity>
                          {!emojiPanelVisible && (
                            <TouchableOpacity
                              activeOpacity={9}
                              style={styles.button}
                              onPress={() =>
                                navigation.navigate("ShareFeed", {
                                  highlight: highlight,
                                  type: "highlight",
                                  profile: null,
                                })
                              }
                            >
                              <ShareIcon
                                width={Platform.isPad ? 42 : 28}
                                height={Platform.isPad ? 42 : 28}
                              />
                            </TouchableOpacity>
                          )}
                        </View>

                        {/* Comment */}
                        <Text
                          style={{
                            paddingVertical: 12,
                            fontFamily: "Regular",
                          }}
                        >
                          <Text style={{ fontFamily: "PoppinsBold" }}>
                            {`${highlight?.user?.firstName} ${highlight?.user?.lastName}`}{" "}
                          </Text>
                          {highlight?.comment}
                        </Text>
                      </>
                    )}
                  </View>
                  <CommentBoard
                    commentBoardId={highlight?.commentBoardId}
                    comments={comments}
                    setComments={setComments}
                    isLoading={isLoadingComments}
                    highlightId={highlightId}
                  />
                </View>
              ) : null}
            </View>
          </ScrollView>

          {highlight &&
            (userInfo.id ? (
              !editFeedModalVisible && (
                <View style={styles.writeCommentContainer}>
                  <TextInput
                    style={styles.input}
                    value={comment}
                    onChangeText={setComment}
                    placeholder="Add a comment..."
                  />
                  <TouchableOpacity
                    onPress={createComment}
                    disabled={comment.trim().length === 0}
                    style={[
                      styles.sendButton,
                      {
                        backgroundColor: isCreatingComment
                          ? "transparent"
                          : "#14FF00",
                      },
                    ]}
                  >
                    {isCreatingComment ? (
                      <ActivityIndicator />
                    ) : (
                      <SendMsgIcon />
                    )}
                  </TouchableOpacity>
                </View>
              )
            ) : (
              <View style={styles.loginContainer}>
                <TouchableOpacity onPress={handleLoginNavigation}>
                  <Text>Login to view and make comments</Text>
                </TouchableOpacity>
              </View>
            ))}

          {!highlight && !isLoading && <NotFound />}
        </KeyboardAvoidingView>
      )}
      <ConfirmationModal
        visible={confirmationModalVisible}
        onClose={() => {
          setConfirmationModalVisible(false);
        }}
        onConfirm={handleConfirmAction}
        actionType={actionType}
        reportType={reportType}
      />
    </SafeAreaView>
  );
};

export default FeedDetail;
