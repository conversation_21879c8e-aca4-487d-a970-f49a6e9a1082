import { useRef, useState, useEffect } from "react";
import { useSelector } from "react-redux";
import {
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  ScrollView,
  StatusBar,
  TouchableOpacity,
  View,
  Image,
  Text,
  TextInput,
  BackHandler,
  Modal,
} from "react-native";
import { Images } from "../../res/Images";
import ReactPlayerVideo, {
  videoContext,
} from "../../components/ReactPlayerVideo";
import ResultHighlights from "../../components/ResultHighlights/ResultHighlights";
import ConfirmDelete from "../../components/ConfirmDelete";
import EditFeed from "../../components/EditFeed";
import BlockReportModal from "../../components/BlockReportModal";
import { notifyError } from "../../constants/misc";
import { LiveLikeReactionPacks } from "../../constants/reactions";
import { shouldShowWaitIcon } from "../../constants/Constant";
import { useIsFocused } from "@react-navigation/native";
import { dispatch } from "../../redux/store";
import {
  BackIcon,
  Edit,
  EmojiIcon,
  MenuIcon,
  RedDeleteIcon,
  ReportIcon,
  SendMsgIcon,
  ShareIcon,
} from "../../res/Svg";
import styles from "./FeedDetailStyles";
import { commonStyles } from "../../res/CommonStyles";
import { Colors } from "../../res/Colors";
import { HeightSize, Size } from "../../res/Size";
import LoadingFeedVideo from "../../components/LoadingFeedVideo";
import analytics from "../../services/analytics";
import NotFound from "../../components/NotFound";
import { FeedHighlightReactions } from "../Feed/FeedHiglightReactions";
import CommentBoard from "../../components/Comment/CommentBoard";

const FeedDetail = ({ navigation, route }) => {
  const [sendLoading, setSendLoading] = useState(false);
  const [comment, setComment] = useState("");
  const [highlight, setHighlight] = useState(null);
  const [isPanelOpen, setIsPanelOpen] = useState(false);
  const [editModal, setEditModal] = useState(false);
  const [editFeedModal, setEditFeedModal] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [blockReportModalVisible, setBlockReportModalVisible] = useState(false);
  const [selectedCommentId, setSelectedCommentId] = useState(null);
  const [modalPosition, setModalPosition] = useState({ top: 0, right: 0 });
  const [selectedUserId, setSelectedUserId] = useState(null);
  const [confirmationVisible, setConfirmationVisible] = useState(false);
  const [actionType, setActionType] = useState(null);
  const [blockModalVisible, setBlockModalVisible] = useState(false);
  const [selectedComment, setSelectedComment] = useState(null);
  const [reportType, setReportType] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  const { userInfo } = useSelector((state) => state.auth.authUser);

  const focused = useIsFocused();

  const highlightId = route?.params?.highlightId;
  const userId = route?.params?.userId;
  const announcementId = route?.params?.announcementId;

  const emojiPanelRef = useRef(null);

  const commentInputRef = useRef(null);

  const { loggedUserId } = useSelector(({ auth: { authUser }, user }) => ({
    loggedUserId: authUser?.userInfo?.id || "",
    authUser,
  }));
  const liveLikeProfileToken = userInfo?.liveLikeProfileToken || "";

  const getHighlight = async () => {
    setIsLoading(true);
    setHighlight(null);
    try {
      if (highlightId && userId) {
        const res = await dispatch.feed.fetchHighlight({ highlightId, userId });
        setHighlight(res.data);
      } else if (highlightId && !userId) {
        // const res = await dispatch.announcement.getOneAnnouncement({ highlightId });
        // setHighlight(res.data);
      } else if (announcementId) {
        // const res = await dispatch.announcement.getOneAnnouncement({ announcementId });
        // setHighlight(res.data);
      }
    } catch (error) {
      console.error("AN ERROR OCCURRED", error);
      // Handle error as needed
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    getHighlight();
  }, [focused]);

  const openModal = () => {
    setModalVisible(true);
  };

  const closeModal = () => {
    setModalVisible(false);
  };

  const openProfileBlockReportModal = (nativeEvent, userId) => {
    setModalPosition({
      top: nativeEvent.pageY,
      right: Size(5), // Adjust the position if needed
    });
    setSelectedUserId(userId);
    setBlockModalVisible(true);
  };

  const closeBlockReportModal = () => {
    setBlockReportModalVisible(false);
    setSelectedCommentId(null);
    setBlockModalVisible(false);
  };

  const handleOpenConfirmation = (action, type) => {
    closeBlockReportModal();
    setConfirmationVisible(true);
    setActionType(action); // 'block' or 'report'
    setReportType(type); // 'COMMENT' or 'POST'
  };

  const handleEditPress = () => {
    setEditFeedModal(true);
    closeModal();
  };

  const handleDeletePress = () => {
    setEditModal(true);
    closeModal();
  };

  const togglePanel = () => {
    setIsPanelOpen(!isPanelOpen);
  };

  useEffect(() => {
    const backAction = () => {
      if (route?.params?.fromProfileHighlights) {
        navigation.navigate("MyProfileScreenStack", {
          screen: "MyProfile",
          params: { selectedTab: "highlights", userId: userId },
        });
      } else {
        navigation.navigate("HomeScreenStack", {
          screen: "Feed",
        });
      }
      return true;
    };

    const backHandler = BackHandler.addEventListener(
      "hardwareBackPress",
      backAction
    );

    const unsubscribe = navigation.addListener("beforeRemove", (e) => {
      // Prevent the default back behavior (especially important on iOS)
      e.preventDefault();

      // Delay navigation to avoid iOS crashing or inconsistent behavior
      setTimeout(() => {
        if (route?.params?.fromProfileHighlights) {
          navigation.navigate("MyProfileScreenStack", {
            screen: "MyProfile",
            params: { selectedTab: "highlights", userId: userId },
          });
        } else {
          navigation.navigate("HomeScreenStack", {
            screen: "Feed",
          });
        }
      }, 0);
    });

    return () => {
      backHandler.remove();
      unsubscribe();
    };
  }, [navigation, route?.params?.fromProfileHighlights, userId]);

  const addUserReaction = async (highlight, reaction) => {
    if (!loggedUserId) {
      notifyWarn("Please login to react");
      return;
    }

    setIsPanelOpen(false);
    function filterById(reactedBy, userId) {
      if (reactedBy) {
        const filteredArray = reactedBy.filter(
          (item) => item.userId !== userId
        );
        return filteredArray;
      }
    }

    try {
      if (highlight) {
        const reactedByUsers = highlight.reactedByUsers;
        const filteredReactions = filterById(reactedByUsers, userInfo.id) || [];
        const reactedByUsersWithNewReaction = [
          ...filteredReactions,
          {
            userId: userInfo?.id,
            others: {
              user: {
                firstName: userInfo?.firstName,
                lastName: userInfo?.lastName,
                photoUrl: userInfo?.photoUrl,
                cludId: userInfo?.cludId,
                clubId: userInfo?.clubId,
                gender: userInfo?.gender,
              },
              emoji: reaction?.file,
              emojiName: reaction?.name,
            },
          },
        ];

        // Update the state immediately
        setHighlight((prevHighlight) => ({
          ...prevHighlight,
          reactedByUsers: reactedByUsersWithNewReaction,
        }));

        // Log reaction before updating state
        await analytics.logReactionAdded(
          highlight.id,
          "highlight",
          reaction.name
        );

        if (highlight && userId) {
          dispatch.feed.editHighlights({
            userId: highlight.userId,
            id: highlight.id,
            reactedByUsers: reactedByUsersWithNewReaction,
            noNotification: true,
            isEmojiUpdate: true,
          });
        }
      }
    } catch (error) {
      console.error("AN ERROR OCCURED", error);
      setHighlight({
        ...highlight,
        reactedByUsers: reactedByUsers,
      });
    }
  };

  const handleSearch = async (data) => {
    const splitName = data.split(" ");
    const filteredNames = splitName.filter((name) => name !== "");
    const checkThirdName = filteredNames[2]
      ? !filteredNames[2].includes("(")
      : false;
    const fullName = `${filteredNames[0]} ${filteredNames[1]}${
      checkThirdName ? ` ${filteredNames[2]}` : ""
    }`;

    const searchResult = await handleSearch(fullName);
    if (searchResult.length > 0) {
      handleOpenProfile(searchResult[0].id);
    } else {
      notifyError("User profile not found");
    }
  };

  const addUserComment = () => {};

  const handleOpenProfile = (id) => {
    if (id) {
      navigation.navigate("MyProfileScreenStack", {
        screen: "MyProfile",
        params: { userId: id },
      });
    } else {
      navigation.navigate("Login");
    }
  };

  const handleLoginNavigation = () => {
    navigation.navigate("Login");
  };

  const isPostReported = (postId) => {
    // Assuming userInfo.reports contains the list of reported posts
    return userInfo?.reports?.some(
      (report) => report.contendId === postId && report.reportType === "POST"
    );
  };

  return (
    <SafeAreaView style={commonStyles.container}>
      <StatusBar
        barStyle="dark-content"
        hidden={false}
        backgroundColor={Colors.white}
        translucent={false}
      />
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.green} />
        </View>
      ) : (
        <KeyboardAvoidingView
          style={highlight ? { flex: 1 } : {}}
          behavior={Platform.OS === "ios" ? "padding" : null}
          keyboardVerticalOffset={Platform.OS === "ios" ? 64 : 0}
        >
          <ScrollView>
            <View
              style={[
                commonStyles.subContainer,
                { marginTop: HeightSize(4), paddingHorizontal: 0 },
              ]}
            >
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                  alignItems: "center",
                  paddingHorizontal: Size(5),
                }}
              >
                {!editFeedModal && (
                  <TouchableOpacity
                    onPress={() => {
                      // Force cleanup before navigation
                      if (highlight?.type === "VIDEO") {
                        setTimeout(() => {
                          setHighlight(null);
                        }, 100);
                      }
                      route?.params?.fromProfileHighlights
                        ? navigation.navigate("MyProfileScreenStack", {
                            screen: "MyProfile",
                            params: {
                              selectedTab: "highlights",
                              userId: userId,
                            },
                          })
                        : route?.params?.fromShareHighlight
                        ? navigation.navigate("HomeScreenStack", {
                            screen: "Message",
                            params: {
                              recipientId: route?.params?.recipientId,
                              fromMyProfileContent:
                                route?.params?.fromMyProfileContent || false,
                            },
                          })
                        : navigation.goBack();
                    }}
                    activeOpacity={9}
                  >
                    <BackIcon />
                  </TouchableOpacity>
                )}
              </View>

              {editModal ? (
                <ConfirmDelete
                  handleCloseModal={setEditModal}
                  shouldShowModal={editModal}
                  data={highlight}
                  selectedTab={"highlights"}
                />
              ) : (
                ""
              )}
              {editFeedModal ? (
                <EditFeed
                  close={setEditFeedModal}
                  highlightObject={highlight}
                  selectedTab={"highlights"}
                  fromProfileHighlights={route?.params?.fromProfileHighlights}
                />
              ) : (
                ""
              )}

              {highlight && !editFeedModal ? (
                <View key={highlight.id}>
                  <View
                    style={{
                      flexDirection: "row",
                      justifyContent: "space-between",
                      alignItems: "center",
                      paddingHorizontal: 16,
                      paddingBottom: 12,
                    }}
                  >
                    {highlight.type !== "RESULT" && (
                      <TouchableOpacity
                        style={styles.nameImageContainer}
                        onPress={() =>
                          handleOpenProfile(highlight?.user?.id, "id")
                        }
                      >
                        {highlight?.user?.photoUrl ? (
                          <Image
                            source={{ uri: highlight?.user?.photoUrl }}
                            style={styles.photoContainer}
                          />
                        ) : (
                          <Image
                            source={Images.profile1}
                            style={styles.photoContainer}
                          />
                        )}

                        <View>
                          <Text style={styles.nameTxt}>
                            {highlight?.user?.firstName
                              ? `${highlight?.user?.firstName} ${highlight?.user?.lastName}`
                              : highlight?.title}
                          </Text>
                          <Text style={styles.teamNameTxt}>
                            {(highlightId &&
                              highlight?.user?.teamName !== "N/A" &&
                              highlight?.user?.teamName) ||
                              highlight?.user?.clubName}
                          </Text>
                        </View>
                      </TouchableOpacity>
                    )}
                    {highlight?.type !== "RESULT" && !editFeedModal && (
                      <View>
                        {isPostReported(highlight?.id) && (
                          <ReportIcon
                            style={{ position: "absolute", right: Size(8) }}
                          />
                        )}
                        <TouchableOpacity
                          onPress={(event) =>
                            userInfo?.id === highlight?.userId
                              ? openModal()
                              : openProfileBlockReportModal(
                                  event.nativeEvent,
                                  highlight?.userId
                                )
                          }
                          style={{ position: "absolute", right: Size(1) }}
                        >
                          <MenuIcon />
                        </TouchableOpacity>
                        {userInfo?.id === highlight?.userId ? (
                          <Modal
                            animationType="fade"
                            transparent={true}
                            visible={modalVisible}
                            onRequestClose={closeModal}
                          >
                            <TouchableOpacity
                              style={styles.modalContainer}
                              activeOpacity={1}
                              onPressOut={closeModal}
                            >
                              <View style={styles.modalContent}>
                                <TouchableOpacity
                                  style={styles.menuOption}
                                  onPress={handleEditPress}
                                >
                                  <View style={styles.optionContainer}>
                                    <Edit />
                                    <Text style={styles.text}>Edit</Text>
                                  </View>
                                </TouchableOpacity>
                                <TouchableOpacity
                                  style={styles.menuOption}
                                  onPress={handleDeletePress}
                                >
                                  <View style={styles.optionContainer}>
                                    <RedDeleteIcon />
                                    <Text
                                      style={[styles.text, styles.deleteText]}
                                    >
                                      Delete
                                    </Text>
                                  </View>
                                </TouchableOpacity>
                              </View>
                            </TouchableOpacity>
                          </Modal>
                        ) : (
                          <BlockReportModal
                            visible={blockModalVisible}
                            onClose={() => setBlockModalVisible(false)}
                            onBlock={() => handleOpenConfirmation("block")}
                            onReport={() =>
                              handleOpenConfirmation("report", "POST")
                            }
                            top={modalPosition.top}
                            right={modalPosition.right}
                            text={"Post"}
                            showDeleteComment={false}
                          />
                        )}
                      </View>
                    )}
                  </View>
                  {highlight.type === "PHOTO" && (
                    <View style={styles.photoStyle}>
                      <Image
                        source={{
                          uri: highlightId ? highlight.url : highlight.assetUrl,
                        }}
                        style={{
                          backgroundColor: Colors.white,
                          width: "100%",
                          height: Platform.isPad
                            ? HeightSize(80)
                            : HeightSize(60),
                        }}
                      />
                    </View>
                  )}

                  {highlight.type === "RESULT" && (
                    <View style={styles.resultContainer}>
                      <ResultHighlights item={highlight} />
                    </View>
                  )}

                  {highlight.type === "VIDEO" &&
                    (shouldShowWaitIcon(highlight) ? (
                      <LoadingFeedVideo
                        isError={highlight?.videoStatus === "FAILED"}
                      />
                    ) : (
                      <ReactPlayerVideo
                        highlight={highlight}
                        isActive={true}
                        context={videoContext.Detail}
                      />
                    ))}

                  <View
                    style={{
                      paddingHorizontal: Size(5),
                    }}
                  >
                    <FeedHighlightReactions highlight={highlight} />

                    {Boolean(loggedUserId && liveLikeProfileToken) && (
                      <>
                        <View
                          style={{
                            marginTop: Platform.isPad
                              ? HeightSize(4)
                              : HeightSize(1),
                            flexDirection: "row",
                          }}
                        >
                          <TouchableOpacity
                            activeOpacity={9}
                            style={styles.button}
                            onPress={() => {
                              togglePanel();
                            }}
                          >
                            <EmojiIcon
                              width={Platform.isPad ? 42 : 28}
                              height={Platform.isPad ? 42 : 28}
                            />
                            {isPanelOpen && (
                              <View
                                ref={emojiPanelRef}
                                style={styles.emojiPanel}
                              >
                                {LiveLikeReactionPacks?.emojis.map(
                                  (item, idx) => (
                                    <TouchableOpacity
                                      key={idx}
                                      onPress={() =>
                                        addUserReaction(highlight, item)
                                      }
                                      style={styles.emojiButton}
                                    >
                                      {/* <Image source={{ uri: item.file }} style={styles.emojiImagePanel} /> */}
                                      <Image
                                        source={{ uri: item.file }}
                                        style={{
                                          height: Size(5),
                                          width: Size(5),
                                        }}
                                      />
                                    </TouchableOpacity>
                                  )
                                )}
                              </View>
                            )}
                          </TouchableOpacity>
                          {!isPanelOpen && (
                            <TouchableOpacity
                              activeOpacity={9}
                              style={styles.button}
                              onPress={() =>
                                navigation.navigate("ShareFeed", {
                                  highlight: highlight,
                                  type: "highlight",
                                  profile: null,
                                })
                              }
                            >
                              <ShareIcon
                                width={Platform.isPad ? 42 : 28}
                                height={Platform.isPad ? 42 : 28}
                              />
                            </TouchableOpacity>
                          )}
                        </View>

                        {/* Comment */}
                        <Text
                          style={{
                            paddingVertical: 12,
                            fontFamily: "Regular",
                          }}
                        >
                          <Text style={{ fontFamily: "PoppinsBold" }}>
                            {`${highlight?.user?.firstName} ${highlight?.user?.lastName}`}{" "}
                          </Text>
                          {highlight?.comment}
                        </Text>
                      </>
                    )}
                  </View>
                  <CommentBoard commentBoardId={highlight?.commentBoardId} />
                </View>
              ) : null}
            </View>
          </ScrollView>

          {highlight &&
            (loggedUserId ? (
              !editFeedModal && (
                <View style={styles.writeCommentContainer}>
                  <TextInput
                    ref={commentInputRef}
                    style={styles.input}
                    value={comment}
                    onChangeText={(text) => setComment(text)}
                    placeholder={`Add a comment for ${highlight?.user?.firstName}`}
                  />
                  <TouchableOpacity
                    onPress={addUserComment}
                    style={[
                      styles.sendButton,
                      {
                        backgroundColor: sendLoading
                          ? "transparent"
                          : "#14FF00",
                      },
                    ]}
                  >
                    {sendLoading ? <ActivityIndicator /> : <SendMsgIcon />}
                  </TouchableOpacity>
                </View>
              )
            ) : (
              <View style={styles.loginContainer}>
                <TouchableOpacity onPress={handleLoginNavigation}>
                  <Text>Login to view and make comments</Text>
                </TouchableOpacity>
              </View>
            ))}

          {!highlight && !isLoading && <NotFound />}
        </KeyboardAvoidingView>
      )}
    </SafeAreaView>
  );
};

export default FeedDetail;
