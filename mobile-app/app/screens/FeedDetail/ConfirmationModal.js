import React from 'react';
import { Modal, View, Text } from 'react-native';
import { Colors } from "../../res/Colors";
import { HeightSize, Size } from "../../res/Size";
import Button from "../../components/Button/Button";

const ConfirmationModal = ({ 
  visible, 
  onClose, 
  onConfirm, 
  actionType, 
  reportType 
}) => {
  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.confirmationModalContainer}>
        <View style={styles.confirmationModalContent}>
          <Text style={styles.confirmationText}>
            Are you sure you want to{" "}
            <Text style={{ color: Colors.red }}>
              {actionType === "block"
                ? "block"
                : actionType === "report"
                ? "report"
                : actionType === "delete"
                ? "delete"
                : "delete"}
            </Text>{" "}
            {actionType === "block" ? "this user" : "this comment?"}
          </Text>

          <View style={styles.confirmationButtonContainer}>
            <Button
              title="Yes"
              onPress={() => onConfirm(reportType)}
              containerStyle={{ width: "45%" }}
              backgroundColor={Colors.black}
              textStyleInsideButton={{
                fontSize: Size(4),
                fontFamily: "Regular",
              }}
              height={HeightSize(5)}
            />
            <Button
              title="No"
              onPress={onClose}
              containerStyle={{
                borderWidth: 1,
                borderColor: "#A5A5A5",
                width: "45%",
              }}
              backgroundColor={Colors.white}
              textStyleInsideButton={{
                color: "#A5A5A5",
                fontSize: Size(4),
                fontFamily: "Regular",
              }}
              height={HeightSize(5)}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default ConfirmationModal;

const styles = {
  confirmationModalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
  },
  confirmationModalContent: {
    width: '80%',
    padding: 20,
    backgroundColor: Colors.white,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  confirmationText: {
    fontSize: 16,
    fontFamily: 'Regular',
    textAlign: 'center',
    marginBottom: 20,
  },
  confirmationButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
};