import { Platform, StyleSheet } from "react-native";
import { HeightSize, Size } from "../../res/Size";
import { Colors } from "../../res/Colors";

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  video: {
    alignSelf: "center",
    width: <PERSON>ze(92),
    height: HeightSize(50),
    borderRadius: <PERSON>ze(10),
  },
  VideoContainer: {
    //flex: 1,
    justifyContent: "center",
    paddingVertical: HeightSize(2),
    //borderWidth: 1
  },
  playButton: {
    position: "absolute",
    top: "50%",
    left: "50%",
    marginLeft: -20,
    marginTop: -20,
    backgroundColor: "lightgrey",
    width: <PERSON>ze(13),
    height: <PERSON>ze(13),
    borderRadius: <PERSON>ze(13),
    justifyContent: "center",
    alignItems: "center",
  },
  photoContainer: {
    width: Size(17),
    height: <PERSON><PERSON>(17),
    borderRadius: <PERSON>ze(17),
    marginRight: <PERSON>ze(4),
  },
  nameImageContainer: {
    flexDirection: "row",
    // justifyContent: "center",
    alignItems: "center",
    marginTop: HeightSize(2),
    paddingsHorizontal: Size(5),
  },
  nameTxt: {
    fontFamily: "PoppinsBold",
    fontSize: Size(3.7),
    marginRight: Size(5),
  },
  teamNameTxt: {
    fontFamily: "PoppinsMedium",
    fontSize: Size(3.7),
    color: Colors.grey,
  },
  loaderContainer: {
    justifyContent: "center",
    alignItems: "center",
    alignSelf: "center",
  },
  commentBox: {
    flexDirection: "row",
    alignItems: "center",
    marginVertical: HeightSize(3),
    flexWrap: "wrap",
  },
  userReaction: {
    flexDirection: "row",
    alignItems: "center",
  },
  reactionUserPhoto: {
    width: Size(5),
    height: Size(5),
  },
  userNameStyle: {
    flexDirection: "row",
    //alignItems: 'center',
    marginBottom: 5,
    justifyContent: "space-between",
    flex: 1,
  },
  textInputStyle: {
    //borderBottomWidth: 2,
    borderBottomColor: Colors.grey,
    width: Size(80),
    //flex: 1,
    //marginHorizontal: Size(5)
  },
  addComment: {
    width: "90%",
    color: "black",
    height: HeightSize(7),
    // paddingBottom: HeightSize(1),
    flexDirection: "row",
    alignItems: "center",
    marginHorizontal: Size(5),
    borderBottomWidth: 2,
  },
  extraStyle: {
    alignSelf: "left",
    width: Size(90),
    backgroundColor: Colors.white,
  },
  commentCountContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  photoStyle: {
    width: "100%",
    // borderRadius: Size(9),
    overflow: "hidden",
    marginTop: 6,
    aspectRatio: 4 / 5,
  },
  comment: {
    fontWeight: "500",
    opacity: 0.5,
  },
  photo: {
    width: "100%",
    height: "100%",
    resizeMode: "cover",
  },
  resultContainer: {
    width: "100%",
    marginTop: 16,
    borderRadius: 32,
    overflow: "hidden",
  },

  container: {
    position: "relative",
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: Size(5),
  },
  mt2: {
    marginTop: HeightSize(1),
  },
  flexRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  emojiContainer: {
    width: Size(92),
    flexDirection: "row",
    justifyContent: "space-between",
    marginVertical: Size(2),
  },
  emojiImage: {
    height: Size(4.5),
    width: Size(4.5),
  },
  // emojiPanel: {
  //   position: 'absolute',
  //   left: 0,
  //   bottom: Size(14),
  //   backgroundColor: Colors.white,
  //   borderRadius: 10,
  //   paddingVertical: Size(2),
  //  // width: '70%',
  //   flexDirection: "row",
  //  // marginHorizontal: Size(3)
  // },
  // emojiButton: {
  //  // marginHorizontal: -4,
  //   paddingHorizontal: Size(2),
  //   backgroundColor: 'transparent',
  //   borderWidth: 0,
  // },
  emojiPanel: {
    position: "absolute",
    left: Size(9),
    backgroundColor: Colors.lightest_grey,
    borderRadius: Size(3),
    flexDirection: "row",
    paddingVertical: Size(2),
  },
  emojiButton: {
    marginHorizontal: Size(3),
    backgroundColor: "transparent",
    border: "none",
  },
  emoji: {
    borderRadius: 26,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#D9D9D9",
    height: Size(6.5),
    width: Size(6.5),
    // marginRight: -12,
  },
  emojiText: {
    fontSize: Size(3.5),
    color: "#808080",
    //marginLeft: Size(0.8),
    textAlign: "center",
    fontFamily: "PoppinsMedium",
  },
  // emojiImagePanel: {
  //   height: Size(7),
  //   width: Size(7),
  //   shadowColor: '#000',
  //   shadowOpacity: 0.1,
  //   shadowRadius: 10,
  //   borderRadius: Size(7),
  //   //borderWidth: 1,
  //   marginHorizontal: Size(2)
  // },
  emojiImagePanel: {
    position: "absolute",
    left: Size(14),
    backgroundColor: Colors.lightest_grey,
    borderRadius: Size(3),
    flexDirection: "row",
    paddingVertical: Size(2),
  },
  reactionsCount: {
    fontSize: 16,
    color: "#808080",
    marginLeft: 24,
    alignSelf: "center",
    textAlign: "center",
  },
  commentsCount: {
    fontSize: 16,
    color: "#808080",
  },
  fontFavela: {
    fontFamily: "Regular",
  },

  commentContentContainer: {
    flexDirection: "column",
    marginTop: HeightSize(2),
    marginBottom: HeightSize(3),
    paddingHorizontal: Size(5),
  },
  commentContainer: {
    flexDirection: "row",
    flex: 1,
    //flexWrap: 'wrap',
    paddingVertical: Size(4),
    borderBottomWidth: 1,
    borderBottomColor: "#e6e6e6",
  },
  userContainer: {
    flex: 1,
  },
  userName: {
    fontFamily: "PoppinsSemiBold",
    fontSize: Size(4),
    lineHeight: Size(5),
    maxWidth: Size(50),
    //flex: 1,
    // textDecorationLine: 'underline',
  },
  commentText: {
    // fontStyle: 'italic',
    fontFamily: "Regular",
    fontSize: Size(3.7),
  },

  writeCommentContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    // paddingHorizontal: Size(5),
    width: "90%",
    borderBottomWidth: 2,
    borderBottomColor: "#666",
    alignSelf: "center",
    //marginHorizontal: Size(3)
    marginBottom: 10,
  },
  input: {
    paddingTop: HeightSize(2),
    paddingBottom: HeightSize(1),
    width: "80%",
    color: "#333",
  },
  sendButton: {
    width: Size(4),
    height: Size(4),
    borderRadius: Size(4),
    //backgroundColor: '#14FF00',
    justifyContent: "center",
    alignItems: "center",
    position: "absolute",
    right: Size(3),
    top: Platform.OS === "ios" ? Size(4) : Size(6),
  },
  loginContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  menuButton: {
    // paddingRight: 10,
  },
  modalContainer: {
    flex: 1,
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  modalContent: {
    backgroundColor: "white",
    borderRadius: 10,
    padding: 20,
    minWidth: 200,
    position: "absolute",
    top: HeightSize(9),
    right: Size(5),
  },
  menuOption: {
    paddingVertical: 10,
  },
  optionContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  text: {
    marginLeft: 10,
    fontSize: 16,
  },
  deleteText: {
    color: "red",
  },
  userPhoto: {
    width: 36,
    height: 36,
    borderRadius: Size(7.5),
    marginRight: Size(3),
    borderWidth: 1,
    borderColor: Colors.black,
  },
  confirmationModalContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.1)",
  },
  confirmationModalContent: {
    backgroundColor: "white",
    paddingHorizontal: Size(4.5),
    borderRadius: Size(9),
    marginHorizontal: Size(5),
    alignItems: "center",
    paddingVertical: HeightSize(2),
    width: "80%",
  },
  confirmationText: {
    marginBottom: HeightSize(2),
    fontSize: Size(5),
    textAlign: "center",
    fontFamily: "PoppinsMedium",
  },
  confirmationButtonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
    paddingHorizontal: Size(5),
  },
  button: {
    backgroundColor: "transparent",
    margin: 0,
    paddingRight: Size(2),
  },
  profileImage: {
    width: 45,
    height: 45,
    borderRadius: 22.5,
  },
  emojiDisplay: {
    flexDirection: "row",
    //width: Size(35),
    paddingVertical: HeightSize(0.5),
    alignItems: "center",
    justifyContent: "space-between",
    // marginHorizontal: Size(2.5),
  },
  userNameContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: Size(3),
  },
  timeText: {
    fontFamily: "Regular",
    fontSize: Size(3.5),
    color: "#999999",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: Colors.white,
  },
  deleteButton: {
    height: "100%",
    width: 60,
    alignItems: "center",
    justifyContent: "center",
  },
});

export default styles;
