import { StyleSheet } from "react-native";
import { HeightSize, Size } from "../../res/Size";
import { Colors } from "../../res/Colors";

const styles = StyleSheet.create({
  headerContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  backSerachIconBox: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: Size(5),
  },
  searchContainer: {
    width: "85%",
    color: "black",
    height: HeightSize(5),
    // paddingBottom: HeightSize(1),
    flexDirection: "row",
    alignItems: "center",
    marginHorizontal: Size(5),
    borderBottomWidth: 2,
  },
  textInputStyle: {
    borderBottomColor: Colors.grey,
    width: Size(85),
  },
  subContainer: {
    //flex: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginVertical: HeightSize(1.5),
    paddingHorizontal: Size(5),
    marginRight: Size(4),
    marginTop: 80,
  },
  txtStyle: {
    fontFamily: "PoppinsBold",
    fontSize: Size(4),
  },
  noResultText: {
    textAlign: "center",
    // marginTop: 20,
    fontFamily: "PoppinsBold",
    fontSize: Size(4),
    opacity: 0.5,
  },
});

export default styles;
