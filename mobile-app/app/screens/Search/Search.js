import React, { useCallback, useEffect, useState } from "react";
import {
  ActivityIndicator,
  Platform,
  SafeAreaView,
  ScrollView,
  StatusBar,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import { commonStyles } from "../../res/CommonStyles";
import { Colors } from "../../res/Colors";
import styles from "./SearchStyles";
import { BackIcon } from "../../res/Svg";
import SearchIcon from "../../res/SearchSvgIcon";
import AsyncStorage from "@react-native-async-storage/async-storage";
import SearchCard from "../../components/SearchCard";
import _debounce from "lodash/debounce";
import { HeightSize } from "../../res/Size";
import { dispatch } from "../../redux/store";
import { searchArrayOrMakeCallToAPI } from "../../constants/misc";
import { useSelector } from "react-redux";
import AnalyticsService from "../../services/analytics";

const Search = ({ navigation }) => {
  const [searchResult, setSearchResult] = useState(null);
  const [recentSearch, setRecentSearch] = useState([]);
  const [loading, setLoading] = useState(false);
  const [name, setName] = useState("");
  const [clearAllClicked, setClearAllClicked] = useState(false);
  const [noResults, setNoResults] = useState(false);
  const [blockedUsersList, setBlockedUsersList] = useState([]);

  const gettingUser = useSelector(
    ({ loading }) => loading.effects.user.userSearch
  );
  const { userList, teamsList } = useSelector(({ user, team }) => ({
    userList: user.usersByProjection,
    teamsList: team.teamsByProjection,
  }));

  const getBlockedUsersList = async () => {
    try {
      const blockedUsers = await dispatch.user.fetchBlockedUsersList();
      setBlockedUsersList(blockedUsers);
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    getBlockedUsersList();
  }, []);

  const handleSearch = async (searchTerm) => {
    const searchResponse = await searchArrayOrMakeCallToAPI({
      searchTerm: searchTerm,
      array: [
        ...userList?.map((item) => ({
          ...item,
          type: "user",
          fullname: `${item?.firstName} ${item?.lastName}`,
        })),
      ],
      makeSearchCall: [dispatch.user.userSearch, dispatch.team.teamSearch],
    });

    const sortedResults = searchResponse?.sort((a, b) => {
      if (a.type === "user" && b.type === "team") return 1;
      else if (a.type === "team" && b.type === "user") return -1;
    });

    setSearchResult(sortedResults);

    // ✅ Log the search event in Firebase Analytics
    await analytics.logSearchPerformed(searchTerm, sortedResults?.length || 0);
  };

  const clearAllRecentSearch = async () => {
    try {
      await AsyncStorage.removeItem("searchResult");
      setRecentSearch([]);
      setClearAllClicked(true);
    } catch (error) {
      console.error("Error clearing recent searches:", error);
    }
  };

  const removeFromRecent = async (id) => {
    try {
      const recentSearchString = await AsyncStorage.getItem("searchResult");
      let recentSearch = JSON.parse(recentSearchString) || [];
      const newRecent = recentSearch.filter((item) => item.id !== id);
      await AsyncStorage.setItem("searchResult", JSON.stringify(newRecent));
      setRecentSearch(newRecent);
    } catch (error) {
      console.error("Error removing from recent search:", error);
    }
  };

  const fetchSearchData = useCallback(_debounce(handleSearch, 400), [
    userList,
    teamsList,
  ]);

  const fetchAllUserData = async () => {
    await dispatch.user.userSearchByProjection();
  };

  const fetchAllTeamData = async () => {
    const res = await dispatch.team.getTeamsByProjection();
    // setTeamsList(res?.Items);
  };

  const fetchUserDetails = async (users) => {
    try {
      const updatedUsers = await Promise.all(
        users.map(async (user) => {
          const response = await dispatch.user.fetchUserDetails(user.id);

          if (response) {
            // Update only specific fields if response is successful
            return {
              ...user, // Retain all original properties of the user
              firstName: response.firstName || user.firstName,
              lastName: response.lastName || user.lastName,
              teamName: response.teamName || user.teamName,
              clubName: response.clubName || user.clubName,
              photoUrl: response.photoUrl || user.photoUrl,
            };
          }

          return user; // Fallback to original user if no response or error
        })
      );

      return updatedUsers; // Store this in state if needed
    } catch (error) {
      console.error("Error fetching user details:", error);
    }
  };

  useEffect(() => {
    const loadRecentSearches = async () => {
      try {
        const savedSearch = await AsyncStorage.getItem("searchResult");
        if (savedSearch) {
          const parsedSearch = JSON.parse(savedSearch);

          if (Array.isArray(parsedSearch) && parsedSearch.length > 0) {
            const updatedUsers = await fetchUserDetails(parsedSearch);
            setRecentSearch(updatedUsers); // Only set state once with updated data
          }
        }
      } catch (error) {
        console.error("Error loading recent searches:", error);
      }
    };
    loadRecentSearches();
  }, []);

  useEffect(() => {
    fetchAllUserData();
  }, []);

  useEffect(() => {
    fetchAllTeamData();
  }, []);

  return (
    <SafeAreaView style={commonStyles.container}>
      <StatusBar
        barStyle="dark-content"
        hidden={false}
        backgroundColor={Colors.white}
        translucent={false}
      />
      <View style={{ position: "relative" }}>
        <View
          //style={[styles.backSerachIconBox, {backgroundColor: Colors.green}]}
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            backgroundColor: "white",
            padding: 20,
            alignItems: "center",
            zIndex: 100,
            flexDirection: "row",
            height: 70,
          }}
        >
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <BackIcon />
          </TouchableOpacity>
          <View style={styles.searchContainer}>
            <TouchableOpacity>
              <SearchIcon green />
            </TouchableOpacity>
            <TextInput
              style={styles.textInputStyle}
              placeholder="Search"
              placeholderTextColor={Colors.light_grey}
              onChangeText={(text) => fetchSearchData(text)}
              autoComplete="off"
              autoCorrect={false}
              spellCheck={false}
            />
          </View>
        </View>
        <View style={styles.subContainer}>
          {!name && (!searchResult || searchResult.length === 0) ? (
            <>
              <TouchableOpacity onPress={() => setClearAllClicked(false)}>
                <Text style={styles.txtStyle}>Recents</Text>
              </TouchableOpacity>
              <TouchableOpacity onPress={clearAllRecentSearch}>
                <Text
                  style={[
                    styles.txtStyle,
                    {
                      color: Colors.black,
                      textDecorationLine: "underline",
                      opacity: clearAllClicked ? 1 : 0.3,
                    },
                  ]}
                >
                  Clear All
                </Text>
              </TouchableOpacity>
            </>
          ) : null}
        </View>
        <ScrollView
          style={{
            paddingBottom: HeightSize(20),
          }}
        >
          {!gettingUser && searchResult
            ? searchResult.map((result) => (
                <SearchCard
                  isRecent={false}
                  removeFromRecent={removeFromRecent}
                  key={result.id}
                  data={result}
                  blockedUsersList={blockedUsersList}
                />
              ))
            : ""}
          {!searchResult &&
            !gettingUser &&
            recentSearch.map((result) => (
              <SearchCard
                removeFromRecent={removeFromRecent}
                key={result.id}
                data={result}
                blockedUsersList={blockedUsersList}
              />
            ))}
        </ScrollView>
      </View>
    </SafeAreaView>
  );
};

export default Search;
