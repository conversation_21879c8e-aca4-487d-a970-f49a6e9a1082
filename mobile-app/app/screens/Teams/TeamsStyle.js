import { StyleSheet } from "react-native";
import { Colors } from "../../res/Colors";
import { HeightSize, Size } from "../../res/Size";

const styles = StyleSheet.create({
container: {
  flex: 1,
  backgroundColor: Colors.white,
},
modalContainer: {
  flex: 1,
  justifyContent: "center",
  alignItems: "center",
  backgroundColor: Colors.black,
},
modalContent: {
  backgroundColor: "white",
  paddingVertical: HeightSize(1),
  borderRadius: Size(5),
  elevation: 5,
  width: "80%",
 // marginTop: HeightSize(40),
  alignSelf: "center",
  paddingHorizontal: Size(2),
  marginBottom: HeightSize(5),
  paddingVertical: HeightSize(3)
},
modalText: {
  fontFamily: "PoppinsBold",
  textAlign: "center",
  fontSize: Size(3.8),
  lineHeight: Size(7)
},
containerStyle: {
  width: "50%",
  height: HeightSize(7),
  marginTop: HeightSize(3)
},
textStyleInsideButton: {
  fontFamily: "Regular",
  fontSize: Size(4.2)
}
});

export default styles;