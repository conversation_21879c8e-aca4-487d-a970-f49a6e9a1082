import React, { useEffect, useState } from 'react';
import { Modal, Text, View } from 'react-native';
import styles from './TeamsStyle';
import Tabs from '../../components/Tabs';
import Button from '../../components/Button/Button';
import { Colors } from '../../res/Colors';
import Client from '../../res/Client';
import { getUserData } from '../../constants/misc';
import { commonStyles } from '../../res/CommonStyles';

const MyTeam = ({navigation}) => {
  // const [selected, setSelected] = useState("results");
  // const [currentTeamId, setCurrentTeamId] = useState("");
  // const [currentClubId, setCurrentClubId] = useState("");

  // useEffect(() => {
  //   const fetchData = async () => {
  //     try {
  //       const data = await getUserData();
  //       //console.log("teams data", data.userData.teamId);
  //       setCurrentTeamId(data.userData.teamId);
  //       setCurrentClubId(data.userData.cludId);
  //       //setUserData(data);
  //     } catch (error) {
  //       console.error("Error fetching user data:", error);
  //     }
  //   };

  //   fetchData();
  //   getSingleTeam();
  // }, []);

  // const getFixtures = async () => {
  //   try{
  //     const response = await Client.get(`matches/fixtures?teamId=${currentTeamId}`, { contentType: 'application/json' });
  //     console.log("response pof fixture", response.data);
  //   }
  //   catch (err) {
  //     console.error(err);
  //   }
  // };

  // const getMatches = async (seasonId) => {
  //   try{
  //     const response = await Client.get(`matches/by-season-team?teamId=${currentTeamId}${
  //     seasonId ? `&seasonId=${seasonId}` : "" }`, { contentType: 'application/json' });
  //     console.log("response pof result", response.data);
  //   }
  //   catch(err) {
  //     console.error(err);
  //   }

  // };

  // const getSingleTeam = async () => {
  //   try{
  //     const response = await Client.get(`teams/${currentTeamId}?clubId=${currentClubId}`, { contentType: 'application/json' });
  //     //console.log("single team", response.data.data.Items);
  //   }
  //   catch (err) {
  //     console.error(err);
  //   }
  // };

  // const handleTabChange = async (tab) => {
  //   setSelected(tab);
  //   if (tab === "fixtures") {
  //     await getFixtures();
  //   } else if (tab === "results") {
  //     await getMatches();
  //   }
  // };

  // return (
  //   <View style={styles.container}>

  //     <Button
  //     title={"PLAYERS AND STATS"}
  //     backgroundColor={Colors.black}
  //     />
  //     <Button
  //     title={"LEAGUE AND STATS"}
  //     backgroundColor={Colors.black}
  //     />
  //     <Button
  //     title={"HIGHLIGHTS"}
  //     backgroundColor={Colors.green}
  //     textStyleInsideButton={{color: Colors.black}}
  //     />
  //     <Tabs
  //       tabs={["results", "fixtures"]}
  //       selected={selected}
  //       setSelected={setSelected}
  //       color="black"
  //     />
  //   </View>
  // )
  return (
    // <Modal
    //     animationType="slide"
    //     transparent={true}
    //     visible={true}
    //   >
    <View style={styles.modalContainer}>
      <View style={styles.modalContent}>
       <Text style={styles.modalText}>
        COMING SOON. 
       </Text>
       <Text style={styles.modalText}>THE TEAM & CLUB FEATURE IS NOT AVAILABLE YET. 
        </Text>
        <Text style={styles.modalText}>SET TO LAUNCH IN 2025.</Text>
        <Button
        title={"Go Back"}
        backgroundColor={Colors.black}
        containerStyle={styles.containerStyle}
        textStyleInsideButton={styles.textStyleInsideButton}
        onPress={()=> navigation.navigate("HomeScreenStack",{screen: "Feed"})}
        />
      </View>
    </View>
    // </Modal>
  )
}

export default MyTeam;