import { Platform, StyleSheet } from "react-native";
import { HeightSize, Size } from "../../res/Size";
import { Colors } from "../../res/Colors";

const styles = StyleSheet.create({
  logoContainer: {
    paddingVertical: Platform.isPad ? HeightSize(2) : HeightSize(4)
  },
  formText: {
    paddingVertical: Platform.isPad ? HeightSize(2) : HeightSize(4),
    // textDecorationLine: "underline"
  },
  radiobtnStyle: {
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: HeightSize(5)
  },
  textLink: {
    textDecorationLine: "underline"
  },
  skipTxt: {
    textAlign: "right",
    textDecorationLine: "underline",
    fontSize: Size(4.3),
    marginRight: Size(9),
    marginVertical: HeightSize(4),
    marginTop : Platform.OS === "ios" ? HeightSize(7) : HeightSize(2)
  },
  bottomText: {
    textAlign: "center",
    fontFamily: "Regular",
    fontSize: Size(3.5),
    color: Colors.grey
  }
})

export default styles;