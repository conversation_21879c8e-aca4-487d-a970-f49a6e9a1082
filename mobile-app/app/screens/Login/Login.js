// REACT //
import { useState, useEffect } from "react";

// REACT NATIVE //
import { StatusBar, Text, View, BackHandler, Platform } from "react-native";

// PLUGINS //
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";

// COMPONENTS //
import CustomTextInput from "../../components/TextInput";
import Button from "../../components/Button/Button";
import ForgotPassword from "../../components/ForgotPassword/ForgotPasswordPopup";

// CONSTANTS //
import { notifyError } from "../../constants/misc";

// OTHERS //
import { commonStyles } from "../../res/CommonStyles";
import { Colors } from "../../res/Colors";
import { LogoImg } from "../../res/Svg";
import styles from "./LoginStyles";
import { HeightSize, Size } from "../../res/Size";
import { dispatch } from "../../redux/store";

const Login = ({ navigation }) => {
  //const [remember, setRemember] = useState(false);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [errorMessage, setErrorMessage] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const handleBackButton = () => true; // Prevent going back
    const backHandler = BackHandler.addEventListener(
      "hardwareBackPress",
      handleBackButton
    );

    return () => {
      backHandler.remove();
    };
  }, []);

  // useEffect(() => {
  //   // Load remembered email on component mount
  //   const loadRememberedEmail = async () => {
  //     try {
  //       const rememberedEmail = await AsyncStorage.getItem("rememberedEmail");
  //       if (rememberedEmail) {
  //         setEmail(rememberedEmail);
  //         setRemember(true); // Automatically check the "Remember me" option
  //       }
  //     } catch (error) {
  //       console.error("Error loading remembered email:", error);
  //     }
  //   };

  //   loadRememberedEmail();
  // }, []);

  const emailRegex = /^[\w.+-]+@([\w-]+\.)+[\w-]{2,}$/;
  const passwordRegex = /^[\s\S]{8,32}$/;

  const validation = () => {
    if (
      email === "" ||
      !emailRegex.test(email) ||
      password === "" ||
      !passwordRegex.test(password)
    ) {
      return true;
    }
    return false;
  };

  const logIn = async () => {
    setLoading(true);

    if (!validation()) {
      try {
        const values = { email, password };

        await dispatch.auth.userLogin({
          values,
          navigation,
          from: "BottomStack",
        });
      } catch (err) {
        console.error("Error during login:", err);

        if (
          err.message === "Network Error" ||
          err.message?.includes("timeout")
        ) {
          notifyError(
            "Unable to connect. Please check your internet connection and try again."
          );
        } else {
          notifyError("Incorrect Username Or Password");
        }
      } finally {
        setLoading(false);
      }
    } else {
      setErrorMessage(true);
      setLoading(false);
    }
  };

  const handleForgotPassword = () => {
    dispatch.auth.forgotPasswordModalBox({ visible: true });
  };

  const openPrivacyPolicy = () => {
    navigation.navigate("PrivacyPolicyWebView", {
      url: "https://join.playerapp.co/privacypolicy",
    });
  };

  const openTermsAndConditions = () => {
    navigation.navigate("PrivacyPolicyWebView", {
      url: "https://join.playerapp.co/tcs",
    });
  };

  return (
    <View style={commonStyles.container}>
      <StatusBar
        barStyle="dark-content"
        hidden={false}
        backgroundColor={Colors.white}
        translucent={false}
      />
      <KeyboardAwareScrollView
        enableOnAndroid
        extraHeight={HeightSize(30)}
        keyboardShouldPersistTaps="always"
      >
        <Text
          style={styles.skipTxt}
          onPress={() => navigation.navigate("Feed")}
        >
          Skip
        </Text>
        <View style={[commonStyles.subContainer, { marginTop: HeightSize(2) }]}>
          <View style={styles.logoContainer}>
            <LogoImg width={Platform.isPad ? Size(20) : Size(34)} />
          </View>
          <Text style={commonStyles.boldText}>LOG IN</Text>
          <CustomTextInput
            label={"Email"}
            textInputStyle={commonStyles.textInputStyle}
            onChangeText={(e) => {
              setEmail(e);
              setErrorMessage(false);
            }}
            value={email}
            errorMsg={
              email !== "" && !emailRegex.test(email) && errorMessage
                ? "Enter valid email"
                : email === "" && errorMessage
                ? "Please Enter Email"
                : ""
            }
          />
          <CustomTextInput
            label={"Password"}
            textInputStyle={[
              commonStyles.textInputStyle,
              { borderBottomWidth: 0 },
            ]}
            style={{ marginTop: HeightSize(5) }}
            onChangeText={(e) => {
              setPassword(e);
              setErrorMessage(false);
            }}
            value={password}
            secureTextEntry={true} // Enable password visibility toggle
            errorMsg={
              password !== "" && !passwordRegex.test(password) && errorMessage
                ? "Incorrect Password"
                : password === "" && errorMessage
                ? "Please Enter Password"
                : ""
            }
            inputContainer={{ borderBottomWidth: 1 }}
          />
        </View>
        <View style={{ marginTop: HeightSize(7), flex: 1, width: "100%" }}>
          <Button
            title={"LOG IN"}
            onPress={logIn}
            progress={loading}
            disable={loading}
            backgroundColor={Colors.black}
          />
          <Text
            style={[
              styles.textLink,
              commonStyles.regularText,
              { textAlign: "center", paddingVertical: HeightSize(1) },
            ]}
            onPress={handleForgotPassword}
          >
            Forgotten your password?
          </Text>
        </View>
        <ForgotPassword />
        <View style={{ marginHorizontal: Size(7), marginTop: HeightSize(5) }}>
          <Text style={styles.bottomText}>
            By logging in and using PLAYER, you agree to the
            <Text
              onPress={openTermsAndConditions}
              style={[styles.textLink, { color: Colors.black }]}
            >
              {" "}
              Terms & Conditions{" "}
            </Text>
            and
            <Text
              onPress={openPrivacyPolicy}
              style={[styles.textLink, { color: Colors.black }]}
            >
              {" "}
              Privacy Policy
            </Text>
            .
          </Text>
        </View>
      </KeyboardAwareScrollView>
    </View>
  );
};

export default Login;
