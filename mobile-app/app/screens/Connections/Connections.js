import { useFocusEffect, useNavigation } from "@react-navigation/native";
import React, { useEffect, useState } from "react";
import { ScrollView, Text, TouchableOpacity, View } from "react-native";
import { liveLikeClientId, notifySuccess } from "../../constants/misc";
import Tabs from "../../components/Tabs";
import styles from "./ConnectionsStyles";
import ConnectionCards from "../../components/ConnectionCards/ConnectionCards";
import { useSelector, shallowEqual } from "react-redux";
import { dispatch } from "../../redux/store";
import { WhiteBackIcon } from "../../res/Svg";
import { selectUserInfo } from "../../redux/selectors/authSelectors";
import { useMemo } from "react";
import { useAppStateEffect } from "../../hooks/useAppStateEffect";
import { useCallback } from "react";
import FollowApi from "../../services/FollowApi";

const Connections = ({ route, navigation }) => {
  const type = route.params.type || "";
  const data = route.params.data || {};

  const [selectedTab, setSelectedTab] = useState(type);
  const followers = useMemo(() => data.followers || [], [data.followers]);
  const followersCount = useMemo(
    () => data.followersCount || 0,
    [data.followersCount]
  );
  const [following, setFollowing] = useState(data.following || []);
  const [followingCount, setFollowingCount] = useState(
    data.followingCount || 0
  );
  const [loggedInUserFollowing, setLoggedInUserFollowing] = useState([]);

  const userInfo = useSelector(selectUserInfo, shallowEqual);

  const isOwnConnectionsScreen = useMemo(
    () => data.userId === userInfo?.id,
    [data.userId, userInfo?.id]
  );

  const getLoggedInUserFollowing = async () => {
    const { data } = await UserApi.getFollowing(userInfo.id);
    setLoggedInUserFollowing(data.results);
  };

  const fetchLoggedInUserFollowers = useCallback(() => {
    if (!isOwnConnectionsScreen && userInfo?.id) {
      getLoggedInUserFollowing();
    }
  }, [isOwnConnectionsScreen, userInfo?.id]);
  useFocusEffect(fetchLoggedInUserFollowers);
  useAppStateEffect({
    onForeground: () => {
      fetchLoggedInUserFollowers();
    },
  });

  const follow = async (followingUserId) => {
    try {
      const { data } = await FollowApi.follow(followingUserId);

      setFollowing((prev) => [...prev, data.followedUser]);
      setFollowingCount((prev) => prev + 1);

      notifySuccess(
        `You followed ${data.followedUser.firstName}${
          data.followedUser.lastName ? ` ${data.followedUser.lastName}` : ""
        }`
      );
    } catch (error) {
      console.error("Error following:", error);
    }
  };

  const unfollow = async (unfollowingUserId) => {
    try {
      const { data } = await FollowApi.unfollow(unfollowingUserId);

      setFollowing((prev) =>
        prev.filter((follower) => follower.id !== unfollowingUserId)
      );
      setFollowingCount((prev) => Math.max(prev - 1, 0));

      notifySuccess(
        `You unfollowed ${data.unfollowedUser.firstName}${
          data.unfollowedUser.lastName ? ` ${data.unfollowedUser.lastName}` : ""
        }`
      );
    } catch (error) {
      console.error("Error unfollowing:", error);
    }
  };

  return (
    <View style={styles.page}>
      <ScrollView contentContainerStyle={styles.contentContainer}>
        <View style={styles.headerContainer}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <WhiteBackIcon />
          </TouchableOpacity>
          <View
            style={{ flex: 1, justifyContent: "center", alignItems: "center" }}
          >
            <Text style={styles.nameText}>{data?.fullName}</Text>
          </View>
        </View>
        <Tabs
          isConnections={true}
          tabs={[
            { connectionCount: followersCount, title: "followers" },
            { connectionCount: followingCount, title: "following" },
          ]}
          selected={selectedTab}
          setSelected={setSelectedTab}
          color="white"
          inactiveColor="lightgray"
        />
        <View style={styles.connectionList}>
          <View style={styles.connectionColumn}>
            {selectedTab.title === "followers" ? (
              followers.length > 0 ? (
                followers.map((follower) => {
                  const isProfileFollowingLoggedUser =
                    follower.id === userInfo.id;

                  const isLoggedUserFollowingProfile = Boolean(
                    (isOwnConnectionsScreen
                      ? following
                      : loggedInUserFollowing
                    ).find((f) => f.id === follower.id)
                  );

                  return (
                    <View key={follower.liveLikeProfileId}>
                      <ConnectionCards
                        connection={follower}
                        follow={follow}
                        unfollow={unfollow}
                        isProfileFollowingLoggedUser={
                          isProfileFollowingLoggedUser
                        }
                        isLoggedUserFollowingProfile={
                          isLoggedUserFollowingProfile
                        }
                        isOwnConnectionsScreen={isOwnConnectionsScreen}
                      />
                    </View>
                  );
                })
              ) : (
                <Text style={styles.noConnectionsText}>
                  No one is following {route.params.data?.fullName}
                </Text>
              )
            ) : following?.length > 0 ? (
              following.map((followingUser) => {
                const isProfileFollowingLoggedUser =
                  followingUser.id === userInfo.id;

                const isLoggedUserFollowingProfile = Boolean(
                  (isOwnConnectionsScreen
                    ? following
                    : loggedInUserFollowing
                  ).find((f) => f.id === followingUser.id)
                );

                return (
                  <View key={followingUser.liveLikeProfileId}>
                    <ConnectionCards
                      connection={followingUser}
                      follow={follow}
                      unfollow={unfollow}
                      isProfileFollowingLoggedUser={
                        isProfileFollowingLoggedUser
                      }
                      isLoggedUserFollowingProfile={
                        isLoggedUserFollowingProfile
                      }
                      isOwnConnectionsScreen={isOwnConnectionsScreen}
                    />
                  </View>
                );
              })
            ) : (
              <Text style={styles.noConnectionsText}>
                {route.params.data?.fullName} is not following anyone
              </Text>
            )}
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

export default Connections;
