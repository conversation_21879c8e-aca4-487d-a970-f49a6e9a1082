import { Platform, StyleSheet } from "react-native";
import { HeightSize, Size } from "../../res/Size";
import { Colors } from "../../res/Colors";

const styles = StyleSheet.create({
  page: {
    flex: 1,
    backgroundColor: 'black',
  },
  headerContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingVertical: Platform.OS === "ios" ? HeightSize(4) : HeightSize(2),
    alignItems: "center"
  },
  headerContent: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 10,
    paddingHorizontal: 15,
  },
  headerTitle: {
    color: 'white',
    fontSize: Size(4.7),
  },
  contentContainer: {
    paddingHorizontal: Size(5),
    marginTop: HeightSize(1),
  },
  connectionList: {
    //marginTop: HeightSize(2),
  },
  connectionColumn: {
    flexDirection: 'column',
    gap: HeightSize(2),
  },
  noConnectionsText: {
    color: 'white',
    textAlign: 'center',
  },
  nameText: {
    fontFamily: "PoppinsMedium",
    color: Colors.white,
    fontSize: Size(4)
  }
});

export default styles;