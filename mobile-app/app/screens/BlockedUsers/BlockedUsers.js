import React, { useEffect, useState } from "react";
import {
  FlatList,
  Image,
  SafeAreaView,
  StatusBar,
  Text,
  TouchableOpacity,
  View,
  ActivityIndicator,
  Platform,
  Modal,
} from "react-native";
import { BackIcon, UnblockText } from "../../res/Svg";
import { commonStyles } from "../../res/CommonStyles";
import { Colors } from "../../res/Colors";
import { HeightSize, Size } from "../../res/Size";
import styles from "./BlockedUsersStyles";
import { dispatch } from "../../redux/store";
import { Images } from "../../res/Images";
import Button from "../../components/Button/Button";
import { useSelector } from "react-redux";

const BlockedUsers = ({ navigation }) => {
  const [blockedUsersList, setBlockedUsersList] = useState([]);
  const [loading, setLoading] = useState(true); // New loading state
  const [confirmationVisible, setConfirmationVisible] = useState(false);
  const [selectedUserToUnblock, setSelectedUserToUnblock] = useState(null); // New state to track the selected user

  const { userInfo } = useSelector((state) => state.auth.authUser);

  useEffect(() => {
    getBlockedUsersList();
  }, []);

  const getBlockedUsersList = async () => {
    try {
      const blockedUsers = await dispatch.user.fetchBlockedUsersList();
      setBlockedUsersList(blockedUsers);
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false); // Stop loading once data is fetched
    }
  };

  const handleUnblock = async (blockedUserId) => {
    try {
      await dispatch.user.unblockUser({ blockedUserId });

      // Update the blocked users list after unblocking
      setBlockedUsersList((prevList) =>
        prevList.filter((user) => user.id !== blockedUserId)
      );

      // Fetch and update the userInfo after unblocking
      await dispatch.user.fetchUserDetails(userInfo.id);
    } catch (error) {
      console.error("Failed to unblock user:", error);
    }
  };

  const handleConfirmAction = () => {
    if (selectedUserToUnblock) {
      handleUnblock(selectedUserToUnblock);
      setConfirmationVisible(false);
      setSelectedUserToUnblock(null); // Reset selected user
    }
  };

  const renderBlockerUsersList = ({ item }) => {
    return (
      <View style={styles.listContainer}>
        {item?.photoUrl !== "" ? (
          <Image
            source={{ uri: item?.photoUrl }}
            style={{
              width: Size(15),
              height: Size(15),
              borderRadius: Size(7.5),
            }}
          />
        ) : (
          <Image
            source={Images.profile1}
            style={{
              width: Size(15),
              height: Size(15),
              borderRadius: Size(7.5),
            }}
          />
        )}
        <View style={{ flex: 1, marginLeft: Size(3) }}>
          <Text style={styles.nameText}>
            @{item.firstName + " " + item.lastName}
          </Text>
        </View>
        <Button
          title={"Unblock"}
          onPress={() => {
            setSelectedUserToUnblock(item.id); // Set the selected user
            setConfirmationVisible(true);
          }}
          backgroundColor={Colors.black}
          containerStyle={styles.btnContainerStyle}
          textStyleInsideButton={styles.btnText}
        />
        {/* Confirmation Modal */}
        <Modal
          visible={confirmationVisible}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setConfirmationVisible(false)}
        >
          <View style={styles.confirmationModalContainer}>
            <View style={styles.confirmationModalContent}>
              <Text style={styles.confirmationText}>
                Are you sure you want to{" "}
                <Text style={{ color: Colors.red }}>unblock</Text> this user?
              </Text>

              <Text style={styles.subText}>
                {" "}
                If you unblock this user, they will be able to view your posts
                and interact with you again. They will not be notified that you
                unblocked them.
              </Text>
              <View style={styles.confirmationButtonContainer}>
                <Button
                  title="Unblock"
                  onPress={handleConfirmAction} // Unblock the selected user
                  containerStyle={{ width: "45%" }}
                  backgroundColor={Colors.black}
                  textStyleInsideButton={{
                    fontSize: Size(4),
                    fontFamily: "Regular",
                  }}
                  height={HeightSize(5)}
                />
                <Button
                  title="Cancel"
                  onPress={() => {
                    setConfirmationVisible(false);
                    setSelectedUserToUnblock(null); // Reset selected user
                  }}
                  containerStyle={{
                    borderWidth: 1,
                    borderColor: "#A5A5A5",
                    width: "45%",
                  }}
                  backgroundColor={Colors.white}
                  textStyleInsideButton={{
                    color: "#A5A5A5",
                    fontSize: Size(4),
                    fontFamily: "Regular",
                  }}
                  height={HeightSize(5)}
                />
              </View>
            </View>
          </View>
        </Modal>
      </View>
    );
  };

  return (
    <SafeAreaView style={commonStyles.container}>
      <StatusBar
        barStyle="dark-content"
        hidden={false}
        backgroundColor={Colors.white}
        translucent={false}
      />
      <View style={[commonStyles.subContainer, { marginTop: HeightSize(4) }]}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          activeOpacity={9}
          style={styles.backBtn}
        >
          <BackIcon
            width={Platform.isPad ? 40 : 24}
            height={Platform.isPad ? 40 : 24}
          />
        </TouchableOpacity>
        <Text style={commonStyles.boldText}>BLOCKED USERS</Text>
      </View>

      {loading ? (
        <ActivityIndicator
          size="large"
          color={Colors.black}
          style={{ marginTop: HeightSize(7) }}
        />
      ) : (
        <FlatList
          data={blockedUsersList}
          renderItem={renderBlockerUsersList}
          keyExtractor={(item) => item.id}
          contentContainerStyle={{ marginTop: HeightSize(4) }}
          ListEmptyComponent={() => (
            <View
              style={{
                justifyContent: "center",
                alignItems: "center",
                flex: 1,
                paddingHorizontal: Size(7),
                marginTop: HeightSize(6),
              }}
            >
              <Text
                style={{
                  fontSize: Size(6),
                  textAlign: "center",
                  fontFamily: "Regular",
                  width: "75%",
                }}
              >
                You haven’t blocked any users.
              </Text>
              <Text
                style={{
                  fontSize: Size(5),
                  textAlign: "center",
                  fontFamily: "Regular",
                  marginTop: HeightSize(2),
                  color: "#A5A5A5",
                }}
              >
                If you do block a user in the future, you can always come back
                here and unblock them again.
              </Text>
            </View>
          )}
        />
      )}
    </SafeAreaView>
  );
};

export default BlockedUsers;
