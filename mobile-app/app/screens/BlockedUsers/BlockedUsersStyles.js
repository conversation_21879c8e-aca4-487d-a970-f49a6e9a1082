import { StyleSheet } from "react-native";
import { HeightSize, Size } from "../../res/Size";

const styles = StyleSheet.create({
  backBtn: {
    marginBottom: HeightSize(3)
  },
  errorText: {
    color: 'red',
    textAlign: 'center',
    marginTop: HeightSize(4),
  },
  listContainer: {
    flex: 1,
    flexDirection: "row",
    paddingVertical: HeightSize(1.5), 
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: Size(5)
  },
  nameText: {
    fontFamily: "PoppinsBold",
    fontSize: Size(4),
    paddingRight: Size(5)
  },
  btnContainerStyle: {
    width : Size(25),
    height : HeightSize(5),
    borderRadius: Size(4)
  },
  btnText: {
    fontFamily: "Regular",
    fontSize: Size(3.7)
  },
  confirmationModalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
  },
  confirmationModalContent: {
    backgroundColor: 'white',
    paddingHorizontal: <PERSON>ze(4.5),
    borderRadius: Size(9),
    marginHorizontal: Size(5),
    alignItems: 'center',
    paddingVertical: HeightSize(4),
    width: "90%",
    marginBottom: HeightSize(20)
  },
  confirmationText: {
    marginBottom: HeightSize(2),
    fontSize: Size(5),
    textAlign: 'center',
    fontFamily: 'PoppinsMedium'
  },
  confirmationButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: "100%",
    paddingHorizontal: Size(5),
    marginTop: HeightSize(3)
  },
  subText: { 
    fontSize: Size(4.5), 
    textAlign: 'center', 
    fontFamily: "Regular", 
    color: "#A5A5A5", 
    marginTop: HeightSize(1), 
    paddingHorizontal: Size(2),
    lineHeight: Size(8)
  }
});

export default styles;