import { StyleSheet } from "react-native";
import { Colors } from "../../res/Colors";
import { HeightSize, Size } from "../../res/Size";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white
  },
  searchContainer: {
    alignSelf: "stretch",
    height: 44,
    marginTop: 16, 
    flexDirection: "row",
    alignItems: "center",
    marginHorizontal: 16,
    paddingHorizontal: 8,
    gap: 8,
    borderWidth: 1,
    borderColor: Colors.inputBorder,
    borderRadius: 8,
    alignItems: "center",
    backgroundColor: Colors.inputBackground
  },
  searchListContainer: {
    flex: 1
  },
  searchContentContainer: {
    flexGrow: 1
  },
  backButton: {
    position: "absolute",
    padding: 8,
    left: 8,
    top: 0,
    bottom: 0
  },
  sendTo: {
    fontWeight: "600",
    fontSize: 16,
    color: Colors.black
  },
  textInputStyle: {
    paddingHorizontal: Size(1),
    fontFamily: "Regular",
    textAlignVertical: "bottom",
    flexWrap: "wrap",
    flex: 1,
    fontSize: Size(3.5)
  },
  backSerachIconBox: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 8,
    height: 40
  },
  sendbtn: {
    color: Colors.black, 
    fontFamily: "Bold", 
    fontSize: Size(5), 
    marginVertical: HeightSize(3)
  },
  userImageContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    borderWidth: 2,
    borderColor: Colors.green
  },
  selectedUsersContainer: {
    borderBottomWidth: 1,
    borderColor: Colors.border,
    justifyContent: "space-between"
  },
  selectedItemsList: {
    flexGrow: 0
  },
  selectedItemsContentContainer: {
    height: 112,
    paddingHorizontal: 16,
    alignItems: "center",
    gap: 8
  },
  selectedItemPressable: {
    width: 64,
    height: 92,
    justifyContent: "space-between"
  },
  userName: {
    fontSize: 12,
    fontWeight: 'bold',
    textAlign: "center"
  },
  crossIcon: {
    position: "absolute",
    tintColor: Colors.black,
    right: -3,
    top: -3,
  },
  loader: {
    marginTop: 16
  }
});

export default styles;