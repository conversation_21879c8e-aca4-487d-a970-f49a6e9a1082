import { StyleSheet } from "react-native";
import { Colors } from "../../res/Colors";
import { HeightSize, Size } from "../../res/Size";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white
  },
  searchContainer: {
    width: '85%', 
    color: 'black',
    height: HeightSize(7), 
   // paddingBottom: HeightSize(1),
    flexDirection: "row",
    alignItems: "center",
    marginHorizontal: Size(5),
    borderBottomWidth: 2,
    alignItems: "center"
  },
  textInputStyle: {
   // borderWidth: 1, 
    borderBottomColor: Colors.grey, 
  //width: Size(70),
    paddingHorizontal: Size(1),
    fontFamily: "Regular",
    textAlignVertical: "bottom",
    flexWrap: "wrap",
    flex: 1,
    fontSize: Size(3.5)
  },
  backSerachIconBox: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: Size(5)
  },
  sendbtn: {
    color: Colors.black, 
    fontFamily: "Bold", 
    fontSize: Size(5), 
    marginVertical: HeightSize(3)
  }
});

export default styles;