import React, { useState, useEffect, useCallback } from "react";
import {
  ActivityIndicator,
  BackHandler,
  SafeAreaView,
  ScrollView,
  StatusBar,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import styles from "./ShareFeedStyles";
import { BackIcon } from "../../res/Svg";
import SearchIcon from "../../res/SearchSvgIcon";
import ShareModal from "../../components/Share/Share";
import {
  notifyError,
  notifySuccess,
  searchArrayOrMakeCallToAPI,
} from "../../constants/misc";
import { WEB_URL } from "../../../Config";
import { Colors } from "../../res/Colors";
import { HeightSize, Size } from "../../res/Size";
import _debounce from "lodash/debounce";
import SearchCardForShare from "../../components/SearchCardForShare";
import Button from "../../components/Button/Button";
import { dispatch } from "../../redux/store";
import { useSelector } from "react-redux";
import { commonStyles } from "../../res/CommonStyles";
import analytics from "../../services/analytics";
import ChatApi from "../../services/ChatApi";
import { useMemo } from "react";
import { selectUserInfo } from "../../redux/selectors/authSelectors";
import { shallowEqual } from "redux";

const ShareFeed = ({ route, navigation, setUserId }) => {
  const [searchResult, setSearchResult] = useState(null);
  const [user, setUser] = useState(null);
  const [selectedItems, setSelectedItems] = useState([]);
  const [sending, setSending] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  const handleItemSelect = (itemId) => {
    setSelectedItems((prevItems) => {
      if (prevItems.includes(itemId)) {
        return prevItems.filter((id) => id !== itemId);
      } else {
        return [...prevItems, itemId];
      }
    });
  };

  const highlight = route.params?.highlight;
  const profile = route.params?.profile;
  const typeOfContent = route.params?.type;

  const shareId = useMemo(() => {
    switch (typeOfContent) {
      case "announcement":
      case "highlight":
      case "post":
        return highlight.id;
      case "profile":
        return profile.id;

      default:
        break;
    }
  }, [typeOfContent]);

  const userInfo = useSelector(selectUserInfo, shallowEqual);

  const gettingUser = useSelector(
    ({ loading }) => loading.effects.user.userSearch
  );
  const userList = useSelector((state) => state.user.usersByProjection);

  const handleSearch = async (query) => {
    const searchResposne = await searchArrayOrMakeCallToAPI({
      searchTerm: query,
      array: [
        ...userList?.map((item) => ({
          ...item,
          type: "user",
          fullname: `${item?.firstName} ${item?.lastName}`,
        })),
      ],
      makeSearchCall: [dispatch.user.userSearch],
    });

    const sortResponse = searchResposne.filter((record) => {
      return record.id !== userInfo.id;
    });

    const searchResponse = sortResponse?.length > 0 ? sortResponse : null;
    setSearchResult(searchResponse);

    // ✅ Log the search event
    await analytics.logSearchPerformed(query, sortResponse.length);
  };

  const fetchSearchData = useCallback(_debounce(handleSearch, 400), []);

  const fetchAllUserData = async () => {
    await dispatch.user.userSearchByProjection();
  };

  const highlightText = `Check out this post on PLAYER. \n \n`;
  const highlightUrl = `${`${WEB_URL}/user/comment?highlightId=${highlight?.id}&userId=${highlight?.userId}`}`;

  const announcementText = `Check out PLAYERs competition: \n \n`;
  const announcementUrl = `${WEB_URL}/user/announcement?announcementId=${highlight?.id}`;

  const profileText = `Check out this profile on PLAYER. \n \n`;
  const profileUrl = `${WEB_URL}/profile?id=${profile?.id}`;

  const postText = `Check out PLAYERs post: \n \n`;
  const postUrl = `${WEB_URL}/user/announcement?announcementId=${highlight?.id}`;

  const getShareContent = () => {
    switch (typeOfContent) {
      case "announcement":
        return { url: announcementUrl, title: announcementText };
      case "highlight":
        return { url: highlightUrl, title: highlightText };
      case "profile":
        return { url: profileUrl, title: profileText };
      case "post": {
        return { url: postUrl, title: postText };
      }

      default:
        break;
    }
  };

  const handleSend = async () => {
    setSending(true);
    try {
      await ChatApi.share({
        recipientIds: selectedItems,
        type: typeOfContent,
        id: shareId,
      });
      notifySuccess("Shared!");

      // ✅ Log the share event
      await analytics.logContentShare(
        highlight?.id || profile?.id,
        typeOfContent,
        "message"
      );

      if (typeOfContent === "profile") {
        navigation.navigate("MyProfileScreenStack", {
          screen: "MyProfile",
          params: { userId: profile.id },
        });
      } else {
        navigation.navigate("Feed");
      }
    } catch (error) {
      notifyError("Error occurred:", error);
    }
    setSending(false);
  };

  useEffect(() => {
    fetchAllUserData();
  }, []);

  useEffect(() => {
    if (user) {
      setSearchResult(null);
    }
  }, [user]);

  useEffect(() => {
    const handleScroll = (event) => {
      const isTop = event.nativeEvent.contentOffset.y === 0;
      setIsScrolled(!isTop);
    };

    return () => {
      // Clean up if needed
    };
  }, []);

  // console.log("search", searchResult)
  useEffect(() => {
    const backAction = () => {
      navigation.navigate("Feed");
      return true;
    };

    const backHandler = BackHandler.addEventListener(
      "hardwareBackPress",
      backAction
    );

    return () => backHandler.remove();
  }, []);

  // console.log("highlight",highlight.userId)

  return (
    <SafeAreaView style={commonStyles.container}>
      <StatusBar
        barStyle="dark-content"
        hidden={false}
        backgroundColor={Colors.white}
        translucent={false}
      />
      <View style={styles.container}>
        <View style={styles.backSerachIconBox}>
          <TouchableOpacity
            onPress={() => {
              if (typeOfContent === "profile") {
                navigation.navigate("MyProfileScreenStack", {
                  screen: "MyProfile",
                  params: { id: profile.id },
                });
              } else {
                navigation.goBack();
              }
            }}
          >
            <BackIcon />
          </TouchableOpacity>
          <View style={styles.searchContainer}>
            <TouchableOpacity>
              <SearchIcon green />
            </TouchableOpacity>
            <TextInput
              style={styles.textInputStyle}
              placeholder="Type a name ..."
              placeholderTextColor={Colors.light_grey}
              onChangeText={(text) => {
                fetchSearchData(text);
              }}
            />
          </View>
        </View>
        <ScrollView style={{ paddingBottom: HeightSize(20) }}>
          {!gettingUser && searchResult
            ? searchResult?.map((user) => (
                <SearchCardForShare
                  key={user.id}
                  user={user}
                  setUserId={setUserId}
                  setUser={setUser}
                  handleItemSelect={handleItemSelect}
                  selectedItems={selectedItems}
                />
              ))
            : ""}
        </ScrollView>
        {gettingUser ? <ActivityIndicator /> : ""}
        {selectedItems.length > 0 && (
          <Button
            title={"SEND"}
            onPress={() => handleSend()}
            backgroundColor={Colors.green}
            textStyleInsideButton={styles.sendbtn}
            progress={sending}
          />
        )}
        <View
          style={{
            backgroundColor: Colors.white,
            marginTop: HeightSize(1),
            borderTopWidth: 1,
            borderColor: "rgba(0, 0, 0, 0.1)",
          }}
        >
          <ShareModal
            title={getShareContent(typeOfContent).title}
            url={getShareContent(typeOfContent).url}
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

export default ShareFeed;
