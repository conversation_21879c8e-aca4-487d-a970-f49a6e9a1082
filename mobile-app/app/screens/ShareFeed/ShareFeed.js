import { useState, useEffect, useCallback } from "react";
import {
  ActivityIndicator,
  BackHandler,
  FlatList,
  Image,
  KeyboardAvoidingView,
  Platform,
  StatusBar,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import styles from "./ShareFeedStyles";
import { BackIcon } from "../../res/Svg";
import SearchIcon from "../../res/SearchSvgIcon";
import ShareModal from "../../components/Share/Share";
import {
  notifyError,
  notifySuccess,
  searchArrayOrMakeCallToAPI,
} from "../../constants/misc";
import { WEB_URL } from "../../../Config";
import { Colors } from "../../res/Colors";
import { HeightSize } from "../../res/Size";
import _debounce from "lodash/debounce";
import SearchCardForShare from "../../components/SearchCardForShare";
import Button from "../../components/Button/Button";
import { dispatch } from "../../redux/store";
import { useSelector } from "react-redux";
import { commonStyles } from "../../res/CommonStyles";
import analytics from "../../services/analytics";
import ChatApi from "../../services/ChatApi";
import { useMemo } from "react";
import { selectUserInfo } from "../../redux/selectors/authSelectors";
import { shallowEqual } from "redux";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { Images } from "../../res/Images";
import { Ionicons } from "@expo/vector-icons";

const ShareFeed = ({ route, navigation, setUserId }) => {
  const { top, bottom } = useSafeAreaInsets();
  const [searchResult, setSearchResult] = useState(null);
  const [user, setUser] = useState(null);
  const [selectedItems, setSelectedItems] = useState([]);
  const [sending, setSending] = useState(false);

  const handleItemSelect = (selectedItem) => {
    setSelectedItems((prevItems) => {
      if (prevItems.find((it) => it.id === selectedItem.id)) {
        return prevItems.filter((it) => it.id !== selectedItem.id);
      } else {
        return [...prevItems, selectedItem];
      }
    });
  };

  const highlight = route.params?.highlight;
  const profile = route.params?.profile;
  const typeOfContent = route.params?.type;

  const shareId = useMemo(() => {
    switch (typeOfContent) {
      case "announcement":
      case "highlight":
      case "post":
        return highlight.id;
      case "profile":
        return profile.id;

      default:
        break;
    }
  }, [typeOfContent]);

  const userInfo = useSelector(selectUserInfo, shallowEqual);

  const gettingUser = useSelector(
    ({ loading }) => loading.effects.user.userSearch
  );
  const userList = useSelector((state) => state.user.usersByProjection);

  const handleSearch = async (query) => {
    const searchResposne = await searchArrayOrMakeCallToAPI({
      searchTerm: query,
      array: [
        ...userList?.map((item) => ({
          ...item,
          type: "user",
          fullname: `${item?.firstName} ${item?.lastName}`,
        })),
      ],
      makeSearchCall: [dispatch.user.userSearch],
    });

    const sortResponse = searchResposne.filter((record) => {
      return record.id !== userInfo.id;
    });

    const searchResponse = sortResponse?.length > 0 ? sortResponse : null;
    setSearchResult(searchResponse);

    // ✅ Log the search event
    await analytics.logSearchPerformed(query, sortResponse.length);
  };

  const fetchSearchData = useCallback(_debounce(handleSearch, 400), []);

  const fetchAllUserData = async () => {
    await dispatch.user.userSearchByProjection();
  };

  const highlightText = `Check out this post on PLAYER. \n \n`;
  const highlightUrl = `${`${WEB_URL}/user/comment?highlightId=${highlight?.id}&userId=${highlight?.userId}`}`;

  const announcementText = `Check out PLAYERs competition: \n \n`;
  const announcementUrl = `${WEB_URL}/user/announcement?announcementId=${highlight?.id}`;

  const profileText = `Check out this profile on PLAYER. \n \n`;
  const profileUrl = `${WEB_URL}/profile?id=${profile?.id}`;

  const postText = `Check out PLAYERs post: \n \n`;
  const postUrl = `${WEB_URL}/user/announcement?announcementId=${highlight?.id}`;

  const getShareContent = () => {
    switch (typeOfContent) {
      case "announcement":
        return { url: announcementUrl, title: announcementText };
      case "highlight":
        return { url: highlightUrl, title: highlightText };
      case "profile":
        return { url: profileUrl, title: profileText };
      case "post": {
        return { url: postUrl, title: postText };
      }

      default:
        break;
    }
  };

  const handleSend = async () => {
    setSending(true);
    try {
      await ChatApi.share({
        recipientIds: selectedItems.map((item) => item.id),
        type: typeOfContent,
        id: shareId,
      });
      notifySuccess("Shared!");

      // ✅ Log the share event
      await analytics.logContentShare(
        highlight?.id || profile?.id,
        typeOfContent,
        "message"
      );

      navigation.goBack();
    } catch (error) {
      notifyError("Error occurred:", error);
    }
    setSending(false);
  };

  useEffect(() => {
    fetchAllUserData();
  }, []);

  useEffect(() => {
    if (user) {
      setSearchResult(null);
    }
  }, [user]);

  // console.log("search", searchResult)
  useEffect(() => {
    const backAction = () => {
      navigation.navigate("Feed");
      return true;
    };

    const backHandler = BackHandler.addEventListener(
      "hardwareBackPress",
      backAction
    );

    return () => backHandler.remove();
  }, []);

  const renderSearchResultItem = ({ item }) => (
    <SearchCardForShare
      key={item.id}
      user={item}
      setUserId={setUserId}
      setUser={setUser}
      handleItemSelect={handleItemSelect}
      selectedItems={selectedItems}
    />
  );

  const renderSelectedItem = ({ item }) => (
    <TouchableOpacity
      key={item.id}
      style={styles.selectedItemPressable}
      onPress={() => handleItemSelect(item)}
    >
      {item.photoUrl && item.photoUrl.length > 5 ? (
        <Image
          source={{ uri: item.photoUrl }}
          style={styles.userImageContainer}
        />
      ) : (
        <Image source={Images.profile1} style={styles.userImageContainer} />
      )}
      <Text style={styles.userName} numberOfLines={2} ellipsizeMode="tail">
        {item.firstName}
        {"\n"}
        {item.lastName}
      </Text>
      <Ionicons name="close" size={14} color="black" style={styles.crossIcon} />
    </TouchableOpacity>
  );

  return (
    <KeyboardAvoidingView
      style={[commonStyles.container, { paddingTop: top }]}
      behavior={Platform.select({
        ios: "height",
        android: "padding",
      })}
    >
      <StatusBar
        barStyle="dark-content"
        hidden={false}
        backgroundColor={Colors.white}
        translucent={false}
      />
      <View style={styles.backSerachIconBox}>
        <TouchableOpacity style={styles.backButton} onPress={navigation.goBack}>
          <BackIcon />
        </TouchableOpacity>
        <Text style={styles.sendTo}>Send to</Text>
      </View>
      {!selectedItems.length && (
        <ShareModal
          title={getShareContent(typeOfContent).title}
          url={getShareContent(typeOfContent).url}
        />
      )}
      {selectedItems.length > 0 && (
        <View style={styles.selectedUsersContainer}>
          <FlatList
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.selectedItemsList}
            contentContainerStyle={styles.selectedItemsContentContainer}
            data={selectedItems}
            renderItem={renderSelectedItem}
          />
        </View>
      )}
      <View style={styles.searchContainer}>
        <TouchableOpacity>
          <SearchIcon green />
        </TouchableOpacity>
        <TextInput
          style={styles.textInputStyle}
          placeholder="Search for user ..."
          placeholderTextColor={"rgba(0,0,0,0.3)"}
          onChangeText={fetchSearchData}
          autoComplete="off"
          autoCorrect={false}
        />
      </View>
      {!gettingUser && searchResult?.length && (
        <FlatList
          style={styles.searchListContainer}
          contentContainerStyle={[
            styles.searchContentContainer,
            { paddingBottom: bottom + 4 + HeightSize(9) },
          ]}
          data={searchResult}
          renderItem={renderSearchResultItem}
        />
      )}
      {gettingUser ? <ActivityIndicator style={styles.loader} /> : ""}
      {selectedItems.length > 0 && (
        <Button
          title={"SEND"}
          onPress={handleSend}
          backgroundColor={Colors.green}
          textStyleInsideButton={styles.sendbtn}
          progress={sending}
          containerStyle={{
            position: "absolute",
            bottom: bottom + 4,
            left: 24,
            right: 24,
          }}
        />
      )}
    </KeyboardAvoidingView>
  );
};

export default ShareFeed;
