import { Platform, StyleSheet } from "react-native";
import { Colors } from "../../res/Colors";
import { HeightSize, Size } from "../../res/Size";

const styles = StyleSheet.create({
mainContainer: {
    marginTop: HeightSize(7),
    marginLeft: Size(5)
    
},
listContainer: {
    marginTop: HeightSize(5)
},
itemText: {
    fontFamily: "Bold",
    fontSize: Size(4.3),
    marginLeft: Size(5),
    paddingVertical: HeightSize(5)
},
line: {
    borderBottomWidth: 0.8,
    borderBottomColor: Colors.black,
  },
skipTxt: {
    textAlign: "right",
    textDecorationLine: "underline",
    fontSize: Size(4.3),
    marginRight: Size(9),
    marginVertical: HeightSize(4),
    marginTop : Platform.OS === "ios" ? HeightSize(7) : 0
  },
})

export default styles;