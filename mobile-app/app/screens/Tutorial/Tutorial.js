import React, { useEffect, useState } from "react";
import { FlatList, StatusBar, Text, TouchableOpacity, View } from "react-native";
import styles from "./TutorialStyles";
import Button from "../../components/Button/Button";
import { HeightSize } from "../../res/Size";
import { commonStyles } from "../../res/CommonStyles";
import { Colors } from "../../res/Colors";
import { dispatch } from "../../redux/store";

const data = [
  { id: 1, title: 'CREATE YOUR PROFILE', screen: 'SignUp' },
  { id: 2, title: 'SHOWCASE YOUR TALENT', screen: 'ClubDropDown' },
  { id: 3, title: 'ELEVATE YOUR GAME', screen: 'BottomStack' },
  // Add more items as needed
];

const Tutorial = ({ navigation }) => {
  const [isSignUpCompleted, setIsSignUpCompleted] = useState(false);

  const ListItem = ({ item, navigation }) => {
    const onPressItem = () => {
      if (isSignUpCompleted || item.id === 1) {
        navigation.navigate(item.screen);
      }
    };

    return (
      <TouchableOpacity
        style={[
          styles.item,
          (!isSignUpCompleted && item.id !== 1) && { opacity: 0.3 }
        ]}
        onPress={onPressItem}
        disabled={!isSignUpCompleted && item.id !== 1}
      >
        <Text style={styles.itemText}>{`${item.id}. ${item.title}`}</Text>
        <View style={styles.line} />
      </TouchableOpacity>
    );
  };

  const handleSkip = () => {
    navigation.navigate("BottomStack");
  };

  return (
    <View style={commonStyles.container}>
      <StatusBar barStyle="dark-content" hidden={false} backgroundColor={Colors.white} translucent={false} />
      <Text style={styles.skipTxt} onPress={handleSkip}>Skip</Text>
      <View style={styles.mainContainer}>
        <Text style={commonStyles.boldText}>LETS GET STARTED</Text>
      </View>
      <View style={styles.listContainer}>
        <FlatList
          data={data}
          renderItem={({ item, index }) => <ListItem item={item} index={index} navigation={navigation}/>}
          keyExtractor={(item, index) => index.toString()}
        />
      </View>
      <Button 
        title={"LOG IN"}
        containerStyle={{marginTop: HeightSize(12)}}
        onPress={() => {
          navigation.reset({
            index: 0,
            routes: [{ name: 'Login' }],
          });
        }}
        backgroundColor={Colors.black}
      />
      {/* <Button 
        title={"SIGN UP"}
        containerStyle={{marginTop: HeightSize(2)}}
        onPress={() => navigation.navigate("SignUp")}
        backgroundColor={Colors.black}
      /> */}
    </View>
  );
};

export default Tutorial;
