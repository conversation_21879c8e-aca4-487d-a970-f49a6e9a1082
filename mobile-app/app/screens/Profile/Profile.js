import React, { useState } from "react";
import {
  View,
  Text,
  StatusBar,
  TouchableOpacity,
  Modal,
  ScrollView,
  Image,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
} from "react-native";
import { BackIcon, UserImage } from "../../res/Svg";
import { commonStyles } from "../../res/CommonStyles";
import { Colors } from "../../res/Colors";
import styles from "./ProfileStyles";
import { HeightSize, Size } from "../../res/Size";
import RolesPopupModal from "../../components/DropDown/DropDown";
import CustomTextInput from "../../components/TextInput";
import { Ionicons } from "@expo/vector-icons";
import DatePicker from "react-native-modern-datepicker";
import moment from "moment";
import Button from "../../components/Button/Button";
import * as ImagePicker from "expo-image-picker";
import { uploadS3 } from "../../../s3.config";

//const BASE_IMAGE_URL = "https://player-assets.s3.eu-west-2.amazonaws.com/";

const Profile = ({ route, navigation }) => {
  const [roles, setRoles] = useState("");
  const [visible, setVisible] = useState(false);
  const [selectedDate, setSelectedDate] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [gender, setGender] = useState("");
  const [genderVisible, setGenderVisible] = useState(false);
  const [image, setImage] = useState(null);
  const [imageFileName, setImageFileName] = useState(null);
  const [preferredFoot, setPreferredFoot] = useState("");
  const [preferredFootVisible, setPreferredFootVisible] = useState(false);
  const [showFieldsMandatory, setShowFieldsMandatory] = useState(false);
  const [doneAttempted, setDoneAttempted] = useState(false);
  const [height, setHeight] = useState("");
  const [location, setLocation] = useState("");
  const email = route?.params?.email || "";
  const password = route?.params?.password || "";
  const phoneNumber = route?.params?.phoneNumber || "";
  const firstName = route?.params?.firstName || "";
  const lastName = route?.params?.lastName || "";
  // alert(JSON.stringify(image));

  const pickImage = async () => {
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    });

    if (!result.canceled) {
      setImage(result.assets[0].uri);
      setImageFileName(Date.now());
    }
  };

  const validation = () => {
    if (
      roles.length === 0 ||
      selectedDate === null ||
      preferredFoot.length === 0 ||
      gender.length === 0 ||
      height === "" ||
      location === ""
    ) {
      return true;
    } else {
      return false;
    }
  };

  const uploadS3Function = async (onSuccess) => {
    if (!image) {
      onSuccess(null);
      return;
    }

    uploadS3(
      image, // Send the URI directly
      imageFileName,
      (uploadedUrl) => onSuccess(uploadedUrl)
    );
  };

  const handleSubmit = () => {
    setDoneAttempted(true);
    if (validation()) {
      return;
    }
    uploadS3Function((fileUrl) => {
      profile(fileUrl);
    });
  };

  const profile = async (file) => {
    if (!validation()) {
      navigation.navigate("ClubDropDown", {
        email: email,
        password: password,
        firstName: firstName,
        lastName: lastName,
        phoneNumber: phoneNumber,
        position: roles,
        preferredFoot: preferredFoot,
        gender: gender,
        birthday: selectedDate,
        location: location,
        height: height,
        photoUrl: file,
      });
    } else {
      //alert(JSON.stringify("All Fields Are Mandatory"))
    }
  };

  const handleDateChange = (date) => {
    setSelectedDate(date);
    setModalVisible(false);
  };

  const togglePreferredFoot = (foot) => {
    setPreferredFoot(foot.name);
    setPreferredFootVisible(false);
  };

  const toggleRole = (role) => {
    setRoles(role.name);
    setVisible(false);
  };

  const toggleGender = (g) => {
    setGender(g.name);
  };

  return (
    <SafeAreaView style={commonStyles.container}>
      <StatusBar
        barStyle="dark-content"
        hidden={false}
        backgroundColor={Colors.white}
        translucent={false}
      />
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === "ios" ? "padding" : null}
        keyboardVerticalOffset={Platform.OS === "ios" ? 64 : 0}
      >
        <ScrollView>
          <View style={styles.mainContainer}>
            <View style={{ marginLeft: Size(5) }}>
              <TouchableOpacity onPress={() => navigation.push("SignUp")}>
                <BackIcon />
              </TouchableOpacity>
              <Text
                style={[commonStyles.boldText, { marginTop: HeightSize(3) }]}
              >
                YOUR PROFILE
              </Text>
              <TouchableOpacity
                onPress={() => pickImage()}
                style={styles.imageContainer}
              >
                {image ? (
                  <Image
                    source={{ uri: image }}
                    style={{
                      width: Size(30),
                      height: Size(30),
                      borderRadius: Size(15),
                    }}
                  />
                ) : (
                  <View style={{ alignSelf: "center" }}>
                    <Image
                      source={require("../../../assets/profile.png")}
                      style={{
                        width: Size(30),
                        height: Size(30),
                        borderRadius: Size(15),
                      }}
                    />
                  </View>
                )}
                <Text
                  style={[
                    commonStyles.regularText,
                    { paddingVertical: HeightSize(3) },
                  ]}
                >
                  {image ? "EDIT YOUR PHOTO" : "UPLOAD YOUR PHOTO"}
                </Text>
              </TouchableOpacity>
            </View>

            <RolesPopupModal
              visible={visible}
              roles={positionData}
              toggleRole={toggleRole}
              selectedRole={roles}
              onPress={() => setVisible(false)}
              showTick={false}
              label={"POSITION"}
              onClick={() => setVisible(!visible)}
              inEditProfile={true}
            />

            <View style={{ paddingTop: HeightSize(2.5) }}>
              <RolesPopupModal
                visible={preferredFootVisible}
                roles={preferredFootData}
                toggleRole={togglePreferredFoot}
                selectedRole={preferredFoot}
                onPress={() => setPreferredFootVisible(false)}
                showTick={false}
                label={"PREFERRED FOOT"}
                onClick={() => setPreferredFootVisible(!preferredFootVisible)}
                inEditProfile={true}
              />
            </View>
            <Text style={styles.itemText}>DATE OF BIRTH</Text>

            <View style={styles.container}>
              <Text style={commonStyles.regularText}>
                {selectedDate
                  ? moment(selectedDate, "YYYY-MM-DD").format("DD . MMMM .YYYY")
                  : "--"}
              </Text>
              <TouchableOpacity
                style={styles.iconContainer}
                onPress={() => setModalVisible(true)}
              >
                <Ionicons
                  name="chevron-down-outline"
                  size={Size(5)}
                  color="black"
                />
              </TouchableOpacity>
            </View>
            <View style={styles.line} />

            <CustomTextInput
              label={"HEIGHT"}
              labelTxt={{
                marginLeft: Size(5),
                fontFamily: "Bold",
                fontSize: Size(4),
                marginLeft: Size(5),
              }}
              textInputStyle={[
                commonStyles.regularText,
                {
                  paddingVertical: HeightSize(2),
                  paddingLeft: Size(5),
                  borderBottomWidth: 1,
                  borderBottomColor: "rgba(0, 0, 0, 0.1)",
                },
              ]}
              keyboardType={"numeric"}
              placeholder={"eg. 1.83m"}
              color={Colors.grey}
              onChangeText={(e) => {
                setHeight(e);
              }}
              value={height}
            />

            <CustomTextInput
              label={"YOUR LOCATION"}
              labelTxt={{
                marginLeft: Size(5),
                fontFamily: "Bold",
                fontSize: Size(4),
                marginLeft: Size(5),
              }}
              textInputStyle={[
                commonStyles.regularText,
                {
                  paddingVertical: HeightSize(2),
                  paddingLeft: Size(5),
                  borderBottomWidth: 1,
                  borderBottomColor: "rgba(0, 0, 0, 0.1)",
                },
              ]}
              placeholder={"For example: Hackney, London"}
              color={Colors.grey}
              onChangeText={(e) => {
                setLocation(e);
              }}
              value={location}
            />
            <View style={{ paddingTop: HeightSize(2.5) }}>
              <RolesPopupModal
                visible={genderVisible}
                roles={genderData}
                toggleRole={toggleGender}
                selectedRole={gender}
                onPress={() => setGenderVisible(false)}
                showTick={false}
                label={"YOUR GENDER"}
                onClick={() => setGenderVisible(!genderVisible)}
                inEditProfile={true}
              />
            </View>
            {doneAttempted && validation() && (
              <Text style={[commonStyles.errTxt, styles.errMsgStyle]}>
                All Fields Are Mandatory.
              </Text>
            )}
            <Button
              title={"DONE"}
              containerStyle={{ marginVertical: HeightSize(2) }}
              onPress={() => handleSubmit()}
              backgroundColor={Colors.black}
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
      <Modal
        visible={modalVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <DatePicker
              mode="calendar"
              onDateChange={handleDateChange}
              selected={selectedDate}
              style={{ borderRadius: 10 }}
            />
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

export default Profile;

const positionData = [
  {
    id: 1,
    name: "Goalkeeper",
  },
  {
    id: 2,
    name: "Centre Back",
  },
  {
    id: 3,
    name: "Right Back",
  },
  {
    id: 4,
    name: "Left Back",
  },
  {
    id: 5,
    name: "Wing Back",
  },
  {
    id: 6,
    name: "Winger",
  },
  {
    id: 7,
    name: "Defensive Midfield",
  },
  {
    id: 8,
    name: "Centre Midfield",
  },
  {
    id: 9,
    name: "Attacking Midfield",
  },
  {
    id: 10,
    name: "Forward",
  },
  {
    id: 11,
    name: "Striker",
  },
];

const genderData = [
  {
    id: 1,
    name: "Male",
  },
  {
    id: 2,
    name: "Female",
  },
];

const preferredFootData = [
  {
    id: 1,
    name: "Right Footed",
  },
  {
    id: 2,
    name: "Left Footed",
  },
  {
    id: 3,
    name: "Both Footed",
  },
];
