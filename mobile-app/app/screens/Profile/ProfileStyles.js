import { StyleSheet } from "react-native";
import { HeightSize, Size } from "../../res/Size";

const styles = StyleSheet.create({
  mainContainer: {
    marginTop: HeightSize(5)
  },
  imageContainer: {
    alignSelf: "center",
    marginTop: HeightSize(4),
    justifyContent: "center", 
    alignItems: "center",
  },
  dropDownContainerStyle: {
    marginHorizontal: Size(4),
    marginTop: HeightSize(2),
  },
  itemText: {
    fontFamily: "Bold",
    fontSize: Size(4),
    marginLeft: Size(5),
    paddingTop: HeightSize(2.5),
  },
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: HeightSize(2.5),
    marginLeft: Size(5),
  },
  iconContainer: {
    marginRight: Size(5),
  },
  line: {
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)'
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: Size(1),
    elevation: 5,
    width: "90%"
  },
  errMsgStyle: {
    textAlign: "center",
    paddingTop: HeightSize(1),
    fontSize: Size(3.2)
  }
})

export default styles;