import { StyleSheet } from "react-native";
import { HeightSize , Size} from "../../res/Size";

const styles = StyleSheet.create({
  mainContainer: {
    marginTop: HeightSize(5)
  },
  imageContainer: {
    alignSelf: "center",
    marginTop: HeightSize(2),
    justifyContent: "center", 
    alignItems: "center",
  },
  rowContainer: {
    flexDirection: "row",
    alignItems: "center",
    
  },
  modalContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  modalContent: {
    // backgroundColor: "white",
    // paddingVertical: HeightSize(1),
    // borderRadius: Size(7),
    // elevation: 5,
    // width: "90%",
    // marginTop: HeightSize(40),
    // paddingHorizontal: Size(3),
    // marginBottom: HeightSize(5)
    width: '90%', 
    maxHeight: '80%', 
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 20,
    overflow: 'hidden',
    marginBottom: HeightSize(5)
  },
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: HeightSize(2.5),
    marginLeft: Size(5),
  },
  iconContainer: {
    marginRight: Size(5),
  },
  bannerContainer: {
    //flex: 1,
    borderRadius: Size(5),
    overflow: "hidden",
    marginTop: HeightSize(3)
  },
  scrollViewContent: {
    flexGrow: 1,
  },
  
});

export default styles;