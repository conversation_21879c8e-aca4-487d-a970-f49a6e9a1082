import React, { useCallback, useEffect, useState } from "react";
import {
  Image,
  KeyboardAvoidingView,
  Modal,
  Platform,
  SafeAreaView,
  ScrollView,
  StatusBar,
  Text,
  TouchableWithoutFeedback,
  View,
  TouchableOpacity,
  BackHandler,
  TextInput,
  FlatList,
} from "react-native";
import { Colors } from "../../res/Colors";
import { commonStyles } from "../../res/CommonStyles";
import { BackIcon } from "../../res/Svg";
import { HeightSize, Size } from "../../res/Size";
import styles from "./EditProfileStyles";
import * as ImagePicker from "expo-image-picker";
import { uploadS3 } from "../../../s3.config";
import CustomTextInput from "../../components/TextInput";
import {
  cleanDate,
  nonPlayers,
  notifySuccess,
  trimString,
} from "../../constants/misc";
import DatePicker from "react-native-date-picker";
import RolesPopupModal from "../../components/DropDown/DropDown";
import Button from "../../components/Button/Button";
import { shallowEqual, useSelector } from "react-redux";
import { dispatch } from "../../redux/store";
import _debounce from "lodash/debounce";
import SelectWithPicture from "../../components/SelectWithPicture";
import moment from "moment";
import TeamDropdown from "../../components/TeamsDropDown";

const EditProfile = ({ navigation, route }) => {
  const [image, setImage] = useState(route?.params?.photoUrl || null);
  const [imageFileName, setImageFileName] = useState(
    route?.params?.photoFileName || null
  );
  const [oldProfileImage, setOldProfileImage] = useState(null);
  const [banner, setBanner] = useState(route?.params?.banner || null);
  const [firstName, setFirstName] = useState(route?.params?.firstName || "");
  const [lastName, setLastName] = useState(route?.params?.lastName || "");
  const [age, setAge] = useState(route?.params?.age || "");
  const [location, setLocation] = useState(route?.params?.location || "");
  const [errorMessage, setErrorMessage] = useState(false);
  const [preferredFootVisible, setPreferredFootVisible] = useState(false);
  const [preferredFoot, setPreferredFoot] = useState(
    route?.params?.preferredFoot || ""
  );
  // const [modalVisible, setModalVisible] = useState(false);
  const [showCalendar, setShowCalendar] = useState(false);
  // const [clubName, setClubName] = useState(route?.params?.organizationName || "");
  // const [clubId, setClubId] = useState("");
  const [bio, setBio] = useState(route?.params?.bio || "");
  const [contractExpiryDate, setContractExpiryDate] = useState(
    route?.params?.contractExpiry || ""
  );
  const [selectedDate, setSelectedDate] = useState(() => {
    return route?.params?.dob
      ? new Date(cleanDate(route.params.dob))
      : new Date();
  });
  const [height, setHeight] = useState(route?.params?.height || "");
  const [roles, setRoles] = useState(route?.params?.position || "");
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [selectedNonPlayerRoles, setSelectedNonPlayerRoles] = useState(
    route?.params?.nonPlayerRole || []
  );
  const [nonPlayerRoleVisible, setNonPlayerRoleVisible] = useState(false);
  const [selectClub, setSelectClub] = useState(
    route?.params?.organizationName || ""
  );
  const [searchValue, setSearchValue] = useState("");
  const [teams, setTeams] = useState([]);
  const [teamLogo, setTeamLogo] = useState("");
  const [selectTeam, setSelectTeam] = useState({});
  const [filteredTeams, setFilteredTeams] = useState([]);
  const [clubs, setClubs] = useState([]);
  const [representedBy, setRepresentedBy] = useState(
    route?.params?.representedBy || ""
  );
  const alphabetRegex = /^[A-Za-z\s,.'-]{1,30}$/;

  const { userInfo } = useSelector(
    (state) => ({
      userInfo: state.auth.authUser.userInfo,
    }),
    shallowEqual
  );
  const allClubs = useSelector(({ club }) => club.allClubs);

  const email = route?.params?.email || "";

  const validation = () => {
    if (
      firstName === "" ||
      !alphabetRegex.test(firstName) ||
      lastName === "" ||
      !alphabetRegex.test(lastName)
    ) {
      return true;
    } else {
      return false;
    }
  };

  useEffect(() => {
    const backHandler = () => {
      navigation.goBack();
      return true;
    };

    BackHandler.addEventListener("hardwareBackPress", backHandler);
    return () => {
      BackHandler.removeEventListener("hardwareBackPress", backHandler);
    };
  }, []);

  const toggleNonPlayerRole = (role) => {
    let updatedRoles = [...selectedNonPlayerRoles];
    if (updatedRoles.includes(role.name)) {
      updatedRoles = updatedRoles.filter((r) => r !== role.name);
    } else {
      updatedRoles.push(role.name);
    }
    setSelectedNonPlayerRoles(updatedRoles);
    setNonPlayerRoleVisible(false);
  };

  // Current date for disabling future dates
  const currentDate = new Date();
  // Set the maximum date of birth (14 years ago)
  const maximumDate = new Date();
  maximumDate.setFullYear(currentDate.getFullYear() - 14);

  // Set the minimum date of birth (100 years ago)
  const minimumDate = new Date();
  minimumDate.setFullYear(currentDate.getFullYear() - 100);

  const handleDateChange = (date) => {
    setSelectedDate(date);
    setShowCalendar(false);
  };

  const togglePreferredFoot = (foot) => {
    setPreferredFoot(foot.key); // Store key instead of name
    setPreferredFootVisible(false);
  };

  const toggleRole = (role) => {
    setRoles(role.key); // Store key instead of name
    setVisible(false);
  };

  const fetchClubs = async () => {
    const res = await dispatch.club.getAllClubs();
    setClubs(res);
  };

  useEffect(() => {
    if (!allClubs.length) {
      fetchClubs();
    }
    setClubs(allClubs);
  }, []);


  const handleClubSelection = async (clubId) => {
    const teamsInGivenClub = [];
    // get the teams from the state

    const res = await dispatch.team.getTeamsByClubId(clubId);
    teamsInGivenClub.push(...res?.data);
    // if only one team, select the team for the player
    if (teamsInGivenClub?.length === 1) {
      setSelectTeam(teamsInGivenClub[0]);
    }
    setTeams(teamsInGivenClub);
  };

  const getTeamsBasedOnSelectedClub = useCallback(
    _debounce(handleClubSelection, 100),
    []
  );

  // Load the existing profile when the component mounts
  useEffect(() => {
    if (route?.params?.photoUrl) {
      setOldProfileImage(route.params.photoUrl);
    }
  }, [route?.params?.photoUrl]);

  const editProfile = async () => {
    setLoading(true);
    if (!validation()) {
      try {
        const data = {
          firstName: firstName,
          lastName: lastName,
          position: roles,
          preferredFoot: preferredFoot,
          birthday: selectedDate.toISOString().split("T")[0],
          location: location,
          height: height,
          id: route?.params?.id,
          bio: bio,
          nonPlayerRole: selectedNonPlayerRoles,
          clubName: selectClub?.clubName,
          teamName: selectTeam?.teamName || searchValue,
          teamLogo: selectTeam?.logoUrl || teamLogo,
          representedBy: representedBy,
          contractExpiry: contractExpiryDate,
        };

        const response = await dispatch.user.updateUser(data);

        if (response === 1) {
          setLoading(false);
          navigation.navigate("MyProfile");
          notifySuccess("profile has been updated");
        } else {
          setLoading(false);
          console.error("Profile update failed:", response.data.message);
        }
      } catch (err) {
        setLoading(false);
        console.error("error---", err);
      }
    } else {
      setErrorMessage(true);
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={commonStyles.container}>
      <StatusBar
        barStyle="dark-content"
        hidden={false}
        backgroundColor={Colors.white}
        translucent={false}
      />
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === "ios" ? "padding" : null}
        keyboardVerticalOffset={Platform.OS === "ios" ? 64 : 0}
      >
        <ScrollView>
          <View style={styles.mainContainer}>
            <View style={{ marginHorizontal: Size(5) }}>
              <TouchableOpacity onPress={() => navigation.goBack()}>
                <BackIcon />
              </TouchableOpacity>
              <Text
                style={[commonStyles.boldText, { marginTop: HeightSize(3) }]}
              >
                EDIT PROFILE
              </Text>

              <View
                style={[
                  styles.rowContainer,
                  {
                    alignItems: "flex-start",
                  },
                ]}
              >
                {/* First Name */}
                <CustomTextInput
                  label={"First Name"}
                  style={{
                    flex: 1,
                  }}
                  textInputStyle={commonStyles.textInputStyle}
                  onChangeText={(e) => {
                    setFirstName(e);
                    setErrorMessage(false);
                  }}
                  value={firstName}
                  errorMsg={
                    trimString(firstName) == "" &&
                    !alphabetRegex.test(firstName) &&
                    errorMessage
                      ? "Please Enter First Name"
                      : !alphabetRegex.test(firstName) && errorMessage
                      ? "Name Should Contain Only Alphabets"
                      : ""
                  }
                  labelTxt={{ opacity: 0.4 }}
                />

                {/* Last Name */}
                <CustomTextInput
                  label={"Last Name"}
                  style={{
                    marginLeft: 30,
                    flex: 1,
                  }}
                  textInputStyle={commonStyles.textInputStyle}
                  onChangeText={(e) => {
                    setLastName(e);
                    setErrorMessage(false);
                  }}
                  value={lastName}
                  errorMsg={
                    trimString(lastName) == "" &&
                    !alphabetRegex.test(lastName) &&
                    errorMessage
                      ? "Please Enter Last Name"
                      : !alphabetRegex.test(lastName) && errorMessage
                      ? "Last Name Should Contain Only Alphabets"
                      : ""
                  }
                  labelTxt={{ opacity: 0.4 }}
                />
              </View>

              <View style={{ position: "relative" }}>
                {/* Email */}
                <CustomTextInput
                  label={"Email"}
                  style={{ flex: 1, opacity: 0.4 }}
                  textInputStyle={commonStyles.textInputStyle}
                  value={email}
                  placeholderTextColor={Colors.textColor}
                  editable={false}
                />
                <View
                  style={{
                    position: "absolute",
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: "transparent",
                  }}
                />
              </View>

              {/* Preferred Foot Popup */}
              {userInfo.userType !== "NON_PLAYER" && (
                <View
                  style={[
                    styles.rowContainer,
                    {
                      justifyContent: "space-between",
                      marginTop: HeightSize(2),
                      alignItems: "flex-start",
                    },
                  ]}
                >
                  <RolesPopupModal
                    style={{ flex: 1 }}
                    visible={preferredFootVisible}
                    roles={preferredFootData}
                    toggleRole={togglePreferredFoot}
                    selectedRole={
                      preferredFootData.find(
                        (item) => item.key === preferredFoot
                      )?.name || ""
                    }
                    onPress={() => setPreferredFootVisible(false)}
                    showTick={false}
                    label={"Preferred Foot"}
                    onClick={() =>
                      setPreferredFootVisible(!preferredFootVisible)
                    }
                    inEditProfile={false}
                    itemText={[
                      commonStyles.regularText,
                      { opacity: 0.4, marginLeft: 0 },
                    ]}
                  />

                  {/* Height */}
                  <CustomTextInput
                    label={"Height"}
                    labelTxt={{ opacity: 0.4 }}
                    style={{ marginLeft: 30, flex: 1, marginTop: 0 }}
                    textInputStyle={[commonStyles.textInputStyle]}
                    placeholder={"eg. 1.83m"}
                    color={Colors.grey}
                    onChangeText={(e) => {
                      setHeight(e);
                    }}
                    value={height}
                    
                  />
                </View>
              )}

              <View
                style={[
                  styles.rowContainer,
                  { justifyContent: "space-between", alignItems: "flex-start" },
                ]}
              >
                {userInfo?.userType !== "NON_PLAYER" && (
                  <CustomTextInput
                    label={"Date of Birth"}
                    textInputStyle={commonStyles.textInputStyle}
                    style={{ flex: 1 }}
                    buttonInsteadOfTextInput={true}
                    onPress={() => setShowCalendar(true)}
                    selectedName={
                      selectedDate && !isNaN(new Date(selectedDate))
                        ? selectedDate.toISOString().split("T")[0]
                        : undefined
                    }
                    labelTxt={{ opacity: 0.4 }}
                  />
                )}

                {/* Location */}
                <CustomTextInput
                  label={"Location"}
                  style={{
                    marginLeft: userInfo?.userType !== "NON_PLAYER" && 30,
                    flex: 1,
                  }}
                  textInputStyle={commonStyles.textInputStyle}
                  onChangeText={(e) => {
                    setLocation(e);
                    setErrorMessage(false);
                  }}
                  value={location}
                  errorMsg={
                    trimString(location) == "" &&
                    !alphabetRegex.test(location) &&
                    errorMessage
                      ? "Please Enter Location"
                      : !alphabetRegex.test(location) && errorMessage
                      ? "Location Should Contain Only Alphabets"
                      : null
                  }
                  labelTxt={{ opacity: 0.4 }}
                />
              </View>
              <View style={{ zIndex: 2, marginTop: HeightSize(2) }}>
                <Text style={[styles.label, { marginBottom: 0, opacity: 0.4 }]}>
                  Football Club / Organisation
                </Text>
                <SelectWithPicture
                  setSelectClub={setSelectClub}
                  selectClub={selectClub}
                  searchValue={searchValue}
                  setSearchValue={setSearchValue}
                  setTeams={setTeams}
                  getTeamsBasedOnSelectedClub={getTeamsBasedOnSelectedClub}
                  setTeamLogo={setTeamLogo}
                  setSelectTeam={setSelectTeam}
                  clubModel={clubs}
                  userInfo={userInfo}
                />
              </View>

              {teams?.length > 0 && (
                <>
                  <Text
                    style={[
                      styles.label,
                      { marginTop: HeightSize(3), opacity: 0.4 },
                    ]}
                  >
                    Teams
                  </Text>
                  <View>
                  
                    {teams?.length > 0 && (
                      <View>
                        <TeamDropdown
                          teams={teams}
                          selectTeam={selectTeam}
                          setSelectTeam={setSelectTeam}
                        />
                      </View>
                    )}
                  </View>
                </>
              )}
              {userInfo.userType !== "NON_PLAYER" && (
                <View
                  style={{ position: "relative", marginTop: HeightSize(2) }}
                >
                  <CustomTextInput
                    label={"Contract Expiry Date"}
                    textInputStyle={commonStyles.textInputStyle}
                    style={{ marginTop: HeightSize(1) }}
                    onChangeText={(e) => {
                      setContractExpiryDate(e);
                      setErrorMessage(false);
                    }}
                    value={contractExpiryDate}
                    color={"#97A0AF"}
                    placeholder={
                      "eg. July 2025 (Leave blank if not applicable)"
                    }
                    labelTxt={{ opacity: 0.4 }}
                  />
                </View>
              )}
              {userInfo.userType !== "NON_PLAYER" && (
                <View
                  style={{ position: "relative", marginTop: HeightSize(2) }}
                >
                  <CustomTextInput
                    label={"Represented by"}
                    textInputStyle={commonStyles.textInputStyle}
                    style={{ marginTop: HeightSize(1) }}
                    onChangeText={(e) => {
                      setRepresentedBy(e);
                      setErrorMessage(false);
                    }}
                    value={representedBy}
                    color={"#97A0AF"}
                    placeholder={
                      "eg. Manager/Agent (Leave blank if not applicable)"
                    }
                    labelTxt={{ opacity: 0.4 }}
                  />
                </View>
              )}
              {userInfo.userType !== "NON_PLAYER" && (
                <View style={{ paddingTop: HeightSize(2.5) }}>
                  <RolesPopupModal
                    visible={visible}
                    roles={positionData}
                    selectedRole={
                      positionData.find((item) => item.key === roles)?.name ||
                      ""
                    }
                    toggleRole={toggleRole}
                    onPress={() => setVisible(false)}
                    showTick={false}
                    inEditProfile={false}
                    onClick={() => setVisible(!visible)}
                    label={"Position"}
                    itemText={[
                      commonStyles.regularText,
                      { opacity: 0.4, marginLeft: 0 },
                    ]}
                  />
                </View>
              )}

              <CustomTextInput
                label={"Bio"}
                textInputStyle={[
                  commonStyles.textInputStyle,
                  { height: HeightSize(15) },
                ]}
                onChangeText={(e) => {
                  setBio(e);
                  setErrorMessage(false);
                }}
                value={bio}
                labelTxt={{ opacity: 0.4 }}
                multiline={true}
              />
              {userInfo.userType === "NON_PLAYER" && (
                <>
                  <RolesPopupModal
                    visible={nonPlayerRoleVisible}
                    roles={nonPlayers}
                    toggleRole={toggleNonPlayerRole}
                    selectedRole={selectedNonPlayerRoles}
                    onPress={() => setNonPlayerRoleVisible(false)}
                    showTick={true}
                    onClick={() =>
                      setNonPlayerRoleVisible(!nonPlayerRoleVisible)
                    }
                    inEditProfile={true}
                    container={{ marginLeft: 0 }}
                    line={{ borderBottomColor: Colors.black }}
                    label={"Role"}
                    itemText={[
                      commonStyles.regularText,
                      { marginLeft: 0, opacity: 0.5 },
                    ]}
                    style={{ marginTop: HeightSize(3) }}
                  />
                </>
              )}

              {/* <Modal visible={modalVisible} transparent>
                <TouchableWithoutFeedback onPress={() => setModalVisible(false)}>
                  <View style={styles.modalContainer}>
                    <View style={styles.modalContent}>
                      <ScrollView contentContainerStyle={styles.scrollViewContent}
                        showsVerticalScrollIndicator={false}
                      >
                        <ClubList onSelect={handleClubSelect} />
                      </ScrollView>
                    </View>
                  </View>
                </TouchableWithoutFeedback>
              </Modal> */}
            </View>
            <Button
              title={"SAVE"}
              containerStyle={{ marginVertical: HeightSize(2.5) }}
              onPress={() => editProfile()}
              backgroundColor={Colors.black}
              progress={loading}
              disable={loading}
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
      {/* <Modal
        visible={showCalendar}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowCalendar(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}> */}
      {/* <DatePicker
              mode="calendar"
              onDateChange={handleDateChange}
              selected={selectedDate}
              style={{ borderRadius: 10 }}
              maximumDate={currentDate.toISOString().split("T")[0]}
              minimumDate={minimumDate.toISOString().split("T")[0]}
            /> */}
      {showCalendar && (
        <DatePicker
          modal
          open={showCalendar}
          mode="date"
          date={
            selectedDate && !isNaN(new Date(selectedDate))
              ? new Date(selectedDate)
              : new Date()
          }
          maximumDate={new Date()}
          onConfirm={(date) => {
            setShowCalendar(false);
            setSelectedDate(date);
          }}
          onCancel={() => setShowCalendar(false)}
        />
      )}

      {/* </View>
        </View>
      </Modal> */}
    </SafeAreaView>
  );
};

export default EditProfile;

const preferredFootData = [
  { id: 1, name: "Right Footed", key: "RIGHT_FOOTED" },
  { id: 2, name: "Left Footed", key: "LEFT_FOOTED" },
  { id: 3, name: "Both Footed", key: "BOTH_FOOTED" },
];

const positionData = [
  { id: 1, name: "Goalkeeper", key: "GOAL_KEEPERS" },
  { id: 2, name: "Centre Back", key: "CENTRE_BACK" },
  { id: 3, name: "Right Back", key: "RIGHT_BACK" },
  { id: 4, name: "Left Back", key: "LEFT_BACK" },
  { id: 5, name: "Wing Back", key: "WING_BACK" },
  { id: 6, name: "Winger", key: "WINGER" },
  { id: 7, name: "Defensive Midfield", key: "DEFENSIVE_MIDFIELD" },
  { id: 8, name: "Centre Midfield", key: "CENTRAL_MIDFIELD" },
  { id: 9, name: "Attacking Midfield", key: "ATTACKING_MIDFIELD" },
  { id: 10, name: "Forward", key: "FORWARD" },
  { id: 11, name: "Striker", key: "STRIKER" },
];
