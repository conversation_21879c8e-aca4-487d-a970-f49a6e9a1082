// REACT //
import React from "react";

// REACT NATIVE //
import {
  SafeAreaView,
  StatusBar,
  Text,
  TouchableOpacity,
  View,
  FlatList,
  Platform,
} from "react-native";

// PLUGINS //
import DeviceInfo from "react-native-device-info";

// CONSTANTS //
import { clearAsyncStorage } from "../../constants/Constant";
import { navigate } from "../../constants/navigation";

// OTHERS //
import { commonStyles } from "../../res/CommonStyles";
import { HeightSize } from "../../res/Size";
import { ArrowRightIcon, BackIcon } from "../../res/Svg";
import { Colors } from "../../res/Colors";
import styles from "./SettingsStyles";
import { dispatch } from "../../redux/store";

const Settings = ({ navigation }) => {
  const menuOptions = [
    { key: "account", label: "ACCOUNT SETTINGS" },
    { key: "security", label: "PRIVACY AND SECURITY" },
  ];

  // const handleLogout = async () => {
  //   try {
  //     // Clear authentication-related data
  //     await clearAsyncStorage();
  //     navigation.reset({
  //       index: 0,
  //       routes: [{ name: 'AuthNavigation' }],
  //     });
  //   } catch (error) {
  //     console.error("Logout failed:", error);

  //   }
  // };

  async function handleLogout() {
    await dispatch.auth.logout();
    // navigation.reset({
    //   index: 0,
    //   routes: [{ name: 'AuthNavigation' }],
    // });
  }

  const handleItemPress = async (key) => {
    switch (key) {
      case "account":
        navigation.navigate("Account");
        break;
      case "security":
        navigation.navigate("PrivacySecurity");
        break;
      default:
        break;
    }
  };

  const appVersion = DeviceInfo.getVersion();
  const buildNumber = DeviceInfo.getBuildNumber();

  const renderItem = ({ item }) => (
    <TouchableOpacity
      style={styles.menuItem}
      onPress={() => handleItemPress(item.key)}
    >
      <Text style={styles.menuItemText}>{item.label}</Text>
      <View style={styles.arrowRight}>
        {item.label !== "LOGOUT" && <ArrowRightIcon />}
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={commonStyles.container}>
      <StatusBar
        barStyle="dark-content"
        hidden={false}
        backgroundColor={Colors.white}
        translucent={false}
      />
      <View style={[commonStyles.subContainer, { marginTop: HeightSize(4) }]}>
        <TouchableOpacity
          onPress={() => {
            navigation.goBack();
          }}
          activeOpacity={9}
          style={styles.backBtn}
        >
          <BackIcon
            width={Platform.isPad ? 40 : 24}
            height={Platform.isPad ? 40 : 24}
          />
        </TouchableOpacity>
        <Text style={commonStyles.boldText}>SETTINGS</Text>
        <FlatList
          data={menuOptions}
          renderItem={renderItem}
          keyExtractor={(item) => item.key}
          contentContainerStyle={styles.menuList}
        />
      </View>

      <TouchableOpacity onPress={handleLogout} style={styles.logoutWrapper}>
        <Text style={styles.logoutText}>Log out</Text>
      </TouchableOpacity>

      <Text style={styles.versionCode}>{`${appVersion} (${buildNumber})`}</Text>
    </SafeAreaView>
  );
};

export default Settings;
