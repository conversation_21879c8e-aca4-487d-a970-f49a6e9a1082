import { StyleSheet } from "react-native";
import { HeightSize, Size } from "../../res/Size";
import { Colors } from "../../res/Colors";

const styles = StyleSheet.create({
  backBtn: {
    marginBottom: HeightSize(3),
  },
  menuList: {
    marginTop: HeightSize(4),
    //paddingHorizontal: Size(2),
  },
  menuItem: {
    paddingVertical: Size(6),
    // borderWidth: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    //borderBottomColor: Colors.lightGrey,
  },
  menuItemText: {
    fontSize: Size(4),
    fontFamily: "Bold",
    color: Colors.black,
  },
  arrowRight: {
    paddingTop: HeightSize(0.7),
  },
  versionCode: {
    position: "absolute",
    bottom: 25,
    textAlign: "center",
    width: "100%",
    fontSize: Size(4),
    fontFamily: "Regular",
    color: Colors.dark_grey,
  },
  logoutWrapper: {
    position: "absolute",
    bottom: 80,
    padding: 10,
    alignSelf: "center",
  },
  logoutText: {
    textAlign: "center",
    fontFamily: "PoppinsMedium",
    textDecorationLine: "underline",
    fontSize: 16,
    color: Colors.black,
  },
});

export default styles;
