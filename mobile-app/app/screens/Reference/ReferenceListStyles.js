import { StyleSheet } from "react-native";
import { HeightSize, Size } from "../../res/Size";
import { Colors } from "../../res/Colors";

const styles = StyleSheet.create({
  backSerachIconBox: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: Size(5),
    justifyContent: "space-between",
    paddingVertical: HeightSize(2),
    borderWidth: 1
  },
  containerStyle: {
    width: "100%",
  },
  textStyleInsideButton: {
    fontFamily: 'favelaBlack',
    fontSize: Size(4)
  },
  mainContainer: {
    marginHorizontal: Size(5),
    flex: 1
  },
  noReferencesText: {
    textAlign: "center",
    marginTop: HeightSize(5),
    fontFamily: "PoppinsBold",
    fontSize: Size(4),
    opacity: 0.5
  },
  titleText: {
    fontFamily: 'favelaBlack',

  },
  fixedButtonContainer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    paddingHorizontal: HeightSize(2),
    paddingVertical: HeightSize(1.5),
    zIndex: 10,
  },
  confirmationModalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  confirmationModalContent: {
    width: '80%',
    padding: 20,
    backgroundColor: Colors.white,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  confirmationText: {
    fontSize: 16,
    fontFamily: 'Regular',
    textAlign: 'center',
    marginBottom: 20,
  },
  confirmationButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  modalContainer: {
    flex: 1,
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  modalContent: {
    backgroundColor: "white",
    borderRadius: 10,
    padding: 20,
    minWidth: 200,
    position: "absolute",
  },
  menuOption: {
    paddingVertical: 10,
  },
  optionContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  text: {
    marginLeft: 10,
    fontSize: 16,
  },
  deleteText: {
    color: "red",
  },
})

export default styles;