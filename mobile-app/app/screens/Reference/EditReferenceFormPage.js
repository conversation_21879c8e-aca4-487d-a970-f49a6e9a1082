import React, { useEffect, useState } from 'react';
import { SafeAreaView, StatusBar, View, StyleSheet, Platform, TouchableOpacity, ScrollView, Text, Image } from 'react-native';
import { Colors } from '../../res/Colors';
import { commonStyles } from '../../res/CommonStyles';
import { HeightSize, Size } from '../../res/Size';
import { BackIcon } from '../../res/Svg';
import UserApi from '../../services/UserApi';
import { useSelector } from 'react-redux';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import EditReferenceForm from '../../components/References/EditReferenceForm';

const EditReferenceFormPage = ({ navigation, route }) => {
    const referenceId = route.params.referenceId || "";

    return (
        <SafeAreaView style={commonStyles.container}>
            <StatusBar barStyle="dark-content" hidden={false} backgroundColor={Colors.white} translucent={false} />
            <KeyboardAwareScrollView
                style={{ flex: 1 }}
                contentContainerStyle={{ paddingTop: Platform.OS === "ios" ? HeightSize(4) : 0 }}
                keyboardShouldPersistTaps="handled"
                extraScrollHeight={20} // Adjust for additional space above the keyboard
            >
                <View style={{ paddingTop: Platform.OS === "ios" ? HeightSize(4) : 0 }}>
                    <View style={styles.headerContainer}>
                        <TouchableOpacity onPress={() => navigation.goBack()}>
                            <BackIcon />
                        </TouchableOpacity>
                    </View>
                    <View style={styles.formContainer}>
                        <EditReferenceForm referenceId={referenceId} />
                    </View>
                </View>
            </KeyboardAwareScrollView>
        </SafeAreaView>

    )
}

const styles = StyleSheet.create({
    headerContainer: {
        flexDirection: "row",
        justifyContent: "space-between",
        paddingVertical: HeightSize(2),
        paddingRight: Size(15),
        paddingLeft: Size(5)
    },
    formContainer: {
        justifyContent: 'space-between',
        height: '60%',
    },
})

export default EditReferenceFormPage;