import React, { useEffect, useState } from "react";
import {
  SafeAreaView,
  StatusBar,
  View,
  StyleSheet,
  Platform,
  TouchableOpacity,
  ScrollView,
  Text,
  Image,
} from "react-native";
import { Colors } from "../../res/Colors";
import { commonStyles } from "../../res/CommonStyles";
import { HeightSize, Size } from "../../res/Size";
import { BackIcon } from "../../res/Svg";
import Client from "../../res/Client";
import ReferenceForm from "../../components/References/ReferenceForm";
import UserApi from "../../services/UserApi";
import { shallowEqual, useSelector } from "react-redux";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { selectUserInfo } from "../../redux/selectors/authSelectors";

const ReferenceFormPage = ({ navigation, route }) => {
  const [toUserDetails, seTtoUserDetails] = useState(null);
  const toUserId = route.params.toUserId || "";

  const userInfo = useSelector(selectUserInfo, shallowEqual);

  const isSameUser = userInfo.id === toUserId;

  const gettoUserinfo = async () => {
    try {
      const {
        data: { data },
      } = await UserApi.getUserProfile(toUserId);
      seTtoUserDetails(data);
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    gettoUserinfo();
  }, []);

  return (
    <SafeAreaView style={commonStyles.container}>
      <StatusBar
        barStyle="dark-content"
        hidden={false}
        backgroundColor={Colors.white}
        translucent={false}
      />
      <KeyboardAwareScrollView
        style={{ flex: 1 }}
        contentContainerStyle={{
          paddingTop: Platform.OS === "ios" ? HeightSize(4) : 0,
        }}
        keyboardShouldPersistTaps="handled"
        extraScrollHeight={20} // Adjust for additional space above the keyboard
      >
        <View style={{ paddingTop: Platform.OS === "ios" ? HeightSize(4) : 0 }}>
          <View style={styles.headerContainer}>
            <TouchableOpacity onPress={() => navigation.goBack()}>
              <BackIcon />
            </TouchableOpacity>
          </View>
          <View contentContainerStyle={styles.content}>
            {!isSameUser && (
              <View style={{ paddingHorizontal: Size(5) }}>
                <Text style={styles.title}>
                  Write reference to {toUserDetails?.firstName}
                </Text>
                <Text style={styles.subtitle}>
                  This reference will appear on {toUserDetails?.firstName}'s
                  Profile
                </Text>
                <View style={styles.userInfo}>
                  <Image
                    source={{ uri: toUserDetails?.photoUrl }}
                    style={styles.userImage}
                  />
                  <Text style={styles.userName}>
                    {toUserDetails?.firstName} {toUserDetails?.lastName}
                  </Text>
                </View>
              </View>
            )}

            <View style={styles.formContainer}>
              <ReferenceForm
                isSameUser={isSameUser}
                toUserDetails={isSameUser ? null : toUserDetails}
              />
            </View>
          </View>
        </View>
      </KeyboardAwareScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    // flex: 1,
    backgroundColor: Colors.white,
  },
  headerContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingVertical: HeightSize(2),
    paddingRight: Size(15),
    paddingLeft: Size(5),
  },
  page: {
    // flex: 1,
  },
  content: {
    padding: 20,
  },
  title: {
    fontSize: Size(4.8),
    color: "black",
    fontFamily: "PoppinsMedium",
  },
  subtitle: {
    fontSize: Size(3.4),
    color: "black",
    opacity: 0.5,
    fontFamily: "Regular",
  },
  userInfo: {
    flexDirection: "row",
    alignItems: "center",
    marginVertical: HeightSize(3),
  },
  userImage: {
    width: Size(12),
    height: Size(12),
    borderRadius: Size(6),
    marginRight: Size(3),
  },
  userName: {
    fontSize: Size(4.4),
    color: "black",
    fontFamily: "PoppinsBold",
  },
  formContainer: {
    // flex: 1,
    justifyContent: "space-between",
    height: "60%",
  },
});

export default ReferenceFormPage;
