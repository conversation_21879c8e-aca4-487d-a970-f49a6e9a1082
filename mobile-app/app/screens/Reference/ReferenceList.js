import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  ScrollView,
  SafeAreaView,
  StatusBar,
  TouchableOpacity,
  ActivityIndicator,
  Modal,
} from "react-native";
import { useNavigation, useRoute } from "@react-navigation/native";
import moment from "moment";
import { commonStyles } from "../../res/CommonStyles";
import { BackIcon } from "../../res/Svg";
import styles from "./ReferenceListStyles";
import Button from "../../components/Button/Button";
import { Colors } from "../../res/Colors";
import Tabs from "../../components/Tabs";
import ReferenceCard from "../../components/References/ReferenceCard";
import { HeightSize, Size } from "../../res/Size";
import ReferenceApi from "../../services/ReferenceApi";
import { useDispatch, useSelector } from "react-redux";
import CustomHeader from "../../components/custom-header/CustomHeader";
import { useIsFocused } from "@react-navigation/native";
import { notifySuccess } from "../../constants/misc";

const ReferenceList = () => {
  const route = useRoute();
  const navigation = useNavigation();

  const currentTab = route.params?.tab;

  const [selected, setSelected] = useState(currentTab || "Received");
  const [references, setReferences] = useState({
    given: [],
    received: [],
  });
  const [toUserIdDetails, setToUserIdDetails] = useState({});
  const [loading, setLoading] = useState(true); // Add loading state
  const [isDeleteReferenceModalVisible, setIsDeleteReferenceModalVisible] =
    useState(false);
  const [itemToDelete, setItemToDelete] = useState(null);

  const dispatch = useDispatch();
  const { userInfo } = useSelector((state) => state.auth.authUser);

  const toUserId = route.params?.toUserId;
  const focused = useIsFocused();

  // Fetching the user profile being visited
  const gettoUserDetails = async () => {
    try {
      const details = await dispatch.user.getUserProfileForGuest(toUserId);
      setToUserIdDetails(details);
    } catch (error) {
      console.error(error);
    }
  };

  const handleSelection = (tab) => {
    setSelected(tab);
    navigation.setParams({ tab });
  };

  const getUserReferences = async () => {
    try {
      const {
        data: { data },
      } = await ReferenceApi.getReferences(toUserId);
      setReferences(data);
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteButtonPress = (item) => {
    setItemToDelete(item);
    setIsDeleteReferenceModalVisible(true);
  };

  // API call function for deleting
  const handleDelete = async () => {
    try {
      // Replace with your actual API endpoint
      await ReferenceApi.deleteReference(itemToDelete);

      // Success: Item deleted
      notifySuccess("Reference Deleted Successfully");

      // Refresh the references list after deletion
      getUserReferences();

      // Close the modal after successful deletion
      setIsDeleteReferenceModalVisible(false);
    } catch (error) {
      console.error("Error deleting item:", error);

      // Notify user of the error
      notifyError("Error deleting reference. Please try again.");
    }
  };

  useEffect(() => {
    setLoading(true);
    getUserReferences();
  }, [focused]);

  useEffect(() => {
    gettoUserDetails();
  }, []);

  return (
    <SafeAreaView style={commonStyles.container}>
      <StatusBar
        barStyle="dark-content"
        hidden={false}
        backgroundColor={Colors.white}
        translucent={false}
      />
      <View style={{ marginTop: HeightSize(2) }}>
        <CustomHeader title={"REFERENCES"} />
      </View>
      <View style={styles.mainContainer}>
        {loading ? (
          <ActivityIndicator
            size="large"
            color={Colors.primary}
            style={{ flex: 1, justifyContent: "center", alignItems: "center" }}
          />
        ) : (
          <>
            {toUserIdDetails.userType === "NON_PLAYER" && (
              <Tabs
                tabs={["Received", "Given"]}
                selected={selected}
                setSelected={handleSelection}
                color="black"
                inactiveColor={"gray"}
                style={{ paddingBottom: HeightSize(2) }}
                isConnections={false}
              />
            )}
            <ScrollView
              contentContainerStyle={{
                paddingBottom: HeightSize(20),
              }}
              showsVerticalScrollIndicator={false}
            >
              {selected === "Received" ? (
                references.received.length > 0 ? (
                  references.received
                    .sort(
                      (a, b) => new Date(b.createdAt) - new Date(a.createdAt)
                    ) // Sort in descending order
                    .map((item, index) => (
                      <ReferenceCard
                        key={index}
                        name={item.fromProfileName}
                        relationship={item.relationship}
                        title={item?.fromRole.join(",")}
                        date={moment(item.createdAt).format("MMMM, DD YYYY")}
                        message={item.message}
                        photo={item?.fromProfilePhoto}
                        userId={item?.fromProfileId}
                        type="received"
                        referenceId={item?.id}
                        onDeletePress={() => handleDeleteButtonPress(item.id)}
                        toUserId={toUserId}
                        fromProfileId={item?.fromProfileId}
                        toProfileId={item?.toProfileId}
                      />
                    ))
                ) : (
                  <Text style={styles.noReferencesText}>
                    No references received
                  </Text>
                )
              ) : null}

              {selected === "Given" ? (
                references.given.length > 0 ? (
                  references.given
                    .sort(
                      (a, b) => new Date(b.createdAt) - new Date(a.createdAt)
                    )
                    .map((item, index) => (
                      <ReferenceCard
                        key={index}
                        relationship={item.relationship}
                        name={item?.toProfileName}
                        title={item?.toRole.join(",")}
                        date={moment(item.createdAt).format("MMMM, DD YYYY")}
                        message={item.message}
                        photo={item?.toProfilePhoto}
                        userId={item?.toProfileId}
                        type="given"
                        onDeletePress={() => handleDeleteButtonPress(item.id)}
                        referenceId={item?.id}
                        toUserId={toUserId}
                        fromProfileId={item?.fromProfileId}
                        toProfileId={item?.toProfileId}
                      />
                    ))
                ) : (
                  <Text style={styles.noReferencesText}>
                    No references given
                  </Text>
                )
              ) : null}
            </ScrollView>

            {userInfo?.userType === "NON_PLAYER" && (
              <View style={styles.fixedButtonContainer}>
                <Button
                  title="WRITE REFERENCE"
                  backgroundColor={"black"}
                  containerStyle={styles.containerStyle}
                  textStyleInsideButton={styles.textStyleInsideButton}
                  onPress={() =>
                    navigation.navigate("ReferenceFormPage", {
                      toUserId: toUserId,
                    })
                  }
                />
              </View>
            )}
          </>
        )}
      </View>

      {/* Delete Reference Modal */}
      <Modal
        visible={isDeleteReferenceModalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setIsDeleteReferenceModalVisible(false)}
      >
        <View style={styles.confirmationModalContainer}>
          <View style={styles.confirmationModalContent}>
            <Text style={styles.confirmationText}>
              Are you sure you want to
              <Text style={{ color: Colors.red }}> delete </Text>
              this Reference?
            </Text>

            <View style={styles.confirmationButtonContainer}>
              <Button
                title="Yes"
                onPress={handleDelete}
                containerStyle={{ width: "45%" }}
                backgroundColor={Colors.black}
                textStyleInsideButton={{
                  fontSize: Size(4),
                  fontFamily: "Regular",
                }}
                height={HeightSize(5)}
              />
              <Button
                title="No"
                onPress={() => setIsDeleteReferenceModalVisible(false)}
                containerStyle={{
                  borderWidth: 1,
                  borderColor: "#A5A5A5",
                  width: "45%",
                }}
                backgroundColor={Colors.white}
                textStyleInsideButton={{
                  color: "#A5A5A5",
                  fontSize: Size(4),
                  fontFamily: "Regular",
                }}
                height={HeightSize(5)}
              />
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

export default ReferenceList;
