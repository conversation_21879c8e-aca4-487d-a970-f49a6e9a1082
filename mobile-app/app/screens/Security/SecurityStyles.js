import { Platform, StyleSheet } from "react-native";
import { HeightSize, Size } from "../../res/Size";

const styles = StyleSheet.create({
  backBtn: {
    marginBottom: HeightSize(3)
  },
  containerStyle: {
    //marginTop: HeightSize(12)
  },
  inputContainer: {
   paddingTop: HeightSize(3),
   paddingBottom: HeightSize(32)
  },
  input: {
    borderBottomWidth: 1,
    //borderColor: '#ccc',
    marginBottom: HeightSize(1),
    //paddingVertical: HeightSize(1),
    paddingVertical: Platform.OS === "ios" ? HeightSize(1) : 0,
  },
  labelTxt: {
    opacity: 0.4,
    fontSize: Size(3.5),
    fontFamily: "Regular",
    marginTop: HeightSize(2)
  }
});

export default styles;