import React, { useState } from "react";
import { Platform, SafeAreaView, ScrollView, StatusBar, Text, TextInput, TouchableOpacity, View } from "react-native";
import { commonStyles } from "../../res/CommonStyles";
import { HeightSize, Size } from "../../res/Size";
import { BackIcon } from "../../res/Svg";
import { Colors } from "../../res/Colors";
import styles from "./SecurityStyles";
import Button from "../../components/Button/Button";
import * as yup from 'yup';
import { useFormik } from "formik";
import { notifySuccess } from "../../constants/misc";
import { dispatch } from "../../redux/store";
import { useSelector } from "react-redux";

const Security = ({ navigation }) => {
  const [loading, setLoading] = useState(false);
  const initialValues = {
    oldPassword: '',
    newPassword: '',
    confirmPassword: '',
  };

  const validationSchema = yup.object().shape({
    oldPassword: yup.string().required('Please enter your current password'),
    newPassword: yup.string().required('Please enter a new password'),
    confirmPassword: yup.string()
      .oneOf([yup.ref('newPassword'), null], 'Passwords must match')
      .required('Please confirm your new password'),
  });

  const submitPasswordChange = async (values) => {
    const cleanedValues = {
      oldPassword: values.oldPassword,
      newPassword: values.newPassword,
    };

    try {
      const response = await dispatch.auth.changePassword(cleanedValues);

      if (response === 1) {
        navigation.navigate('MyProfile');
        notifySuccess("Password Changed Successfully!")
      } else {
        // Handle other cases if necessary
      }
    } catch (error) {
      console.error('Password change failed:', error);
    }
  };

  const { values, errors, touched, handleBlur, handleChange, handleSubmit } =
    useFormik({
      initialValues: initialValues,
      onSubmit: submitPasswordChange,
      validationSchema: validationSchema,
    });

  return (
    <SafeAreaView style={commonStyles.container}>
      <StatusBar barStyle="dark-content" hidden={false} backgroundColor={Colors.white} translucent={false} />
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={[commonStyles.subContainer, { marginTop: HeightSize(4) }]}>
          <TouchableOpacity onPress={() => { navigation.goBack() }} activeOpacity={9} style={styles.backBtn}>
            <BackIcon width={Platform.isPad ? 40 : 24} height={Platform.isPad ? 40 : 24} />
          </TouchableOpacity>
          <Text style={commonStyles.boldText}>SECURITY</Text>
          <View style={styles.inputContainer}>
            <Text style={styles.labelTxt}>Current Password</Text>
            <TextInput
              onBlur={handleBlur('oldPassword')}
              value={values.oldPassword}
              onChangeText={handleChange('oldPassword')}
              secureTextEntry={true}
              //placeholder="Current Password"
              style={styles.input}
            />
            {touched.oldPassword && errors.oldPassword &&
              <Text style={commonStyles.errTxt}>{errors.oldPassword}</Text>
            }
            <Text style={styles.labelTxt} >New Password</Text>
            <TextInput
              onBlur={handleBlur('newPassword')}
              value={values.newPassword}
              onChangeText={handleChange('newPassword')}
              secureTextEntry={true}
              //placeholder="New Password"
              style={styles.input}
            />
            {touched.newPassword && errors.newPassword &&
              <Text style={commonStyles.errTxt}>{errors.newPassword}</Text>
            }
            <Text style={styles.labelTxt}>Confirm New Password</Text>
            <TextInput
              onBlur={handleBlur('confirmPassword')}
              value={values.confirmPassword}
              onChangeText={handleChange('confirmPassword')}
              secureTextEntry={true}
              //placeholder="Confirm New Password"
              style={styles.input}
            />
            {touched.confirmPassword && errors.confirmPassword &&
              <Text style={commonStyles.errTxt}>{errors.confirmPassword}</Text>
            }
          </View>
        </View>
        <Button
          title={"DONE"}
          onPress={() => {
            handleSubmit()
          }}
          progress={loading}
          disable={loading}
          backgroundColor={Colors.black}
          containerStyle={styles.containerStyle}
        />
      </ScrollView>
    </SafeAreaView>
  )
}

export default Security;