// REACT //
import React from "react";

// REACT NATIVE //
import {
  SafeAreaView,
  StatusBar,
  Text,
  TouchableOpacity,
  View,
  FlatList,
  Platform,
} from "react-native";

// OTHERS //
import styles from "./PrivacySecurityStyles";
import { BackIcon, SettingShareIcon } from "../../res/Svg";
import { commonStyles } from "../../res/CommonStyles";
import { Colors } from "../../res/Colors";
import { HeightSize } from "../../res/Size";

const PrivacySecurity = ({ navigation }) => {
  const menuOptions = [
    {
      key: "Terms and conditions",
      label: "TERMS AND CONDITIONS",
      url: "https://join.playerapp.co/tcs",
    },
    {
      key: "Privacy Policy",
      label: "PRIVACY POLICY",
      url: "https://join.playerapp.co/privacypolicy",
    },
  ];

  const handleItemPress = (item) => {
    navigation.navigate("WebViewScreen", {
      url: item.url,
      title: item.label,
    });
  };

  const renderItem = ({ item }) => (
    <TouchableOpacity
      style={styles.menuItem}
      onPress={() => handleItemPress(item)}
    >
      <Text style={styles.menuItemText}>{item.label}</Text>
      <View style={styles.arrowRight}>
        <SettingShareIcon />
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={commonStyles.container}>
      <StatusBar
        barStyle="dark-content"
        hidden={false}
        backgroundColor={Colors.white}
        translucent={false}
      />
      <View style={[commonStyles.subContainer, { marginTop: HeightSize(4) }]}>
        <TouchableOpacity
          onPress={() => {
            navigation.goBack();
          }}
          activeOpacity={9}
          style={styles.backBtn}
        >
          <BackIcon
            width={Platform.isPad ? 40 : 24}
            height={Platform.isPad ? 40 : 24}
          />
        </TouchableOpacity>
        <Text style={commonStyles.boldText}>PRIVACY AND SECURITY</Text>
        <FlatList
          data={menuOptions}
          renderItem={renderItem}
          keyExtractor={(item) => item.key}
          contentContainerStyle={styles.menuList}
        />
      </View>
    </SafeAreaView>
  );
};

export default PrivacySecurity;
