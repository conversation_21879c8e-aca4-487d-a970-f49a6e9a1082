import { StyleSheet } from "react-native";
import { HeightSize, Size } from "../../res/Size";
import { Colors } from "../../res/Colors";

const styles = StyleSheet.create({
  backBtn: {
    marginBottom: HeightSize(3),
  },
  menuList: {
    marginTop: HeightSize(4),
  },
  menuItem: {
    paddingVertical: Size(6),
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  menuItemText: {
    fontSize: Size(4),
    fontFamily: "Bold",
    color: Colors.black,
  },
  arrowRight: {
    paddingTop: HeightSize(0.7),
  },
  containerStyle: {
    marginTop: HeightSize(50),
    borderWidth: 2,
    borderColor: Colors.red,
    borderRadius: Size(3),
    width: "60%",
  },
});

export default styles;
