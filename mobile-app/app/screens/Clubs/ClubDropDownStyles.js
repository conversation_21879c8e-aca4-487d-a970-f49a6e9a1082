import { StyleSheet } from "react-native";
import { HeightSize, Size } from "../../res/Size";

const styles = StyleSheet.create({
  skipTxt: {
    textAlign: "right",
    textDecorationLine: "underline",
    fontSize: Size(4.3),
    marginRight: Size(3)
  },
  iconContainer: {
    flexDirection: "row",
    marginHorizontal: Size(5),
    marginTop: HeightSize(5),
    justifyContent: "space-between"
  },
  playText: {
    paddingVertical: HeightSize(4)
  },
  modalContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  closeButton: {
    width: '100%',
    alignItems: "flex-end",
  },
  modalContent: {
    backgroundColor: "white",
    paddingVertical: HeightSize(1),
    borderRadius: Size(7),
    elevation: 5,
    width: "90%",
    marginTop: HeightSize(40),
    paddingHorizontal: Size(3),
    marginBottom: HeightSize(5)
  },
  nextBtn: {
    width: "90%",
    position: "absolute",
    bottom: HeightSize(2)
  },
  radiobtnStyle: {
    justifyContent: "center",
    alignItems: "center",
    paddingTop: HeightSize(2)
  },
  radioBtnContainer: {
    alignSelf: "center",
    // marginTop: HeightSize(40)
  },
  teamStyle: {
    paddingVertical: HeightSize(0.5),
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0, 0, 0, 0.1)",
    paddingHorizontal: Size(2)
  }
})

export default styles;