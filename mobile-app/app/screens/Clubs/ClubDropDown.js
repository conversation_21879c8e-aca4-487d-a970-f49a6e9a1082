// REACT //
import React, { useEffect, useState } from "react";

// PLUGINS //
import { SvgUri } from "react-native-svg";

// COMPONENTS //
import {
  BackHandler,
  FlatList,
  Modal,
  SafeAreaView,
  StatusBar,
  Text,
  TouchableOpacity,
  View,
  Image,
  ActivityIndicator,
  TouchableWithoutFeedback,
} from "react-native";
import CustomTextInput from "../../components/TextInput";
import ClubList from "../../components/club/Club";
import RadioButton from "../../components/RadioButton/RadioButton";
import Button from "../../components/Button/Button";

// SERVICES //
import TeamApi from "../../services/TeamApi";
import AuthApi from "../../services/AuthApi";

// OTHERS //
import { commonStyles } from "../../res/CommonStyles";
import { Colors } from "../../res/Colors";
import styles from "./ClubDropDownStyles";
import { BackIcon } from "../../res/Svg";
import { HeightSize, Size } from "../../res/Size";
import Client from "../../res/Client";

const ClubDropDown = ({ route, navigation }) => {
  const [name, setName] = useState("");
  const [modalVisible, setModalVisible] = useState(false);
  const [team, setTeam] = useState("");
  const [teamId, setTeamId] = useState("");
  const [logo, setLogo] = useState(null);
  const [currentPageNumber, setCurrentPageNumber] = useState(0);
  const [clubId, setClubId] = useState("");
  const [teamList, setTeamList] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showErrMsg, setShowErrMsg] = useState(false);
  const [profile, setProfile] = useState(null);
  const email = route?.params?.email || "";
  const password = route?.params?.password || "";
  const firstName = route?.params?.firstName || "";
  const lastName = route?.params?.lastName || "";
  const phoneNumber = route?.params?.phoneNumber || "";
  const position = route?.params?.position || "";
  const preferredFoot = route?.params?.preferredFoot || "";
  const gender = route?.params?.gender || "";
  const birthday = route?.params?.birthday || "";
  const location = route?.params?.location || "";
  const height = route?.params?.height || "";
  const photoUrl = route?.params?.photoUrl || "";

  //console.log("profile", profile);

  useEffect(() => {
    const backHandler = () => {
      if (currentPageNumber === 0) {
        navigation.goBack();
      } else {
        setCurrentPageNumber(currentPageNumber - 1);
      }
      return true;
    };

    BackHandler.addEventListener("hardwareBackPress", backHandler);
    return () => {
      BackHandler.removeEventListener("hardwareBackPress", backHandler);
    };
  }, [currentPageNumber, navigation]);

  useEffect(() => {
    getTeams();
  }, [clubId]);

  const getTeams = async () => {
    try {
      const response = await TeamApi.teamsByClubId(`teams?clubId=${clubId}`);
      if (response && response.data) {
        // const teamNames = response.data?.data?.map((team, index) => {
        //   console.log("Team Name:", team.teamName);
        //   console.log("Team ID:", team.id);
        //   return {
        //     id: index + 1,
        //     team: team.teamName,
        //     teamId: team.id
        //   };
        // });
        const teamData = response.data?.data;
        const teamNames = [];

        if (teamData) {
          for (let i = 0; i < teamData.length; i++) {
            const team = teamData[i];
            teamNames.push({
              id: i + 1,
              team: team.teamName,
              teamId: team.id,
            });
          }
        }

        setTeamList(teamNames);
        setLoading(false); // Set loading to false when data is fetched
      } else {
        console.error("Response or response data is empty.");
      }
    } catch (err) {
      console.error("team err ---", err);
    }
  };

  const signUp = async () => {
    try {
      const data = {
        email: email,
        password: password,
        firstName: firstName,
        lastName: lastName,
        phoneNumber: `+91${phoneNumber}`,
        position: position,
        preferredFoot: preferredFoot,
        gender: gender,
        birthday: birthday,
        location: location,
        height: height,
        photoUrl: photoUrl,
        clubName: name,
        teamName: team,
        clubId: clubId,
        teamId: teamId,
        cludId: clubId,
      };
      const response = await AuthApi.signup(data);

      if (response && response?.data?.message === "Signed up sucessfully") {
        // navigation.navigate("BottomStack", {
        //   screen: "Feed",
        //   params: {
        //     firstNameFromSignUp: firstName,
        //     fromSignUp: true
        //   }
        // });
        navigation.navigate("AuthNavigation", { screen: "Login" });
      }
    } catch (err) {
      // setLoading(false);
      console.error(err);
    }
  };

  const renderClubLogo = (url) => {
    if (url.includes(".svg")) {
      return <SvgUri uri={url} width={30} height={30} />;
    } else {
      return <Image source={{ uri: url }} style={{ width: 30, height: 30 }} />;
    }
  };

  const handleClubSelect = (clubName, clubLogoUrl, clubId) => {
    setName(clubName);
    setLogo(clubLogoUrl);
    setClubId(clubId);
    setModalVisible(false);
    setCurrentPageNumber(1);
    getTeams();
  };

  const handleBack = () => {
    if (currentPageNumber === 0) {
      navigation.goBack();
    } else {
      setCurrentPageNumber(currentPageNumber - 1);
    }
  };

  const renderTeam = ({ item }) => {
    return (
      <TouchableOpacity
        onPress={() => {
          setTeam(item.team);
          setTeamId(item.teamId);
        }}
        style={[
          styles.teamStyle,
          {
            backgroundColor: team === item.team ? Colors.green : Colors.white,
          },
        ]}
      >
        <Text style={commonStyles.regularText}>{item.team}</Text>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={commonStyles.container}>
      <StatusBar
        barStyle="dark-content"
        hidden={false}
        backgroundColor={Colors.white}
        translucent={false}
      />
      <View style={styles.iconContainer}>
        <TouchableOpacity onPress={() => handleBack()}>
          <BackIcon />
        </TouchableOpacity>
        {/* <Text style={[commonStyles.regularText, styles.skipTxt]}>Skip</Text> */}
      </View>
      <View style={commonStyles.subContainer}>
        {currentPageNumber === 0 && (
          <View>
            <Text style={commonStyles.boldText}>CHOOSE YOUR CLUB</Text>
            <Text style={[commonStyles.regularText, styles.playText]}>
              Your most recent or current club?
            </Text>
            <CustomTextInput
              label={"Club Name"}
              textInputStyle={commonStyles.textInputStyle}
              buttonInsteadOfTextInput={true}
              onPress={() => setModalVisible(true)}
              selectedName={name}
              labelTxt={{ paddingBottom: HeightSize(1) }}
            />

            <Modal visible={modalVisible} transparent>
              <TouchableWithoutFeedback onPress={() => setModalVisible(false)}>
                <View style={styles.modalContainer}>
                  <View style={styles.modalContent}>
                    <ClubList onSelect={handleClubSelect} />
                  </View>
                </View>
              </TouchableWithoutFeedback>
            </Modal>
          </View>
        )}
        {(currentPageNumber === 1 || currentPageNumber === 2) && (
          <View style={{ flexDirection: "row", marginBottom: HeightSize(2) }}>
            {renderClubLogo(logo)}
            <Text style={[commonStyles.boldText, { marginLeft: Size(4) }]}>
              {name}
            </Text>
          </View>
        )}

        {currentPageNumber === 1 && (
          <View>
            <Text
              style={[
                commonStyles.boldText,
                { paddingVertical: HeightSize(2), fontSize: Size(3.8) },
              ]}
            >
              CHOOSE YOUR TEAM:
            </Text>
            {loading ? (
              <ActivityIndicator
                size="large"
                color={Colors.green}
                style={{ marginVertical: HeightSize(10) }}
              />
            ) : (
              <>
                {teamList.length > 0 ? (
                  <FlatList
                    data={teamList}
                    renderItem={renderTeam}
                    keyExtractor={(item, index) => index.toString()}
                  />
                ) : (
                  <Text style={commonStyles.regularText}>No team found</Text>
                )}
              </>
            )}
            {/* {showErrMsg && team == "" && <Text style={commonStyles.errTxt}>Please Select Your Team</Text>} */}
          </View>
        )}

        {currentPageNumber === 2 && (
          <View>
            <Text
              style={[
                commonStyles.boldText,
                { paddingVertical: HeightSize(2), fontSize: Size(3.8) },
              ]}
            >
              YOUR TEAM:
            </Text>
            <Text style={[commonStyles.regularText]}>{team}</Text>
          </View>
        )}
      </View>
      {/* <View style={[styles.radioBtnContainer, { marginTop: currentPageNumber === 0 || currentPageNumber === 2 ? HeightSize(43) : HeightSize(10) }]}>
        <RadioButton
          label={"Are You Club Admin?"}
          unselectedImage={require("../../../assets/notselected.png")}
          selectedImage={require("../../../assets/tick.png")}
          // selected={remember}
          // onSelect={handleRemember}
          style={styles.radiobtnStyle}
        />

        <RadioButton
          label={"You Agree That You Have Authority"}
          unselectedImage={require("../../../assets/notselected.png")}
          selectedImage={require("../../../assets/tick.png")}
          // selected={remember}
          // onSelect={handleRemember}
          style={styles.radiobtnStyle}
        />
      </View> */}
      {(currentPageNumber === 1 || currentPageNumber === 2) && (
        <Button
          title={"NEXT"}
          containerStyle={styles.nextBtn}
          onPress={() => {
            // setShowTeam(true);
            // if (team !== "") {
            //   setCurrentPageNumber(2);
            //   signUp();
            // } else {
            //   setShowErrMsg(true);
            // }
            signUp();
          }}
          backgroundColor={Colors.black}
        />
      )}
    </SafeAreaView>
  );
};

export default ClubDropDown;
