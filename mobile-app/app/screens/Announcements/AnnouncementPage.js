// REACT //
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useSelector } from "react-redux";

// PLUGINS //
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";

// COMPONENTS //
import Competition from "../../components/Competitions/Competitions";
import {
  ActivityIndicator,
  FlatList,
  SafeAreaView,
  ScrollView,
  StatusBar,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  Image,
  BackHandler,
} from "react-native";
import { Images } from "../../res/Images";
import BlockReportModal from "../../components/BlockReportModal";

// CONSTANTS //
import { notifySuccess } from "../../constants/misc";

// OTHERS //
import { dispatch } from "../../redux/store";
import styles from "./AnnouncementsPageStyles";
import { HeightSize, Size } from "../../res/Size";
import { Colors } from "../../res/Colors";
import { commonStyles } from "../../res/CommonStyles";
import { SendMsgIcon, MenuIcon, ReportIcon, BackIcon } from "../../res/Svg";
import moment from "moment";
import ConfirmationModal from "../FeedDetail/ConfirmationModal";
import NotFound from "../../components/NotFound";
import analytics from "../../services/analytics";
import UserAvatar from "../../components/UserAvatar";
const LiveLike = require("@livelike/javascript");

const AnnouncementPage = ({ navigation, route }) => {
  const { userInfo } = useSelector((state) => state.auth.authUser);

  const [fetchedComments, setFetchedComments] = useState([]);
  const [commentsCount, setCommentsCount] = useState(0);
  const [commentsLoading, setCommentsLoading] = useState(false);
  const [sendLoading, setSendLoading] = useState(false);
  const [comment, setComment] = useState("");
  const [blockReportModalVisible, setBlockReportModalVisible] = useState(false);
  const [selectedCommentId, setSelectedCommentId] = useState(null);
  const [confirmationVisible, setConfirmationVisible] = useState(false);
  const [actionType, setActionType] = useState(null);
  const [reportType, setReportType] = useState(null);
  const [selectedComment, setSelectedComment] = useState(null);
  const [modalPosition, setModalPosition] = useState({ top: 0, right: 0 });
  const [selectedUserId, setSelectedUserId] = useState(null);
  const [shouldFetchMore, setNextCusor] = useState(false);
  const [commentId, setCommentId] = useState(null);
  const [commentBoardId, setCommentBoardId] = useState(null);
  const [shouldShowEmojiPanel, setShouldShowEmojiPanel] = useState({
    visible: false,
    highlightId: "",
  });
  const [showLoginPopup, setShowLoginPopup] = useState(false);

  const commentInputRef = useRef(null);
  const { announcementId } = route.params || {};

  const { activeAnnouncements } = useSelector(
    ({ announcement }) => announcement
  );

  const { loggedUserId } = useSelector(({ auth: { authUser }, user }) => ({
    loggedUserId: authUser?.userInfo?.id || "",
    authUser,
  }));

  const announcement = useMemo(() => {
    if (activeAnnouncements && activeAnnouncements.length > 0) {
      const foundAnnouncement = activeAnnouncements.find(
        (item) => item.id === announcementId
      );
      return foundAnnouncement;
    }
  }, [activeAnnouncements, announcementId]);

  useEffect(() => {
    const backAction = () => {
      navigation.goBack();
      return true;
    };

    const backHandler = BackHandler.addEventListener(
      "hardwareBackPress",
      backAction
    );

    return () => backHandler.remove();
  }, []);

  const openBlockReportModal = (idx, nativeEvent, userId) => {
    setSelectedCommentId(idx);
    setSelectedComment(fetchedComments[idx]);
    setModalPosition({
      top: nativeEvent.pageY,
      right: Size(5), // Adjust the 'right' value as needed, since 'pageX' gives absolute X coordinate from the left
    });
    setSelectedUserId(userId);
    setBlockReportModalVisible(true);
  };

  const closeBlockReportModal = () => {
    setBlockReportModalVisible(false);
    setSelectedCommentId(null);
  };

  const handleOpenConfirmation = (action, type) => {
    closeBlockReportModal();
    setActionType(action); // 'block' or 'report'
    setReportType(type); // 'COMMENT' or 'POST'
    setConfirmationVisible(true);
  };

  const handleDelete = async () => {
    if (!commentId || !commentBoardId) {
      console.error(
        "Invalid parameters: commentId or commentBoardId is missing."
      );
      return;
    }

    try {
      // Calling deleteComment with proper parameters
      const response = await LiveLike.deleteComment({
        commentBoardId: commentBoardId,
        commentId: commentId,
      });

      // Check if the response is successful (status code 204)
      if (response.status === 204) {
        notifySuccess("Comment deleted successfully.");

        setCommentId(null);
        setCommentBoardId(null);

        setFetchedComments((prevComments) => {
          const updatedComments = prevComments.filter((comment) => {
            const isDeleted =
              comment.id === commentId || comment.comment_id === commentId;
            return !isDeleted;
          });
          return updatedComments;
        });
      } else {
        console.error("Failed to delete comment. Response:", response);
        notifyError("Failed to delete comment. Please try again.");
      }
    } catch (error) {
      console.error("Error deleting comment:", error);
      notifyError("Error deleting comment. Please try again.");
    }
  };

  const handleConfirmAction = (reportType) => {
    setConfirmationVisible(false);

    if (actionType === "block") {
      handleBlockUser();
    } else if (actionType === "report") {
      // Pass the reportType to handleReportComment if needed
      handleReportComment(reportType);
    } else if (actionType === "delete") {
      handleDelete();
    }
    closeBlockReportModal();
  };

  const handleBlockUser = async () => {
    try {
      if (!selectedUserId) {
        console.error("No user selected to block");
        return;
      }

      const payload = {
        blockedUserId: selectedUserId,
        reason: "", // Optionally include a reason for blocking
      };

      // Dispatch the blockUser action
      const status = await dispatch.user.blockUser(payload);

      if (status.status === 1) {
        // Fetch the updated userInfo before navigating
        await dispatch.user.fetchUserDetails(userInfo.id);
        // Navigate back to MessageList and pass the blocked user ID
        navigation.navigate("Feed");
      }
    } catch (err) {
      console.error(
        "Error in blocking user:",
        err.response ? err.response.data : err.message
      );
    }
  };

  // Report comment function
  const handleReportComment = async (reportType) => {
    // Get reporter's full name
    const reporterFullName = `${userInfo?.firstName || "Unknown"} ${
      userInfo?.lastName || "User"
    }`;

    // Determine reported user's full name and ID based on reportType
    let reportedFullName = "Unknown User";
    let reportedUserId = "UnknownUserId";
    let reportedContent = "No content";

    if (reportType === "POST") {
      // Report type is POST
      reportedFullName = `${announcement?.user?.firstName || "Unknown"} ${
        announcement?.user?.lastName || "User"
      }`;
      reportedUserId = announcement?.userId || "UnknownUserId";
      reportedContent = announcement?.comment || "No content";
    } else if (reportType === "COMMENT") {
      // Report type is COMMENT
      const authorData = JSON.parse(selectedComment?.custom_data || "{}");

      reportedFullName = `${authorData.firstName || "Unknown"} ${
        authorData.lastName || "User"
      }`;
      reportedUserId = authorData.id || "UnknownUserId";
      reportedContent = selectedComment?.text || "No content";
    }

    // Prepare the payload for the report
    const payload = {
      reporterUserId: loggedUserId || "UnknownUserId",
      reportedUserId: reportedUserId,
      reporterFullName: reporterFullName,
      reportedFullName: reportedFullName,
      reason: "",
      reportedContent: reportedContent,
      reportedPhotoUrl: "", // Optional
      reporterPhotoUrl: userInfo?.photoUrl || "",
      reportType: reportType,
      contendId: reportType === "POST" ? highlightId : selectedComment?.id,
      ...(reportType === "COMMENT" && {
        metadata: {
          announcementId: announcementId || announcement?.id,
        },
      }),
    };

    // Submit the report
    try {
      const status = await dispatch.user.submitReport(payload);
      if (status === 1) {
        await dispatch.user.fetchUserDetails(userInfo.id);
      } else {
        console.error("Report submission failed");
      }
    } catch (error) {
      console.error("Error in submitting report:", error.message);
    }

    // Close the modal
    closeBlockReportModal();
  };

  const createcommentBoard = async () => {
    try {
      const commentBoard = await LiveLike.createCommentBoard({
        title: announcementId || announcement?.id,
        customId: announcement.id,
        repliesDepth: 2,
        allowComments: true,
        customData: `created by ${
          announcement?.user?.id || announcement?.title
        } on ${Date.now()}`,
      });
      if (announcementId) {
        dispatch.announcement.updateAnnouncement({
          id: announcement?.id || announcementId,
          commentBoardId: commentBoard.id,
          totalCommentCount: 0,
        });
      }
      return commentBoard;
    } catch (error) {
      console.error(error);
    }
  };

  const fetchComments = async (cursor) => {
    if (loggedUserId) {
      setCommentsLoading(true);

      let commentBoardId = "";

      try {
        if (!announcement?.commentBoardId) {
          const getCommentBoardDetail = await LiveLike.getCommentBoardDetails({
            customId: announcementId || announcement?.id,
          });

          if (getCommentBoardDetail.id) {
            commentBoardId = getCommentBoardDetail.id;
            if (announcementId) {
              dispatch.announcement.updateAnnouncement({
                id: announcementId,
                commentBoardId: getCommentBoardDetail.id,
              });
            }
          }
        } else {
          commentBoardId = announcement?.commentBoardId || "";
        }
      } catch (error) {
        setCommentsLoading(false);
        if (error === "Resource not found") {
          createcommentBoard();
        }
      }

      // Check if the comment board id has been created or fetched
      if (commentBoardId) {
        try {
          const getCommentPayload = {
            commentBoardId,
            sorting: LiveLike.CommentSort.NEWEST,
          };

          let commentResponse;

          if (cursor && !cursor.done) {
            commentResponse = await Promise.resolve(cursor.next());
            const filteredComments = Array.isArray(
              commentResponse?.value?.results
            )
              ? commentResponse.value.results.filter(
                  (comment) => comment.is_deleted !== true
                )
              : [];

            setFetchedComments([...fetchedComments, ...filteredComments]);

            // Only reset the cursor if there are no more comments to fetch
            if (commentResponse.done) {
              setNextCusor(!commentResponse.done);
            }
          } else {
            commentResponse = await LiveLike.getComments(getCommentPayload);

            // Ensure that commentResponse?.results is an array and filter out deleted comments
            const filteredComments = Array.isArray(commentResponse?.results)
              ? commentResponse.results.filter(
                  (comment) => comment.is_deleted !== true
                )
              : [];

            // Set the filtered comments to the state
            setFetchedComments(filteredComments);

            if (!commentResponse.done) {
              setNextCusor(commentResponse);
            }
          }

          setCommentsCount(commentResponse.count);
          setCommentsLoading(false);
        } catch (error) {
          console.error(error);
          setCommentsLoading(false);
        }
      }
    }
  };

  const addUserComment = async () => {
    if (!announcement.id) {
      console.warn("No announcement ID found, exiting function.");
      return;
    }

    setSendLoading(true);

    let commentBoardId = "";

    try {
      if (!announcement.commentBoardId) {
        if (!announcementId) {
          console.error(
            "announcementId is undefined, unable to fetch comment board details."
          );
          throw new Error("anouncementId is required but is undefined.");
        }

        const getCommentBoardDetail = await LiveLike.getCommentBoardDetails({
          customId: announcementId || announcement?.id,
        });

        if (getCommentBoardDetail?.id) {
          commentBoardId = getCommentBoardDetail.id;
        } else {
          console.warn("No valid comment board ID returned.");
        }
      } else {
        commentBoardId = announcement.commentBoardId || "";
      }

      if (commentBoardId) {
        const newComment = {
          id: userInfo.id,
          firstName: userInfo.firstName,
          lastName: userInfo.lastName,
          clubName: userInfo.clubName || "",
          teamName: userInfo.teamName || "",
          photoUrl: userInfo.photoUrl || "/images/profile.png",
        };

        const addComments = await LiveLike.addComment({
          text: comment,
          customData: JSON.stringify(newComment),
          commentBoardId,
        });

        if (addComments) {
          const currentComments = [...fetchedComments];
          const currentCount = commentsCount || 0;

          // Update local state with the new comment
          setFetchedComments([
            {
              custom_data: JSON.stringify(newComment),
              text: comment,
              comment_board_id: addComments.comment_board_id,
              comment_id: addComments.id,
            },
            ...currentComments,
          ]);
          setCommentsCount(currentCount + 1);

          notifySuccess("Comment Added");
          setSendLoading(false);
          if (commentInputRef.current) {
            commentInputRef.current.value = "";
          }
          setComment("");
        } else {
          console.warn("Failed to add comment.");
        }
      } else {
        console.error("No valid commentBoardId found, comment not added.");
      }
    } catch (error) {
      setSendLoading(false);
      console.error("Error occurred while adding comment:", error);

      // Revert state changes if error occurs
      setFetchedComments(currentComments);
      setCommentsCount(currentCount);
    }
  };

  const parseString = (data, purpose = "userName") => {
    const rawData = data.split("/");
    const output = purpose === "userName" ? rawData[0] : rawData[1];
    return output.replace(/\s/g, "");
  };

  const getAuthorDataFromCommentCustomData = (customData) => {
    try {
      // Handle new case of the customData for comments
      return JSON.parse(customData);
    } catch (error) {
      // Handle old case of the customData for comments
      console.error("Invalid JSON in custom_data:", error);
      const firstName = parseString(customData, "userName");
      const id = parseString(customData, "id");
      return { firstName, id };
    }
  };

  const Avatar = ({ id, name, text }) => {
    // Get the first letter of the name or use a default placeholder
    // const initial = name ? name.charAt(0).toUpperCase() : "?";

    return (
      <TouchableOpacity
        onPress={() => handleOpenProfile(id)}
        style={{ flex: 1 }}
      >
        <Text style={styles.userName}>{name}</Text>
        <Text style={styles.commentText}>{text}</Text>
      </TouchableOpacity>
    );
  };

  const handleOpenProfile = (id) => {
    if (id) {
      navigation.navigate("MyProfileScreenStack", {
        screen: "MyProfile",
        params: { id: id },
      });
    } else {
      navigation.navigate("Login");
    }
  };

  const handleLoadMore = () => {
    if (shouldFetchMore && !commentsLoading) {
      fetchComments(shouldFetchMore);
    }
  };

  useEffect(() => {
    if (announcement) {
      fetchComments(false);
    }
  }, [announcement]);

  const addUserReaction = useCallback(
    async (highlight, reaction) => {
      setShouldShowEmojiPanel({ visible: false, highlightId: "" });

      const filterById = (reactedBy, userId) => {
        if (reactedBy) {
          return reactedBy.filter((item) => item.userId !== userId);
        }
        return [];
      };

      try {
        const filteredReactions = filterById(
          highlight.reactedByUsers,
          userInfo?.id
        );

        const reactedByUsersWithNewReaction = [
          ...filteredReactions,
          {
            userId: userInfo?.id,
            others: {
              user: {
                firstName: userInfo.firstName,
                lastName: userInfo.lastName,
                photoUrl: userInfo.photoUrl,
                cludId: userInfo.cludId,
                gender: userInfo.gender,
              },
              emoji: reaction.file,
              emojiName: reaction.name,
            },
          },
        ];

        await analytics.logReactionAdded(
          highlight.id,
          "announcement",
          reaction.name
        );

        await dispatch.announcement.updateAnnouncement({
          id: highlight.id,
          reactedByUsers: reactedByUsersWithNewReaction,
        });
      } catch (error) {
        console.error("Error adding user reaction:", error);
      }
    },
    [announcement, userInfo?.id]
  );

  return (
    <SafeAreaView style={commonStyles.container}>
      <StatusBar
        barStyle="dark-content"
        hidden={false}
        backgroundColor={Colors.white}
        translucent={false}
      />
      <KeyboardAwareScrollView
        enableOnAndroid
        extraHeight={HeightSize(40)}
        keyboardShouldPersistTaps="always"
      >
        {commentsLoading ? (
          <ActivityIndicator size="large" color={Colors.green} />
        ) : (
          <ScrollView>
            <View
              style={[
                commonStyles.subContainer,
                {
                  marginTop: HeightSize(4),
                  paddingHorizontal: 0,
                },
              ]}
            >
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                  alignItems: "center",
                  paddingHorizontal: Size(5),
                }}
              >
                <TouchableOpacity
                  onPress={() => {
                    navigation.goBack();
                  }}
                  activeOpacity={9}
                >
                  <BackIcon />
                </TouchableOpacity>
              </View>
              <Competition
                announcement={announcement}
                loggedUserId={loggedUserId}
                isDetailPage={true}
                isActive={true}
                shouldShowEmojiPanel={shouldShowEmojiPanel}
                setShouldShowEmojiPanel={setShouldShowEmojiPanel}
                addUserReaction={addUserReaction}
                setShowLoginPopup={setShowLoginPopup}
              />

              {announcement && (
                <View key={announcement.id} style={styles.listWrapper}>
                  {announcement.announcementType === "VOTE" && (
                    <FlatList
                      data={announcement.voteSubmittedAssets}
                      keyExtractor={(item, index) => index.toString()}
                      renderItem={({ item }) => (
                        <View style={styles.listItem}>
                          <Text
                            onPress={() => handleOpenProfile(item?.userId)}
                            style={[styles.listUserName]}
                          >
                            {item?.orderIndex}.{"  "}
                            <Text style={styles.userNameHighlight}>
                              {item?.userName}
                            </Text>
                            <Text style={styles.userText}> - {item?.text}</Text>
                          </Text>
                        </View>
                      )}
                    />
                  )}
                  {announcement?.announcementType === "WINNER" && (
                    <View>
                      {/* <Text style={styles.title}>
                        Special mentions to the SOTM runner-ups:
                      </Text> */}
                      <FlatList
                        data={announcement?.voteSubmittedAssets}
                        keyExtractor={(item, index) => index.toString()}
                        renderItem={({ item }) => (
                          <View style={styles.listItem}>
                            <Text
                              onPress={() => handleOpenProfile(item?.userId)}
                              style={[styles.listUserName]}
                            >
                              <Text style={styles.userNameHighlight}>
                                {item?.userName}
                              </Text>
                              <Text style={styles.userText}>
                                {" "}
                                - {item?.text}
                              </Text>
                            </Text>
                          </View>
                        )}
                      />
                    </View>
                  )}
                </View>
              )}

              {/* Comments Wrapper */}
              {announcement && (
                <View style={styles.commentContentContainer}>
                  {fetchedComments.length > 0 && (
                    <FlatList
                      data={fetchedComments}
                      keyExtractor={(item, idx) => item?.id || idx.toString()}
                      renderItem={({ item, index }) => {
                        // Check if custom_data exists and is a valid string before parsing
                        let parsedData;
                        if (item?.author?.custom_data) {
                          try {
                            parsedData = JSON.parse(item.author.custom_data);
                          } catch (error) {
                            console.error("Invalid JSON:", error);
                            parsedData = {}; // Fallback to an empty object if parsing fails. It returns null initially
                          }
                        } else {
                          parsedData = getAuthorDataFromCommentCustomData(
                            item.custom_data
                          ); // Fallback to an empty object if custom_data is undefined
                        }

                        const commentUserID =
                          parsedData?.userId || parsedData?.id;

                        const isReported = userInfo?.reports?.some(
                          (report) => report.contendId === item?.id
                        );

                        const commentId = item.id || item.comment_id;

                        const commentBoardId = item.comment_board_id;

                        return (
                          <View style={styles.commentContainer}>
                            <View style={styles.userContainer}>
                              <View
                                style={{ flexDirection: "row", gap: Size(3) }}
                              >
                                <UserAvatar
                                  user={parsedData}
                                  onPress={() =>
                                    handleOpenProfile(commentUserID)
                                  }
                                  size={10}
                                />
                                <View style={{ paddingRight: 50 }}>
                                  <View style={styles.userNameContainer}>
                                    <TouchableOpacity
                                      onPress={() =>
                                        handleOpenProfile(commentUserID)
                                      }
                                    >
                                      <Text style={styles.userName}>
                                        @
                                        {parsedData?.firstName &&
                                        parsedData?.lastName
                                          ? `${parsedData.firstName}${parsedData.lastName}`
                                          : "Unknown User"}
                                      </Text>
                                    </TouchableOpacity>
                                    <Text style={styles.timeText}>
                                      {moment(item.created_at).fromNow(true)}
                                    </Text>
                                  </View>
                                  <Text style={styles.commentText}>
                                    {item.text}
                                  </Text>
                                </View>
                              </View>
                            </View>

                            <View
                              style={{
                                opacity: 0.4,
                                flexDirection: "row",
                                alignItems: "center",
                                width: 30,
                              }}
                            >
                              {isReported && <ReportIcon />}
                              <TouchableOpacity
                                onPress={(event) =>
                                  openBlockReportModal(
                                    index,
                                    event.nativeEvent,
                                    commentUserID
                                  )
                                }
                              >
                                <MenuIcon />
                              </TouchableOpacity>
                            </View>

                            {blockReportModalVisible &&
                              selectedCommentId === index && (
                                <BlockReportModal
                                  visible={blockReportModalVisible}
                                  onClose={closeBlockReportModal}
                                  //onBlock={() => handleOpenConfirmation("block")}
                                  onReport={() =>
                                    handleOpenConfirmation("report", "COMMENT")
                                  }
                                  top={modalPosition.top}
                                  right={modalPosition.right}
                                  text={"Comment"}
                                  showBlockOption={false}
                                  showDeleteComment={
                                    commentUserID === userInfo?.id
                                  }
                                  showReportOption={
                                    commentUserID !== userInfo?.id
                                  }
                                  onDelete={() => {
                                    handleOpenConfirmation("delete", "delete");
                                    setCommentId(commentId);
                                    setCommentBoardId(commentBoardId);
                                  }}
                                />
                              )}

                            {/* Confirmation Modal */}
                            <ConfirmationModal
                              visible={confirmationVisible}
                              onClose={() => setConfirmationVisible(false)}
                              onConfirm={handleConfirmAction}
                              actionType={actionType}
                              reportType={reportType}
                            />
                          </View>
                        );
                      }}
                      onEndReached={handleLoadMore} // Trigger when user scrolls to bottom
                      onEndReachedThreshold={0.5} // Fetch more when user reaches half the list
                      ListFooterComponent={
                        commentsLoading ? <ActivityIndicator /> : null
                      }
                    />
                  )}
                </View>
              )}
            </View>
          </ScrollView>
        )}

        {announcement && (
          <View style={styles.writeCommentContainer}>
            <TextInput
              ref={commentInputRef}
              style={styles.input}
              value={comment}
              onChangeText={(text) => setComment(text)}
              placeholder={`Add a comment`}
            />
            <TouchableOpacity
              onPress={() => addUserComment()}
              style={[
                styles.sendButton,
                { backgroundColor: sendLoading ? "" : "#14FF00" },
              ]}
              disabled={sendLoading || !comment.trim()}
            >
              {sendLoading ? <ActivityIndicator /> : <SendMsgIcon />}
            </TouchableOpacity>
          </View>
        )}

        {!announcement && <NotFound />}
      </KeyboardAwareScrollView>
    </SafeAreaView>
  );
};

export default AnnouncementPage;
