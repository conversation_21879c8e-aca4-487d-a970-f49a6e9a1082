import { Platform, StyleSheet } from "react-native";
import { HeightSize, Size } from "../../res/Size";
import { Colors } from "../../res/Colors";

const styles = StyleSheet.create({
  writeCommentContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    // paddingHorizontal: Size(5),
    width: "90%",
    borderBottomWidth: 2,
    marginBottom: 10,
    borderBottomColor: "#666",
    alignSelf: "center",
    //marginHorizontal: Size(3)
  },
  input: {
    paddingTop: HeightSize(2),
    paddingBottom: HeightSize(1),
    width: "80%",
    color: "#333",
  },
  sendButton: {
    width: Size(4),
    height: Size(4),
    borderRadius: Size(4),
    //backgroundColor: '#14FF00',
    justifyContent: "center",
    alignItems: "center",
    position: "absolute",
    right: <PERSON>ze(3),
    top: Platform.OS === "ios" ? Size(4) : Size(6),
  },
  commentContentContainer: {
    marginTop: HeightSize(2),
    marginBottom: HeightSize(3),
    paddingHorizontal: Size(5),
    borderTopColor: "#B5B5B5",
    borderTopWidth: 1,
  },
  title: {
    fontFamily: "PoppinsMedium",
    fontSize: 12,
    color: Colors.black,
    marginBottom: 2,
  },
  listWrapper: {
    paddingHorizontal: 16,
    marginTop: -10,
  },
  listItem: {
    paddingBottom: 1.5,
  },
  listUserName: {
    fontFamily: "Regular",
    fontSize: 12,
    lineHeight: Size(5),
    color: "#35498F",
  },
  userName: {
    fontFamily: "PoppinsSemiBold",
    fontSize: Size(3.8),
    lineHeight: Size(5),
    maxWidth: Size(50),
  },
  userNameHighlight: {
    textDecorationLine: "underline",
  },
  userText: {
    color: "#000",
    fontSize: 12,
    fontFamily: "Regular",
    flex: 1,
  },
  commentContainer: {
    flexDirection: "row",
    flex: 1,
    //flexWrap: 'wrap',
    paddingVertical: Size(4),
    borderBottomWidth: 1,
    borderBottomColor: "#e6e6e6",
  },
  userContainer: {
    flex: 1,
  },
  userPhoto: {
    width: 36,
    height: 36,
    borderRadius: Size(7.5),
    marginRight: Size(3),
    borderWidth: 1,
    borderColor: Colors.black,
  },
  userNameContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: Size(3),
  },
  timeText: {
    fontFamily: "Regular",
    fontSize: Size(3.5),
    color: "#999999",
  },
  userFirstName: {
    fontSize: 12,
    fontFamily: "PoppinsBold",
    color: "#000",
  },
  userTeamName: {
    fontSize: 12,
    fontFamily: "PoppinsMedium",
    color: "#808080",
  },
  commentText: {
    fontFamily: "Regular",
    fontSize: 12,
    color: "#000",
  },
  confirmationModalContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.1)",
  },
  confirmationModalContent: {
    backgroundColor: "white",
    paddingHorizontal: Size(4.5),
    borderRadius: Size(9),
    marginHorizontal: Size(5),
    alignItems: "center",
    paddingVertical: HeightSize(2),
    width: "80%",
  },
  confirmationText: {
    marginBottom: HeightSize(2),
    fontSize: Size(5),
    textAlign: "center",
    fontFamily: "PoppinsMedium",
  },
  confirmationButtonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
    paddingHorizontal: Size(5),
  },
  button: {
    backgroundColor: "transparent",
    margin: 0,
    paddingRight: Size(2),
  },
});

export default styles;
