import { StyleSheet } from "react-native";
import { Colors } from "../../res/Colors";
import { HeightSize, Size } from "../../res/Size";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  uploadContainerStyle: {
    width: "100%",
    height: HeightSize(75),
    backgroundColor: "#B5B5B51A",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: HeightSize(2),
    position: "relative",
    overflow: "hidden",
  },
  textStyle: {
    marginTop: HeightSize(2),
    justifyContent: "center",
    alignItems: "center",
  },
  video: {
    width: "100%",
    height: "100%",
  },
  imgStyle: {
    width: "100%",
    height: "100%",
  },
  textInputStyle: {
    borderBottomColor: Colors.grey,
    width: Size(90),
    marginVertical: HeightSize(2),
    borderBottomWidth: 2,
    maxHeight: 120
  },
  progressBarBackground: {
    height: 12,
    width: "100%",
    overflow: "hidden",
    marginVertical: 10,
    position: "absolute",
    bottom: -10,
    left: 0,
  },
  progressBar: {
    height: "100%",
    backgroundColor: Colors.green,
  },
  overlayCenter: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  playButton: {
    width: Size(16),
    height: Size(16),
    backgroundColor: "rgba(255, 255, 255, 0.5)",
    borderRadius: Size(16),
    justifyContent: "center",
    alignItems: "center",
  },
});

export default styles;
