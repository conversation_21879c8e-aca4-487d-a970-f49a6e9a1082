// REACT //
import React, { useEffect, useRef, useState } from "react";
import { shallowEqual, useSelector } from "react-redux";

// PLUGINS //
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import Video from "react-native-video";

// COMPONENTS //
import {
  Text,
  View,
  SafeAreaView,
  StatusBar,
  TouchableOpacity,
  Image,
  TextInput,
  BackHandler,
  Keyboard,
  Animated,
  Platform,
  Dimensions,
} from "react-native";
import Button from "../../components/Button/Button";

// CONSTANTS //
import { notifyError, notifySuccess } from "../../constants/misc";

// OTHERS //
import styles from "./UploadHighlightsStyles";
import { BackIcon, UploadIcon } from "../../res/Svg";
import { commonStyles } from "../../res/CommonStyles";
import { HeightSize, Size } from "../../res/Size";
import { Colors } from "../../res/Colors";
import { uploadS3 } from "../../../s3.config";
import { dispatch } from "../../redux/store";
import SelectMedia from "./SelectMedia";
import { TouchableWithoutFeedback } from "react-native";
import { selectUserInfo } from "../../redux/selectors/authSelectors";
import ErrorPopup from "../../components/ErrorPopup";

const UploadHighlights = ({ navigation, route }) => {
  const [image, setImage] = useState(null);
  const [imageFileName, setImageFileName] = useState(null);
  const [streamUrlObject, setStreamUrlObject] = useState(null);
  const [assetUrl, setAssetUrl] = useState("");
  const [uploadType, setUploadType] = useState("");
  const [uploaded, setUploaded] = useState(false);
  const [comment, setComment] = useState("");
  const [loading, setLoading] = useState(false);
  const [file, setFile] = useState();
  const [progress, setProgress] = useState(false);
  const [uploadLoader, setUploadLoader] = useState(false);
  const [videoDuration, setVideoDuration] = useState(0);
  const [videoOrientation, setVideoOrientation] = useState("");
  const [videoWidth, setVideoWidth] = useState(null);
  const [videoHeight, setVideoHeight] = useState(null);
  const [showMedia, setShowMedia] = useState(false);
  const [startUpload, setStartUpload] = useState(false);
  const [isFetchingMedia, setIsFetchingMedia] = useState(false);
  const [isMediaFilterVisible, setIsMediaFilterVisible] = useState(false);
  const [uploadError, setUploadError] = useState(null);

  const videoRef = useRef(null);
  const userInfo = useSelector(selectUserInfo, shallowEqual);
  const isMounted = useRef(true);
  const uploadS3FunctionRef = useRef(null);

  const uploading = useSelector(
    ({ loading }) => loading.effects.feed.createHighlights
  );

  const uploadProgress = useSelector((state) => state.feed.uploadProgress);

  // Add cleanup effect
  useEffect(() => {
    return () => {
      isMounted.current = false;
      // Clean up all states
      setImage(null);
      setFile(null);
      setImageFileName(null);
      setStreamUrlObject(null);
      setAssetUrl("");
      setUploadType("");
      setUploaded(false);
      setComment("");
      setLoading(false);
      setProgress(false);
      setUploadLoader(false);
      setShowMedia(false);
      setStartUpload(false);
      dispatch.feed.setUploadProgress(0);
    };
  }, []);

  useEffect(() => {
    const backAction = () => {
      // If we're in the middle of upload
      if (startUpload && uploadS3FunctionRef.current) {
        uploadS3FunctionRef.current.cancel();
        // Reset states
        setImage(null);
        setFile(null);
        setShowMedia(true);
        setStartUpload(false);
        setUploaded(false);
        setIsFetchingMedia(false);
        setStreamUrlObject(null);
        setAssetUrl("");
        setUploadType("");
        setComment("");
        dispatch.feed.setUploadProgress(-1);
        setUploadLoader(false);
        setUploadError(null);
        return true;
      }

      if (showMedia) {
        navigation.goBack();
        return true;
      }

      return false;
    };

    const backHandler = BackHandler.addEventListener(
      "hardwareBackPress",
      backAction
    );

    return () => backHandler.remove();
  }, [startUpload, showMedia, navigation]);

  const setOrientation = (height, width) => {
    if (width > height) {
      setVideoOrientation("landscape");
    } else if (height > width) {
      setVideoOrientation("portrait");
    } else {
      setVideoOrientation("square");
    }
  };

  const handleMediaSelection = (media) => {
    if (!media || (!media.uri && !media.fullPath)) {
      console.warn("Invalid media selection:", media);
      return;
    }

    // Destructure properties with defaults
    const {
      uri,
      filename = `${Date.now()}`, // Fallback to current timestamp
      width = 0,
      height = 0,
      duration = 0,
      fullPath,
    } = media;

    // Use the appropriate file path based on the platform
    const filePath = Platform.OS === "android" ? uri : fullPath;

    // Determine if the media is a video based on file path (this depends on your `isVideo` function)
    const isMediaVideo = isVideo(filePath);

    // Update state in a single batch (assuming you are using `useState`)
    setImage(filePath);
    setFile(media);
    setImageFileName(filename);
    setUploadType(isMediaVideo ? "VIDEO" : "PHOTO");
    setVideoWidth(width);
    setVideoHeight(height);
    setVideoDuration(duration);

    // Set orientation only if dimensions are available
    if (width && height) {
      setOrientation(height, width);
    } else {
      console.warn("Height or Width not available in the asset");
    }
  };

  const isVideo = (uri) => {
    if (uri && uri.includes(".")) {
      const extension = uri.split(".").pop().toLowerCase();
      const videoExtensions = [
        "mp4",
        "avi",
        "mov",
        "wmv",
        "flv",
        "mkv",
        "webm",
        "mpeg",
      ];
      return videoExtensions.includes(extension);
    }
    return false;
  };

  const uploadS3Function = async () => {
    setUploadLoader(true);
    setStartUpload(true);

    try {
      if (!image) {
        throw new Error("Please select an image or video to upload.");
      }

      dispatch.feed.setUploadProgress(0);
      uploadS3FunctionRef.current = uploadS3;

      await uploadS3(
        image,
        imageFileName,
        videoWidth,
        videoHeight,
        videoDuration,
        (assetUrl, fileName, urls) => {
          // Success callback
          setStreamUrlObject({
            ...urls,
            duration: videoDuration,
            orientation: videoOrientation,
          });
          setAssetUrl(assetUrl);
          setUploaded(true);
          setUploadLoader(false);
          dispatch.feed.setUploadProgress(100);
        },
        (error) => {
          // Error callback
          console.error("Upload Error: ", error);
          setUploadError(error);
          setFile(null);
          setImage(null);
          setImageFileName(null);
          dispatch.feed.setUploadProgress(0);
          setUploadLoader(false);
        },
        (progress) => {
          // Progress callback
          dispatch.feed.setUploadProgress(progress);
        },
        () => {
          // Cancellation callback
          console.debug("Upload cancelled by user");
          setFile(null);
          setImage(null);
          setImageFileName(null);
          dispatch.feed.setUploadProgress(0);
          setUploadLoader(false);

          // Show SelectMedia component
          setShowMedia(true);
          setStartUpload(false);
          setUploaded(false);
          setIsFetchingMedia(false);
          setStreamUrlObject(null);
          setAssetUrl("");
          setUploadType("");
          setComment("");
        }
      );
    } catch (error) {
      console.error("Upload Error: ", error);
      // For cancellation, just return without showing error
      if (error.message === "Upload cancelled") {
        return;
      }
      // For other errors, show error message
      setUploadError(error);
      setFile(null);
      setImage(null);
      setImageFileName(null);
      setUploadLoader(false);
    }
  };

  const handleSubmit = async () => {
    //setLoading(true);
    setProgress(true);
    if (comment.length < 1) {
      notifyError("Please add a Caption");
      // setLoading(false);
      setProgress(false);
      return;
    } else {
      await dispatch.feed.createHighlights({
        userId: userInfo.id,
        type: uploadType,
        url: assetUrl,
        streamUrl: streamUrlObject,
        comment: comment,
        queueProcessed: false,
      });

      //setLoading(false);
      setProgress(false);
      Keyboard.dismiss();
      notifySuccess("Highlights Uploaded Successfully");
      route?.params?.fromProfileHighlights
        ? navigation.navigate("MyProfileScreenStack", {
            screen: "MyProfile",
            params: {
              refreshHighlights: true,
              timestamp: Date.now(), // Force refresh
            },
          })
        : navigation.navigate("Feed");
    }
  };

  const handleShowMediaFilterChange = (value) => {
    setIsMediaFilterVisible(value);
  };

  const handleMediaFilesChange = (isEmpty) => {
    setIsFetchingMedia(isEmpty);
  };

  useEffect(() => {
    if (!userInfo.id) {
      navigation.navigate("Feed");
    }
  }, []);

  return (
    <SafeAreaView style={commonStyles.container}>
      {/* <StatusBar
        barStyle="dark-content"
        hidden={false}
        backgroundColor={Colors.white}
        translucent={false}
      /> */}

      <>
        <View
          style={{
            ...(Platform.OS === "ios" && {
              position: "relative",
              zIndex: 10,
            }),
            minHeight: Dimensions.get("window").height,
          }}
        >
          {startUpload && (
            <TouchableOpacity
              onPress={() => {
                if (uploadS3FunctionRef.current) {
                  uploadS3FunctionRef.current.cancel();
                }

                // Reset all upload-related states
                setImage(null);
                setFile(null);
                setShowMedia(true);
                setStartUpload(false);
                setUploaded(false);
                setIsFetchingMedia(false);
                setStreamUrlObject(null);
                setAssetUrl("");
                setUploadType("");
                setComment("");
                dispatch.feed.setUploadProgress(-1);
                setUploadLoader(false);
                setUploadError(null);
              }}
              style={{
                paddingHorizontal: Size(5),
                height: HeightSize(8),
                justifyContent: "center",
              }}
              activeOpacity={0.9}
            >
              <BackIcon />
            </TouchableOpacity>
          )}

          <View>
            {!startUpload && (
              <SelectMedia
                onMediaSelect={handleMediaSelection}
                onFilterVisibilityChange={handleShowMediaFilterChange}
                onMediaFilesChange={handleMediaFilesChange}
                uploadS3Function={uploadS3Function}
                progress={progress}
              />
            )}

            {uploaded && (
              <View style={{ paddingHorizontal: Size(5) }}>
                <TextInput
                  placeholder="Enter Caption"
                  placeholderTextColor="rgba(0,0,0,0.2)"
                  style={styles.textInputStyle}
                  value={comment}
                  onChangeText={(e) => setComment(e)}
                  returnKeyType="default"
                  multiline={true}
                  onSubmitEditing={Keyboard.dismiss}
                />
              </View>
            )}

            {startUpload && (
              <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
                <View>
                  <View
                    style={{
                      paddingHorizontal: Size(5),
                      ...(uploaded && {
                        paddingHorizontal: 0,
                      }),
                    }}
                  >
                    <View
                      style={{
                        backgroundColor: Colors.black,
                        width: "100%",
                        aspectRatio: 3 / 4,
                        justifyContent: "center",
                        alignItems: "center",
                      }}
                    >
                      {isVideo(image) ? (
                        <Video
                          ref={videoRef}
                          source={{ uri: image }}
                          style={styles.video}
                          resizeMode="contain"
                          // onLoad={() => setLoading(false)}
                          onError={(error) => console.error("err", error)}
                          onEnd={() => videoRef.current.seek(0)}
                          paused={false}
                          repeat={true}
                          onBuffer={(buffer) =>
                            console.debug("Buffering", buffer)
                          }
                          ignoreSilentSwitch="ignore"
                          useTextureView={true}
                          playInBackground={false}
                          bufferConfig={{
                            minBufferMs: 15000,
                            maxBufferMs: 50000,
                            bufferForPlaybackMs: 2500,
                            bufferForPlaybackAfterRebufferMs: 5000,
                            backBufferDurationMs: 120000,
                            cacheSizeMB: 0,
                            live: {
                              targetOffsetMs: 500,
                            },
                          }}
                        />
                      ) : (
                        <Image
                          source={{ uri: image }}
                          style={styles.imgStyle}
                          resizeMode="contain"
                        />
                      )}

                      {uploadProgress >= 0 && uploadProgress < 100 && (
                        <View style={styles.progressBarBackground}>
                          <View
                            style={[
                              styles.progressBar,
                              {
                                width: `${Math.max(10, uploadProgress)}%`,
                              },
                            ]}
                          />
                        </View>
                      )}
                    </View>
                  </View>
                </View>
              </TouchableWithoutFeedback>
            )}
          </View>
        </View>

        {uploadError && (
          <ErrorPopup
            error={uploadError}
            onClose={() => setUploadError(null)}
          />
        )}

        {image && !isFetchingMedia && uploaded && (
          <View
            style={{
              paddingHorizontal: Size(5),
              marginTop: HeightSize(3),
              width: "100%",
              position: "absolute",
              bottom: 25,
              ...(!isMediaFilterVisible ? { zIndex: 10 } : { zIndex: -1 }),
            }}
          >
            <Button
              title={"PUBLISH"}
              onPress={() => {
                handleSubmit();
              }}
              backgroundColor={Colors.green}
              progress={progress || uploadLoader}
              disable={progress || uploadLoader}
              width="100%"
              loadingText={`UPLOADING ${uploadType}...`}
              loadingType="text"
              textStyleInsideButton={{
                color: Colors.black,
                fontFamily: "Bold",
                fontSize: Size(4),
              }}
            />
          </View>
        )}
      </>
    </SafeAreaView>
  );
};

export default UploadHighlights;
