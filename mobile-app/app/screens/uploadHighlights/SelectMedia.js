// REACT //
import React, {
  useEffect,
  useMemo,
  useState,
  useCallback,
  useRef,
  memo,
} from "react";

// PLUGINS //
import * as MediaLibrary from "expo-media-library";
import Video from "react-native-video";
import * as Animatable from "react-native-animatable";
import * as FileSystem from "expo-file-system";
import Modal from "react-native-modal";

// COMPONENTS //
import {
  View,
  FlatList,
  TouchableOpacity,
  Dimensions,
  Text,
  Image,
  StyleSheet,
  ActivityIndicator,
  Platform,
  Linking,
  AppState,
  Alert,
} from "react-native";

// CONSTANTS //
import { convertSecondsToMinutes, notifyError } from "../../constants/misc";

// OTHERS //
import { Colors } from "../../res/Colors";
import {
  AlertIcon,
  ArrowDown,
  BackIcon,
  SettingIcon,
  SettingIconGreen,
} from "../../res/Svg";
import { Images } from "../../res/Images";
import { commonStyles } from "../../res/CommonStyles";
import { HeightSize, Size } from "../../res/Size";
import BottomDrawer from "../../components/BottomDrawer";
import { useNavigation } from "@react-navigation/native";
import Button from "../../components/Button/Button";
import { BackHandler } from "react-native";
import { BlurView } from "@react-native-community/blur";

const SelectMedia = ({
  onMediaSelect,
  onFilterVisibilityChange,
  onMediaFilesChange,
  uploadS3Function,
  progress,
}) => {
  const { width: SCREEN_WIDTH } = Dimensions.get("screen");
  const [selectedMedia, setSelectedMedia] = useState("");
  const [mediaFiles, setMediaFiles] = useState([]);
  const [hasNextPage, setHasNextPage] = useState(false);
  const [endCursor, setEndCursor] = useState(null);
  const [showMediaFilter, setShowMediaFilter] = useState(false);
  const [showManageMedia, setShowManageMedia] = useState(false);
  const [mediaTypeFilter, setMediaTypeFilter] = useState("all");
  const [appState, setAppState] = useState();
  const [isFetchingMedia, setIsFetchingMedia] = useState(false);
  const [access, setAccess] = useState(null); // Initialize access state to null
  const [albums, setAlbums] = useState([]);
  const [isFetchingAlbums, setIsFetchingAlbums] = useState(true);
  const [selectedAlbum, setSelectedAlbum] = useState(null);
  const [isVideoTooLarge, setIsVideoTooLarge] = useState(false);
  const [enableSwipeToClose, setEnableSwipeToClose] = useState(true);
  const [mediaProcessing, setMediaProcessing] = useState(false);

  const [counts, setCounts] = useState({
    all: 0,
    video: 0,
    favorites: 0,
  });
  const mediaListenerFiredRef = useRef(false);

  const isCurrentRequest = useRef(true);

  const currentAlbumIdRef = useRef(null);

  const previousPermissionRef = useRef(access);

  useEffect(() => {
    previousPermissionRef.current = access;
  }, [access]);

  const navigation = useNavigation();

  const isLimitedAccess = useMemo(() => access != "all", [access]);

  const combinedItems = useMemo(() => {
    const base = [
      {
        id: "all-media",
        title: "All Media",
        icon: Images.media,
        type: "all",
        assetCount: counts.all || 0,
        showCount: true,
      },
    ];

    if (Platform.OS === "ios") {
      // On iOS, try to find real albums for videos/favorites
      const videoAlbum = albums.find(
        (a) => a.title.toLowerCase() === "videos" || a.type === "video"
      );

      const favoritesAlbum = albums.find(
        (a) => a.title.toLowerCase() === "favorites" || a.type === "favorites"
      );

      if (videoAlbum) {
        base.push({
          id: `album-${videoAlbum.id}`,
          title: "Videos",
          icon: Images.video, // static icon
          type: "album",
          assetCount: videoAlbum.assetCount,
          albumId: videoAlbum.id,
          showCount: true,
        });
      }

      if (favoritesAlbum) {
        base.push({
          id: `album-${favoritesAlbum.id}`,
          title: "Favorites",
          icon: Images.media, // static icon
          type: "album",
          assetCount: favoritesAlbum.assetCount,
          albumId: favoritesAlbum.id,
          showCount: true,
        });
      }
    } else {
      // On Android, always show static Videos and Favorites without finding albums
      base.push(
        {
          id: "video-media",
          title: "Videos",
          icon: Images.video,
          type: "video",
          assetCount: counts.video || 0,
          showCount: true,
        },
        {
          id: "favorites",
          title: "Favorites",
          icon: Images.media,
          type: "favorites",
          assetCount: counts.favorites || 0,
          showCount: true,
        }
      );
    }

    // Then add the rest of albums excluding found video/favorites on iOS
    const filteredAlbums = albums.filter((a) => {
      if (Platform.OS === "ios") {
        return (
          a.title.toLowerCase() !== "videos" &&
          a.title.toLowerCase() !== "favorites" &&
          a.type !== "video" &&
          a.type !== "favorites"
        );
      }
      return true;
    });

    const albumItems = filteredAlbums.map((album) => ({
      id: `album-${album.id}`,
      title: album.title,
      icon: album.coverAsset?.uri
        ? { uri: album.coverAsset.uri }
        : Images.defaultAlbum,
      type: "album",
      assetCount: album.assetCount,
      albumId: album.id,
      showCount: true,
    }));

    return [...base, ...albumItems];
  }, [albums, counts.all, counts.video, counts.favorites]);

  const fetchAlbums = async (accessOverride = null) => {
    const currentAccess = accessOverride || access;
    if (currentAccess === "limited" || currentAccess === "none") {
      setAlbums([]);
      setIsFetchingAlbums(false);
      return;
    }
    setIsFetchingAlbums(true);
    try {
      const fetchedAlbums = await MediaLibrary.getAlbumsAsync({
        includeSmartAlbums: true,
      });

      const smartAlbums = fetchedAlbums.filter((album) => album.isSmartAlbum);
      const regularAlbums = fetchedAlbums.filter(
        (album) => !album.isSmartAlbum
      );

      const favoritesAlbum = smartAlbums.find(
        (album) =>
          album.title.toLowerCase() === "favorites" ||
          album.type === "favorites"
      );

      const filteredRegularAlbums = regularAlbums.filter(
        (album) =>
          album.assetCount > 0 && !["Recently Deleted"].includes(album.title)
      );

      const finalAlbums = favoritesAlbum
        ? [favoritesAlbum, ...filteredRegularAlbums]
        : filteredRegularAlbums;

      const albumsWithCovers = await Promise.all(
        finalAlbums.map(async (album) => {
          try {
            const assets = await MediaLibrary.getAssetsAsync({
              album: album.id,
              first: 1,
              sortBy: [MediaLibrary.SortBy.creationTime],
              mediaType: ["photo", "video"],
            });

            const coverAsset = assets.assets[0] || null;

            if (
              !coverAsset ||
              !coverAsset.uri ||
              assets.assets.length === 0 ||
              assets.totalCount === 0
            ) {
              return null;
            }

            return {
              ...album,
              coverAsset,
            };
          } catch (error) {
            console.error(`Error processing album ${album.title}:`, error);
            return null;
          }
        })
      );

      setAlbums(albumsWithCovers.filter(Boolean));
    } catch (error) {
      console.error("Error fetching albums:", error);
    } finally {
      setIsFetchingAlbums(false);
    }
  };

  const getTotalMediaCount = async (type = "all", accessOverride = null) => {
    const currentAccess = accessOverride || access;
    if (currentAccess === "none") {
      return 0;
    }

    let mediaTypeOptions = [
      MediaLibrary.MediaType.photo,
      MediaLibrary.MediaType.video,
    ];
    if (type === "video") mediaTypeOptions = [MediaLibrary.MediaType.video];
    if (type === "photo") mediaTypeOptions = [MediaLibrary.MediaType.photo];

    try {
      if (currentAccess === "limited") {
        if (type === "favorites") {
          return 0;
        }
        const { totalCount } = await MediaLibrary.getAssetsAsync({
          mediaType: mediaTypeOptions,
          first: 1,
        });

        return totalCount || 0;
      }

      // Full access logic (currentAccess === 'all')
      if (type === "favorites") {
        const allSysAlbums = await MediaLibrary.getAlbumsAsync({
          includeSmartAlbums: true,
        });
        const favoritesAlbum = allSysAlbums.find(
          (album) =>
            album.title?.toLowerCase() === "favorites" ||
            album.type === "favorites"
        );

        return favoritesAlbum?.assetCount || 0;
      }

      const { totalCount } = await MediaLibrary.getAssetsAsync({
        mediaType: mediaTypeOptions,
        first: 1,
      });

      return totalCount || 0;
    } catch (error) {
      console.error(
        `Error in getTotalMediaCount (type: ${type}, access: ${currentAccess}):`,
        error
      );
      return 0;
    }
  };

  const fetchCounts = async (accessOverride = null) => {
    const all = await getTotalMediaCount("all", accessOverride);
    const video = await getTotalMediaCount("video", accessOverride);
    const favorites = await getTotalMediaCount("favorites", accessOverride);
    setCounts({ all, video, favorites });
  };

  const fetchMedia = async (
    cursor = null,
    selectedType,
    shouldContinue = () => true,
    accessOverride = null
  ) => {
    const currentAccess = accessOverride || access;
    if (
      currentAccess === "none" ||
      !shouldContinue() ||
      (isFetchingMedia && cursor)
    ) {
      if (currentAccess === "none" || !shouldContinue()) {
        setIsFetchingMedia(false);
      }
      return;
    }

    setIsFetchingMedia(true);

    try {
      let queryMediaType = [
        MediaLibrary.MediaType.photo,
        MediaLibrary.MediaType.video,
      ];
      if (selectedType === "photo") {
        queryMediaType = [MediaLibrary.MediaType.photo];
      }
      if (selectedType === "video") {
        queryMediaType = [MediaLibrary.MediaType.video];
      }

      let options = {
        mediaType: queryMediaType,
        first: 20,
        after: cursor,
        sortBy:
          Platform.OS === "ios"
            ? [
                [MediaLibrary.SortBy.creationTime, false],
                [MediaLibrary.SortBy.modificationTime, false],
              ]
            : [
                [MediaLibrary.SortBy.modificationTime, false],
                [MediaLibrary.SortBy.creationTime, false],
              ],
      };

      if (currentAccess === "all") {
        if (selectedType === "favorites") {
          const allSysAlbums = await MediaLibrary.getAlbumsAsync({
            includeSmartAlbums: true,
          });
          const favoritesAlbum = allSysAlbums.find(
            (album) =>
              album?.title?.toLowerCase() === "favorites" ||
              album?.type === "favorites"
          );
          if (favoritesAlbum?.id) {
            options.album = favoritesAlbum.id;
          } else {
            setMediaFiles((prev) => (cursor ? prev : []));
            setHasNextPage(false);
            setIsFetchingMedia(false);
            return;
          }
        } else if (
          selectedType &&
          !["all", "video", "photo"].includes(selectedType)
        ) {
          options.album = selectedType;
        }
      } else if (currentAccess === "limited") {
        if (options.album) {
          delete options.album;
        }
      }

      const media = await MediaLibrary.getAssetsAsync(options);

      if (!shouldContinue()) {
        setIsFetchingMedia(false);
        return;
      }

      let processedAssets = [];

      if (media.assets && media.assets.length > 0) {
        processedAssets = await Promise.all(
          media.assets.map(async (asset) => {
            try {
              let displayUri = asset.uri;

              if (
                Platform.OS === "ios" &&
                asset.mediaType === "video" &&
                !asset.uri.startsWith("file://")
              ) {
                const assetInfo = await MediaLibrary.getAssetInfoAsync(
                  asset.id
                );
                if (assetInfo.localUri) {
                  const newPath = `${FileSystem.cacheDirectory}${
                    asset.filename || Date.now()
                  }.mp4`;
                  await FileSystem.copyAsync({
                    from: assetInfo.localUri,
                    to: newPath,
                  });
                  displayUri = newPath;
                }
              }

              return {
                ...asset,
                fullPath: displayUri,
                mediaType:
                  asset.mediaType || (asset.duration ? "video" : "photo"),
              };
            } catch (error) {
              console.warn(`Error processing asset ${asset.id}:`, error);
              return null;
            }
          })
        );

        processedAssets = processedAssets.filter(Boolean);
      }

      if (shouldContinue()) {
        setMediaFiles((prev) =>
          cursor ? [...(prev || []), ...processedAssets] : processedAssets
        );
        setHasNextPage(media.hasNextPage || false);
        setEndCursor(media.endCursor || null);

        if (!cursor && processedAssets.length > 0) {
          setSelectedMedia(processedAssets[0]);
          if (onMediaSelect) onMediaSelect(processedAssets[0]);
        }
      }
    } catch (error) {
      console.error("fetchMedia: General error:", error);
      if (shouldContinue()) {
        notifyError("Failed to load media. Please try again.");
      }
    } finally {
      if (shouldContinue()) {
        setIsFetchingMedia(false);
      }
    }
  };

  const checkPermissionsAndFetchMedia = async () => {
    let currentStatus = null;
    let currentAccessPrivileges = null;

    try {
      const permissions = await MediaLibrary.getPermissionsAsync();
      currentStatus = permissions.status;
      currentAccessPrivileges = permissions.accessPrivileges;

      if (currentStatus !== "granted") {
        const requestedPermissions =
          await MediaLibrary.requestPermissionsAsync();
        currentStatus = requestedPermissions.status;
        currentAccessPrivileges = requestedPermissions.accessPrivileges;
      }

      const validAccessValues = ["all", "limited", "none"];
      if (
        !validAccessValues.includes(currentAccessPrivileges) &&
        currentStatus !== "granted"
      ) {
        console.warn(
          `Unexpected accessPrivileges: ${currentAccessPrivileges}, status: ${currentStatus}. Defaulting to 'none'.`
        );
        currentAccessPrivileges = "none";
      } else if (
        !validAccessValues.includes(currentAccessPrivileges) &&
        currentStatus === "granted"
      ) {
        console.warn(
          `Unexpected accessPrivileges: ${currentAccessPrivileges} with granted status. Defaulting to 'all'.`
        );
        currentAccessPrivileges = "all";
      }

      setAccess(currentAccessPrivileges);

      if (currentStatus === "granted") {
        if (
          currentAccessPrivileges === "all" ||
          currentAccessPrivileges === "limited"
        ) {
          const typeToFetch = selectedAlbum?.albumId || mediaTypeFilter;

          currentAlbumIdRef.current = selectedAlbum
            ? `album_${selectedAlbum.albumId}`
            : `type_${mediaTypeFilter}`;

          setMediaFiles([]);
          setHasNextPage(true);
          setEndCursor(null);

          await fetchMedia(
            null,
            typeToFetch,
            () => true,
            currentAccessPrivileges
          );
          await fetchCounts(currentAccessPrivileges);
          await fetchAlbums(currentAccessPrivileges);
        } else {
          setMediaFiles([]);
          setHasNextPage(false);
          setAlbums([]);
          setCounts({ all: 0, video: 0, favorites: 0 });
        }
      } else {
        setMediaFiles([]);
        setHasNextPage(false);
        setAlbums([]);
        setCounts({ all: 0, video: 0, favorites: 0 });
        notifyError("Media library access is required to select media.");
      }
    } catch (error) {
      console.error("Error in checkPermissionsAndFetchMedia:", error);
      setAccess("none");
      notifyError(
        "Something went wrong checking permissions. Please try again."
      );
    }
  };

  useEffect(() => {
    checkPermissionsAndFetchMedia();
  }, []);

useEffect(() => {
  const handleAppStateChange = async (nextAppState) => {
    const isGoingToBackground =
      appState?.match(/active/) && nextAppState !== "active";
    const isComingToForeground =
      appState?.match(/inactive|background/) && nextAppState === "active";

    if (isGoingToBackground) {
      mediaListenerFiredRef.current = false;
    }

    if (isComingToForeground) {
      try {
        const oldAccess = previousPermissionRef.current;
        const { status, accessPrivileges: rawAccess } =
          await MediaLibrary.getPermissionsAsync();

        let newAccess = rawAccess;
        const validAccess = ["all", "limited", "none"];
        if (!validAccess.includes(newAccess)) {
          newAccess = status === "granted" ? "all" : "none";
        }

        setAccess(newAccess);

        const typeToFetch = selectedAlbum?.albumId || mediaTypeFilter;
        currentAlbumIdRef.current = selectedAlbum
          ? `album_${selectedAlbum.albumId}`
          : `type_${mediaTypeFilter}`;

        const resetAndClearMedia = () => {
          setMediaFiles([]);
          setHasNextPage(false);
          setAlbums([]);
          setCounts({ all: 0, video: 0, favorites: 0 });
        };

        const resetAndRefetch = async () => {
          setMediaFiles([]);
          setHasNextPage(true);
          setEndCursor(null);
          await Promise.all([
            fetchAlbums(newAccess),
            fetchCounts(newAccess),
            fetchMedia(null, typeToFetch, () => true, newAccess),
          ]);
        };

        if (status === "granted") {
          if (newAccess === "all" || newAccess === "limited") {
            if (newAccess === "all" && oldAccess === "all") {
              await Promise.all([
                fetchMedia(null, typeToFetch, () => true, newAccess),
                fetchAlbums(newAccess),
                fetchCounts(newAccess),
              ]);
            } else {
              await resetAndRefetch();
            }

           setShowManageMedia(false);
     
          } else {
            resetAndClearMedia();
          }
        } else {
          resetAndClearMedia();
          notifyError("Media library access is required to select media.");
        }
      } catch (error) {
        console.error("Error refreshing media after foreground:", error);
        notifyError("Failed to refresh media. Please try again.");
      }
    }

    setAppState(nextAppState);
  };

  const subscription = AppState.addEventListener("change", handleAppStateChange);
  return () => subscription.remove();
}, [
  appState,
  mediaTypeFilter,
  selectedAlbum,
  fetchMedia,
  fetchAlbums,
  fetchCounts,
  setAccess,
  setAppState,
  setMediaFiles,
  setHasNextPage,
  setEndCursor,
  setAlbums,
  setCounts,
  notifyError,
]);


  useEffect(() => {
    const initMediaSelection = async () => {
      if (onMediaFilesChange) {
        onMediaFilesChange(mediaFiles.length === 0);
      }

      if (mediaFiles.length > 0 && !selectedMedia) {
        await handleSelectMedia(mediaFiles[0]);
      }
    };

    initMediaSelection();
  }, [mediaFiles]);

    // Listen for changes to media library (iOS only)
useEffect(() => {
  const listener = MediaLibrary.addListener(async () => {
    if (!mediaListenerFiredRef.current) {
      mediaListenerFiredRef.current = true;

      const assets = await MediaLibrary.getAssetsAsync({
        first: 1,
      });

  
      if (assets.assets.length === 0) {
        setMediaFiles([]);
        setSelectedMedia(null); 
        setHasNextPage(false);
      } else {
        setMediaFiles([]);
        setHasNextPage(true);
        await fetchMedia(null, mediaTypeFilter);
      }

      toggleManageMediaVisibility(false);
    }
  });

  return () => {
    listener.remove();
  };
}, []);



  const loadMoreMedia = useCallback(() => {
    if (!isFetchingMedia && hasNextPage) {
      const currentType = selectedAlbum?.albumId || mediaTypeFilter;
      const shouldContinue = () => {
        const currentId = selectedAlbum
          ? `album_${selectedAlbum.albumId}`
          : `type_${mediaTypeFilter}`;
        return currentAlbumIdRef.current === currentId;
      };
      fetchMedia(endCursor, currentType, shouldContinue);
    }
  }, [isFetchingMedia, hasNextPage, endCursor, mediaTypeFilter, selectedAlbum]);

  const handleFilterChange = async (item) => {
    if (!item) {
      console.warn("No item provided to handleFilterChange");
      return;
    }

    try {
      // Generate a unique ID for this album/filter
      const filterId =
        item.type === "album" ? `album_${item.albumId}` : `type_${item.type}`;

      // If we're already showing this album, do nothing
      if (currentAlbumIdRef.current === filterId) {
        toggleFilterVisibility(false);
        return;
      }

      // Cancel any in-progress request
      isCurrentRequest.current = false;

      // Clear existing media
      setMediaFiles([]);
      setHasNextPage(true);
      setEndCursor(null);

      // Update the current album ID
      currentAlbumIdRef.current = filterId;

      // Set the new filter state
      const isAlbum = item.type === "album" || item.type === "favorites";
      setMediaTypeFilter(isAlbum ? "album" : item.type);
      setSelectedAlbum(isAlbum ? item : null);

      // Reset for new request
      isCurrentRequest.current = true;
      const shouldContinue = () =>
        isCurrentRequest.current && currentAlbumIdRef.current === filterId;

      toggleFilterVisibility(false);

      const fetchType = item.type === "album" ? item.albumId : item.type;

      await fetchMedia(null, fetchType, shouldContinue);
    } catch (error) {
      console.error("Error changing filter:", error);
      if (isCurrentRequest.current) {
        notifyError("Failed to load media. Please try again.");
      }
    }
  };

  const toggleFilterVisibility = (newVisibilityState) => {
    setShowMediaFilter(newVisibilityState);
    // Notify the parent of the change
    if (onFilterVisibilityChange) {
      onFilterVisibilityChange(newVisibilityState);
    }
  };

  const toggleManageMediaVisibility = (visible) => {
    setShowManageMedia(visible);

    // Notify the parent of the change
    if (onFilterVisibilityChange) {
      onFilterVisibilityChange(visible);
    }
  };

  const openManageMedia = () => {
    setShowManageMedia(false);
    Linking.openSettings();
  };

  const ITEM_HEIGHT = 100;
  const getItemLayout = (data, index) => ({
    length: ITEM_HEIGHT,
    offset: ITEM_HEIGHT * index,
    index,
  });

  const handleSelectMedia = async (item) => {
    setMediaProcessing(true);
    setSelectedMedia(item);
    setIsVideoTooLarge(false); // Reset initially

    try {
      let fileUri = item.uri;
      let fileInfo;

      if (Platform.OS === "ios" && item.mediaType === "video") {
        const cachedFileUri = `${FileSystem.cacheDirectory}${item.filename}`;
        fileInfo = await FileSystem.getInfoAsync(cachedFileUri, { size: true });

        if (fileInfo.exists) {
          fileUri = cachedFileUri;
        } else {
          const assetInfo = await MediaLibrary.getAssetInfoAsync(item.id);
          await FileSystem.copyAsync({
            from: assetInfo.localUri || assetInfo.uri || item.uri,
            to: cachedFileUri,
          });
          fileUri = cachedFileUri;
        }
      }

      if (!fileInfo) {
        fileInfo = await FileSystem.getInfoAsync(fileUri, { size: true });
      }
      const sizeInMB = (fileInfo?.size ?? 0) / (1024 * 1024);
      const durationInSeconds = item?.duration || 0;

      setMediaProcessing(false);
      if (
        item.mediaType === "video" &&
        (sizeInMB > 420 || durationInSeconds > 180)
      ) {
        setIsVideoTooLarge(true);
        return;
      }

      if (onMediaSelect) {
        onMediaSelect({ ...item, uri: fileUri });
      }
 
    } catch (error) {
      console.error("Error getting or copying media asset:", error);
      notifyError("Failed to process media. Please try again.");
    }
  };

  const MediaItem = memo(({ item }) => {
    const isSelectedAndTooLarge =
      isVideoTooLarge && selectedMedia?.id === item.id;

    return (
      <TouchableOpacity
        onPress={() => {
          handleSelectMedia(item);
        }}
        style={{ padding: 1.5, position: "relative" }}
      >
        {isSelectedAndTooLarge && (
          <View style={styles.thumbnailOverlay}>
            <AlertIcon width={23} height={21} />
          </View>
        )}

        {item.mediaType === "photo" ? (
          <Image
            source={{
              uri: Platform.OS === "ios" ? item.fullPath : item.uri,
            }}
            style={{ width: SCREEN_WIDTH / 4 - 1.5, aspectRatio: 1 }}
          />
        ) : (
          <View>
            <Image
              source={{
                uri: item.uri,
              }}
              style={{ width: SCREEN_WIDTH / 4 - 1.5, aspectRatio: 1 }}
              resizeMode="cover"
            />
            <Text style={styles.videoDuration}>
              {convertSecondsToMinutes(item?.duration || 0)}
            </Text>
          </View>
        )}
      </TouchableOpacity>
    );
  });
const handlePermissionRequest = async () => {
  try {
    if (Platform.OS === "ios") {
      mediaListenerFiredRef.current = false;
      await MediaLibrary.presentPermissionsPickerAsync();
    } else {
      const { accessPrivileges } = await MediaLibrary.requestPermissionsAsync();
      setAccess(accessPrivileges);
      toggleManageMediaVisibility(false); 
    }
  } catch (error) {
    console.error("Error requesting media permissions:", error);
    notifyError("Something went wrong. Please try again.");
  }
};


  const renderItem = useCallback(
    ({ item }) => {
      return <MediaItem item={item} />;
    },
    [isVideoTooLarge]
  );

  const FilterButton = () =>
    isLimitedAccess ? (
      <Text style={styles.newPostText}>New Post</Text>
    ) : (
      <TouchableOpacity
        onPress={() => {
          if (!isLimitedAccess) {
            toggleFilterVisibility(true);
          }
        }}
        style={[styles.filterButton, { zIndex: 10 }]}
        // disabled={isFetchingMedia || isLimitedAccess}
        activeOpacity={0.7}
      >
        <Text style={styles.filterText}>
          {(() => {
            const text = selectedAlbum?.title
              ? selectedAlbum.title
              : mediaTypeFilter
              ? mediaTypeFilter.charAt(0).toUpperCase() +
                mediaTypeFilter.slice(1)
              : "All";

            return text.length > 8 ? text.slice(0, 8) + "..." : text;
          })()}
        </Text>

        <ArrowDown
          width={Platform.isPad ? 20 : 10}
          height={Platform.isPad ? 20 : 10}
          color={Colors.black}
        />
      </TouchableOpacity>
    );

  const renderPhotoPreview = () => {
    if (selectedMedia?.mediaType === "photo") {
      return (
        <Image
          source={{
            uri:
              Platform.OS === "ios"
                ? selectedMedia.fullPath
                : selectedMedia.uri,
          }}
          style={{ width: SCREEN_WIDTH, aspectRatio: 1 }}
          resizeMode="contain"
        />
      );
    }
  };

  const renderVideoPreview = () => {
    if (mediaProcessing) {
      return (
        <View
          style={{
            width: SCREEN_WIDTH,
            height: SCREEN_WIDTH,
            justifyContent: "center",
            alignSelf: "center",
          }}
        >
          <ActivityIndicator size={"small"} color={Colors.green} />
        </View>
      );
    }

    if (!isVideoTooLarge) {
      return (
        <Video
          key={selectedMedia.fullPath}
          source={{
            uri:
              Platform.OS === "ios"
                ? selectedMedia.fullPath
                : selectedMedia.uri,
          }}
          style={{
            width: SCREEN_WIDTH,
            height: SCREEN_WIDTH,
          }}
          paused={isVideoTooLarge}
          resizeMode="contain"
          repeat={true}
          controls={!isVideoTooLarge}
          bufferConfig={{
            minBufferMs: 15000,
            maxBufferMs: 50000,
            bufferForPlaybackMs: 2500,
            bufferForPlaybackAfterRebufferMs: 5000,
            backBufferDurationMs: 120000,
            cacheSizeMB: 0,
            live: {
              targetOffsetMs: 500,
            },
          }}
          useTextureView={true}
          playInBackground={false}
        />
      );
    }

    if (isVideoTooLarge) {
      return (
        <Image
          source={{
            uri:
              Platform.OS === "ios"
                ? selectedMedia.fullPath
                : selectedMedia.uri,
          }}
          style={{ width: SCREEN_WIDTH, aspectRatio: 1 }}
          resizeMode="contain"
        />
      );
    }

    return null;
  };

  const renderMediaPreview = () => {
    switch (selectedMedia?.mediaType) {
      case "photo":
        return renderPhotoPreview();
      case "video":
        return renderVideoPreview();
      default:
        return (
          <View
            style={{
              width: SCREEN_WIDTH,
              height: SCREEN_WIDTH,
              justifyContent: "center",
              alignSelf: "center",
            }}
          >
            {isFetchingMedia ? (
              <ActivityIndicator size={"small"} color={Colors.green} />
            ) : (
              <Text style={{ color: Colors.white, textAlign: "center" }}>
                No media selected
              </Text>
            )}
          </View>
        );
    }
  };

  return (
    <>
      <View>
        <View
          style={{
            flexDirection: "row",
            justifyContent: "space-between",
            alignItems: "center",
            marginHorizontal: Size(5),
            marginTop: HeightSize(3),
          }}
        >
          <TouchableOpacity
            onPress={navigation.goBack}
            style={{
              height: HeightSize(8),
              justifyContent: "center",
              position: "relative",
              zIndex: 1,
            }}
            activeOpacity={9}
          >
            <BackIcon />
          </TouchableOpacity>
          <View
            style={{
              position: "absolute",
              width: "100%",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <FilterButton />
          </View>
          <Button
            title={"Next"}
            onPress={() => {
              uploadS3Function();
            }}
            backgroundColor={Colors.green}
            progress={progress}
            disable={progress || isVideoTooLarge || mediaProcessing}
            width="25%"
            height={HeightSize(5)}
            disableOpacity={true}
            disableColor={Colors.green}
            textStyleInsideButton={{
              color: Colors.black,
              fontFamily: "PoppinsBold",
              fontSize: Size(4),
            }}
          />
        </View>

        <View style={{ backgroundColor: Colors.black, position: "relative" }}>
          {renderMediaPreview()}
          {isVideoTooLarge && (
            <View style={styles.errorOverlay}>
              <BlurView
                style={StyleSheet.absoluteFill}
                blurType="dark"
                blurAmount={90}
                reducedTransparencyFallbackColor="rgba(0,0,0,0.7)"
              />
              <AlertIcon width={42} height={36} />
              <Text style={styles.errorOverlayTitle}>
                Oh no! Your video is too large
              </Text>
              <Text style={styles.errorOverlayDescription}>
                Try uploading a different video - it needs to be under 3 minutes
                and 420MB.
              </Text>
            </View>
          )}
        </View>

        <FlatList
          data={mediaFiles}
          // removeClippedSubviews={true}
          style={{
            height:
              Dimensions.get("window").height - SCREEN_WIDTH - HeightSize(11),
            backgroundColor: Colors.black,
          }}
          keyExtractor={(item) => item.id.toString()}
          numColumns={4}
          onEndReached={loadMoreMedia}
          onEndReachedThreshold={0.5}
          initialNumToRender={20}
          maxToRenderPerBatch={20}
          windowSize={10}
          getItemLayout={getItemLayout}
          renderItem={renderItem}
          ListEmptyComponent={
            isFetchingMedia ? (
              <View style={styles.emptyContainer}>
                {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].map((item, index) => (
                  <Animatable.View
                    key={`skeleton-${index}`}
                    animation="pulse"
                    easing="ease-out"
                    iterationCount="infinite"
                    style={styles.skeletonLoader}
                  />
                ))}
              </View>
            ) : (
              <View style={styles.noMediaText}>
                <Text>
                  {selectedAlbum && selectedAlbum.title
                    ? `No media in album "${selectedAlbum.title}"`
                    : mediaTypeFilter === "all"
                    ? "No media"
                    : `No ${mediaTypeFilter}s`}
                </Text>
              </View>
            )
          }
          ListHeaderComponent={
            isLimitedAccess &&
            !isFetchingMedia && (
              <View
                style={{
                  paddingRight: 12,
                  paddingLeft: 7,
                  paddingVertical: 10,
                  backgroundColor: "#323232",
                  flexDirection: "row",
                  alignItems: "center",
                  gap: 5,
                }}
              >
                <Text
                  style={{
                    color: "#e6e6e6",
                    fontFamily: "Regular",
                    //flex: 1,
                    width: "85%",
                    fontSize: 10,
                    //borderWidth: 1
                  }}
                >
                  {mediaFiles.length === 0
                    ? "Please allow all media access for a better experience."
                    : "You’ve only given PLAYER access to some of your photos and videos. You can manage your settings here."}
                </Text>
                <TouchableOpacity
                  onPress={() => {
                    toggleManageMediaVisibility(true);
                  }}
                >
                  <Text
                    style={{
                      color: Colors.green,
                      fontFamily: "PoppinsMedium",
                      fontSize: 12,
                    }}
                  >
                    Manage
                  </Text>
                </TouchableOpacity>
              </View>
            )
          }
          ListFooterComponent={
            hasNextPage && (
              <View
                style={{
                  paddingVertical: 10,
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <ActivityIndicator size={"small"} color={Colors.green} />
              </View>
            )
          }
        />
      </View>

      <Modal
        isVisible={showMediaFilter && !isLimitedAccess}
        onBackdropPress={() => toggleFilterVisibility(false)}
        onSwipeComplete={() => toggleFilterVisibility(false)}
        swipeDirection={enableSwipeToClose ? "down" : null}
        style={{ justifyContent: "flex-end", margin: 0 }}
        propagateSwipe={true}
      >
        <View
          style={{
            height: "70%",
            backgroundColor: "white",
            borderTopLeftRadius: 20,
            borderTopRightRadius: 20,
            padding: 20,
          }}
        >
          <TouchableOpacity
            onPress={() => toggleFilterVisibility(false)}
            style={[commonStyles.modalCloseWrapper, { top: 10, right: 10 }]}
          >
            <Image source={Images.cross} style={commonStyles.modalCloseIcon} />
          </TouchableOpacity>

          <Text
            style={[styles.filterTitle, { marginTop: 10, marginBottom: 20 }]}
          >
            Select album
          </Text>

          <FlatList
            data={combinedItems.filter((item) => {
              if (!item.icon) return false;
              if (
                Platform.OS === "android" &&
                item.type?.toLowerCase() === "favorites"
              ) {
                return false;
              }
              return true;
            })}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={{
                  flex: 1,
                  alignItems: "center",
                  justifyContent: "flex-start",
                  minHeight: 100,
                  paddingHorizontal: 5,
                }}
                onPress={() => handleFilterChange(item)}
                activeOpacity={0.8}
              >
                <View
                  style={
                    item?.title === "All Media" ||
                    item?.title === "Videos" ||
                    item?.title === "Favorites"
                      ? styles.listItem
                      : styles.albumItem
                  }
                >
                  <Image
                    source={item.icon}
                    style={
                      item?.title === "All Media" ||
                      item?.title === "Videos" ||
                      item?.title === "Favorites"
                        ? styles.iconImages
                        : styles.albumImage
                    }
                  />
                </View>

                <Text style={styles.albumName}>{item.title}</Text>

                {item.showCount && (
                  <Text style={styles.albumCount}>
                    {item.assetCount.toLocaleString()}{" "}
                    {item.assetCount === 1 ? "item" : "items"}
                  </Text>
                )}
              </TouchableOpacity>
            )}
            keyExtractor={(item) => `${item.type}-${item.id}`}
            numColumns={3}
            contentContainerStyle={{
              paddingBottom: 100,
              flexGrow: 1,
            }}
            columnWrapperStyle={{
              justifyContent: "space-between",
              marginBottom: 12,
              flexWrap: "wrap",
            }}
            showsVerticalScrollIndicator={true}
            nestedScrollEnabled={true} // Add this
            bounces={true}
            alwaysBounceVertical={true}
            onScrollBeginDrag={() => setEnableSwipeToClose(false)}
            onScrollEndDrag={() => setEnableSwipeToClose(true)}
            onMomentumScrollEnd={() => setEnableSwipeToClose(true)}
          />
        </View>
      </Modal>

      <Modal
        isVisible={showManageMedia}
        onBackdropPress={() => toggleManageMediaVisibility(false)}
        onSwipeComplete={() => toggleManageMediaVisibility(false)}
        swipeDirection="down"
        style={{ justifyContent: "flex-end", margin: 0 }}
        propagateSwipe={true}
        animationIn="slideInUp"
        animationOut="slideOutDown"
        backdropTransitionOutTiming={0}
        hideModalContentWhileAnimating={true}
        useNativeDriver={true}
      >
        <View style={styles.manageModalContainer}>
          <TouchableOpacity
            onPress={() => {
              toggleManageMediaVisibility(false);
            }}
            style={[commonStyles.modalCloseWrapper, { top: 10, zIndex: 2 }]}
          >
            <Image source={Images.cross} style={commonStyles.modalCloseIcon} />
          </TouchableOpacity>

          <Text style={styles.filterTitle}>Manage</Text>

          <View style={[styles.filtersContainer]}>
            <View>
              <TouchableOpacity
                onPress={() => {
                  handlePermissionRequest();
                }}
                style={styles.listItem}
              >
                <Image source={Images.photo} style={styles.filterIcon} />
              </TouchableOpacity>
              <Text
                style={[styles.mediaName, { width: 100, alignSelf: "center" }]}
              >
                Select more media
              </Text>
            </View>

            <View>
              <TouchableOpacity
                style={styles.listItem}
                onPress={openManageMedia}
              >
                <SettingIconGreen width={40} height={40} />
              </TouchableOpacity>
              <Text
                style={[styles.mediaName, { width: 100, alignSelf: "center" }]}
              >
                Change settings
              </Text>
            </View>
            <View style={{ width: 100, height: 100 }} />
          </View>
        </View>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  videoDuration: {
    fontFamily: "favelaBlack",
    lineHeight: 10,
    color: Colors.white,
    position: "absolute",
    bottom: 4,
    right: 4,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    paddingVertical: 3,
    paddingHorizontal: 7,
    fontSize: 7.5,
    borderRadius: 12,
    zIndex: 2,
  },
  emptyContainer: {
    flex: 1,
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "center", // Centers skeleton items
    alignItems: "center", // Ensures they are visible
  },
  skeletonLoader: {
    width: Dimensions.get("screen").width / 4 - 4,
    aspectRatio: 1,
    backgroundColor: "#e6e6e6",
    margin: 2,
    borderRadius: 5, // Optional for better aesthetics
  },
  noMediaText: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    height: Dimensions.get("screen").width / 4 - 4,
  },
  filterButton: {
    //  position: "absolute",
    //right: 10,
    //top: 10,
    //left: "50%",

    paddingHorizontal: 14,
    paddingVertical: 5,
    backgroundColor: "rgba(255,255,255,0.7)",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 20,
    elevation: 5,
  },
  filterText: {
    color: Colors.black,
    fontFamily: "PoppinsSemiBold",
    fontSize: 14,
    marginRight: 6,
  },
  filtersContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    padding: Size(4),

    //flexWrap: 'wrap',
    //gap: 5,
  },
  filterTitle: {
    fontFamily: "PoppinsSemiBold",
    fontSize: 18,
    color: Colors.black,
    textAlign: "center",
  },
  filterIconWrapper: {
    // width: '48%',
    alignItems: "center",
    // marginBottom: 8,
  },
  filterIcon: {
    width: 45,
    height: 45,
  },
  mediaName: {
    fontFamily: "PoppinsMedium",
    fontSize: 14,
    color: Colors.black,
    textAlign: "center",
    marginTop: 4,
  },
  itemWrapper: {
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#012531",
  },
  albumsContainer: {
    flex: 1,
    padding: 8,

    // backgroundColor: Colors.green,
  },
  emptyState: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  emptyText: {
    color: Colors.white,
    textAlign: "center",
  },
  albumItem: {
    width: 100,
    height: 100,
    backgroundColor: "#F5F5F5",
    borderRadius: 8,
    overflow: "hidden",
    margin: 4,
  },
  albumImage: {
    width: "100%",
    aspectRatio: 1,
    borderRadius: 8,
    backgroundColor: "#e6e6e6",
    resizeMode: "cover",
  },
  albumThumbnail: {
    width: "100%",
    height: 150,
  },
  albumName: {
    marginTop: 6,
    fontSize: 14,
    fontWeight: "bold",
    color: "black",
    textAlign: "center",
  },
  albumCount: {
    fontSize: 12,
    color: "#666",
    textAlign: "center",
  },
  listItem: {
    backgroundColor: "#012531",
    width: 100,
    height: 100,
    borderRadius: 8,
    overflow: "hidden",
    margin: 4,
    justifyContent: "center",
    alignItems: "center",
  },
  nextBtn: {
    backgroundColor: Colors.green,
  },
  newPostText: {
    fontSize: 16,
    color: Colors.black,
    marginRight: 10,
    fontFamily: "PoppinsBold",
  },
  errorOverlay: {
    position: "absolute",
    width: "100%",
    height: "100%",
    zIndex: 1,
    alignItems: "center",
    justifyContent: "center",
    overflow: "hidden",
  },
  errorOverlayTitle: {
    color: Colors.white,
    textAlign: "center",
    width: "80%",
    fontFamily: "PoppinsBold",
    fontSize: 14,
    marginTop: 16,
  },
  errorOverlayDescription: {
    color: Colors.white,
    textAlign: "center",
    width: "70%",
    fontFamily: "Regular",
    fontSize: 12,
    marginTop: 4,
  },
  thumbnailOverlay: {
    position: "absolute",
    width: "100%",
    height: "100%",
    backgroundColor: "rgba(0,0,0,0.7)",
    zIndex: 1,
    alignItems: "center",
    justifyContent: "center",
    left: 1.5,
    top: 1.5,
  },
  iconImages: {
    width: 50,
    height: 50,
    resizeMode: "contain",
  },
  manageModalContainer: {
    backgroundColor: "white",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 30,
    maxHeight: "50%",
  },
  manageModalTitle: {
    fontSize: 18,
    fontWeight: "bold",
    textAlign: "center",
    marginBottom: 30,
  },
  manageOptionsContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    paddingHorizontal: 20,
  },
  manageOption: {
    alignItems: "center",
    width: "40%",
  },
  optionButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: "#F5F5F5",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 10,
  },
  manageOptionIcon: {
    width: 30,
    height: 30,
    resizeMode: "contain",
  },
  manageOptionText: {
    textAlign: "center",
    fontSize: 12,
    color: "#333",
    marginTop: 5,
    fontFamily: "PoppinsMedium",
  },
});
export default SelectMedia;
