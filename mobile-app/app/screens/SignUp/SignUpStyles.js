import { StyleSheet } from "react-native";
import { HeightSize, Size } from "../../res/Size";

const styles = StyleSheet.create({
  profileTxt: {
    textAlign: "right",
    marginHorizontal: Size(7),
    marginTop: HeightSize(5),
    textDecorationLine: "underline",
    fontSize: Size(4.3)
  },
  formText: {
    paddingVertical: HeightSize(3),
    textDecorationLine: "underline"
  },
  rowContainer: {
    flexDirection: "row",
    alignItems: "center",
    
  },
  textLink: {
    textDecorationLine: "underline"
  },
  agreeContainer: {
    marginTop: HeightSize(12),
  },
  termsMsg: {
   //textAlign: "left" 
   paddingHorizontal: Size(12)
  },
  asteriskStyle: {
    resizeMode: "center",
    
  }
})

export default styles;