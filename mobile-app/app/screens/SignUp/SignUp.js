import React, { useEffect, useState } from "react";
import { BackHandler, Image, KeyboardAvoidingView, Platform, SafeAreaView, StatusBar, Text, View } from "react-native";
import { commonStyles } from "../../res/CommonStyles";
import { HeightSize, Size } from "../../res/Size";
import styles from "./SignUpStyles";
import CustomTextInput from "../../components/TextInput";
import RadioButton from "../../components/RadioButton/RadioButton";
import Button from "../../components/Button/Button";
import { Colors } from "../../res/Colors";
import ForgotPasswordPopup from "../../components/ForgotPassword/ForgotPasswordPopup";
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { Images } from "../../res/Images";
import { trimString } from "../../constants/misc";
import { dispatch } from "../../redux/store";
import SignupForm from "../../components/SignUp/SignupForm";


const SignUp = ({ route, navigation }) => {

  const [agree, setAgree] = useState(false);
  const [forgotPasswordVisible, setForgotPasswordVisible] = useState(false);
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [errorMessage, setErrorMessage] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState("");
  const [signUpAttempted, setSignUpAttempted] = useState(false);

  const alphabetRegex = /^[A-Za-z]{1,30}$/;
  const emailRegex = /^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/;
  const passwordRegex = /^(?=.*[A-Z]).{6,}$/;
  const phoneNumberRegex = /^[0-9]{10}$/;

  

  useEffect(() => {
    const backAction = () => {
      navigation.navigate('Tutorial');
      return true;
    };

    const backHandler = BackHandler.addEventListener(
      "hardwareBackPress",
      backAction
    );

    return () => backHandler.remove();
  }, []);



  const validation = () => {
    if (email == "" ||
    !emailRegex.test(trimString(email)) ||
      password == "" ||
      !passwordRegex.test(password) ||
      firstName == "" ||
      !alphabetRegex.test(trimString(firstName)) ||
      lastName == "" ||
      !alphabetRegex.test(trimString(lastName)) ||
      phoneNumber == "" ||
      !phoneNumberRegex.test(phoneNumber) ||
      !agree ||
      !signUpAttempted

    ) {
      return true;
    } else {
      return false;
    }
  }

  const signUp = () => {
    setSignUpAttempted(true);
    if (!validation()) {
      navigation.navigate("Profile", {
        firstName: firstName,
        lastName: lastName,
        email: email,
        password: password,
        phoneNumber: phoneNumber
      })
    } else {
      setErrorMessage(true);
    }
  }

  const handleAgreement = () => {
    setAgree(!agree);
  }

  const handleSkip = () => {
    navigation.navigate("BottomStack");
  };

  return (
    
      <SafeAreaView style={commonStyles.container}>
        <StatusBar barStyle="dark-content" hidden={false} backgroundColor={Colors.white} translucent={false} />
        <KeyboardAvoidingView
        style={{ flex: 1 }} 
        behavior={Platform.OS === "ios" ? "padding" : null} 
        keyboardVerticalOffset={Platform.OS === "ios" ? 64 : 0}
      >
        <Text style={[commonStyles.regularText, styles.profileTxt]} onPress={handleSkip}>Skip</Text>
        <View style={[commonStyles.subContainer, {marginTop: HeightSize(3)}]}>
          <Text style={[commonStyles.boldText]}>SIGN UP</Text>
          <Text style={[commonStyles.regularText, styles.formText]} onPress={() => navigation.navigate("Login")}>
            Already a member? Log in
          </Text>
          {/* <View style={styles.rowContainer}>
            <CustomTextInput
              label={"First Name"}
              style={{ flex: 1 , height: HeightSize(7) }}
              textInputStyle={commonStyles.textInputStyle}
              onChangeText={(e) => {
                setFirstName(trimString(e));
                setErrorMessage(false);
              }}
              value={firstName}
              errorMsg={
                trimString(firstName) == "" &&
                  !alphabetRegex.test(trimString(firstName)) &&
                  errorMessage
                    ? "Please Enter First Name"
                    : !alphabetRegex.test(trimString(firstName)) && errorMessage
                    ? "Name Should Contain Only Alphabets"
                    : ""
              }
            />
            <CustomTextInput
              label={"Last Name"}
              style={{ marginLeft: 30, flex: 1 , height: HeightSize(7) }}
              textInputStyle={commonStyles.textInputStyle}
              onChangeText={(e) => {
                setLastName(trimString(e));
                setErrorMessage(false);
              }}
              value={lastName}
              errorMsg={
                trimString(lastName) == "" &&
                  !alphabetRegex.test(trimString(lastName)) &&
                  errorMessage
                    ? "Please Enter Last Name"
                    : !alphabetRegex.test(trimString(lastName)) && errorMessage
                    ? "Last Name Should Contain Only Alphabets"
                    : ""
              }
            />
          </View>
          <CustomTextInput
            label={"Email"}
            style={{marginTop: HeightSize(3)}}
            textInputStyle={commonStyles.textInputStyle}
            onChangeText={(e) => {
              setEmail(trimString(e));
              setErrorMessage(false);
            }}
            value={email}
            errorMsg={
              trimString(email) == "" &&
                  !emailRegex.test(trimString(email)) &&
                  errorMessage
                    ? "Please Enter Email"
                    : !emailRegex.test(trimString(email)) && errorMessage
                    ? "Please Enter Valid Email"
                    : ""
            }
          />
          <CustomTextInput
            label={"Password"}
            textInputStyle={commonStyles.textInputStyle}
            onChangeText={(e) => {
              setPassword(trimString(e));
              setErrorMessage(false);
            }}
            value={password}
            secureTextEntry={true}
            errorMsg={
              password !== "" && !passwordRegex.test(password) && errorMessage
                ? "Your Password Is Weak"
                : (password == "" && errorMessage)
                  ? "Please Enter Password"
                  : ""
            }
          />
          <CustomTextInput
            label={"Phone Number"}
            textInputStyle={[commonStyles.textInputStyle]}
            value={phoneNumber}
            keyboardType={"numeric"}
            maxLength={10}
            onChangeText={(value) => {
              setPhoneNumber(value.trim());
            }}
            errorMsg={
              phoneNumber !== "" && !phoneNumberRegex.test(phoneNumber) && errorMessage
              ? "Please Enter 10 digits"
              : phoneNumber == "" && errorMessage 
                ? "Please Enter Phone Number"
                : ""
            }
          />

          <RadioButton
            label={
              <View>
                <Text style={commonStyles.regularText}>
                  I agree with the PlayerApp <Text style={[styles.textLink, commonStyles.regularText]}  onPress={() => navigation.navigate("PrivacyPolicyWebView")}>Privacy Policy</Text>
                </Text>
                <Text style={commonStyles.regularText}>and <Text style={[styles.textLink, commonStyles.regularText]}>Terms of usage</Text>
                  {(signUpAttempted && !agree &&
                    firstName !== "" && lastName !== ""
                    && email !== "" && password !== "" && phoneNumber !== "") ?

                    <Image source={Images.asteriskIcon}
                      style={styles.asteriskStyle}
                    />
                    : null
                  }
                </Text>
              </View>
            }
            unselectedImage={require('../../../assets/notselected.png')}
            selectedImage={require('../../../assets/tick.png')}
            selected={agree}
            onSelect={handleAgreement}
            style={styles.agreeContainer}
          /> */}
        </View>
        {/* {(signUpAttempted && !agree &&
          firstName !== "" && lastName !== ""
          && email !== "" && password !== "" && phoneNumber !== "" ) ?
          <Text style={[commonStyles.errTxt, styles.termsMsg]}>Please agree to the terms and conditions.</Text>
          : null
        }
        <Button
          title={"SIGN UP"}
          containerStyle={{
            marginVertical: HeightSize(3),
            backgroundColor: (signUpAttempted && !agree &&
              firstName !== "" && lastName !== ""
              && email !== "" && password !== "" && phoneNumber !== "") ? Colors.grey : Colors.black
          }}
          onPress={() => {
            signUp();

          }}
        /> */}
        {/* <Text style={[styles.textLink, commonStyles.regularText, { textAlign: "center", paddingVertical: HeightSize(1) }]}
          onPress={() => setForgotPasswordVisible(true)}
        >
          Forgotten your password?
        </Text>
        <ForgotPasswordPopup
          visible={forgotPasswordVisible}
          onClose={() => setForgotPasswordVisible(false)}
        /> */}
        <SignupForm/>
        </KeyboardAvoidingView>
      </SafeAreaView>
    
  );
}

export default SignUp;
