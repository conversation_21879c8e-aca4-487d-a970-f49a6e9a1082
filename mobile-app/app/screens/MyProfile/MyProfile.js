// REACT //
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { shallowEqual, useSelector } from "react-redux";

// PLUGINS //
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Ionicons } from "@expo/vector-icons";

// COMPONENTS //
import {
  ActivityIndicator,
  Image,
  Modal,
  Platform,
  SafeAreaView,
  ScrollView,
  RefreshControl,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
  BackHandler,
} from "react-native";
import { Images } from "../../res/Images";
import FastImage from "react-native-fast-image";
import ShareProfile from "../../components/ShareProfile";
import Tabs from "../../components/Tabs";
import ProfileHighlights from "../../components/ProfileHighlights/ProfileHighlights";
import ExperienceModal from "../../components/ExperienceModal/ExperienceModal";
import ExperienceCard from "../../components/ExperienceCard/ExperienceCard";
import PhysicalData from "../../components/PhysicalData/PhysicalData";
import SwitchButton from "../../components/SwitchButton";
import VerificationRequest from "../../components/VerificationRequest";
import * as ImagePicker from "expo-image-picker";
import BlockReportModal from "../../components/BlockReportModal";
import Button from "../../components/Button/Button";

// CONSTANTS //
import {
  PreferredFoot,
  calculateAge,
  getUserData,
  notifySuccess,
  notifyWarn,
  readableFoot,
  readablePosition,
  trim150,
} from "../../constants/misc";

// NAVIGATION //
import { useFocusEffect, useIsFocused } from "@react-navigation/native";

// OTHERS //
import { HeightSize, Size } from "../../res/Size";
import { Colors } from "../../res/Colors";
import styles from "./MyProfileStyles";
import {
  ArrowDown,
  BackIcon,
  CheckSquare,
  DottedMore,
  LogoImg,
  LogoWhiteImg,
  PlusIcon,
  SettingIcon,
  ShareProfileIcon,
  WhiteEditIcon,
} from "../../res/Svg";
import { WEB_URL } from "../../../Config";
const LiveLike = require("@livelike/javascript");
import { debounce } from "lodash";
import { dispatch } from "../../redux/store";
import _uniqWith from "lodash/uniqWith";
import _iseEqual from "lodash/isEqual";
import { uploadS3 } from "../../../s3.config";
import moment from "moment";
import { commonStyles } from "../../res/CommonStyles";
import {
  selectAuthStatus,
  selectUserInfo,
} from "../../redux/selectors/authSelectors";
import { getSingleModel } from "../../constants/Constant";
import { useAppStateEffect } from "../../hooks/useAppStateEffect";
import UserApi from "../../services/UserApi";
import FollowApi from "../../services/FollowApi";

const MyProfile = ({ navigation, route }) => {
  const selectedTab = route?.params?.selectedTab;
  const [selected, setSelected] = useState(
    selectedTab ? selectedTab : "experience"
  );
  const [isLoading, setLoading] = useState(true);
  const [isRefreshing, setRefreshing] = useState(false);
  const [bannerImg, setBannerImg] = useState(null);
  const [bannerFileName, setBannerFileName] = useState(null);
  const [userDetails, setUserDetails] = useState(null);
  const [visible, setVisible] = useState(false);
  const [imageViewData, setImageViewData] = useState();
  const [visibleHighlights, setVisibleHighlights] = useState([]);
  const [userHighlights, setUserHighlights] = useState({
    data: [],
    lastEvaluatedKey: null,
  });
  const [fetchingMore, setFetchingMore] = useState(false);
  const [isFetching, setIsFetching] = useState(false);
  const [followers, setFollowers] = useState();
  const [following, setFollowing] = useState();
  const [followersCount, setFollowersCount] = useState(0);
  const [followingCount, setFollowingCount] = useState(0);
  const [dropdownVisible, setDropdownVisible] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [blockModalVisible, setBlockModalVisible] = useState(false);
  const [confirmationVisible, setConfirmationVisible] = useState(false);
  const [actionType, setActionType] = useState(null);
  const [modalPosition, setModalPosition] = useState({ top: 0, right: 0 });
  const [isBioModalVisible, setIsBioModalVisible] = useState(false);
  const [profilePicLoading, setProfilePicLoading] = useState(false);

  // Get userId from both route.params and deep link params
  const userId = route?.params?.userId || route?.params?.id;
  const fromSearch = route?.params?.fromSearch;
  const userType = route?.params?.type;

  const videoRef = useRef(null);
  const userInfo = useSelector(selectUserInfo, shallowEqual);
  const { loggedInUser, guestUser } = useSelector(({ user }) => ({
    loggedInUser: user.data,
    guestUser: user.guest,
  }));

  // Effect to update userDetails when redux state changes
  useEffect(() => {
    if (userId && guestUser) {
      setUserDetails(guestUser);
    } else if (!userId && loggedInUser) {
      setUserDetails(loggedInUser);
    }
  }, [userId, guestUser, loggedInUser]);

  const fetchingHighlight = useSelector(
    ({ loading }) => loading.effects.feed.fetchHighlightsByUserId
  );

  const getCachedProfile = async (id) => {
    try {
      // Don't use cache for own profile
      if (id === userInfo?.id) return null;

      const cachedData = await AsyncStorage.getItem(`profile_${id}`);
      if (cachedData) {
        const { data, timestamp } = JSON.parse(cachedData);
        // Cache other users' profiles for 10 min
        if (Date.now() - timestamp < 60 * 10 * 1000) {
          return data;
        }
      }
      return null;
    } catch (error) {
      console.error("Error reading cache:", error);
      return null;
    }
  };

  const setCachedProfile = async (id, data) => {
    try {
      const cacheData = JSON.stringify({
        data,
        timestamp: Date.now(),
      });
      await AsyncStorage.setItem(`profile_${id}`, cacheData);
    } catch (error) {
      console.error("Error saving profile cache:", error);
    }
  };

  const getProfile = async (forceRefresh = false) => {
    try {
      const auth = getSingleModel("auth");
      if (!auth?.authUser?.authenticated) {
        return;
      }

      setLoading(true);

      const isOwnProfile = !userId || userId === userInfo?.id;

      if (isOwnProfile) {
        // Always fetch fresh data for own profile
        const response = await dispatch.user.getUserProfile({
          id: userInfo?.id,
        });
        if (response?.data?.data) {
          const freshData = response.data.data;
          setUserDetails(freshData);
        }
      } else {
        // For other profiles, try cache first
        const cachedData = !forceRefresh && (await getCachedProfile(userId));
        if (cachedData) {
          setUserDetails(cachedData);
        } else {
          // Fetch fresh data for other profiles
          const freshData = await dispatch.user.getUserProfileForGuest(userId);
          if (freshData) {
            setUserDetails(freshData);

            // Cache only other users' profiles
            await setCachedProfile(userId, freshData);
          }
        }
      }
    } catch (error) {
      console.error("Error in getProfile:", error);
      if (error?.response?.status === 401) {
        return;
        // navigation.navigate("AuthNavigation", { screen: "Login" }); // Removed to prevent double navigation on logout
      }
    } finally {
      setLoading(false);
    }
  };

  // Effect to fetch profile initially
  useEffect(() => {
    const auth = getSingleModel("auth");
    if (auth?.authUser?.authenticated) {
      getProfile(true);
    }
  }, [userId, userInfo?.id]);

  // Pull-to-refresh handler
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await getProfile(true);
    setRefreshing(false);
  }, [userId]);

  // Handle Android back button & iOS swipe gesture
  useFocusEffect(
    useCallback(() => {
      const handleBackPress = () => {
        // if (navigation.canGoBack()) {
        //   navigation.goBack();
        // } else {
        // If no previous screen, navigate to Feed while restoring scroll position
        navigation.navigate("HomeScreenStack", {
          screen: "Feed",
          params: { restoreScrollPosition: true },
        });
        // }
        return true;
      };

      // Handle swipe-back on iOS
      const unsubscribe = navigation.addListener("beforeRemove", (e) => {
        // if (navigation.canGoBack()) {
        //   return;
        // }
        e.preventDefault();
        navigation.navigate("HomeScreenStack", {
          screen: "Feed",
          params: { restoreScrollPosition: true },
        });
      });

      // Handle Android back button
      const backHandler = BackHandler.addEventListener(
        "hardwareBackPress",
        handleBackPress
      );

      return () => {
        backHandler.remove();
        unsubscribe();
      };
    }, [navigation])
  );

  const getUserHighlights = async (
    requestPayload = { limit: 100 },
    isFetchingMore = false,
    isRefresh = false
  ) => {
    try {
      if (isFetchingMore) {
        setFetchingMore(true);
      } else {
        setIsFetching(true);
      }

      const targetId = userId || userInfo?.id;
      if (!targetId) return;

      const data = await dispatch.feed.fetchHighlightsByUserId({
        id: targetId,
        requestPayload,
      });

      if (data?.data) {
        const sortedData = [...data.data].sort(
          (a, b) => new Date(b.createdAt) - new Date(a.createdAt)
        );

        setUserHighlights({
          data: sortedData,
          lastEvaluatedKey: data.lastEvaluatedKey || null,
        });

        // For initial load or refresh, show first 10 items
        if (!isFetchingMore) {
          setVisibleHighlights(sortedData.slice(0, 10));
        }
      }
    } catch (error) {
      console.error("Error fetching user highlights:", error);
    } finally {
      setFetchingMore(false);
      setIsFetching(false);
    }
  };

  // Add this new function to handle load more
  const loadMoreHighlights = () => {
    const currentLength = visibleHighlights.length;
    const allHighlights = userHighlights.data;

    if (currentLength < allHighlights.length) {
      const nextBatch = allHighlights.slice(currentLength, currentLength + 10);
      setVisibleHighlights((prev) => [...prev, ...nextBatch]);
    }
  };

  // Handle refresh
  const handleRefresh = useCallback(() => {
    // Reset pagination and fetch fresh data
    setUserHighlights({ data: [], lastEvaluatedKey: null });
    setVisibleHighlights([]);
    getUserHighlights({ limit: 100 }, false, true);
  }, [userId, userInfo?.id]);

  // Initial load
  useEffect(() => {
    getUserHighlights({ limit: 100 }, false, true);
  }, [userId, userInfo?.id]);

  // Handle navigation focus and refresh
  useEffect(() => {
    const unsubscribe = navigation.addListener("focus", () => {
      if (route.params?.refreshHighlights) {
        handleRefresh();
        navigation.setParams({ refreshHighlights: undefined });
      }
    });

    return unsubscribe;
  }, [navigation, route.params?.refreshHighlights]);

  const getAllTeams = async () => {
    dispatch.team.getAllTeams();
  };

  const getFollowers = async () => {
    try {
      const { data } = await UserApi.getFollowers(userId);
      setFollowersCount(data.count);
      setFollowers(data.followers);
    } catch (error) {
      console.error("Error getting followers:", error);
    }
  };

  const getFollowing = async () => {
    const { data } = await UserApi.getFollowing(userId);
    setFollowingCount(data.count);
    setFollowing(data.following);
  };

  const follow = async () => {
    try {
      const { data } = await FollowApi.follow(userId);

      setFollowers((prev) => [...prev, data.followingUser]);
      setFollowersCount((prev) => prev + 1);

      notifySuccess(
        `You followed ${data.followedUser.firstName}${
          data.followedUser.lastName ? ` ${data?.followedUser.lastName}` : ""
        }`
      );
      setDropdownVisible(false);
    } catch (error) {
      console.error("Error following:", error);
    }
  };

  const unfollow = async () => {
    try {
      const { data } = await FollowApi.unfollow(userId);

      setFollowers((prev) =>
        prev.filter((follower) => follower.id !== data.unfollowingUser.id)
      );
      setFollowersCount((prev) => Math.max(prev - 1, 0));

      notifySuccess(
        `You unfollowed ${data.unfollowedUser.firstName}${
          data.unfollowedUser.lastName ? ` ${data.unfollowedUser.lastName}` : ""
        }`
      );
      setDropdownVisible(false);
    } catch (error) {
      console.error("Error unfollowing:", error);
    }
  };

  const isLoggedInUserFollowingProfile = useMemo(() => {
    return Boolean(followers?.find((follower) => follower.id === userInfo?.id));
  }, [followers, userInfo?.id]);

  const isProfileFollowingLoggerInUser = useMemo(() => {
    return Boolean(following?.find((follow) => follow.id === userInfo?.id));
  }, [following, userInfo?.id]);

  useEffect(() => {
    getAllTeams();
  }, []);

  const fetchFollowersAndFollowing = useCallback(() => {
    getFollowers();
    getFollowing();
  }, [userId]);
  useFocusEffect(fetchFollowersAndFollowing);
  useAppStateEffect({
    onForeground: () => {
      fetchFollowersAndFollowing();
    },
  });

  const handleTabChange = (tab) => {
    setSelected(tab);
  };

  const closeBlockReportModal = () => {
    setBlockModalVisible(false);
  };

  const openBlockReportModal = (nativeEvent) => {
    setModalPosition({
      top: nativeEvent.pageY,
      right: Size(10),
    });
    setBlockModalVisible(true);
  };

  const handleOpenConfirmation = (type) => {
    closeBlockReportModal();
    setActionType(type);
    setConfirmationVisible(true);
  };

  const handleConfirmAction = () => {
    if (actionType === "block") {
      handleBlockUser();
    } else if (actionType === "report") {
      handleReportComment();
    }
    setConfirmationVisible(false);
    closeBlockReportModal();
  };

  // Add a dedicated function for experience updates
  const handleExperienceUpdate = useCallback(async () => {
    // Force refresh profile data
    await getProfile(true);

    // If we're not already on our own profile, navigate back to it
    if (route.params?.userId !== userInfo?.id) {
      navigation.reset({
        index: 0,
        routes: [
          {
            name: "MyProfile",
            params: {
              userId: userInfo?.id,
              forceRefresh: true,
              refresh: Date.now(),
            },
          },
        ],
      });
    }
  }, [userInfo?.id, route.params?.userId]);

  const handleBlockUser = async () => {
    try {
      if (!userDetails.id) {
        console.error("No user selected to block");
        return;
      }

      const status = await dispatch.user.blockUser({
        blockedUserId: userDetails.id,
        reason: "",
      });

      if (status.status === 1) {
        await dispatch.user.fetchUserDetails(userInfo?.id);
      }
    } catch (err) {
      console.error(
        "Error in blocking user:",
        err.response ? err.response.data : err.message
      );
    }
  };

  const handleProfilePicPicker = async () => {
    if (userInfo?.id !== userDetails?.id) {
      return;
    }

    setProfilePicLoading(true);
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
    });

    if (!result.canceled) {
      const uri = result.assets[0].uri;
      const fileName = uri.split("/").pop();

      // uploadS3Function for profile photo
      const uploadImage = async (imageUri, fileName) => {
        return new Promise((resolve, reject) => {
          uploadS3(
            imageUri,
            fileName,
            "",
            "",
            "",
            (uploadedUrl) => resolve(uploadedUrl),
            (error) => reject(error)
          );
        });
      };

      try {
        const uploadedProfile = await uploadImage(uri, fileName);
        await dispatch.user.updateUser({
          photoUrl: uploadedProfile,
          id: userInfo?.id,
          shouldNotShowNotification: true,
        });
        await getProfile(true);
      } catch (error) {
        console.error("Profile image upload failed:", error);
      }
    }
    setProfilePicLoading(false);
  };

  const handleImagePicker = (type) => {
    setModalVisible(false);
    pickImage(type);
  };

  const pickImage = async (type) => {
    let result;
    if (type === "camera") {
      result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
      });
    } else {
      result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
      });
    }

    if (!result.canceled) {
      const uri = result.assets[0].uri;
      const fileName = uri.split("/").pop();
      setBannerImg(uri);
      setBannerFileName(fileName);
      await uploadS3Function(uri, fileName);
    }
  };

  const uploadS3Function = async (imageUri, fileName) => {
    setLoading(true);
    if (!imageUri) {
      console.error("No image URI provided");
      setLoading(false);
      return;
    }

    const uploadImage = async (imageUri, fileName) => {
      return new Promise((resolve, reject) => {
        uploadS3(
          imageUri,
          fileName,
          "",
          "",
          "",
          (uploadedUrl) => resolve(uploadedUrl),
          (error) => reject(error)
        );
      });
    };

    try {
      const uploadedBanner = await uploadImage(imageUri, fileName);
      if (typeof onSuccess === "function") {
        onSuccess(uploadedBanner);
      }
      await dispatch.user.updateUser({
        banner: uploadedBanner,
        id: userInfo?.id,
        shouldNotShowNotification: true,
      });

      await getProfile(true);
      setLoading(false);
    } catch (error) {
      console.error("Image upload failed:", error);
      if (typeof onSuccess === "function") {
        onSuccess(null);
      }
      setLoading(false);
    }
  };

  /** Function to toggle bio modal */
  const toggleBioModal = () => {
    setIsBioModalVisible((prev) => !prev);
  };

  /** Function to handle share profile */
  const handleShare = () => {
    navigation.navigate("ShareProfile", {
      highlight: null,
      type: "profile",
      profile: {
        id: userDetails?.id,
        firstName: userDetails?.firstName,
        lastName: userDetails?.lastName,
        photoUrl: userDetails?.photoUrl,
      },
    });
  };

  // Memoize color calculations
  const colors = useMemo(
    () => ({
      bgColorAndText:
        userDetails?.userType !== "NON_PLAYER" ? Colors.black : Colors.white,
      textColor:
        userDetails?.userType !== "NON_PLAYER" ? Colors.white : Colors.black,
      borderColor:
        userDetails?.userType !== "NON_PLAYER"
          ? Colors.dark_grey
          : Colors.black,
      textColorInverted:
        userDetails?.userType !== "NON_PLAYER" ? Colors.black : Colors.white,
      bgTextBtn:
        userDetails?.userType !== "NON_PLAYER" ? Colors.white : Colors.black,
    }),
    [userDetails?.userType]
  );

  // Use memoized colors
  const {
    bgColorAndText,
    textColor,
    borderColor,
    textColorInverted,
    bgTextBtn,
  } = colors;

  useEffect(() => {
    const invalidateCache = async () => {
      if (userDetails?.id === userInfo?.id) {
        await AsyncStorage.removeItem(`profile_${userInfo?.id}`);
      }
    };

    // Invalidate cache when experiences change
    if (
      userDetails?.experiences?.length !==
      (userDetails?.experiences || []).length
    ) {
      invalidateCache();
    }
  }, [userDetails?.experiences]);

  if (isLoading) {
    return (
      <SafeAreaView
        style={{
          flex: 1,
          backgroundColor: Colors.black,
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <ActivityIndicator size="large" color={Colors.white} />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: bgColorAndText }}>
      <StatusBar
        barStyle="default"
        hidden={false}
        backgroundColor={Colors.black}
        translucent={false}
      />
      <View style={{ position: "relative" }}>
        <View
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            backgroundColor: colors.bgColorAndText,
            padding: 20,
            alignItems: "center",
            zIndex: 100,
          }}
        >
          <TouchableOpacity
            onPress={() =>
              navigation.navigate("HomeScreenStack", {
                screen: "Feed",
                params: { restoreScrollPosition: true },
              })
            }
            style={{ position: "absolute", top: "100%", left: Size(4) }}
          >
            <BackIcon
              color={userDetails?.userType == "NON_PLAYER" ? "black" : "white"}
            />
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() =>
              navigation.navigate("HomeScreenStack", {
                screen: "Feed",
              })
            }
            style={styles.logoContainer}
          >
            {userDetails?.userType == "NON_PLAYER" ? (
              <LogoImg width={Size(37)} height={HeightSize(3.5)} />
            ) : (
              <LogoWhiteImg width={Size(37)} height={HeightSize(3.5)} />
            )}
          </TouchableOpacity>

          <View style={styles.headerContainer}>
            {/* <ShareProfile header={profileHeader} title={profileTitle}
              url={profileUrl}
              id="profile"
              userType={userDetails?.userType} /> */}

            {/* Profile Share Button */}
            <TouchableOpacity
              onPress={handleShare}
              style={{
                width: 30,
                height: 30,
                borderRadius: 15,
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <ShareProfileIcon
                width={Platform.isPad ? 40 : 25}
                height={Platform.isPad ? 40 : 25}
                color={
                  userDetails?.userType == "NON_PLAYER"
                    ? Colors.black
                    : Colors.white
                }
              />
            </TouchableOpacity>

            {userDetails?.id === userInfo?.id ? (
              <TouchableOpacity
                onPress={() => navigation.navigate("Settings")}
                style={{
                  width: 30,
                  height: 30,
                  borderRadius: 15,
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <SettingIcon
                  width={Platform.isPad ? 40 : 25}
                  height={Platform.isPad ? 40 : 25}
                  color={
                    userDetails?.userType == "NON_PLAYER"
                      ? Colors.black
                      : Colors.white
                  }
                />
              </TouchableOpacity>
            ) : (
              <TouchableOpacity
                onPress={(event) => openBlockReportModal(event.nativeEvent)}
              >
                <DottedMore
                  color={
                    userDetails?.userType == "NON_PLAYER"
                      ? Colors.black
                      : Colors.white
                  }
                  width={Platform.isPad ? 40 : 20}
                  height={Platform.isPad ? 40 : 20}
                />
              </TouchableOpacity>
            )}

            {blockModalVisible && (
              <BlockReportModal
                visible={blockModalVisible}
                onClose={() => setBlockModalVisible(false)}
                onBlock={() => handleOpenConfirmation("block")}
                onReport={() => handleOpenConfirmation("report")}
                top={modalPosition.top}
                right={modalPosition.right}
                showReportOption={false} // Hide report option on messages screen
              />
            )}

            {/* Confirmation Modal */}
            <Modal
              visible={confirmationVisible}
              transparent={true}
              animationType="fade"
              onRequestClose={() => setConfirmationVisible(false)}
            >
              <View style={styles.confirmationModalContainer}>
                <View style={styles.confirmationModalContent}>
                  <Text style={styles.confirmationText}>
                    Are you sure you want to{" "}
                    <Text style={{ color: Colors.red }}>
                      {actionType === "block" ? "block" : "report"}
                    </Text>{" "}
                    {actionType === "block" ? "this user?" : "this post?"}
                  </Text>
                  <View style={styles.confirmationButtonContainer}>
                    <Button
                      title="Yes"
                      onPress={() => handleConfirmAction()}
                      containerStyle={{ width: "45%" }}
                      backgroundColor={Colors.black}
                      textStyleInsideButton={{
                        fontSize: Size(4),
                        fontFamily: "Regular",
                      }}
                      height={HeightSize(5)}
                    />
                    <Button
                      title="No"
                      onPress={() => setConfirmationVisible(false)}
                      containerStyle={{
                        borderWidth: 1,
                        borderColor: "#A5A5A5",
                        width: "45%",
                      }}
                      backgroundColor={Colors.white}
                      textStyleInsideButton={{
                        color: "#A5A5A5",
                        fontSize: Size(4),
                        fontFamily: "Regular",
                      }}
                      height={HeightSize(5)}
                    />
                  </View>
                </View>
              </View>
            </Modal>
          </View>
        </View>
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={[
            styles.contentContainerStyle,
            {
              backgroundColor: bgColorAndText,
              marginTop: 60,
              paddingBottom: 100,
            },
          ]}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={onRefresh}
              colors={["#9Bd35A", "#689F38"]} // Android
              tintColor="#689F38" // iOS
            />
          }
        >
          <View>
            <View>
              {userDetails?.id === userInfo?.id && (
                <>
                  <TouchableOpacity
                    style={styles.bannerEditBtn}
                    onPress={() => handleImagePicker("library")}
                  >
                    <WhiteEditIcon />
                  </TouchableOpacity>
                  <Modal
                    animationType="slide"
                    transparent={true}
                    visible={modalVisible}
                    onRequestClose={() => setModalVisible(false)}
                  >
                    <View style={modalStyles.centeredView}>
                      <View style={modalStyles.modalView}>
                        <TouchableOpacity
                          style={modalStyles.button}
                          onPress={() => handleImagePicker("library")}
                        >
                          <Text style={modalStyles.textStyle}>
                            Photo Gallery
                          </Text>
                          <Ionicons
                            name="images"
                            size={Size(7)}
                            color={Colors.white}
                            // style={{ opacity: focused ? 1 : 0.3 }}
                          />
                        </TouchableOpacity>
                        <TouchableOpacity
                          style={modalStyles.button}
                          onPress={() => handleImagePicker("camera")}
                        >
                          <Text style={modalStyles.textStyle}>Take Photo</Text>
                          <Ionicons
                            name="camera"
                            size={Size(7)}
                            color={Colors.white}
                          />
                        </TouchableOpacity>
                        <TouchableOpacity
                          style={modalStyles.cancelButton}
                          onPress={() => setModalVisible(false)}
                        >
                          <Text style={modalStyles.textStyle}>Cancel</Text>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </Modal>
                </>
              )}

              {!userDetails?.banner ? (
                <Image
                  source={Images.blankImage}
                  style={{ width: "100%", height: HeightSize(17) }}
                />
              ) : (
                <Image
                  source={{ uri: userDetails?.banner }}
                  style={{ width: "100%", height: HeightSize(17) }}
                />
              )}
            </View>

            {userInfo?.id === userDetails?.id ? (
              <TouchableOpacity
                onPress={handleProfilePicPicker}
                disabled={profilePicLoading}
                style={styles.profileView}
              >
                {profilePicLoading ? (
                  <ActivityIndicator
                    size="large"
                    color={Colors.primary}
                    style={styles.photoContainer}
                  />
                ) : !userDetails?.photoUrl ? (
                  <Image
                    source={Images.profile1}
                    style={styles.photoContainer}
                  />
                ) : (
                  <FastImage
                    source={{ uri: userDetails?.photoUrl }}
                    style={styles.photoContainer}
                    resizeMode={FastImage.resizeMode.cover}
                  />
                )}
                <View
                  pointerEvents="none"
                  style={{
                    position: "absolute",
                    top: 80,
                    left: Size(22),
                    backgroundColor: "white",
                    borderRadius: 12,
                    padding: 2,
                    zIndex: 3,
                    alignSelf: "center",
                    borderWidth: 1,
                    borderColor: Colors.green,
                  }}
                >
                  <WhiteEditIcon width={15} height={15} />
                </View>
              </TouchableOpacity>
            ) : (
              <View  
              style={styles.profileView}>
                {!userDetails?.photoUrl ? (
                  <Image
                    source={Images.profile1}
                    style={styles.photoContainer}
                  />
                ) : (
                  <FastImage
                    source={{ uri: userDetails?.photoUrl }}
                    style={styles.photoContainer}
                    resizeMode={FastImage.resizeMode.cover}
                  />
                )}
              </View>
            )}

            <View style={styles.followContainer}>
              <TouchableOpacity
                onPress={() => {
                  navigation.navigate("Connections", {
                    type: {
                      connectionCount: followersCount,
                      title: "followers",
                    },
                    fromSearch: fromSearch,

                    data: {
                      followers,
                      following,
                      followersCount,
                      followingCount,
                      userId,
                      fullName: `${userDetails.firstName} ${userDetails.lastName}`,
                    },
                  });
                }}
              >
                <Text
                  style={[
                    styles.followTxt,
                    { textAlign: "center", color: textColor },
                  ]}
                >
                  {followersCount}
                </Text>
                <Text style={[styles.followTxt, { color: textColor }]}>
                  Followers
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => {
                  navigation.navigate("Connections", {
                    type: {
                      connectionCount: followingCount,
                      title: "following",
                    },
                    fromSearch: fromSearch,

                    data: {
                      followers,
                      following,
                      followersCount,
                      followingCount,
                      userId,
                      fullName: `${userDetails.firstName} ${userDetails.lastName}`,
                    },
                  });
                }}
              >
                <Text
                  style={[
                    styles.followTxt,
                    { textAlign: "center", color: textColor },
                  ]}
                >
                  {followingCount}
                </Text>
                <Text style={[styles.followTxt, { color: textColor }]}>
                  Following
                </Text>
              </TouchableOpacity>
            </View>
            <View style={styles.buttonsContainer}>
              {userDetails?.id === userInfo?.id ? (
                <TouchableOpacity
                  style={[styles.verifyButton, { backgroundColor: bgTextBtn }]}
                  onPress={() =>
                    navigation.navigate("HomeScreenStack", {
                      screen: "MessageList",
                      params: {
                        fromMyProfileContent: true,
                      },
                    })
                  }
                >
                  <Text
                    style={[
                      styles.verifyButtonText,
                      { color: textColorInverted },
                    ]}
                  >
                    Inbox
                  </Text>
                </TouchableOpacity>
              ) : isLoggedInUserFollowingProfile ? (
                <View style={styles.dropdown}>
                  <TouchableOpacity
                    style={[
                      styles.dropdownButton,
                      { backgroundColor: "lightgray" },
                    ]}
                    onPress={() => setDropdownVisible(!dropdownVisible)}
                  >
                    <Text style={[styles.followingText, { color: textColor }]}>
                      Following
                    </Text>
                    <ArrowDown color={textColor} />
                  </TouchableOpacity>
                  {dropdownVisible && (
                    <View
                      style={[
                        styles.dropdownContent,
                        { backgroundColor: bgTextBtn },
                      ]}
                    >
                      <TouchableOpacity
                        onPress={unfollow}
                        style={styles.unfollowButton}
                        hitSlop={{ top: 25, bottom: 25, left: 15, right: 15 }}
                      >
                        <Text
                          style={[
                            styles.unfollowText,
                            { color: textColorInverted },
                          ]}
                        >
                          Unfollow
                        </Text>
                      </TouchableOpacity>
                    </View>
                  )}
                </View>
              ) : (
                <TouchableOpacity
                  style={[
                    styles.verifyButton,
                    {
                      backgroundColor: isLoggedInUserFollowingProfile
                        ? "lightgray"
                        : Colors.green,
                    },
                  ]}
                  onPress={() => {
                    if (loggedInUser) {
                      follow();
                    } else {
                      notifyWarn("Please login to follow");
                    }
                  }}
                >
                  <Text
                    style={[styles.verifyButtonText, { color: Colors.black }]}
                  >
                    {isProfileFollowingLoggerInUser ? "Follow Back" : "Follow"}
                  </Text>
                </TouchableOpacity>
              )}
              {userDetails?.id === userInfo?.id ? (
                <TouchableOpacity
                  onPress={() => {
                    navigation.navigate("EditProfile", {
                      photoUrl: userDetails?.photoUrl,
                      firstName: userDetails?.firstName,
                      lastName: userDetails?.lastName,
                      age: calculateAge(userDetails?.birthday),
                      dob: userDetails?.birthday,
                      location: userDetails?.location,
                      organizationName:
                        userDetails?.teamName?.trim() &&
                        userDetails.teamName !== "N/A"
                          ? userDetails?.teamName
                          : userDetails?.clubName,
                      bio: userDetails?.bio,
                      preferredFoot: userDetails?.preferredFoot,
                      height: userDetails?.height,
                      position: userDetails?.position,
                      id: userDetails?.id,
                      banner: userDetails?.banner,
                      photoFileName: userDetails?.photoUrl,
                      bannerFileName: userDetails?.banner,
                      email: userDetails?.email,
                      nonPlayerRole: userDetails?.nonPlayerRole,
                      contractExpiry: userDetails?.contractExpiry,
                      representedBy: userDetails?.representedBy,
                    });
                  }}
                  style={[
                    styles.verifyButton,
                    { marginLeft: 0, backgroundColor: bgTextBtn },
                  ]}
                >
                  <Text
                    style={[
                      styles.verifyButtonText,
                      { color: textColorInverted },
                    ]}
                  >
                    Edit Profile
                  </Text>
                </TouchableOpacity>
              ) : (
                <TouchableOpacity
                  style={[
                    styles.verifyButton,
                    {
                      backgroundColor: bgTextBtn,
                      position: "absolute",
                      right: 0,
                    },
                  ]}
                  onPress={() =>
                    navigation.navigate("HomeScreenStack", {
                      screen: "Message",
                      params: {
                        recipientId: userId,
                        fromMyProfileContent: true,
                      },
                    })
                  }
                >
                  <Text
                    style={[
                      styles.verifyButtonText,
                      { color: textColorInverted },
                    ]}
                  >
                    Message
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
          <View style={styles.detailsContainer}>
            <View style={{ flexDirection: "row", alignItems: "center" }}>
              <Text
                style={[
                  styles.detailsText,
                  { marginRight: Size(3), color: textColor },
                ]}
              >
                {userDetails?.firstName} {userDetails?.lastName}
                {userDetails?.userType !== "NON_PLAYER" && userDetails?.birthday
                  ? `, ${calculateAge(userDetails?.birthday)}`
                  : ""}
              </Text>

              {userDetails?.verified ? (
                <CheckSquare />
              ) : (
                // : !Boolean(shouldShowVerifySwitch) && userDetails.id === loggedUserInfo.id ? (
                //   <VerificationRequest userDetails={userDetails} userDetails={loggedUserInfo} />
                // )
                <Text style={styles.emptyText}></Text>
              )}
              {/* {Boolean(shouldShowVerifySwitch) && !userDetails?.verified && (
              <SwitchButton
                small
                //isLoading={isLoading}
                handleCheckboxChange={onVerifyPlayer}
                textFont={"font-poppins"}
                uppercaseText={false}
                text="Verify player"
                labelPosition="left"
                activeBgColor=":bg-[#52FF00]"
                inActiveBgColor="bg-gray-300"
              />
            )} */}
            </View>

            {/* Team Name */}
            <Text
              style={[
                styles.detailsText,
                { fontFamily: "Regular", color: textColor },
              ]}
            >
              <Text
                style={[
                  styles.detailsText,
                  { fontFamily: "Regular", color: textColor },
                ]}
              >
                {userDetails?.teamName?.trim() && userDetails.teamName !== "N/A"
                  ? userDetails?.teamName
                  : userDetails?.clubName}
              </Text>
            </Text>
            {userDetails?.userType !== "NON_PLAYER" && (
              <Text
                style={[
                  styles.detailsText,
                  { fontFamily: "Regular", color: textColor },
                ]}
              >
                Contract Expires: {userDetails?.contractExpiry || "N/A"}
              </Text>
            )}

            {/* Position - Only if its a player */}
            {userDetails?.userType !== "NON_PLAYER" &&
              userDetails?.position && (
                <Text
                  style={[
                    styles.detailsText,
                    { fontFamily: "Regular", color: textColor },
                  ]}
                >
                  <Text
                    style={{
                      textTransform: "capitalize",
                    }}
                  >
                    {readablePosition(userDetails?.position)},{" "}
                  </Text>
                  <Text>{readableFoot(userDetails?.preferredFoot)}</Text>
                </Text>
              )}

            {/* Non-Player Role */}
            {userDetails?.userType == "NON_PLAYER" &&
              userDetails?.nonPlayerRole?.length > 0 && (
                <Text
                  style={[
                    styles.detailsText,
                    textColor && {
                      fontFamily: "Regular",
                      color: textColor,
                    },
                  ]}
                >
                  {userDetails?.nonPlayerRole?.map((item, idx) => (
                    <Text key={idx}>
                      {item}
                      {userDetails?.nonPlayerRole?.length - 1 !== idx
                        ? ","
                        : ""}{" "}
                    </Text>
                  ))}
                </Text>
              )}

            {/* Height */}
            {userDetails?.height && userDetails.userType !== "NON_PLAYER" && (
              <Text
                style={[
                  styles.detailsText,
                  { fontFamily: "Regular", color: textColor },
                ]}
              >
                {userDetails?.height}
              </Text>
            )}
            {/* DOB - Only if its a player */}
            {userDetails?.birthday &&
              userDetails?.userType !== "NON_PLAYER" && (
                <Text
                  style={[
                    styles.detailsText,
                    {
                      fontFamily: "Regular",
                      color: textColor,
                      textTransform: "none",
                    },
                  ]}
                >
                  DOB:{" "}
                  {`${moment(
                    userDetails?.birthday,
                    "YYYY/MM/DD" || "YYYY-MM-DD"
                  ).format("DD.MM.YYYY")}`}
                </Text>
              )}
            {/* Location */}
            {userDetails?.location && (
              <Text
                style={[
                  styles.detailsText,
                  { fontFamily: "Regular", color: textColor },
                ]}
              >
                {userDetails?.location}
              </Text>
            )}

            {userDetails?.userType !== "NON_PLAYER" && (
              <Text
                style={[
                  styles.detailsText,
                  { fontFamily: "Regular", color: textColor },
                ]}
              >
                Represented by: {userDetails?.representedBy || "N/A"}
              </Text>
            )}

            {/* Bio */}
            {userDetails?.bio && (
              <View style={[styles.bioWrapper, { borderColor: borderColor }]}>
                <Text
                  style={[
                    styles.detailsText,
                    {
                      fontFamily: "Regular",
                      color: textColor,
                    },
                  ]}
                >
                  {userDetails?.bio.length <= 28
                    ? userDetails?.bio
                    : userDetails?.bio.slice(0, 28) + "..."}
                </Text>

                {/* Read More Button */}
                {userDetails?.bio.length > 28 && (
                  <TouchableOpacity
                    onPress={toggleBioModal}
                    activeOpacity={0.6}
                  >
                    <Text
                      style={[
                        styles.detailsText,
                        {
                          fontFamily: "PoppinsBold",
                          textAlign: "center",
                          color: textColor,
                          fontSize: Size(3.5),
                          textDecorationLine: "underline",
                        },
                      ]}
                    >
                      Read More
                    </Text>
                  </TouchableOpacity>
                )}
              </View>
            )}

            {/* Modal to display full bio */}
            <Modal
              animationType="slide"
              transparent={true}
              visible={isBioModalVisible}
              onRequestClose={toggleBioModal}
            >
              <TouchableWithoutFeedback onPress={toggleBioModal}>
                <View style={commonStyles.modalBackdrop}>
                  <TouchableWithoutFeedback>
                    <View style={commonStyles.modalContainer}>
                      {/* Close Button */}
                      <TouchableOpacity
                        style={commonStyles.modalCloseWrapper}
                        activeOpacity={0.8}
                        onPress={toggleBioModal}
                        hitSlop={{ top: 40, bottom: 40, left: 40, right: 40 }}
                      >
                        <Image
                          source={Images.cross}
                          style={commonStyles.modalCloseIcon}
                        />
                      </TouchableOpacity>

                      {/* User Name */}
                      <Text
                        style={[
                          styles.detailsText,
                          {
                            fontFamily: "favelaBold",
                            color: Colors.black,
                            fontSize: 16,
                            marginBottom: 12,
                            textTransform: "uppercase",
                          },
                        ]}
                      >
                        {userDetails?.firstName}'s Bio:
                      </Text>

                      {/* User Bio */}
                      <Text
                        style={[
                          styles.detailsText,
                          {
                            fontFamily: "Regular",
                            color: Colors.black,
                            fontSize: 12,
                            lineHeight: 20,
                          },
                        ]}
                      >
                        {userDetails?.bio}
                      </Text>

                      {/* See less button */}
                      <TouchableOpacity
                        onPress={toggleBioModal}
                        style={{
                          paddingVertical: HeightSize(0.5),
                          borderRadius: Size(2),
                          marginTop: 12,
                          alignItems: "center",
                        }}
                      >
                        <Text
                          style={{
                            fontFamily: "PoppinsBold",
                            color: Colors.black,
                            fontSize: 14,
                            textDecorationLine: "underline",
                          }}
                        >
                          See less
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </TouchableWithoutFeedback>
                </View>
              </TouchableWithoutFeedback>
            </Modal>

            {/* Experience & Highlights Tab */}
            <Tabs
              tabs={["experience", "highlights"]}
              selected={selected}
              setSelected={handleTabChange}
              color={textColor}
              inactiveColor={textColor}
              isConnections={false}
            />
            {selected === "highlights" ? (
              <View style={{ flex: 1 }}>
                <ProfileHighlights
                  profileHighlights={visibleHighlights}
                  userId={userId}
                  isNonPlayer={userDetails?.userType === "NON_PLAYER"}
                  textColor={textColor}
                  ownProfile={userDetails?.id === userInfo?.id}
                  fetchingHighlight={isFetching}
                  fetchingMore={fetchingMore}
                  onRefresh={handleRefresh}
                  onLoadMore={loadMoreHighlights}
                />

                {(isFetching || fetchingMore) && (
                  <ActivityIndicator
                    size="small"
                    color={Colors.primary}
                    style={{ padding: 15 }}
                  />
                )}

                {!fetchingMore &&
                  visibleHighlights.length < userHighlights.data.length && (
                    <TouchableOpacity
                      onPress={loadMoreHighlights}
                      style={{ paddingVertical: 10 }}
                    >
                      <Text
                        style={{
                          fontFamily: "Regular",
                          textAlign: "center",
                          padding: 15,
                          color: textColor,
                          textDecorationLine: "underline",
                        }}
                      >
                        Load More (
                        {userHighlights.data.length - visibleHighlights.length}{" "}
                        more)
                      </Text>
                    </TouchableOpacity>
                  )}
              </View>
            ) : (
              <View>
                {userDetails &&
                userDetails.experiences &&
                Object.keys(userDetails.experiences).length > 0 ? (
                  <View>
                    <ExperienceCard
                      data={userDetails?.experiences || []}
                      shouldEdit={userDetails?.id === userInfo?.id}
                      userType={userDetails?.userType}
                      bgColorAndText={bgColorAndText}
                      textColor={textColor}
                      onExperienceUpdate={handleExperienceUpdate}
                    />
                  </View>
                ) : userDetails?.id !== userInfo?.id ? (
                  <View>
                    <Text
                      style={[
                        styles.detailsText,
                        {
                          fontFamily: "Regular",
                          textAlign: "center",
                          color: "#757575",
                        },
                      ]}
                    >
                      "No club history & stats"
                    </Text>
                  </View>
                ) : (
                  <View>
                    <Text
                      style={[
                        styles.detailsText,
                        {
                          fontFamily: "Regular",
                          textAlign: "center",
                          color: "#757575",
                          textTransform: "none",
                        },
                      ]}
                    >
                      You don't have any experience yet. Please add new
                      experience by clicking{" "}
                      <Text style={{ color: textColor }}>the '+' button.</Text>
                    </Text>
                  </View>
                )}
              </View>
            )}

            {userDetails?.id === userInfo?.id && selected !== "highlights" && (
              <ExperienceModal
                userType={userDetails?.userType}
                onExperienceUpdate={handleExperienceUpdate}
              />
            )}

            {userDetails?.userType !== "NON_PLAYER" &&
              selected !== "highlights" &&
              userDetails?.id && <PhysicalData userInfo={userDetails} />}

            {selected !== "highlights" && (
              <>
                <Text style={[styles.labelText, { color: textColor }]}>
                  REFERENCES
                </Text>
                <TouchableOpacity
                  style={[styles.viewrefBtn, { borderColor: textColor }]}
                  onPress={() => {
                    navigation.navigate("ReferenceList", {
                      toUserId: userDetails?.id,
                    });
                  }}
                >
                  <Text style={[styles.viewrefBtnTxt, { color: textColor }]}>
                    View References
                  </Text>
                </TouchableOpacity>
              </>
            )}
          </View>
        </ScrollView>
      </View>
    </SafeAreaView>
  );
};

const modalStyles = StyleSheet.create({
  centeredView: {
    flex: 1,
    alignItems: "center",
    position: "absolute",
    right: Size(5),
    top: HeightSize(15),
  },
  modalView: {
    // margin: Size(5),
    backgroundColor: Colors.dark_grey,
    borderRadius: Size(4),
    paddingVertical: Size(4),
    // alignItems: 'center',
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  button: {
    paddingVertical: HeightSize(1),
    paddingHorizontal: Size(5),
    borderBottomWidth: 0.4,
    borderBottomColor: Colors.white,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  cancelButton: {
    paddingTop: HeightSize(1),
    alignItems: "center",
  },
  textStyle: {
    color: "white",
    fontFamily: "PoppinsMedium",
    //textAlign: 'center',
    fontSize: Size(4),
    marginRight: Size(5),
  },
  experiencesContainer: {
    padding: Size(16),
  },
  noExperiencesText: {
    color: Colors.gray,
    textAlign: "center",
    marginTop: Size(16),
  },
});

export default MyProfile;
