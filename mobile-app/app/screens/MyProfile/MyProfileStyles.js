import { StyleSheet } from "react-native";
import { HeightSize, Size } from "../../res/Size";
import { Colors } from "../../res/Colors";

const styles = StyleSheet.create({
  logoContainer: {
    alignItems: "center",
  },
  headerContainer: {
    flexDirection: "row",
    gap: Size(0.5),
    alignItems: "center",
    position: "absolute",
    top: 0,
    right: Size(4),
    transform: [{ translateY: Size(5.5) }],
    zIndex: 1,
  },
  photoContainer: {
    width: Size(30),
    height: Size(30),
    borderRadius: Size(15),
  },
  detailsContainer: {
    flex: 1,
    marginTop: HeightSize(2.5),
    paddingHorizontal: Size(5),
  },
  bioWrapper: {
    flexDirection: "row",
    alignItems: "center",
    gap: Size(2),
    paddingVertical: HeightSize(1),
    borderTopWidth: 1,
    borderBottomWidth: 1,
    marginVertical: HeightSize(1.5),
  },
  detailsText: {
    fontFamily: "PoppinsBold",
    color: Colors.white,
    fontSize: Size(4),
    paddingVertical: HeightSize(0.2),
  },
  editTxt: {
    fontFamily: "Regular",
    textAlign: "center",
    color: Colors.white,
    fontSize: Size(4),
    paddingVertical: HeightSize(1),
    textDecorationLine: "underline",
  },
  plusIcon: {
    width: Size(10),
    height: Size(10),
    resizeMode: "contain",
    alignSelf: "center",
    marginVertical: HeightSize(2),
  },
  labelText: {
    paddingVertical: HeightSize(2),
    fontFamily: "Bold",
    fontSize: Size(4.5),
    color: Colors.white,
  },
  addButtonText: {
    color: "white",
    fontSize: Size(10),
    opacity: 0.4,
  },
  contentContainerStyle: {
    backgroundColor: Colors.black,
    flexGrow: 1,
    paddingBottom: HeightSize(5),
    //paddingHorizontal: Size(2),
  },
  verifyButton: {
    backgroundColor: "#52FF00",
    borderRadius: Size(10),
    paddingVertical: HeightSize(1),
    // paddingHorizontal: Size(4),
    // marginVertical: HeightSize(3),
    marginLeft: Size(35),
    width: Size(27),
    justifyContent: "center",
    alignItems: "center",
  },
  verifyButtonText: {
    color: "black",
    fontSize: Size(3.8),
    fontFamily: "PoppinsMedium",
  },
  buttonsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginHorizontal: Size(4),
    alignItems: "center",
  },
  followContainer: {
    //flex: 1,
    flexDirection: "row",
    marginLeft: Size(42),
    justifyContent: "space-between",
    marginRight: Size(8),
    marginVertical: HeightSize(1),
  },
  followTxt: {
    fontFamily: "Regular",
    fontSize: Size(4),
    color: "white",
  },
  followButton: {
    backgroundColor: Colors.primary,
    padding: Size(2),
    borderRadius: Size(1),
  },
  unfollowButton: {
    backgroundColor: Colors.secondary,
    padding: Size(2),
    borderRadius: Size(1),
  },
  followButtonText: {
    color: Colors.white,
    textAlign: "center",
  },
  unfollowButtonText: {
    color: Colors.black,
    textAlign: "center",
  },
  dropdown: {
    //position: "absolute",
    // borderRadius: 2,
    borderColor: "white",
    left: Size(35),
    position: "relative",
    zIndex: 1,
  },
  dropdownButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: HeightSize(1),
    backgroundColor: "#ffffff8a",
    borderRadius: Size(10),
    paddingHorizontal: Size(3),
    width: Size(27),
    gap: 2,
  },
  unfollowText: {
    color: "black",
    textAlign: "center",
  },
  followingText: {
    color: "white",
    fontSize: Size(3.8),
    fontFamily: "PoppinsMedium",
  },
  dropdownIcon: {
    marginLeft: 5,
  },
  dropdownContent: {
    position: "absolute",
    top: "100%",
    left: 0,
    right: 0,
    backgroundColor: Colors.white,
    borderWidth: 1,
    borderColor: Colors.grey,
    zIndex: 100,
    borderRadius: Size(3),
    marginTop: HeightSize(0.5),
    //padding: 8,
  },
  bannerEditBtn: {
    position: "absolute",
    top: 10,
    zIndex: 10,
    right: 12,
  },
  confirmationModalContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  confirmationModalContent: {
    backgroundColor: "white",
    paddingHorizontal: Size(4.5),
    borderRadius: Size(9),
    marginHorizontal: Size(5),
    alignItems: "center",
    paddingVertical: HeightSize(2),
    width: "80%",
  },
  confirmationText: {
    marginBottom: HeightSize(2),
    fontSize: Size(5),
    textAlign: "center",
    fontFamily: "PoppinsMedium",
  },
  confirmationButtonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
    paddingHorizontal: Size(5),
  },
  viewrefBtnTxt: {
    opacity: 1,
    fontSize: Size(4),
    fontFamily: "Regular",
  },
  viewrefBtn: {
    borderWidth: 1,
    padding: HeightSize(1),
    width: Size(40),
    alignItems: "center",
    borderRadius: Size(9),
  },
  profileView: {
    position: "absolute",
    zIndex: 9999,
    width: Size(30),
    height: Size(30),
    borderRadius: Size(15),
    position: "absolute",
    top: HeightSize(14),
    left: Size(5),
    zIndex: 1,
  }
});

export default styles;
