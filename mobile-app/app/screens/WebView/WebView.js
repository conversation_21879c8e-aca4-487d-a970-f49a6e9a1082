// REACT //
import React from "react";

// REACT NATIVE //
import {
  View,
  ActivityIndicator,
  SafeAreaView,
  StatusBar,
  Platform,
} from "react-native";
import { TouchableOpacity } from "react-native";

// PLUGINS //
import { WebView } from "react-native-webview";

// OTHERS //
import styles from "./WebViewStyles";
import { commonStyles } from "../../res/CommonStyles";
import { Colors } from "../../res/Colors";
import { BackIcon } from "../../res/Svg";

const WebViewScreen = ({ navigation, route }) => {
  const { url, title } = route.params;

  return (
    <SafeAreaView
      style={[commonStyles.container, { backgroundColor: Colors.white }]}
    >
      <StatusBar
        barStyle="dark-content"
        hidden={false}
        backgroundColor={Colors.white}
        translucent={false}
      />

      <View style={styles.backHeader}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          activeOpacity={0.8}
          style={styles.backBtn}
        >
          <BackIcon
            width={Platform.isPad ? 40 : 24}
            height={Platform.isPad ? 40 : 24}
          />
        </TouchableOpacity>
      </View>

      <View style={{ flex: 1 }}>
        <WebView
          source={{ uri: url }}
          startInLoadingState
          renderLoading={() => (
            <ActivityIndicator
              size="large"
              style={styles.loader}
              color={Colors.black}
            />
          )}
        />
      </View>
    </SafeAreaView>
  );
};

export default WebViewScreen;
