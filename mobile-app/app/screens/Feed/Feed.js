import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import {
  BackHandler,
  Image,
  SafeAreaView,
  StatusBar,
  Text,
  TouchableOpacity,
  View,
  Platform,
  Modal,
  FlatList,
  DeviceEventEmitter,
} from "react-native";
import styles from "./FeedStyle";
import { Colors } from "../../res/Colors";
import { LogoImg, MessageIcon, PlusGreenIcon } from "../../res/Svg";
import { HeightSize, Size } from "../../res/Size";
import { shallowEqual, useSelector } from "react-redux";
import { dispatch } from "../../redux/store";
import * as Animatable from "react-native-animatable";
import Button from "../../components/Button/Button";
import analytics from "../../services/analytics";
import UpdateModal from "../../components/UpdateModal";
import { selectUserInfo } from "../../redux/selectors/authSelectors";
import UnreadMessageCount from "../../components/UnreadMessageCount";
import { useUnreadMessageCount } from "../../hooks/useUnreadMessageCount";
import { useFocusEffect } from "@react-navigation/native";
import { useAppStateEffect } from "../../hooks/useAppStateEffect";
import FeedHighlight from "./FeedHighlight";
import { FeedHighlightSeparator } from "./FeedHighlightSeparator";
import Competition from "../../components/Competitions/Competitions";

const Feed = ({ route, navigation }) => {
  const [shouldShowEmojiPanel, setShouldShowEmojiPanel] = useState({
    visible: false,
    highlightId: "",
  });
  const [selectedHighlight, setSelectedHighlight] = useState(null);
  const [modalPosition, setModalPosition] = useState({ top: 0, right: 0 });
  const [blockModalVisible, setBlockModalVisible] = useState(false);
  const [selectedUserId, setSelectedUserId] = useState(null);
  const [confirmationVisible, setConfirmationVisible] = useState(false);
  const [actionType, setActionType] = useState(null);
  const [selectedPostIdx, setselectedPostIdx] = useState(null);
  const [showLoginPopup, setShowLoginPopup] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showUpdateModal, setShowUpdateModal] = useState(false);
  const [activeVideoId, setActiveVideoId] = useState(null);
  const selectedHighlightRef = useRef();
  const scrollViewRef = useRef(null);
  const scrollPositionRef = useRef(0);
  const isMountedRef = useRef(true);

  const viewabilityConfig = {
    itemVisiblePercentThreshold: 60,
    minimumViewTime: 300,
  };
  const onViewableItemsChanged = useRef(({ viewableItems }) => {
    if (viewableItems && viewableItems.length > 0) {
      const visibleItem = viewableItems.find((item) => item.isViewable);
      if (visibleItem) {
        setActiveVideoId(visibleItem.item.id);
      }
    } else {
      setActiveVideoId(null);
    }
  }).current;

  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  const { highlights } = useSelector(({ feed }) => feed);

  const userInfo = useSelector(selectUserInfo, shallowEqual);

  const blockedUsers = userInfo?.blockedUsers || [];

  const isFetchingHighlights = useSelector(
    ({ loading }) => loading.effects.feed.fetchHighlights
  );
  const backHandlerRef = useRef(null);

  const unreadMessageCount = useUnreadMessageCount(userInfo?.id);

  const { activeAnnouncements } = useSelector(
    ({ announcement }) => announcement
  );

  const loggedUserId = userInfo?.id || "";

  const getItemKey = useCallback((item) => item.id, []);

  const feedListData = useMemo(() => {
    const filteredHighlights = highlights.filter(
      (highlight) => !blockedUsers.includes(highlight?.userId)
    );

    const announcementItems =
      activeAnnouncements && activeAnnouncements.length > 0
        ? [
            activeAnnouncements.sort(
              (a, b) => new Date(b.createdAt) - new Date(a.createdAt)
            )[0],
          ]
        : [];

    return [...announcementItems, ...filteredHighlights];
  }, [highlights, blockedUsers, activeAnnouncements]);

  const handleRefresh = async () => {
    setIsRefreshing(true);

    await dispatch.feed.reset();
    await dispatch.feed.fetchHighlights();

    setIsRefreshing(false);
  };

  const fetchHighlights = useCallback(async () => {
    if (!isMountedRef.current || isFetchingHighlights) return;

    await dispatch.feed.fetchHighlights();
  }, [isFetchingHighlights]);

  const fetchAnnouncements = async () => {
    await dispatch.announcement.getAllAnnouncements();
  };

  useEffect(() => {
    fetchAnnouncements();
  }, []);

  useEffect(() => {
    if (route?.params?.restoreScrollPosition && scrollViewRef.current) {
      setTimeout(() => {
        if (scrollViewRef.current?.scrollToOffset) {
          scrollViewRef.current.scrollToOffset({
            y: scrollPositionRef.current,
            animated: false,
          });
        } else {
          console.error(" scrollTo method is missing!");
        }
      }, 100);
    }
  }, [route?.params?.restoreScrollPosition]);

  useEffect(() => {
    const listener = DeviceEventEmitter.addListener("scrollToTop", () => {
      if (scrollViewRef.current) {
        scrollViewRef.current.scrollToOffset({ y: 0, animated: true });
      }
    });

    return () => {
      listener.remove();
    };
  }, []);

  const getHighlights = useCallback(() => {
    if (!userInfo?.id) return;

    fetchHighlights();
  }, [userInfo?.id]);

  useAppStateEffect({
    onForeground: getHighlights,
  });
  useFocusEffect(getHighlights);

  const handleOpenProfile = useCallback((id) => {
    if (id) {
      navigation.navigate("MyProfileScreenStack", {
        screen: "MyProfile",
        params: { userId: id },
      });
    }
  }, []);

  useEffect(() => {
    const backAction = () => {
      if (userInfo?.id) {
        backHandlerRef.current?.remove();
        return true;
      } else {
        // navigation.push("AuthNavigation", { screen: "Login" }); // Removed to prevent double navigation on logout
        return true;
      }
    };

    backHandlerRef.current = BackHandler.addEventListener(
      "hardwareBackPress",
      backAction
    );

    return () => backHandlerRef.current?.remove();
  }, [userInfo]);

  const addUserReaction = useCallback(
    async (highlight, reaction) => {
      setShouldShowEmojiPanel({ visible: false, highlightId: "" });

      const filterById = (reactedBy, userId) => {
        if (reactedBy) {
          return reactedBy.filter((item) => item.userId !== userId);
        }
        return [];
      };

      try {
        const existingHighlights = feedListData?.find(
          (item) => item.id === highlight.id
        );

        if (existingHighlights) {
          const filteredReactions = filterById(
            existingHighlights.reactedByUsers,
            userInfo?.id
          );

          const reactedByUsersWithNewReaction = [
            ...filteredReactions,
            {
              userId: userInfo?.id,
              others: {
                user: {
                  firstName: userInfo.firstName,
                  lastName: userInfo.lastName,
                  photoUrl: userInfo.photoUrl,
                  cludId: userInfo.cludId,
                  gender: userInfo.gender,
                },
                emoji: reaction.file,
                emojiName: reaction.name,
              },
            },
          ];

          await analytics.logReactionAdded(
            highlight.id,
            highlight.announcementType ? "announcement" : "highlight",
            reaction.name
          );

          if (highlight.announcementType) {
            await dispatch.announcement.updateAnnouncement({
              id: highlight.id,
              reactedByUsers: reactedByUsersWithNewReaction,
            });
          } else {
            await dispatch.feed.editHighlights({
              userId: highlight.userId,
              id: highlight.id,
              reactedByUsers: reactedByUsersWithNewReaction,
              noNotification: true,
              isEmojiUpdate: true,
            });
          }
        }
      } catch (error) {
        console.error("Error adding user reaction:", error);
      }
    },
    [feedListData, userInfo?.id]
  );

  useEffect(() => {
    if (userInfo?.photoUrl) {
      highlights.forEach((highlight) => {
        if (
          highlight.user.id === userInfo.id &&
          highlight.user.photoUrl !== userInfo.photoUrl
        ) {
          dispatch.feed.updateHighlightUserPhotoUrl({
            userId: userInfo.id,
            newPhotoUrl: userInfo.photoUrl,
          });
        }
      });
    }
  }, [userInfo.photoUrl]);

  const openProfileBlockReportModal = useCallback((highlight, nativeEvent) => {
    selectedHighlightRef.current = highlight;
    setselectedPostIdx(highlight.id); // Assuming `id` is used to identify the post
    setModalPosition({
      top: nativeEvent.pageY,
      right: Size(5), // Adjust the position if needed
    });
    setSelectedUserId(highlight.userId);
    setSelectedHighlight(highlight); // Store the selected highlight
    setBlockModalVisible(true);
  }, []);

  const closeBlockReportModal = useCallback(() => {
    setselectedPostIdx(null);
    setBlockModalVisible(false);
  }, []);

  const handleOpenConfirmation = useCallback(
    (action) => {
      closeBlockReportModal();
      setActionType(action); // 'block' or 'report'
      setConfirmationVisible(true);
    },
    [closeBlockReportModal]
  );

  const handleConfirmAction = useCallback(() => {
    if (actionType === "block") {
      handleBlockUser();
    } else if (actionType === "report") {
      // Use selectedHighlight directly
      handleReportComment(selectedHighlight);
    }

    setConfirmationVisible(false);
    closeBlockReportModal();
    selectedHighlightRef.current = null;
  }, [actionType, handleBlockUser, handleReportComment, closeBlockReportModal]);

  const handleBlockUser = async () => {
    try {
      if (!selectedUserId) {
        console.error("No user selected to block");
        return;
      }

      const payload = {
        blockedUserId: selectedUserId,
        reason: "", // Optionally include a reason for blocking
      };

      // Dispatch the blockUser action
      const status = await dispatch.user.blockUser(payload);

      if (status.status === 1) {
        // Fetch the updated userInfo before navigating
        await dispatch.user.fetchUserDetails(userInfo.id);
      }
    } catch (err) {
      console.error(
        "Error in blocking user:",
        err.response ? err.response.data : err.message
      );
    }
  };

  // Report comment function
  const handleReportComment = async (highlight) => {
    const selectedHighlight = selectedHighlightRef.current;

    // Get reporter's full name
    const reporterFullName = `${userInfo?.firstName || "Unknown"} ${
      userInfo?.lastName || "User"
    }`;
    // Determine reported user's full name and ID based on reportType
    let reportedFullName = "Unknown User";
    let reportedUserId = "UnknownUserId";
    let reportedContent = "No content";

    // Report type is POST
    reportedFullName = `${selectedHighlight?.user?.firstName || "Unknown"} ${
      selectedHighlight?.user?.lastName || "User"
    }`;
    reportedUserId = selectedHighlight?.userId || "UnknownUserId";
    reportedContent = selectedHighlight?.comment || "No content";

    // Prepare the payload for the report
    const payload = {
      reporterUserId: userInfo?.id || "UnknownUserId",
      reportedUserId: reportedUserId,
      reporterFullName: reporterFullName,
      reportedFullName: reportedFullName,
      reason: "",
      reportedContent: reportedContent,
      reportedPhotoUrl: "", // Optional
      reporterPhotoUrl: userInfo?.photoUrl || "",
      reportType: "POST",
      contendId: selectedHighlight?.id,
    };

    console.log("payload", payload);

    // Submit the report
    try {
      const status = await dispatch.user.submitReport(payload);
      if (status === 1) {
        await dispatch.user.fetchUserDetails(userInfo.id);
      } else {
        console.error("Report submission failed");
      }
    } catch (error) {
      console.error("Error in submitting report:", error.message);
    }

    // Close the modal
    closeBlockReportModal();
  };

  const renderFeedHighlight = useCallback(
    ({ item }) => {
      const isActive = activeVideoId === item.id;

      if (item.announcementType) {
        return (
          <Competition
            announcement={item}
            loggedUserId={loggedUserId}
            isDetailPage={false}
            isActive={isActive}
            shouldShowEmojiPanel={shouldShowEmojiPanel}
            setShouldShowEmojiPanel={setShouldShowEmojiPanel}
            addUserReaction={addUserReaction}
            setShowLoginPopup={setShowLoginPopup}
          />
        );
      }

      return (
        <FeedHighlight
          highlight={item}
          isActive={isActive}
          loggedUserId={loggedUserId}
          setShowLoginPopup={setShowLoginPopup}
          handleOpenProfile={handleOpenProfile}
          userInfo={userInfo}
          openProfileBlockReportModal={openProfileBlockReportModal}
          blockModalVisible={blockModalVisible}
          selectedPostIdx={selectedPostIdx}
          setBlockModalVisible={setBlockModalVisible}
          handleOpenConfirmation={handleOpenConfirmation}
          modalPosition={modalPosition}
          confirmationVisible={confirmationVisible}
          setConfirmationVisible={setConfirmationVisible}
          handleConfirmAction={handleConfirmAction}
          shouldShowEmojiPanel={shouldShowEmojiPanel}
          setShouldShowEmojiPanel={setShouldShowEmojiPanel}
          navigation={navigation}
          actionType={actionType}
          addUserReaction={addUserReaction}
        />
      );
    },
    [
      activeVideoId,
      loggedUserId,
      setShowLoginPopup,
      handleOpenProfile,
      userInfo,
      openProfileBlockReportModal,
      blockModalVisible,
      selectedPostIdx,
      setBlockModalVisible,
      handleOpenConfirmation,
      modalPosition,
      confirmationVisible,
      setConfirmationVisible,
      handleConfirmAction,
      shouldShowEmojiPanel,
      setShouldShowEmojiPanel,
      navigation,
      actionType,
      addUserReaction,
    ]
  );

  return (
    <View style={{ backgroundColor: Colors.white, flex: 1 }}>
      <SafeAreaView>
        <StatusBar
          barStyle="dark-content"
          hidden={false}
          backgroundColor={Colors.white}
          translucent={false}
        />
        <UpdateModal
          isVisible={showUpdateModal}
          onClose={() => setShowUpdateModal(false)}
        />
        <View style={{ position: "relative" }}>
          <View
            // style={[styles.headerContainer, {backgroundColor: Colors.green}]}
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              backgroundColor: "white",
              padding: 20,
              alignItems: "center",
              zIndex: 100,
            }}
          >
            <View
              style={{
                flex: 1,
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <LogoImg width={Size(37)} height={HeightSize(3.5)} />
            </View>
            <TouchableOpacity
              style={styles.messageContainer}
              onPress={() => {
                !loggedUserId
                  ? setShowLoginPopup(true)
                  : navigation.navigate("MessageList");
              }}
            >
              {unreadMessageCount > 0 && (
                <View style={styles.unreadMessageContainer}>
                  <UnreadMessageCount count={unreadMessageCount} />
                </View>
              )}
              <MessageIcon
                width={Platform.isPad ? 44 : 24}
                height={Platform.isPad ? 44 : 24}
              />
            </TouchableOpacity>
          </View>
          <FlatList
            ref={scrollViewRef}
            data={feedListData}
            renderItem={renderFeedHighlight}
            keyExtractor={getItemKey}
            onViewableItemsChanged={onViewableItemsChanged}
            viewabilityConfig={viewabilityConfig}
            removeClippedSubviews={true}
            initialNumToRender={2}
            windowSize={2}
            maxToRenderPerBatch={2}
            updateCellsBatchingPeriod={100}
            bounces={false}
            decelerationRate="normal"
            overScrollMode="never"
            contentInsetAdjustmentBehavior="automatic"
            onEndReached={fetchHighlights}
            onEndReachedThreshold={0.5}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{
              paddingTop:
                Platform.OS === "ios"
                  ? (StatusBar.currentHeight || 0) + 80
                  : 80,
              paddingBottom: HeightSize(10),
              flexGrow: 1,
            }}
            onRefresh={handleRefresh}
            refreshing={isRefreshing}
            ItemSeparatorComponent={FeedHighlightSeparator}
            ListEmptyComponent={
              isFetchingHighlights ? (
                <View
                  style={{
                    marginVertical: HeightSize(2),
                  }}
                >
                  {[1, 2, 3].map((item, index) => (
                    <Animatable.View
                      key={`skeleton-${index}`}
                      animation="pulse"
                      easing="ease-out"
                      iterationCount="infinite"
                      style={[styles.contentContainer, styles.loadingContainer]}
                    />
                  ))}
                </View>
              ) : (
                <View
                  style={{
                    justifyContent: "center",
                    alignItems: "center",
                    marginTop: HeightSize(10),
                  }}
                >
                  <Text
                    style={{
                      fontSize: Size(5),
                      color: "#666",
                      textAlign: "center",
                      fontFamily: "PoppinsBold",
                    }}
                  >
                    No highlights available
                  </Text>
                </View>
              )
            }
          />
          {loggedUserId && (
            <View style={styles.container}>
              <TouchableOpacity
                onPress={() => {
                  if (!loggedUserId) {
                    setShowLoginPopup(true);
                  } else {
                    navigation.navigate("UploadHighlights");
                  }
                }}
              >
                <PlusGreenIcon />
              </TouchableOpacity>
            </View>
          )}

          {/* Login Popup */}
          <Modal
            visible={showLoginPopup}
            transparent={true}
            animationType="slide"
            onRequestClose={() => setShowLoginPopup(false)}
          >
            <View
              style={{
                flex: 1,
                justifyContent: "center",
                alignItems: "center",
                backgroundColor: "rgba(0,0,0,0.5)",
              }}
            >
              <View
                style={{
                  width: "80%",
                  padding: Size(5),
                  backgroundColor: Colors.white,
                  borderRadius: Size(7),
                  alignItems: "center",
                }}
              >
                <Text
                  style={{
                    fontSize: Size(4.7),
                    marginBottom: HeightSize(1),
                    textAlign: "center",
                    fontFamily: "Regular",
                  }}
                >
                  Log in now.
                </Text>
                <Text
                  style={{
                    fontSize: Size(4),
                    textAlign: "center",
                    fontFamily: "Regular",
                    opacity: 0.5,
                  }}
                >
                  You must have an account to use this feature.
                </Text>
                <Button
                  title={"LOG IN"}
                  containerStyle={{ marginTop: HeightSize(2) }}
                  onPress={() => navigation.navigate("Login")}
                  backgroundColor={Colors.black}
                  textStyleInsideButton={{ fontSize: Size(3.7) }}
                  height={HeightSize(7)}
                />
                <Button
                  title={"CANCEL"}
                  containerStyle={{
                    marginTop: HeightSize(2),
                    borderWidth: 1,
                    borderColor: "#A5A5A5",
                  }}
                  onPress={() => {
                    setShowLoginPopup(false);
                  }}
                  backgroundColor={Colors.white}
                  textStyleInsideButton={{
                    color: "#A5A5A5",
                    fontSize: Size(3.7),
                  }}
                  height={HeightSize(6)}
                />
              </View>
            </View>
          </Modal>
        </View>
      </SafeAreaView>
    </View>
  );
};

export default Feed;
