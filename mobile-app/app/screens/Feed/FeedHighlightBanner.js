import { Text, TouchableOpacity, View, Modal } from "react-native";
import styles from "./FeedStyle";
import { Colors } from "../../res/Colors";
import { MenuIcon, ReportIcon } from "../../res/Svg";
import { HeightSize, Size } from "../../res/Size";
import { Images } from "../../res/Images";
import BlockReportModal from "../../components/BlockReportModal";
import Button from "../../components/Button/Button";
import FastImage from "react-native-fast-image";
import UserAvatar from "../../components/UserAvatar";

export const FeedHighlightBanner = ({
  highlight,
  loggedUserId,
  setShowLoginPopup,
  handleOpenProfile,
  userInfo,
  openProfileBlockReportModal,
  blockModalVisible,
  selectedPostIdx,
  setBlockModalVisible,
  handleOpenConfirmation,
  modalPosition,
  confirmationVisible,
  setConfirmationVisible,
  handleConfirmAction,
  actionType,
  isReported,
}) => {
  return (
    <View style={styles.highlightBannerContainer}>
      <TouchableOpacity
        style={styles.nameImageContainer}
        onPress={() =>
          !loggedUserId
            ? setShowLoginPopup(true)
            : handleOpenProfile(highlight.user.id)
        }
      >
        <UserAvatar user={highlight.user} />
        <View>
          <Text style={styles.nameTxt}>
            {`${highlight.user.firstName}${
              highlight.user.lastName ? ` ${highlight.user.lastName}` : ""
            }`}
          </Text>
          <Text style={styles.teamNameTxt}>
            {(highlight.user.teamName !== "N/A" && highlight.user.teamName) ||
              highlight.user.clubName ||
              "Unknown club"}
          </Text>
        </View>
      </TouchableOpacity>
      {userInfo.id !== highlight.userId && (
        <View style={styles.highlightBannerActions}>
          {isReported && <ReportIcon style={styles.reportIcon} />}
          <TouchableOpacity
            onPress={(event) =>
              openProfileBlockReportModal(highlight, event.nativeEvent)
            }
            disabled={!loggedUserId ? true : false}
          >
            <MenuIcon />
          </TouchableOpacity>
          {blockModalVisible && selectedPostIdx === highlight.id && (
            <BlockReportModal
              visible={blockModalVisible}
              onClose={() => setBlockModalVisible(false)}
              onBlock={() => handleOpenConfirmation("block")}
              onReport={() => handleOpenConfirmation("report", "POST")}
              top={modalPosition.top}
              right={modalPosition.right}
              text="Post"
            />
          )}
          <Modal
            visible={confirmationVisible}
            transparent={true}
            animationType="fade"
            onRequestClose={() => setConfirmationVisible(false)}
          >
            <View style={styles.confirmationModalContainer}>
              <View style={styles.confirmationModalContent}>
                <Text style={styles.confirmationText}>
                  Are you sure you want to{" "}
                  <Text style={{ color: Colors.red }}>
                    {actionType === "block" ? "block" : "report"}
                  </Text>{" "}
                  {actionType === "block" ? "this user" : "this post?"}
                </Text>
                <View style={styles.confirmationButtonContainer}>
                  <Button
                    title="Yes"
                    onPress={() => handleConfirmAction()}
                    containerStyle={{ width: "45%" }}
                    backgroundColor={Colors.black}
                    textStyleInsideButton={{
                      fontSize: Size(4),
                      fontFamily: "Regular",
                    }}
                    height={HeightSize(5)}
                  />
                  <Button
                    title="No"
                    onPress={() => setConfirmationVisible(false)}
                    containerStyle={{
                      borderWidth: 1,
                      borderColor: "#A5A5A5",
                      width: "45%",
                    }}
                    backgroundColor={Colors.white}
                    textStyleInsideButton={{
                      color: "#A5A5A5",
                      fontSize: Size(4),
                      fontFamily: "Regular",
                    }}
                    height={HeightSize(5)}
                  />
                </View>
              </View>
            </View>
          </Modal>
        </View>
      )}
    </View>
  );
};
