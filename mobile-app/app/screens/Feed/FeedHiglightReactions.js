import { Image, Text, TouchableOpacity, View } from "react-native";
import styles, { HIGHLIGHT_REACTIONS_PLACEHOLDER_HEIGHT } from "./FeedStyle";
import { Size } from "../../res/Size";
import { COMPETITION_BACKGROUND_COLOR } from "../../components/Competitions/Competitions";

export const getLastEmojis = (arr) => {
  const array = Array.isArray(arr) ? arr : [];

  const emojis = array
    .filter((item) => item?.others?.emoji)
    .map((item) => item.others.emoji);

  return emojis.slice(-5);
};

export const FeedHighlightReactions = ({ highlight, onReactionsPress }) => {
  if (!highlight.reactedByUsers?.length) {
    return (
      <View
        style={{
          height: HIGHLIGHT_REACTIONS_PLACEHOLDER_HEIGHT,
          width: "100%",
        }}
      />
    );
  }

  const isAnnouncement = !!highlight.announcementType;

  return (
    <View style={styles.emojiContainer}>
      <TouchableOpacity
        style={styles.emojiDisplay}
        onPress={onReactionsPress}
        disabled={!onReactionsPress}
      >
        <View style={styles.emojiStack}>
          {getLastEmojis(highlight.reactedByUsers).map((item, index) => (
            <View
              key={`emoji-${index}`}
              style={[
                styles.emoji,
                {
                  marginLeft: index === 0 ? 0 : -Size(2),
                  ...(index > 0 && {
                    borderColor: isAnnouncement
                      ? COMPETITION_BACKGROUND_COLOR
                      : "white",
                    borderWidth: 1,
                  }),
                },
              ]}
            >
              <Image source={{ uri: item }} style={styles.emojiImage} />
            </View>
          ))}
        </View>
        <Text style={styles.emojiText}>
          {highlight.reactedByUsers?.length || ""}
        </Text>
      </TouchableOpacity>
    </View>
  );
};
