import { TouchableOpacity, View, Platform } from "react-native";
import styles from "./FeedStyle";
import { Colors } from "../../res/Colors";
import { HeightSize } from "../../res/Size";
import ResultHighlights from "../../components/ResultHighlights/ResultHighlights";
import ReactPlayerVideo, {
  videoContext,
} from "../../components/ReactPlayerVideo";
import { shouldShowWaitIcon } from "../../constants/Constant";
import LoadingFeedVideo from "../../components/LoadingFeedVideo";
import FastImage from "react-native-fast-image";
import { memo, useMemo } from "react";

const FeedHighlightMedia = ({ highlight, isActive }) => {
  const isLoading = useMemo(
    () => highlight.streamUrl && shouldShowWaitIcon(highlight),
    [highlight]
  );

  return (
    <TouchableOpacity activeOpacity={9}>
      {highlight.type === "PHOTO" && (
        <View style={styles.photoStyle}>
          <FastImage
            source={{ uri: highlight.url }}
            style={{
              backgroundColor: Colors.black,
              width: "100%",
              height: Platform.isPad
                ? HeightSize(82)
                : Platform.OS === "ios"
                ? HeightSize(55)
                : HeightSize(60),
            }}
          />
        </View>
      )}
      {highlight.type === "VIDEO" && (
        <View style={styles.VideoContainer}>
          {isLoading ? (
            <LoadingFeedVideo isError={highlight.videoStatus === "FAILED"} />
          ) : (
            <ReactPlayerVideo
              highlight={highlight}
              isActive={isActive}
              context={videoContext.Feed}
            />
          )}
        </View>
      )}
      {highlight.type === "RESULT" && highlight.allowMatchOnFeeds && (
        <View>
          <ResultHighlights item={highlight} />
        </View>
      )}
    </TouchableOpacity>
  );
};

const arePropsEqual = (prevProps, nextProps) => {
  if (
    prevProps.isActive !== nextProps.isActive ||
    prevProps.highlight.id !== nextProps.highlight.id
  ) {
    return false;
  }

  return true;
};

const memoizedFeedHighlightMedia = memo(FeedHighlightMedia, arePropsEqual);

export default memoizedFeedHighlightMedia;
