import { Image, TouchableOpacity, View, Platform } from "react-native";
import styles from "./FeedStyle";
import { CommentIcon, EmojiIcon, ShareIcon } from "../../res/Svg";
import { HeightSize, Size } from "../../res/Size";
import { useCallback } from "react";
import { LiveLikeReactionPacks } from "../../constants/reactions";

const getShareType = (highlight) => {
  if (!highlight.announcementType) {
    return "highlight";
  }

  if (highlight.announcementType === "PINNED_POST") {
    return "post";
  }

  return "announcement";
};

export const FeedHighlightActions = ({
  highlight,
  shouldShowEmojiPanel,
  setShouldShowEmojiPanel,
  navigation,
  addUserReaction,
  loggedUserId,
  setShowLoginPopup,
}) => {
  const preventIfNotLoggedIn = (action) => {
    if (loggedUserId) {
      return action;
    } else {
      return () => setShowLoginPopup(true);
    }
  };

  const navigateToDetailPage = useCallback(() => {
    if (!highlight.announcementType) {
      return navigation.navigate("FeedDetail", {
        highlightId: highlight.id,
        userId: highlight.userId,
      });
    }

    return navigation.navigate("AnnouncementPage", {
      announcementId: highlight.id,
    });
  }, [highlight]);

  return (
    <View style={styles.iconContainer}>
      <View
        style={{
          marginTop: Platform.isPad && HeightSize(4),
          flexDirection: "row",
          gap: Size(1.5),
        }}
      >
        <TouchableOpacity
          onPress={preventIfNotLoggedIn(() =>
            setShouldShowEmojiPanel({
              visible:
                shouldShowEmojiPanel.visible &&
                highlight.id === shouldShowEmojiPanel.highlightId
                  ? false
                  : true,
              highlightId: highlight.id,
            })
          )}
          style={styles.button}
        >
          <EmojiIcon
            width={Platform.isPad ? 42 : Size(8)}
            height={Platform.isPad ? 42 : Size(8)}
          />
        </TouchableOpacity>
        <TouchableOpacity
          onPress={preventIfNotLoggedIn(navigateToDetailPage)}
          style={styles.button}
        >
          <CommentIcon
            width={Platform.isPad ? 42 : Size(8)}
            height={Platform.isPad ? 42 : Size(8)}
          />
        </TouchableOpacity>
        <TouchableOpacity
          onPress={preventIfNotLoggedIn(() =>
            navigation.navigate("ShareFeed", {
              highlight: highlight,
              type: getShareType(highlight),
              profile: null,
            })
          )}
          style={styles.button}
        >
          <ShareIcon
            width={Platform.isPad ? 42 : Size(8)}
            height={Platform.isPad ? 42 : Size(8)}
          />
        </TouchableOpacity>
      </View>

      {shouldShowEmojiPanel.visible &&
        shouldShowEmojiPanel.highlightId === highlight.id && (
          <View style={styles.emojiPanel}>
            {LiveLikeReactionPacks?.emojis.map((item, idx) => (
              <TouchableOpacity
                key={`emoji-${idx}`}
                onPress={() => {
                  addUserReaction(highlight, item);
                }}
                style={styles.emojiButton}
              >
                <Image
                  source={{ uri: item.file }}
                  style={{
                    height: Size(5.5),
                    width: Size(5.5),
                  }}
                  resizeMode="contain"
                />
              </TouchableOpacity>
            ))}
          </View>
        )}
    </View>
  );
};
