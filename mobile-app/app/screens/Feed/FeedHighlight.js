import { View } from "react-native";
import styles from "./FeedStyle";
import { FeedHighlightMeta } from "./FeedHighlightMeta";
import { FeedHighlightReactions } from "./FeedHiglightReactions";
import { FeedHighlightActions } from "./FeedHighlightActions";
import { FeedHighlightBanner } from "./FeedHighlightBanner";
import FeedHighlightMedia from "./FeedHighlightMedia";

export const FeedHighlight = ({
  highlight,
  isActive,
  loggedUserId,
  setShowLoginPopup,
  handleOpenProfile,
  userInfo,
  openProfileBlockReportModal,
  blockModalVisible,
  selectedPostIdx,
  setBlockModalVisible,
  handleOpenConfirmation,
  modalPosition,
  confirmationVisible,
  setConfirmationVisible,
  handleConfirmAction,
  shouldShowEmojiPanel,
  setShouldShowEmojiPanel,
  navigation,
  actionType,
  addUserReaction,
}) => {
  const isReported = userInfo?.reports?.some(
    (report) => report.contendId === highlight.id
  );

  return (
    <View>
      {highlight.type !== "RESULT" && (
        <FeedHighlightBanner
          highlight={highlight}
          loggedUserId={loggedUserId}
          setShowLoginPopup={setShowLoginPopup}
          handleOpenProfile={handleOpenProfile}
          userInfo={userInfo}
          openProfileBlockReportModal={openProfileBlockReportModal}
          blockModalVisible={blockModalVisible}
          selectedPostIdx={selectedPostIdx}
          setBlockModalVisible={setBlockModalVisible}
          handleOpenConfirmation={handleOpenConfirmation}
          modalPosition={modalPosition}
          confirmationVisible={confirmationVisible}
          setConfirmationVisible={setConfirmationVisible}
          handleConfirmAction={handleConfirmAction}
          actionType={actionType}
          isReported={isReported}
        />
      )}
      <FeedHighlightMedia highlight={highlight} isActive={isActive} />
      <View style={styles.highlightContentContainer}>
        {Boolean(loggedUserId) && (
          <FeedHighlightReactions
            highlight={highlight}
            onReactionsPress={() =>
              navigation.navigate("FeedDetail", {
                highlightId: highlight.id,
                userId: highlight.userId,
                type: "reactions",
              })
            }
          />
        )}
        <FeedHighlightActions
          highlight={highlight}
          shouldShowEmojiPanel={shouldShowEmojiPanel}
          setShouldShowEmojiPanel={setShouldShowEmojiPanel}
          navigation={navigation}
          addUserReaction={addUserReaction}
          loggedUserId={loggedUserId}
          setShowLoginPopup={setShowLoginPopup}
        />
        <FeedHighlightMeta
          highlight={highlight}
          loggedUserId={loggedUserId}
          setShowLoginPopup={setShowLoginPopup}
          handleOpenProfile={handleOpenProfile}
          navigation={navigation}
        />
      </View>
    </View>
  );
};

export default FeedHighlight;
