import { Dimensions, StyleSheet } from "react-native";
import { HeightSize, Size } from "../../res/Size";
import { Colors } from "../../res/Colors";

const { height: screenHeight } = Dimensions.get("window");

export const HIGHLIGHT_BANNER_HEIGHT = HeightSize(8);
export const HIGHLIGHT_MEDIA_HEIGHT = Size(125);
export const HIGHLIGHT_REACTIONS_HEIGHT = HeightSize(5.5);
export const HIGHLIGHT_REACTIONS_PLACEHOLDER_HEIGHT = HeightSize(1.5);
export const HIGHLIGHT_ACTIONS_HEIGHT = HeightSize(3.25);
export const HIGHLIGHT_META_HEIGHT = HeightSize(10);
export const HIGHLIGHT_SEPARATOR_HEIGHT = HeightSize(1.5);

const styles = StyleSheet.create({
  headerContainer: {
    alignSelf: "center",
    width: "100%",
    justifyContent: "center",
    height: 70,
    position: "relative",
  },
  logoContainer: {},
  messageContainer: {
    backgroundColor: Colors.green,
    width: Size(9),
    height: Size(9),
    borderRadius: Size(9),
    alignItems: "center",
    justifyContent: "center",
    position: "absolute",
    top: 0,
    right: Size(4),
    transform: [{ translateY: Size(5.5) }],
    zIndex: 1,
  },
  VideoContainer: {
    overflow: "hidden",
    width: "100%",
    height: HIGHLIGHT_MEDIA_HEIGHT,
  },
  video: {
    width: "100%",
    borderRadius: 0,
    overflow: "hidden",
    height: HIGHLIGHT_MEDIA_HEIGHT,
  },
  photoStyle: {
    width: "100%",
    borderRadius: 0,
    height: HIGHLIGHT_MEDIA_HEIGHT,
    overflow: "hidden",
  },
  buttons: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
  },
  playButton: {
    position: "absolute",
    top: "50%",
    left: "50%",
    marginLeft: -20,
    marginTop: -20,
    backgroundColor: "lightgrey",
    width: Size(13),
    height: Size(13),
    borderRadius: Size(13),
    justifyContent: "center",
    alignItems: "center",
  },
  photoContainer: {
    width: Size(12),
    height: Size(12),
    borderRadius: Size(12),
    marginRight: Size(3),
  },
  highlightBannerContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    height: HIGHLIGHT_BANNER_HEIGHT,
    paddingHorizontal: Size(5),
  },
  nameImageContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingRight: Size(5),
    gap: Size(3),
  },
  highlightBannerActions: {
    flexDirection: "row",
    alignItems: "center",
  },
  highlightContentContainer: {
    paddingHorizontal: Size(4),
  },
  reportIcon: {
    marginRight: Size(3),
  },
  teamNameTxt: {
    fontFamily: "PoppinsMedium",
    fontSize: Size(3.5),
    color: Colors.grey,
  },
  iconContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    height: HIGHLIGHT_ACTIONS_HEIGHT,
  },
  reactionContainer: {
    backgroundColor: "rgba(241, 241, 241, 1)",
    // flexDirection: "row",
    justifyContent: "space-between",
    //borderRadius: Size(3),
    paddingVertical: HeightSize(1),
    //width: "100%",
    //flex: 1,
  },
  metaContainer: {
    paddingTop: Size(2.5),
    paddingBottom: Size(2.5),
  },
  icon: {
    paddingHorizontal: Size(2),
  },
  reactionButton: {
    flexDirection: "row",
  },
  reactionUserPhoto: {
    width: Size(5),
    height: Size(5),
  },
  // emojiContainer: {
  //   width: Size(7),
  //   height: Size(7),
  //   borderRadius: Size(7),
  //   backgroundColor: "rgba(241, 241, 241, 1)",
  //   justifyContent: "center",
  //   alignItems: "center",
  //   position: 'relative',
  //   marginRight: -10,
  // },
  userReaction: {
    flexDirection: "row",
    alignItems: "center",
  },
  reactionUserPhoto: {
    width: Size(5),
    height: Size(5),
    borderRadius: Size(5),
  },
  reactionCount: {
    //marginLeft: Size(5),
    color: Colors.dark_grey,
    fontFamily: "PoppinsMedium",
    fontSize: Size(3.6),
  },
  commentContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  modalContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: Colors.white,
  },
  modalContent: {
    backgroundColor: Colors.white,
    paddingVertical: HeightSize(1),
    borderRadius: Size(7),
    elevation: 5,
    width: "90%",
    marginTop: HeightSize(40),
    paddingHorizontal: Size(3),
    marginBottom: HeightSize(5),
  },
  commentBox: {
    flexDirection: "row",
    alignItems: "center",
    marginVertical: HeightSize(3),
    flexWrap: "wrap",
  },
  nameTxt: {
    fontFamily: "PoppinsBold",
    fontSize: Size(3.7),
  },
  container: {
    position: "absolute",
    alignItems: "center",
    width: Size(10),
    height: Size(10),
    bottom: HeightSize(6),
    alignSelf: "center",
  },
  addButton: {
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: Colors.green,
    width: Size(15),
    height: Size(15),
    borderRadius: Size(7.5),
  },
  buttonText: {
    color: Colors.black,
    fontSize: Size(10),
    fontWeight: "bold",
  },
  reactionWrapper: {
    position: "absolute",
    top: -5, // Adjust as needed
    left: 25,
    zIndex: 1,
  },
  mainCommentTxt: {
    fontFamily: "Regular",
    fontSize: Size(3.5),
    color: Colors.black,
  },
  highlightSeeMore: {
    color: "#00000066",
    textDecorationLine: "underline",
  },
  button: {
    backgroundColor: "transparent",
    margin: 0,
  },
  emojiContainer: {
    width: Size(92),
    flexDirection: "row",
    alignItems: "center",
    height: HIGHLIGHT_REACTIONS_HEIGHT,
  },
  emojiDisplay: {
    flexDirection: "row",
    alignItems: "center",
  },
  emojiStack: {
    flexDirection: "row",
    alignItems: "center",
  },
  emoji: {
    borderRadius: Size(7),
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#D9D9D9",
    height: Size(7),
    width: Size(7),
  },
  emojiImage: {
    height: Size(4),
    width: Size(4),
  },
  emojiText: {
    fontSize: Size(3.5),
    color: "#808080",
    marginLeft: Size(2),
    textAlign: "center",
    fontFamily: "PoppinsMedium",
  },
  commentCount: {
    flexDirection: "row",
    alignItems: "center",
    // marginLeft: Size(50)
  },
  emojiPanel: {
    position: "absolute",
    left: Size(12),
    backgroundColor: Colors.lightest_grey,
    borderRadius: Size(3),
    flexDirection: "row",
    paddingVertical: Size(2),
    paddingHorizontal: Size(4),
    gap: Size(4),
  },
  emojiButton: {
    backgroundColor: "transparent",
    border: "none",
  },
  shareContainer: {
    flexDirection: "row",
    alignItems: "center",
    position: "absolute",
    marginTop: 5,
    height: 48,
    width: "90%",
    borderRadius: 10,
    alignSelf: "center",
    marginRight: 10,
  },
  shareButton: {
    width: 30,
    height: 30,
    borderRadius: 10,
    overflow: "hidden",
    marginRight: 5,
  },
  contentContainer: {
    width: "100%",
    borderRadius: 32,
  },
  loadingContainer: {
    height: screenHeight * 0.6,
    backgroundColor: Colors.lightest_grey,
    marginTop: HeightSize(1),
    marginBottom: HeightSize(2),
  },
  fetchingContainer: {
    height: screenHeight * 0.4,
    backgroundColor: Colors.lightest_grey,
    marginTop: HeightSize(2),
  },
  shimmer: {
    height: 60,
    width: "90%",
    backgroundColor: "#e0e0e0",
    borderRadius: 8,
  },
  confirmationModalContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.1)",
  },
  confirmationModalContent: {
    backgroundColor: "white",
    paddingHorizontal: Size(4.5),
    borderRadius: Size(9),
    marginHorizontal: Size(5),
    alignItems: "center",
    paddingVertical: HeightSize(2),
    width: "80%",
  },
  confirmationText: {
    marginBottom: HeightSize(2),
    fontSize: Size(5),
    textAlign: "center",
    fontFamily: "PoppinsMedium",
  },
  confirmationButtonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
    paddingHorizontal: Size(5),
  },
  unreadMessageContainer: {
    position: "absolute",
    backgroundColor: "#FF0000",
    borderRadius: Size(5),
    width: Size(5),
    height: Size(5),
    top: -Size(2),
    right: -Size(2),
    alignItems: "center",
    justifyContent: "center",
  },
});

export default styles;
