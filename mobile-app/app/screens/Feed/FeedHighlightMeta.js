import { Text, TouchableOpacity } from "react-native";
import styles from "./FeedStyle";

export const FeedHighlightMeta = ({
  highlight,
  loggedUserId,
  setShowLoginPopup,
  handleOpenProfile,
  navigation,
}) => {
  return (
    <TouchableOpacity style={styles.metaContainer} activeOpacity={0.6}>
      <Text style={styles.mainCommentTxt}>
        <Text
          style={{ fontFamily: "PoppinsBold" }}
          onPress={() =>
            !loggedUserId
              ? setShowLoginPopup(true)
              : handleOpenProfile(highlight.user.id)
          }
        >
          {`${highlight.user?.firstName} ${highlight.user?.lastName}`}{" "}
        </Text>
        {highlight.comment.slice(0, 150)}
        {"  "}
        <Text
          style={styles.highlightSeeMore}
          onPress={() => {
            !loggedUserId
              ? setShowLoginPopup(true)
              : navigation.navigate("FeedDetail", {
                  highlightId: highlight.id,
                  userId: highlight.userId,
                });
          }}
        >
          See more
        </Text>
      </Text>
      <Text
        style={{
          fontSize: 12,
          color: "#00000066",
          marginTop: 2,
          fontFamily: "Regular",
        }}
        onPress={() =>
          navigation.navigate("FeedDetail", {
            highlightId: highlight.id,
            userId: highlight.userId,
          })
        }
      >
        View all comments
      </Text>
    </TouchableOpacity>
  );
};
