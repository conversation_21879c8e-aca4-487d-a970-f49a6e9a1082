import { StyleSheet } from "react-native";
import { HeightSize, Size } from "../../res/Size";
import { Colors } from "../../res/Colors";

const styles = StyleSheet.create({
  backBtn: {
    marginBottom: HeightSize(3)
  },
  menuList: {
    marginTop: HeightSize(4),
    //paddingHorizontal: Size(2),
  },
  menuItem: {
    paddingVertical: Size(6),
   // borderWidth: 1,
    flexDirection: 'row',
    justifyContent: 'space-between', 
    alignItems: 'center',
    //borderBottomColor: Colors.lightGrey,
  },
  menuItemText: {
    fontSize: Size(4),
    fontFamily: "Bold",
    color: Colors.black,
  },
  arrowRight: {
    paddingTop: HeightSize(0.7)
  },
  containerStyle: {
    marginTop: HeightSize(50), 
    borderWidth: 2, 
    borderColor: Colors.red, 
    borderRadius: Size(3), 
    width: "60%", 
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '72%',
    backgroundColor: Colors.white,
    paddingHorizontal: Size(4),
    borderRadius: Size(7),
    alignItems: 'center',
    paddingVertical: HeightSize(4)
  },
  modalTitle: {
    fontSize: Size(5),
    fontFamily: "Regular",
    color: Colors.black,
    marginBottom: HeightSize(2),
    textAlign: 'center',
  },
  modalDescription: {
    fontSize: Size(3.7),
    fontFamily: "Regular",
    color: Colors.black,
    textAlign: 'center',
    marginBottom: HeightSize(2),
    lineHeight: HeightSize(3),
    opacity: 0.5
  },
  modalButtonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
   // flex: 1
   // width: '90%',
   // marginHorizontal: Size(2)
  },
  redText: {
    color: Colors.red, 
  },
  underlinedText: {
    textDecorationLine: "underline", 
  },
  
});

export default styles;