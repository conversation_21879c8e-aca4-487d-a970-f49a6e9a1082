import React, { useState } from "react";
import {
  SafeAreaView,
  StatusBar,
  Text,
  TouchableOpacity,
  View,
  FlatList,
  Modal,
  Platform,
} from "react-native";
import styles from "./AccountStyles";
import { ArrowRightIcon, BackIcon, DeleteImg } from "../../res/Svg";
import { commonStyles } from "../../res/CommonStyles";
import { Colors } from "../../res/Colors";
import { HeightSize, Size } from "../../res/Size";
import Button from "../../components/Button/Button";
import AuthApi from "../../services/AuthApi";
import { useSelector, shallowEqual } from "react-redux";
import { notifyError, notifySuccess } from "../../constants/misc";
import { dispatch } from "../../redux/store";
import { selectUserInfo } from "../../redux/selectors/authSelectors";

const Account = ({ navigation }) => {
  const [isModalVisible, setModalVisible] = useState(false);
  const userInfo = useSelector(selectUserInfo, shallowEqual);

  const menuOptions = [
    { key: "Change Password", label: "CHANGE PASSWORD" },
    { key: "Blocked Users", label: "BLOCKED USERS" },
  ];

  const handleItemPress = async (key) => {
    switch (key) {
      case "Change Password":
        navigation.navigate("Security");
        break;
      case "Blocked Users":
        navigation.navigate("BlockedUsers");
        break;
      default:
        break;
    }
  };

  const renderItem = ({ item }) => (
    <TouchableOpacity
      style={styles.menuItem}
      onPress={() => handleItemPress(item.key)}
    >
      <Text style={styles.menuItemText}>{item.label}</Text>
      <View style={styles.arrowRight}>
        <ArrowRightIcon />
      </View>
    </TouchableOpacity>
  );

  const handleDeleteAccount = async () => {
    try {
      const data = {
        email: userInfo?.email,
        userId: userInfo?.id,
        requestFrom: userInfo?.userType,
      };

      const response = await AuthApi.deleteAccount(data);

      if (
        response &&
        response?.data?.message === "User has been deleted successfully!"
      ) {
        // Clear the email and password in the state
        dispatch.auth.setState({
          authUser: {
            tokens: null,
            userInfo: {
              email: "",
              password: "",
              guest: false,
            },
            authenticated: false,
          },
        });

        // Navigate to the Login screen
        navigation.reset({
          index: 0,
          routes: [{ name: "AuthNavigation", params: { screen: "Login" } }],
        });
        notifySuccess(response?.data?.message);
      }

      setModalVisible(false);
    } catch (err) {
      console.error("error---", err);
      notifyError(err);
    }
  };

  return (
    <SafeAreaView style={commonStyles.container}>
      <StatusBar
        barStyle="dark-content"
        hidden={false}
        backgroundColor={Colors.white}
        translucent={false}
      />
      <View style={[commonStyles.subContainer, { marginTop: HeightSize(4) }]}>
        <TouchableOpacity
          onPress={() => {
            navigation.goBack();
          }}
          activeOpacity={9}
          style={styles.backBtn}
        >
          <BackIcon
            width={Platform.isPad ? 40 : 24}
            height={Platform.isPad ? 40 : 24}
          />
        </TouchableOpacity>
        <Text style={commonStyles.boldText}>ACCOUNT SETTINGS</Text>
        <FlatList
          data={menuOptions}
          renderItem={renderItem}
          keyExtractor={(item) => item.key}
          contentContainerStyle={styles.menuList}
        />
        <Button
          title={"Delete my account"}
          containerStyle={styles.containerStyle}
          onPress={() => setModalVisible(true)}
          backgroundColor={Colors.white}
          textStyleInsideButton={{
            color: Colors.red,
            fontSize: Size(3.8),
            fontFamily: "Regular",
            paddingLeft: Size(2),
            paddingTop: HeightSize(0.5),
          }}
          height={HeightSize(6.5)}
          leftIcon={<DeleteImg />}
        />
      </View>

      {/* Delete Account Modal */}
      <Modal
        transparent={true}
        visible={isModalVisible}
        animationType="slide"
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>
              Are you sure you want to{" "}
              <Text style={styles.redText}>delete</Text> your account?
            </Text>
            <Text style={styles.modalDescription}>
              Your account will be deleted{" "}
              <Text style={styles.underlinedText}>permanently</Text> and you
              will be unable to retrieve it again in the future.
            </Text>
            <View style={styles.modalButtonRow}>
              <Button
                title="Delete"
                onPress={handleDeleteAccount}
                backgroundColor={Colors.black}
                textStyleInsideButton={{
                  color: Colors.white,
                  fontFamily: "Regular",
                }}
                width={Size(27)}
                containerStyle={{ borderRadius: Size(6), marginRight: Size(2) }}
                height={HeightSize(5)}
              />
              <Button
                title="Cancel"
                onPress={() => setModalVisible(false)}
                width={Size(27)}
                textStyleInsideButton={{
                  color: "#A5A5A5",
                  fontFamily: "Regular",
                }}
                containerStyle={{
                  borderRadius: Size(6),
                  borderWidth: 1,
                  borderColor: "#A5A5A5",
                }}
                height={HeightSize(5)}
              />
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

export default Account;
