import React, { useCallback, useEffect, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  BackHandler,
  StatusBar,
  Platform,
} from "react-native";
import { Colors } from "../../res/Colors";
import { BackIcon, NewMessageIcon } from "../../res/Svg";
import { commonStyles } from "../../res/CommonStyles";
import { HeightSize, Size } from "../../res/Size";
import MessageListCard from "../../components/MessageCard/MessageListCard";
import { useSelector } from "react-redux";
import { dispatch } from "../../redux/store";
import { CommonActions, useFocusEffect } from "@react-navigation/native";
import { useAppStateEffect } from "../../hooks/useAppStateEffect";
import { MESSAGE_REFRESH_INTERVAL } from "../../constants/chat";
import { selectUserInfo } from "../../redux/selectors/authSelectors";
import { shallowEqual } from "redux";

const MessageList = ({ navigation, route }) => {
  const userInfo = useSelector(selectUserInfo, shallowEqual);
  const chats = useSelector((state) => state.chat.chats);
  const chatsIntervalRef = useRef(null);

  const loadingUser = useSelector(
    ({ loading }) => loading.effects.user.fetchUserDetails
  );
  const loadingChats = useSelector(
    ({ loading }) => loading.effects.chat.getChats
  );
  const isLoading = (loadingUser || loadingChats) && !chats?.length;

  const fetchChats = async () => {
    await dispatch.chat.getChats();
  };

  const startChatsInterval = useCallback(() => {
    if (userInfo?.id && !chatsIntervalRef.current) {
      fetchChats();

      chatsIntervalRef.current = setInterval(() => {
        fetchChats();
      }, MESSAGE_REFRESH_INTERVAL);
    }
  }, [userInfo?.id]);
  const stopChatsInterval = useCallback(() => {
    if (chatsIntervalRef.current) {
      clearInterval(chatsIntervalRef.current);
      chatsIntervalRef.current = null;
    }
  }, []);
  useAppStateEffect({
    onForeground: startChatsInterval,
    onBackground: stopChatsInterval,
  });
  useFocusEffect(
    useCallback(() => {
      if (!userInfo?.id) return;

      startChatsInterval();

      return () => stopChatsInterval();
    }, [userInfo?.id])
  );

  const userId = route?.params?.userId || "";
  // BackHandler to navigate
  useEffect(() => {
    const backAction = () => {
      if (route?.params?.fromMyProfileContent) {
        navigation.dispatch(
          CommonActions.reset({
            index: 0,
            routes: [
              {
                name: "MyProfileScreenStack",
                params: {
                  screen: "MyProfile",
                  params: { userId: userId ? userId : userInfo.id },
                },
              },
            ],
          })
        );
      } else {
        navigation.dispatch(
          CommonActions.reset({
            index: 0,
            routes: [{ name: "Feed" }],
          })
        );
      }
      return true;
    };

    const backHandler = BackHandler.addEventListener(
      "hardwareBackPress",
      backAction
    );

    return () => backHandler.remove();
  }, [route?.params?.fromMyProfileContent]);

  return (
    <View style={styles.container}>
      <StatusBar
        barStyle="dark-content"
        hidden={false}
        backgroundColor={Colors.white}
        translucent={false}
      />
      <View style={styles.headerContainer}>
        <TouchableOpacity
          onPress={() => {
            if (route?.params?.fromMyProfileContent) {
              navigation.dispatch(
                CommonActions.reset({
                  index: 0,
                  routes: [
                    {
                      name: "MyProfileScreenStack",
                      params: {
                        screen: "MyProfile",
                        params: { userId: userId ? userId : userInfo.id },
                      },
                    },
                  ],
                })
              );
            } else {
              navigation.dispatch(
                CommonActions.reset({
                  index: 0,
                  routes: [{ name: "Feed" }],
                })
              );
            }
          }}
        >
          <BackIcon />
        </TouchableOpacity>
        <Text style={commonStyles.boldText}>MESSAGES</Text>
        <TouchableOpacity
          onPress={() => {
            navigation.dispatch(
              CommonActions.reset({
                index: 0,
                routes: [
                  {
                    name: "NewMessage",
                    params: {
                      fromMyProfileContent:
                        route?.params?.fromMyProfileContent || false,
                    },
                  },
                ],
              })
            );
          }}
        >
          <NewMessageIcon />
        </TouchableOpacity>
      </View>

      <ScrollView>
        <View style={{ marginTop: 10 }}>
          {chats?.length > 0 ? (
            chats.map((chat) => (
              <View key={chat.chatroomId}>
                <MessageListCard
                  chat={chat}
                  fromMyProfileContent={
                    route?.params?.fromMyProfileContent || false
                  }
                />
              </View>
            ))
          ) : isLoading ? (
            <ActivityIndicator />
          ) : (
            <View
              style={{
                flex: 1,
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <Text
                style={{
                  fontFamily: "Bold",
                  fontSize: Size(6),
                  color: "#888",
                  lineHeight: 20,
                }}
              >
                No Messages
              </Text>
            </View>
          )}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  headerContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: Size(5),
    paddingVertical: HeightSize(2),
    marginTop: Platform.OS === "ios" ? HeightSize(4) : 0,
  },
  loader: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
});

export default MessageList;
