import React, { useCallback, useEffect, useState } from "react";
import {
  ActivityIndicator,
  BackHandler,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import { BackIcon } from "../../res/Svg";
import { HeightSize, Size } from "../../res/Size";
import { Colors } from "../../res/Colors";
import _debounce from "lodash/debounce";
import { commonStyles } from "../../res/CommonStyles";
import SearchCardForMessage from "../../components/SearchCardForMessage";
import { shallowEqual, useSelector } from "react-redux";
import { searchArrayOrMakeCallToAPI } from "../../constants/misc";
import { dispatch } from "../../redux/store";
import { useNavigation } from "@react-navigation/native";
import { selectUserInfo } from "../../redux/selectors/authSelectors";

const NewMessage = ({ fromMyProfileContent }) => {
  const [searchResult, setSearchResult] = useState(null);
  const [user, setUser] = useState(null);

  const navigation = useNavigation();

  const userInfo = useSelector(selectUserInfo, shallowEqual);

  const loadingUser = useSelector(
    ({ loading }) => loading.effects.user.fetchUserDetails
  );

  const gettingUser = useSelector(
    ({ loading }) => loading.effects.user.userSearch
  );
  const userList = useSelector((state) => state.user.usersByProjection);

  useEffect(() => {
    const backHandler = BackHandler.addEventListener(
      "hardwareBackPress",
      () => {
        if (navigation.canGoBack()) {
          navigation.goBack();
          return true;
        } else {
          navigation.navigate("MessageList");
          return true;
        }
      }
    );

    return () => backHandler.remove();
  }, [navigation]);

  const handleSearch = async (query) => {
    const searchResposne = await searchArrayOrMakeCallToAPI({
      searchTerm: query,
      array: [
        ...userList?.map((item) => ({
          ...item,
          type: "user",
          fullname: `${item?.firstName} ${item?.lastName}`,
        })),
      ],
      makeSearchCall: [dispatch.user.userSearch],
    });

    const sortResponse = searchResposne.filter((record) => {
      return record.id !== userInfo.id;
    });

    const searchResponse = sortResponse?.length > 0 ? sortResponse : null;
    setSearchResult(searchResponse);
  };

  const fetchSearchData = useCallback(_debounce(handleSearch, 400), []);

  const fetchAllUserData = async () => {
    await dispatch.user.userSearchByProjection();
  };

  useEffect(() => {
    fetchAllUserData();
  }, []);

  useEffect(() => {
    if (user) {
      setSearchResult(null);
    }
  }, [user]);

  return loadingUser ? (
    <View
      style={{
        flex: 1,
        backgroundColor: Colors.white,
        justifyContent: "center",
        alignItems: "center",
      }}
    >
      <ActivityIndicator />
    </View>
  ) : (
    <ScrollView style={styles.container}>
      <View
        style={{
          paddingHorizontal: Size(5),
          paddingTop: Platform.OS === "ios" ? HeightSize(4) : 0,
        }}
      >
        <View style={styles.headerContainer}>
          <TouchableOpacity
            onPress={() => {
              navigation.navigate("MessageList", { fromMyProfileContent });
            }}
          >
            <BackIcon />
          </TouchableOpacity>
          <Text style={commonStyles.boldText}>SEND A MESSAGE</Text>
        </View>
        <Text style={commonStyles.regularText}>To: </Text>
        <TextInput
          style={styles.textInputStyle}
          placeholder="Type a name ..."
          placeholderTextColor="#e0e0e0"
          onChangeText={(text) => fetchSearchData(text)}
          autoComplete="off"
          autoCorrect={false}
          spellCheck={false}
        />

        {!gettingUser && searchResult
          ? searchResult.map((user) => (
              <SearchCardForMessage
                key={user.id}
                user={user}
                setUser={setUser}
              />
            ))
          : null}
        {gettingUser ? (
          <View
            style={{
              flex: 1,
              backgroundColor: Colors.white,
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <ActivityIndicator />
          </View>
        ) : null}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  headerContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingVertical: HeightSize(2),
    paddingRight: Size(15),
  },
  textInputStyle: {
    borderBottomWidth: 2,
    borderBottomColor: Colors.grey,
    width: Size(85),
    height: HeightSize(5),
    marginBottom: HeightSize(2),
  },
  noResultsText: {
    textAlign: "center",
    // marginTop: 20,
    fontFamily: "PoppinsBold",
    fontSize: Size(4),
    opacity: 0.5,
  },
});

export default NewMessage;
