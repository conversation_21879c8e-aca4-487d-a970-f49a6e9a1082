import { useEffect, useState } from "react";
import { ActivityIndicator, View } from "react-native";
import { notifyError } from "../../constants/misc";
import ChatHeader from "../../components/ChatItems/ChatHeader";
import ChatInput from "../../components/ChatItems/ChatInput";
import { Colors } from "../../res/Colors";
import { useSelector } from "react-redux";
import { dispatch } from "../../redux/store";
import { useFocusEffect, useNavigation } from "@react-navigation/native";
import uuid from "react-native-uuid";
import { useCallback } from "react";
import { useRef } from "react";
import { MESSAGE_REFRESH_INTERVAL } from "../../constants/chat";
import { useMemo } from "react";
import { useAppStateEffect } from "../../hooks/useAppStateEffect";
import { selectUserInfo } from "../../redux/selectors/authSelectors";
import { shallowEqual } from "react-redux";
import ChatMessageList from "../../components/ChatItems/ChatMessageList";

const DirectMessage = ({ route }) => {
  const navigation = useNavigation();

  const chatIntervalRef = useRef(null);

  const temporaryChatroomId = useMemo(() => uuid.v4(), []);
  const fromMyProfileContent = route?.params?.fromMyProfileContent || false;
  const recipientId = route.params.recipientId;
  const [chatroomId, setChatroomId] = useState(route.params.chatroomId);
  const [isFirstLoad, setIsFirstLoad] = useState(true);

  const userInfo = useSelector(selectUserInfo, shallowEqual);
  const chatroom = useSelector((state) =>
    state.chat.chats?.find(
      (chat) =>
        chat.chatroomId === chatroomId ||
        chat.temporaryChatroomId === temporaryChatroomId
    )
  );
  const otherUser = chatroom?.otherUser;
  const messages = chatroom?.messages || [];

  const fetchChat = async () => {
    await dispatch.chat.getChat({ chatroomId, temporaryChatroomId });
    if (isFirstLoad) {
      setIsFirstLoad(false);
    }
  };

  const fetchChatByRecipient = async () => {
    try {
      const data = await dispatch.chat.getChatByRecipient({
        recipientId,
        temporaryChatroomId,
      });
      if (data?.chatroomId) {
        setChatroomId(data.chatroomId);
      }
    } finally {
      setIsFirstLoad(false);
    }
  };

  const fetchOtherUser = async () => {
    await dispatch.chat.getOtherUser({
      chatroomId,
      temporaryChatroomId,
      recipientId,
    });
  };

  // useFocusEffect(
  //   useCallback(() => {
  //     const setupNotifications = async () => {
  //       try {
  //         const hasPermission = await NotificationService.requestUserPermission();
  //         if (hasPermission) {
  //           const token = await NotificationService.getToken();
  //           await NotificationService.updateToken(token);

  //           NotificationService.setupForegroundNotification();
  //           NotificationService.setBackgroundMessageHandler();
  //           NotificationService.setupNotificationOpened();
  //         }
  //       } catch (error) {
  //         console.error('Error setting up notifications:', error);
  //       }
  //     };

  //     setupNotifications();
  //   }, [])
  // );

  useFocusEffect(
    useCallback(() => {
      const fetchData = async () => {
        await fetchOtherUser();
        if (chatroomId) {
          await fetchChat();
        } else if (recipientId) {
          await fetchChatByRecipient();
        }
      };

      fetchData();

      return async () => {
        await dispatch.chat.clearTemporaryChats();
      };
    }, [])
  );

  useEffect(() => {
    navigation.setParams({
      chatroomId: chatroomId || null,
    });
  }, [chatroomId]);

  // const testNotification = async () => {
  //   try {
  //     // Request permission
  //     const hasPermission = await NotificationService.requestUserPermission();

  //     if (hasPermission) {
  //       // Get token
  //       const token = await NotificationService.getToken();
  //       console.log('FCM Token:', token);

  //       // Test notification
  //       const message = {
  //         notification: {
  //           title: 'Test Notification',
  //           body: 'This is a test push notification'
  //         },
  //         data: {
  //           chatroomId: chatroomId,
  //           senderId: userInfo?.id
  //         }
  //       };

  //       // Log the message
  //       console.log('Test notification message:', message);
  //     } else {
  //       console.log('Notification permission not granted');
  //     }
  //   } catch (error) {
  //     console.error('Error testing notification:', error);
  //   }
  // };

  const sendMessage = async (data) => {
    try {
      let liveLikeChatroomId = chatroomId;
      const temporaryMessageId = uuid.v4();

      dispatch.chat.createTemporaryMessage({
        chatroomId: liveLikeChatroomId,
        temporaryChatroomId,
        temporaryMessageId,
        userInfo,
        data,
      });

      if (!liveLikeChatroomId) {
        liveLikeChatroomId = await dispatch.chat.createChat({
          recipientId,
          temporaryChatroomId,
        });
        setChatroomId(liveLikeChatroomId);
      }

      await dispatch.chat.createMessage({
        chatroomId: liveLikeChatroomId,
        temporaryChatroomId,
        temporaryMessageId,
        data,
      });
    } catch (error) {
      console.error(error);
      notifyError("Error sending message");
    }
  };

  const sendTextMessage = (message) =>
    sendMessage({
      type: "text",
      message,
    });

  const sendImageMessage = (photoUrl) =>
    sendMessage({
      type: "image",
      image_url: photoUrl,
    });

  const sendVideoMessage = (videoUrl) =>
    sendMessage({
      type: "video",
      image_url: videoUrl,
    });

  const startChatInterval = useCallback(() => {
    if (chatroomId && !chatIntervalRef.current) {
      chatIntervalRef.current = setInterval(() => {
        fetchChat();
      }, MESSAGE_REFRESH_INTERVAL);
    }
  }, [chatroomId]);
  const stopChatInterval = useCallback(() => {
    if (chatIntervalRef.current) {
      clearInterval(chatIntervalRef.current);
      chatIntervalRef.current = null;
    }
  }, []);
  useAppStateEffect({
    onForeground: startChatInterval,
    onBackground: stopChatInterval,
  });
  useFocusEffect(
    useCallback(() => {
      if (!chatroomId) return;

      startChatInterval();

      return () => stopChatInterval();
    }, [chatroomId])
  );

  return (
    <View style={{ flex: 1, backgroundColor: Colors.white }}>
      <ChatHeader
        userDetails={otherUser}
        fromMyProfileContent={fromMyProfileContent}
        userId={recipientId}
      />
      {isFirstLoad && !messages.length ? (
        <View
          style={{
            flex: 1,
            backgroundColor: Colors.white,
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <ActivityIndicator />
        </View>
      ) : (
        <ChatMessageList
          messages={messages}
          receiverDetails={otherUser}
          fromMyProfileContent={fromMyProfileContent}
          recipientId={recipientId}
        />
      )}
      <ChatInput
        sendTextMessage={sendTextMessage}
        sendImageMessage={sendImageMessage}
        sendVideoMessage={sendVideoMessage}
      />
    </View>
  );
};

export default DirectMessage;
