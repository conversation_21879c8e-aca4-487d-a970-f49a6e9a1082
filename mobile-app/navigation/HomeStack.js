import { createNativeStackNavigator } from "@react-navigation/native-stack";
import React from "react";
import { Platform } from "react-native";
import Feed from "../app/screens/Feed/Feed";
import FeedDetail from "../app/screens/FeedDetail/FeedDetail";
import ShareFeed from "../app/screens/ShareFeed/ShareFeed";
import MessageList from "../app/screens/Message/MessageList";
import UploadHighlights from "../app/screens/uploadHighlights/UploadHighlights";
import AnnouncementPage from "../app/screens/Announcements/AnnouncementPage";
import UpdateModal from "../app/components/UpdateModal";
import DirectMessage from "../app/screens/Message/DirectMessage";
import NewMessage from "../app/screens/Message/NewMessage";

const HomeStack = createNativeStackNavigator();

export const HomeScreenStack = ({ route }) => {
  const guestLogin = route?.params?.guestLogin ?? false; // default to false if not provided

  return (
    <HomeStack.Navigator
      initialRouteName="Feed"
      screenOptions={{
        animation: Platform.OS === "ios" ? "fade" : "none",
        animationDuration: 300,
        headerShadowVisible: false,
        headerShown: false,
      }}
    >
      <HomeStack.Screen
        name="Feed"
        component={Feed}
        initialParams={{ guestLogin: guestLogin }}
        options={{
          gestureEnabled: false,
        }}
      />
      <HomeStack.Screen
        name="FeedDetail"
        component={FeedDetail}
        options={{
          gestureEnabled: false,
          unmountOnBlur: true,
        }}
      />
      <HomeStack.Screen name="ShareFeed" component={ShareFeed} />
      <HomeStack.Screen name="Message" component={DirectMessage} />
      <HomeStack.Screen name="MessageList" component={MessageList} />
      <HomeStack.Screen name="NewMessage" component={NewMessage} />
      <HomeStack.Screen name="UploadHighlights" component={UploadHighlights} />
      <HomeStack.Screen name="AnnouncementPage" component={AnnouncementPage} />
      <HomeStack.Screen name="UpdateModal" component={UpdateModal} />
    </HomeStack.Navigator>
  );
};
