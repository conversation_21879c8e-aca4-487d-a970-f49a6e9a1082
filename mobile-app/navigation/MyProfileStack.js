import { createNativeStackNavigator } from "@react-navigation/native-stack";
import React from "react";
import { Platform } from "react-native";
import MyProfile from "../app/screens/MyProfile/MyProfile";
import Settings from "../app/screens/Settings/Setting";
import Security from "../app/screens/Security/Security";
import EditProfile from "../app/screens/EditProfile/EditProfile";
import ReferenceList from "../app/screens/Reference/ReferenceList";
import Connections from "../app/screens/Connections/Connections";
import ReferenceFormPage from "../app/screens/Reference/ReferenceFormPage";
import Account from "../app/screens/Account/Account";
import BlockedUsers from "../app/screens/BlockedUsers/BlockedUsers";
import EditReferenceFormPage from "../app/screens/Reference/EditReferenceFormPage";
import UploadHighlights from "../app/screens/uploadHighlights/UploadHighlights";
import PrivacySecurity from "../app/screens/PrivacySecurity/PrivacySecurity";
import WebViewScreen from "../app/screens/WebView/WebView";


const MyProfileStack = createNativeStackNavigator();

export const MyProfileScreenStack = ({ route }) => {
  return (
    <MyProfileStack.Navigator
      screenOptions={{
        animation: Platform.OS === "ios" ? "fade" : "none",
        animationDuration: 300,
        headerShadowVisible: false,
        headerShown: false,
      }}
    >
      <MyProfileStack.Screen 
        name="MyProfile" 
        component={MyProfile}
        options={{
          gestureEnabled: true,
          gestureDirection: "horizontal",
          // unmountOnBlur: true,
          // screen: ({ navigation }) => {
          //   navigation.setParams({ selectedTab: null });
          // }
        }}
      />
      <MyProfileStack.Screen name="Settings" component={Settings} />
      <MyProfileStack.Screen name="Security" component={Security} />
      <MyProfileStack.Screen name="EditProfile" component={EditProfile} />
      <MyProfileStack.Screen name="ReferenceList" component={ReferenceList} />
      <MyProfileStack.Screen name="ReferenceFormPage" component={ReferenceFormPage} />
      <MyProfileStack.Screen name="EditReferenceFormPage" component={EditReferenceFormPage} />
      <MyProfileStack.Screen name="Connections" component={Connections} />
      <MyProfileStack.Screen name="Account" component={Account} />
      <MyProfileStack.Screen name="BlockedUsers" component={BlockedUsers} />
      <MyProfileStack.Screen name="UploadHighlights" component={UploadHighlights} />
      <MyProfileStack.Screen name="PrivacySecurity" component={PrivacySecurity} />
      <MyProfileStack.Screen name="WebViewScreen" component={WebViewScreen} />
    </MyProfileStack.Navigator>
  );
};