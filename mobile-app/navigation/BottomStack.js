import React, { useState } from "react";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { Colors } from "../app/res/Colors";
import { HomeScreenStack } from "./HomeStack";
import { Ionicons } from "@expo/vector-icons";
import { HeightSize, Size } from "../app/res/Size";
import { SearchScreenStack } from "./SearchStack";
import {
  HomeIcon,
  HomeIconGrey,
  ProfileIcon,
  ProfileIconGrey,
  SearchIcon,
  SearchIconGrey,
} from "../app/res/Svg";
import { getTabBarStyle } from "../app/constants/misc";
import { MyProfileScreenStack } from "./MyProfileStack";
import { shallowEqual, useSelector } from "react-redux";
import {
  Image,
  Modal,
  View,
  Text,
  TouchableOpacity,
  Platform,
  DeviceEventEmitter
} from "react-native";
import Button from "../app/components/Button/Button";
import { selectUserInfo } from "../app/redux/selectors/authSelectors";
import { dispatch } from "../app/redux/store";

const Tab = createBottomTabNavigator();

export const BottomStack = ({ navigation, route }) => {
const userInfo = useSelector(selectUserInfo, shallowEqual);
  const [showLoginPopup, setShowLoginPopup] = useState(false);

  const handleRestrictedAccess = () => {
    if (route?.params?.guestLogin) {
      setShowLoginPopup(true);
      return false; // Explicitly return false for guest users
    } else {
      return true; // Return true for logged-in users
    }
  };

  return (
    <>
      <Tab.Navigator
        screenOptions={({ route }) => ({
          tabBarActiveTintColor: Colors.black,
          tabBarInactiveTintColor: Colors.grey,
          tabBarShowLabel: false,
          headerShown: false,
        })}
      >
        <Tab.Screen
          name="HomeScreenStack"
          component={HomeScreenStack}
          options={({ route }) => ({
            tabBarIcon: ({ color, focused }) =>
              focused ? (
                <HomeIcon width={Platform.isPad ? 30 : 24} height={Platform.isPad ? 30 : 24} />
              ) : (
                <HomeIconGrey width={Platform.isPad ? 30 : 24} height={Platform.isPad ? 30 : 24} />
              ),
            tabBarStyle: { display: getTabBarStyle(route) }, // ✅ Correctly pass the route object
          })}
          listeners={({ navigation }) => ({
            tabPress: (e) => {
              e.preventDefault();
              const isFocused = navigation.isFocused();

              if (isFocused) {
                DeviceEventEmitter.emit("scrollToTop"); // ✅ Emit event
              } else {
                navigation.navigate("HomeScreenStack", {
                  screen: "Feed",
                });
              }
            },
          })}
        />

        <Tab.Screen
          options={({ route }) => ({
            tabBarIcon: ({ color, focused }) => (
              <SearchIcon
                width={Platform.isPad ? 30 : 24}
                height={Platform.isPad ? 30 : 24}
                focused={focused}
              />
            ),
            tabBarStyle: { display: getTabBarStyle(route) },
            unmountOnBlur: true,
          })}
          listeners={({ navigation }) => ({
            //  blur: () => navigation.setParams({ screen: undefined }),
            tabPress: (e) => {
              e.preventDefault();
              if (handleRestrictedAccess()) {
                navigation.navigate("SearchScreenStack");
              }
            },
          })}
          name="SearchScreenStack"
          component={SearchScreenStack}
          initialParams={{ guestLogin: route?.params?.guestLogin }}
        />

        <Tab.Screen
          options={({ route }) => ({
            tabBarIcon: ({ focused }) =>
              focused ? (
                <ProfileIcon
                  width={Platform.isPad ? 30 : 24}
                  height={Platform.isPad ? 30 : 24}
                />
              ) : (
                <ProfileIconGrey
                  width={Platform.isPad ? 30 : 24}
                  height={Platform.isPad ? 30 : 24}
                />
              ),
            tabBarStyle: { display: getTabBarStyle(route) },
            unmountOnBlur: true,
          })}
          listeners={({ navigation, route }) => ({
            tabPress: (e) => {
              e.preventDefault();
              const isFocused = navigation.isFocused();

              if (isFocused) {
                // Instead of resetting, navigate normally to avoid Feed issue
                navigation.navigate("MyProfileScreenStack", {
                  screen: "MyProfile",
                  params: {
                    userId: userInfo?.id, // Show logged-in user profile
                    fromBottomTab: true,
                    forceRefresh: true,
                    refresh: Date.now(),
                  },
                });
              } else {
                if (handleRestrictedAccess()) {
                  navigation.navigate("MyProfileScreenStack", {
                    screen: "MyProfile",
                    params: {
                      userId: userInfo?.id,
                      fromBottomTab: true,
                      forceRefresh: true,
                      refresh: Date.now(),
                    },
                  });
                }
              }
            },
          })}

          name="MyProfileScreenStack"
          component={MyProfileScreenStack}
          initialParams={{ guestLogin: route?.params?.guestLogin }}
        />
      </Tab.Navigator>

      {/* Login Popup */}
      < Modal
        visible={showLoginPopup}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowLoginPopup(false)}
      >
        <View
          style={{
            flex: 1,
            justifyContent: "center",
            alignItems: "center",
            backgroundColor: "rgba(0,0,0,0.5)",
          }}
        >
          <View
            style={{
              width: "80%",
              padding: Size(5),
              backgroundColor: Colors.white,
              borderRadius: Size(7),
              alignItems: "center",
            }}
          >
            <Text
              style={{
                fontSize: Size(4.7),
                marginBottom: HeightSize(1),
                textAlign: "center",
                fontFamily: "Regular",
              }}
            >
              Log in now.
            </Text>
            <Text
              style={{
                fontSize: Size(4),
                textAlign: "center",
                fontFamily: "Regular",
                opacity: 0.5,
              }}
            >
              You must have an account to use this feature.
            </Text>
            <Button
              title={"LOG IN"}
              containerStyle={{ marginTop: HeightSize(2) }}
              onPress={async () => {
                setShowLoginPopup(false);
                // Dispatch logout to switch to AuthNavigation/Login and unmount all tabs
                await dispatch.auth.logout();
              }}
              backgroundColor={Colors.black}
              textStyleInsideButton={{ fontSize: Size(3.7) }}
              height={HeightSize(7)}
            />
            {/* <Button
              title={"SIGN UP"}
              containerStyle={{ marginTop: HeightSize(2) }}
              onPress={() => navigation.navigate("SignUp")}
              backgroundColor={Colors.black}
              textStyleInsideButton={{ fontSize: Size(3.7) }}
              height={HeightSize(7)}
            /> */}
            <Button
              title={"CANCEL"}
              containerStyle={{
                marginTop: HeightSize(2),
                borderWidth: 1,
                borderColor: "#A5A5A5",
              }}
              onPress={() => {
                setShowLoginPopup(false);
              }}
              backgroundColor={Colors.white}
              textStyleInsideButton={{ color: "#A5A5A5", fontSize: Size(3.7) }}
              height={HeightSize(6)}
            />
          </View>
        </View>
      </Modal >
    </>
  );
};
