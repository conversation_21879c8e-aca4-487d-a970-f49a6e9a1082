import { createNativeStackNavigator } from "@react-navigation/native-stack";
import React from "react";
import { Platform } from "react-native";
import Search from "../app/screens/Search/Search";

const SearchStack = createNativeStackNavigator();

export const SearchScreenStack = ({ route }) => {
  return (
    <SearchStack.Navigator
      screenOptions={{
        animation: Platform.OS === "ios" ? "fade" : "none",
        animationDuration: 300,
        headerShadowVisible: false,
        headerShown: false,
      }}
    >
      <SearchStack.Screen name="Search" component={Search} />
    </SearchStack.Navigator>
  );
};