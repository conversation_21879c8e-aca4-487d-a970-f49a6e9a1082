import { createNativeStackNavigator } from "@react-navigation/native-stack";
import React from "react";
import { Platform } from "react-native";
import MyTeam from "../app/screens/Teams/Teams";


const MyTeamStack = createNativeStackNavigator();

export const MyTeamScreenStack = ({ route }) => {
    return (
      <MyTeamStack.Navigator
        screenOptions={{
          animation: Platform.OS === "ios" ? "default" : "none",
          headerShadowVisible: false,
          headerShown: false,
        }}
      >
        <MyTeamStack.Screen name="MyTeam" component={MyTeam} />
      </MyTeamStack.Navigator>
    );
  };