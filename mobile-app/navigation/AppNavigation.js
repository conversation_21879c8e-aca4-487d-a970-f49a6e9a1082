import { useEffect, useState } from "react";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { BottomStack } from "./BottomStack";
import { AuthNavigation } from "./AuthNavigation";
import * as Linking from "expo-linking";
import { dispatch } from "../app/redux/store";
import AnalyticsService from "../app/services/analytics";
import { shallowEqual, useSelector } from "react-redux";
import {
  selectAuthStatus,
  selectUserInfo,
} from "../app/redux/selectors/authSelectors";

export const Router = () => {
  const [loading, setLoading] = useState(true);
  const Stack = createNativeStackNavigator();

  const { authenticated } = useSelector(selectAuthStatus);
  const userInfo = useSelector(selectUserInfo, shallowEqual);

  useEffect(() => {
    dispatch.auth.loadAuthState();
  }, []);

  useEffect(() => {
    const handleAnalytics = async () => {
      try {
        if (authenticated && userInfo?.id) {
          await AnalyticsService.setUserId(userInfo.id);
        } else {
          await AnalyticsService.setUserId(null);
        }
      } catch (error) {
        console.error("[Auth] Error checking authentication status:", error);
      } finally {
        setLoading(false);
      }
    };
    handleAnalytics();
  }, [authenticated, userInfo]);

  const handleQueryParamLogout = async () => {
    const url = Linking.parse(Linking.createURL());
    if (url.queryParams && url.queryParams.id) {
      await dispatch.auth.logout();
    }
  };

  useEffect(() => {
    handleQueryParamLogout();
  }, []);

  if (loading) {
    return null;
  }

  return (
    <Stack.Navigator
      screenOptions={{ headerShown: false, gestureEnabled: authenticated }}
    >
      {authenticated ? (
        <Stack.Screen name="BottomStack" component={BottomStack} />
      ) : (
        <Stack.Screen name="AuthNavigation" component={AuthNavigation} />
      )}
    </Stack.Navigator>
  );
};
