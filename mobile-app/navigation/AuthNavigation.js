import React from "react";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import Tutorial from "../app/screens/Tutorial/Tutorial";
import SignUp from "../app/screens/SignUp/SignUp";
import Profile from "../app/screens/Profile/Profile";
import Login from "../app/screens/Login/Login";
import ClubDropDown from "../app/screens/Clubs/ClubDropDown";
import PrivacyPolicyWebView from "../app/components/WebViewLink";
import { Platform } from "react-native";
import Feed from "../app/screens/Feed/Feed";

const Auth = createNativeStackNavigator();

export const AuthNavigation = () => {
  return (
    <Auth.Navigator
      screenOptions={{
        animation: Platform.OS === "ios" ? "fade" : "none",
        animationDuration: 300,
        headerShadowVisible: false,
        headerShown: false,
      }}
      initialRouteName="Login"
    >
      <Auth.Screen name={"Tutorial"} component={Tutorial} />
      <Auth.Screen name={"SignUp"} component={SignUp} />
      <Auth.Screen name={"Profile"} component={Profile} />
      <Auth.Screen name={"Login"} component={Login} />
      <Auth.Screen name={"ClubDropDown"} component={ClubDropDown} />
      <Auth.Screen
        name={"PrivacyPolicyWebView"}
        component={PrivacyPolicyWebView}
      />
      <Auth.Screen name={"Feed"} component={Feed} />
    </Auth.Navigator>
  );
};
