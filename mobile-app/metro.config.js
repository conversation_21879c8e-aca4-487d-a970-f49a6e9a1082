const { getDefaultConfig } = require('expo/metro-config');

module.exports = (() => {
  const config = getDefaultConfig(__dirname);

  // Remove 'svg' from assetExts and add it to sourceExts
  config.resolver.assetExts = config.resolver.assetExts.filter((ext) => ext !== 'svg');
  config.resolver.sourceExts.push('svg');

  // Add resolverMainFields for proper module resolution
  config.resolver.resolverMainFields = ['react-native', 'browser', 'main'];

  return config;
})();
