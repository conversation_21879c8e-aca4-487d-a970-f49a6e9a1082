import { LogBox, Platform } from "react-native";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import React, { useEffect, useState, useRef } from "react";
import { Provider, shallowEqual, useSelector } from "react-redux";
import * as Linking from "expo-linking";
import useFonts from "./app/hooks/useFonts";
import analytics from "@react-native-firebase/analytics";
import { Router } from "./navigation/AppNavigation";
import { _navigator } from "./app/constants/navigation";
import { useCheckForUpdate } from "./app/hooks/useCheckForUpdate";
import { selectUserInfo } from "./app/redux/selectors/authSelectors";
import store, { dispatch } from "./app/redux/store";
import ForwardRefNavigationContainer from "./app/constants/ForwardRefNavigationContainer";
import ForwardedToast from "./app/constants/ForwardedToast";
import googleServices from "./android/app/google-services.json";
import UpdateModal from "./app/components/UpdateModal";
import AppLoader from "./app/components/AppLoader";
import { getSingleModel } from "./app/constants/Constant";
import {
  initializeLiveLike,
  prefetchLiveLikeEmojis,
} from "./app/utils/LiveLike";
import messaging from "@react-native-firebase/messaging";
const LiveLike = require("@livelike/javascript");

// Ignore all logs
LogBox.ignoreAllLogs(true);

// Detect environment from Firebase Project ID
const projectId = googleServices.project_info.project_id || "";

let ENV = "development"; // Default

if (projectId.includes("staging")) {
  ENV = "staging";
} else if (projectId.includes("ec4c6")) {
  ENV = "production";
}

function MainApp() {
  const toastRef = useRef(null);
  const { isUpdateAvailable, loading: loadingCheckUpdates } =
    useCheckForUpdate();

  useEffect(() => {
    const initializeAnalytics = async () => {
      try {
        await analytics().setAnalyticsCollectionEnabled(true);
        await analytics().logEvent("app_initialized", {
          environment: ENV,
          platform: Platform.OS,
          timestamp: new Date().toISOString(),
          is_debug: __DEV__,
        });

        console.debug(
          `[Analytics] Initialised with Project ID ${projectId} and Environment ${ENV}`
        );
      } catch (error) {
        console.error("[Analytics] Initialisation error:", error);
      }
    };

    initializeAnalytics();

    if (getSingleModel("auth")?.authUser?.authenticated) {
      initializeLiveLike();
    }
  }, []);

  useEffect(() => {
    let initialURL = null;

    const handleDeepLink = (event) => {
      const url = event?.url || initialURL;
      if (!url) return;

      console.debug(`[Router] Deep link URL: ${url}`);
      const { path, queryParams } = Linking.parse(url);

      const highlightId = Array.isArray(queryParams?.highlightId)
        ? queryParams?.highlightId[0]
        : queryParams?.highlightId;

      const userId = Array.isArray(queryParams?.userId)
        ? queryParams?.userId[0]
        : queryParams?.userId;

      if (path === "user/comment" && highlightId && userId) {
        const navigateToFeedDetail = () => {
          if (_navigator.current) {
            _navigator.current?.navigate("FeedDetail", {
              highlightId,
              userId,
              fromDeepLink: true,
            });
          } else {
            setTimeout(navigateToFeedDetail, 500);
          }
        };

        navigateToFeedDetail();
      }
    };

    Linking.getInitialURL().then((url) => {
      if (url) {
        initialURL = url;
      }
    });

    const subscription = Linking.addEventListener("url", handleDeepLink);

    return () => {
      subscription.remove();
    };
  }, []);

  if (loadingCheckUpdates) {
    return <AppLoader />;
  }

  if (isUpdateAvailable) {
    return <UpdateModal isVisible={true} isForced={true} />;
  }

  return (
    <ForwardRefNavigationContainer ref={_navigator}>
      <Router />
      <ForwardedToast ref={toastRef} />
    </ForwardRefNavigationContainer>
  );
}

export default function App() {
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    const setupApp = async () => {
      await useFonts();
      await prefetchLiveLikeEmojis();

      setIsReady(true);
    };

    setupApp();
  }, []);

  if (!isReady) {
    return <AppLoader />;
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <Provider store={store}>
        <MainApp />
      </Provider>
    </GestureHandlerRootView>
  );
}
