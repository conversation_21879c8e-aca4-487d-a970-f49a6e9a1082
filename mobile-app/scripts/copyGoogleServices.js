require("dotenv").config();
const fs = require("fs");
const path = require("path");

// Debugging: Print the selected environment
console.log("🔍 ENVFILE:", process.env.ENVFILE);

// Fix: Remove `.env.` prefix before using in `envMap`
const currentEnv = process.env.ENVFILE?.replace(".env.", "") || "development";

console.log(`📌 Current ENV Selected: ${currentEnv}`);

const envMap = {
  development: {
    android: "env-config/google-services.development.json",
    ios: "env-config/GoogleService-Info.development.plist",
  },
  staging: {
    android: "env-config/google-services.staging.json",
    ios: "env-config/GoogleService-Info.staging.plist",
  },
  production: {
    android: "env-config/google-services.json",
    ios: "env-config/GoogleService-Info.plist",
  },
};

// Read correct file based on ENV
const androidSource = envMap[currentEnv]?.android;
const iosSource = envMap[currentEnv]?.ios;

console.log(`🟢 Android Source: ${androidSource}`);
console.log(`🟢 iOS Source: ${iosSource}`);

const androidDest = "android/app/google-services.json";
const iosDest = "ios/GoogleService-Info.plist";

// Function to delete old files before copying
const deleteFile = (filePath) => {
  if (fs.existsSync(filePath)) {
    fs.unlinkSync(filePath);
    console.log(`🗑 Deleted old file: ${filePath}`);
  }
};

// Function to copy new files
const copyFile = (src, dest) => {
  if (!src) {
    console.error(`❌ ERROR: No source file defined for ${dest}`);
    process.exit(1);
  }
  if (fs.existsSync(src)) {
    deleteFile(dest); // Ensure old file is deleted before copying
    fs.copyFileSync(src, dest);
    console.log(`✅ Copied: ${src} → ${dest}`);
  } else {
    console.warn(`⚠️ Warning: ${src} does not exist!`);
  }
};

// Copy the files after deleting old ones
copyFile(androidSource, androidDest);
copyFile(iosSource, iosDest);

console.log("🎉 Google Services files copied successfully!");
