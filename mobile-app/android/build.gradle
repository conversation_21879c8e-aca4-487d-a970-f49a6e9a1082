// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext {
        buildToolsVersion = findProperty('android.buildToolsVersion') ?: '34.0.0'
        minSdkVersion = Integer.parseInt(findProperty('android.minSdkVersion') ?: '23')
        compileSdkVersion = Integer.parseInt(findProperty('android.compileSdkVersion') ?: '34')
        targetSdkVersion = Integer.parseInt(findProperty('android.targetSdkVersion') ?: '34')
        kotlinVersion = findProperty('android.kotlinVersion') ?: '1.9.23'

        ndkVersion = "26.1.10909125"
       // Enable IMA (Interactive Media Ads) integration with ExoPlayer
    useExoplayerIMA = false

    // Enable support for RTSP (Real-Time Streaming Protocol) with ExoPlayer
    useExoplayerRtsp = true

    // Enable support for Smooth Streaming with ExoPlayer
    useExoplayerSmoothStreaming = true

    // Enable support for DASH (Dynamic Adaptive Streaming over HTTP) with ExoPlayer
    useExoplayerDash = true

    // Enable support for HLS (HTTP Live Streaming) with ExoPlayer
    useExoplayerHls = true 
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.google.gms:google-services:4.3.3'
        classpath('com.android.tools.build:gradle:8.0.0') 
        classpath('com.facebook.react:react-native-gradle-plugin') 
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion") 
    }
}

apply plugin: "com.facebook.react.rootproject"

allprojects {
    repositories {
        // For React Native binaries
        maven {
            url = uri("$rootDir/../node_modules/react-native/android")
        }
        // For Android JSC binaries
        maven {
            url = uri("$rootDir/../node_modules/jsc-android/dist")
        }
        google() 
        mavenCentral() 
        mavenLocal()
        maven { url 'https://www.jitpack.io' } 
        
        // Deprecated: Avoid using jcenter() unless required
        // jcenter()
    }
}

