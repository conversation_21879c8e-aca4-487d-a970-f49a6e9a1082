import android.os.Handler
import android.os.Looper
import android.util.Log

class PerformanceMonitor private constructor() {
    private var handler: Handler? = null
    private var isMonitoring = false
    private val checkInterval = 100L // milliseconds
    
    companion object {
        @JvmStatic
        val shared = PerformanceMonitor()
        private const val TAG = "PerformanceMonitor"
    }

    fun startMonitoring() {
        if (isMonitoring) return
        
        handler = Handler(Looper.getMainLooper())
        isMonitoring = true

        handler?.post(object : Runnable {
            private var lastFrameTimeNanos = System.nanoTime()

            override fun run() {
                val currentTimeNanos = System.nanoTime()
                val frameDurationMs = (currentTimeNanos - lastFrameTimeNanos) / 1_000_000
                
                if (frameDurationMs > 16) { // Threshold for frame drops (60 FPS = 16.6ms)
                    val stackTrace = Thread.currentThread().stackTrace
                    Log.w(TAG, "Main thread blocked: ${frameDurationMs}ms")
                    Log.w(TAG, "Stack trace: ${stackTrace.joinToString("\n")}")
                }

                lastFrameTimeNanos = currentTimeNanos
                if (isMonitoring) {
                    handler?.postDelayed(this, checkInterval)
                }
            }
        })
    }

    fun stopMonitoring() {
        isMonitoring = false
        handler?.removeCallbacksAndMessages(null)
        handler = null
    }
}