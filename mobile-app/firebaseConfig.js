import { initializeApp } from "firebase/app";
import analytics from "@react-native-firebase/analytics";

// Get environment from ENVFILE and remove `.env.` prefix
const getEnvironment = () => {
  const env = process.env.ENVFILE?.replace(".env.", "") || "development";

  if (!["development", "staging", "production"].includes(env)) {
    console.warn("⚠️ No valid ENVFILE specified, defaulting to development");
    return "development";
  }

  return env;
};

const ENV = getEnvironment();

const firebaseConfig = {
  development: {
    apiKey: "AIzaSyBNn8BjzJ8OHyRyOBW_nz_MEfsv7H4uQCI",
    authDomain: "playerappmobile-development.firebaseapp.com",
    projectId: "playerappmobile-development",
    storageBucket: "playerappmobile-development.firebasestorage.app",
    messagingSenderId: "954239271262",
    appId: "1:954239271262:android:dad3120369e2e789110fcf",
  },
  production: {
    apiKey: "AIzaSyD1Zq02EgH8Q2gvbt53SSfOY2qo0QyZyRo",
    authDomain: "player-ec4c6.firebaseapp.com",
    projectId: "player-ec4c6",
    storageBucket: "player-ec4c6.firebasestorage.app",
    messagingSenderId: "946507714541",
    appId: "1:946507714541:android:d04f8ca820b5f8789d21e4",
  },
};

const selectedConfig = firebaseConfig[ENV];

if (!selectedConfig) {
  console.error(`[Analytics] No config found for environment ${ENV}`);
  throw new Error(`Firebase configuration for ${ENV} is missing!`);
}

// Initialize Firebase
const app = initializeApp(selectedConfig);

export { app, ENV };
