import ffmpeg from 'fluent-ffmpeg';

// THIS IS JUST A SAMPLE CODE. DO NOT USE IT IN PRODUCTION
// THIS IS JUST TO TEST THE FUNCTIONALITY OF FFMEG BEFORE USING ON LAMBDA. 
// NOT RELATED TO UNIT TESTING
async function processLargeVideoThumbnail(signedUrl, time = '00:00:00') {
    return new Promise((resolve, reject) => {
        ffmpeg(signedUrl)
          .inputOptions(['-re']) // Read the video at normal speed
          .screenshots({
            timestamps: [time],
            folder: "outputpng",
            filename: 'thumbnail.png',
          })
          .on('end', () => resolve(console.log('Thumbnail generated successfully')))
          .on('error', (err) => {
            console.error('Error generating thumbnail:', err);
            return reject(err);
          });
      });
}


// (async () => {
//   await processLargeVideoThumbnail("output/segment1.ts");
//   console.log('Video optimized successfully')
// })()



export function optimizeVideo(
    inputPath,
    videoBitrate = '3000k', // Lower bitrate for better streaming
    audioBitrate = '96k', // Higher audio bitrate for better sound quality
    segmentDuration = '4', // Slightly longer segment duration
    outputPath ='output'
  ) {
    return new Promise((resolve, reject) => {
      ffmpeg(inputPath)
        .inputOptions(['-re'])
        .outputOptions([
            '-c:v libx264', // Use H.264 codec for video
            '-preset veryfast', // Use 'veryfast' preset for quicker encoding and decent quality
            '-tune zerolatency', // Tune for zero latency streaming
            '-profile:v main', // Use 'main' profile for compatibility across devices
            '-level 3.1', // Level 3.1 is widely compatible and suited for most web video
            '-pix_fmt yuv420p',// Convert to 8-bit for high10 compatibility
            '-c:a aac', // Use AAC codec for audio
            `-b:v ${videoBitrate}`, // Adjust the video bitrate as needed
            `-b:a ${audioBitrate}`, // Adjust the audio bitrate as needed
            '-hls_segment_filename',

            `${outputPath}/segment%d.ts`,
            `-hls_time ${segmentDuration}`, // Duration of each segment in seconds
            '-hls_list_size 0', // Keep all segments in the playlist
            '-hls_playlist_type vod', // VOD mode for adaptive streaming
            '-f hls',
          ])
        .output(`output/index.m3u8`)
        .on('end', () => resolve())
        .on('error', (err, sdt, sdter) => {
          console.log(sdt, sdter);
          
          console.error('An error occurred on optimizeVideo:', err);
          return reject(err);
        })
        .run();
    });
  }

(async () => {
  await optimizeVideo("testing.mp4");
  console.log('Video optimized successfully')
})()