org: okemmadueric
app: player-app-backend-serverless
service: player-app-backend
useDotenv: true

package:
  excludeDevDependencies: true
  patterns:
    - "!**"
    - "dist/**"
    - "package.json"
    - "yarn.lock"
    - "node_modules/**"

plugins:
  - serverless-dynamodb
  - serverless-plugin-optimize
  - serverless-offline

provider:
  name: aws
  stage: ${opt:stage, self:custom.defaultStage}
  runtime: nodejs18.x
  region: ${env:AWS_REGION}

  apiGateway:
    binaryMediaTypes:
      - "*/*"
  environment:
    # Params are configured in the serverless dashboard
    NODE_ENV: ${self:provider.stage}
    USERS_TABLE_NAME: ${self:custom.usersTable}
    CLUBS_TABLE_NAME: ${self:custom.clubsTable}
    TEAMS_TABLE_NAME: ${self:custom.teamsTable}
    SEASONS_TABLE_NAME: ${self:custom.seasonsTable}
    MATCHES_TABLE_NAME: ${self:custom.matchesTable}
    HIGHLIGHT_TABLE_NAME: ${self:custom.highlightTable}
    LEAGUES_TABLE_NAME: ${self:custom.leaguesTableName}
    P2P_CHAT_TABLE_NAME: ${self:custom.p2pChatTableName}
    P2P_UNREAD_CHAT_TABLE_NAME: ${self:custom.p2pUnreadChatTableName}
    EXPERIENCE_TABLE_NAME: ${self:custom.experienceTableName}
    ANNOUNCEMENT_TABLE_NAME: ${self:custom.announcementTableName}
    USERS_PHYSICAL_DATA_TABLE_NAME: ${self:custom.userPhysicalDataTableName}
    FOLLOW_TABLE_NAME: ${self:custom.followTableName}
    REFERENCE_TABLE_NAME: ${self:custom.referenceTableName}
    REPORTS_TABLE_NAME: ${self:custom.reportsTableName}
    BLOCK_USERS_TABLE_NAME: ${self:custom.usersBlockTableName}
    FEATURED_SQUAD_TABLE_NAME: ${self:custom.featuredSquadTableName}
    SIGNUP_EMAILS_USERS_TABLE_NAME: ${self:custom.signupEmailsUsersTableName}
    MOBILE_VERSION_MANAGER_TABLE_NAME: ${self:custom.mobileVersionManager}

    # #################################################
    STREAMING_BUCKET: ${env:STREAMING_BUCKET}
    IMAGE_BUCKET: ${env:IMAGE_BUCKET}
    VIDEO_BUCKET: ${env:VIDEO_BUCKET}
    LIVELIKE_CLIENT_ID: ${env:LIVELIKE_CLIENT_ID}
    LIVELIKE_TOKEN: ${env:LIVELIKE_TOKEN}
    SQS_URL: ${env:SQS_URL}
    USER_POOL_ID: ${env:USER_POOL_ID}
    AWS_COGNITO_CLIENT_ID: ${env:AWS_COGNITO_CLIENT_ID}
    IS_LOCAL: ${env:IS_LOCAL}
    SLACK_WEBHOOK_URL: ${env:SLACK_WEBHOOK_URL}
    AWS_BACKEND_LAMBDA_SERVICE_NAME: ${env:AWS_BACKEND_LAMBDA_SERVICE_NAME}
    # #################################################

    PLAYER_AWS_REGION: ${self:provider.region}
    # _COGNITO_CLIENT_SECRET: ${param:cognitoClientSecret}
    # DYNAMODB_ENDPOINT: ${self:custom.endpoints.dynamodbURL}
    # S3_ENDPOINT: ${self:custom.endpoints.s3URL}

  iam:
    role:
      statements:
        - Effect: Allow
          Action:
            - cognito-idp:AdminAddUserToGroup
            - cognito-idp:AdminInitiateAuth
            - cognito-idp:AdminSetUserPassword
            - cognito-idp:AdminConfirmSignUp
            - cognito-idp:AdminCreateUser
            - cognito-idp:AdminDeleteUser
            - cognito-idp:AdminGetUser
            - cognito-idp:AdminDisableProviderForUser
            - cognito-idp:AdminDisableUser
            - cognito-idp:AdminEnableUser
            - cognito-idp:AdminLinkProviderForUser
            - cognito-idp:AdminRemoveUserFromGroup
            - cognito-idp:AdminUpdateUserAttributes
          Resource: "*"
        - Effect: "Allow"
          Action:
            - "dynamodb:Query"
            - "dynamodb:Scan"
            - "dynamodb:GetItem"
            - "dynamodb:PutItem"
            - "dynamodb:UpdateItem"
            - "dynamodb:DeleteItem"
            - "dynamodb:PartiQLSelect"
            - "dynamodb:BatchGetItem"
          Resource:
            - { "Fn::GetAtt": ["UsersTable", "Arn"] }
            - { "Fn::GetAtt": ["ClubsTable", "Arn"] }
            - { "Fn::GetAtt": ["TeamsTable", "Arn"] }
            - { "Fn::GetAtt": ["SeasonsTable", "Arn"] }
            - { "Fn::GetAtt": ["MatchesTable", "Arn"] }
            - { "Fn::GetAtt": ["HighlightTable", "Arn"] }
            - { "Fn::GetAtt": ["LeaguesTableName", "Arn"] }
            - { "Fn::GetAtt": ["ExperienceTableName", "Arn"] }
            - { "Fn::GetAtt": ["P2PChatTableName", "Arn"] }
            - { "Fn::GetAtt": ["P2PUnreadChatTableName", "Arn"] }
            - { "Fn::GetAtt": ["AnnouncementTableName", "Arn"] }
            - { "Fn::GetAtt": ["FollowTableName", "Arn"] }
            - { "Fn::GetAtt": ["ReferenceTableName", "Arn"] }
            - { "Fn::GetAtt": ["ReportsTableName", "Arn"] }
            - { "Fn::GetAtt": ["UserPhysicalDataTableName", "Arn"] }
            - { "Fn::GetAtt": ["FeaturedSquadTableName", "Arn"] }
            - { "Fn::GetAtt": ["UsersBlockTableName", "Arn"] }
            - { "Fn::GetAtt": ["signupEmailsUsersTableName", "Arn"] }
            - { "Fn::GetAtt": ["MobileVersionManager", "Arn"] }
            - "Fn::Join":
                [
                  "/",
                  [
                    "Fn::GetAtt": [HighlightTable, Arn],
                    "index",
                    "userId-index",
                  ],
                ]
            - "Fn::Join":
                [
                  "/",
                  ["Fn::GetAtt": [TeamsTable, Arn], "index", "teams-indexing"],
                ]

            - "Fn::Join":
                [
                  "/",
                  [
                    "Fn::GetAtt": [HighlightTable, Arn],
                    "index",
                    "feedStatus-createdAt-index",
                  ],
                ]

            - "Fn::Join":
                [
                  "/",
                  [
                    "Fn::GetAtt": [HighlightTable, Arn],
                    "index",
                    "feedVideo-timeStamp-index",
                  ],
                ]

            - "Fn::Join":
                [
                  "/",
                  [
                    "Fn::GetAtt": [UsersBlockTableName, Arn],
                    "index",
                    "blockedId-index",
                  ],
                ]

        - Effect: "Allow"
          Action:
            - "s3:CreateBucket"
            - "s3:ListAllMyBuckets"
            - "s3:GetBucketLocation"
            - "s3:PutObject"
            - "s3:PutObjectAcl"
            - "s3:DeleteObject"
            - "s3:GetObject"
            - "s3:ListBucket"
            - "s3:DeleteBucket"
            - "s3:ListBucketMultipartUploads"

          Resource: "*"

        - Effect: "Allow"
          Action:
            - "ses:SendEmail"
          Resource: "*"
        - Effect: "Allow"
          Action:
            - sqs:SendMessage
            - sqs:ReceiveMessage
            - sqs:DeleteMessage
            - sqs:GetQueueUrl
            - sqs:GetQueueAttributes
            - sqs:ListQueues
            - sqs:CreateQueue
            - sqs:DeleteQueue
            - sqs:SetQueueAttributes
            - sqs:ChangeMessageVisibility
          Resource: "*"

custom:
  defaultStage: dev
  usersTable: player-db-users-${self:provider.stage}
  clubsTable: player-db-clubs-${self:provider.stage}
  teamsTable: player-db-teams-${self:provider.stage}
  seasonsTable: player-db-seasons-${self:provider.stage}
  matchesTable: player-db-matchesTable-${self:provider.stage}
  highlightTable: player-db-highlightTable-${self:provider.stage}
  leaguesTableName: player-db-leaguesTableName-${self:provider.stage}
  experienceTableName: player-db-experienceTableName-${self:provider.stage}
  p2pChatTableName: player-db-p2pChatTableName-${self:provider.stage}
  p2pUnreadChatTableName: player-db-p2pUnreadChatTableName-${self:provider.stage}
  announcementTableName: player-db-announcementTableName-${self:provider.stage}
  followTableName: player-db-followTableName-${self:provider.stage}
  referenceTableName: player-db-referenceTableName-${self:provider.stage}
  reportsTableName: player-db-reportsTableName-${self:provider.stage}
  userPhysicalDataTableName: player-db-userPhysicalDataTableName-${self:provider.stage}
  usersBlockTableName: player-db-usersBlockTableName-${self:provider.stage}
  featuredSquadTableName: player-db-featuredSquadTableName-${self:provider.stage}
  signupEmailsUsersTableName: player-db-signupEmailsUsersTableName-${self:provider.stage}
  mobileVersionManager: player-db-mobileVersionManager-${self:provider.stage}

  dynamodb:
    stages:
      - ${self:provider.stage}
    start:
      migrate: true
  endpoints:
    dynamodbURL: "http://localhost:8000"
    s3URL: "http://localhost:4569"

functions:
  main:
    handler: dist/main.handler
    events:
      - http:
          method: ANY
          path: /
      - http:
          method: ANY
          path: "/{proxy+}"
  s3VideoProcessor:
    handler: dist/main.videoOptimizer
    timeout: 900
    description: "Video Optimizer func. using to handle video processing"
    memorySize: 2048
    layers:
      - arn:aws:lambda:${self:provider.region}:563996809753:layer:ffmpeg:2
    # events:
    #   - s3:
    #       bucket: optimized-media-${self:provider.stage}
    #       event: s3:ObjectCreated:*
    #       existing: true
    #       rules:
    #         - prefix: videosOnly/
  processUserDBStream:
    handler: dist/main.processUserDBStream
    events:
      - stream:
          type: dynamodb
          arn:
            Fn::GetAtt: [UsersTable, StreamArn]

resources:
  Resources:
    # WE REMOVED AUTO CREATION OF POOLS. USER POOLS WIL BE CREATED MANUALLY FROM AWS
    UserPool:
      Type: AWS::Cognito::UserPool
      Properties:
        # Generate a name based on the stage
        UserPoolName: player-app-${self:provider.stage}-user-pool
        # Set email as an alias
        Schema:
          - Name: email
            Required: true
            Mutable: true
        Policies:
          PasswordPolicy:
            MinimumLength: 6
        # AutoVerifiedAttributes: ["email"]

    UserClient:
      Type: AWS::Cognito::UserPoolClient
      Properties:
        # Generate an app client name based on the stage
        ClientName: player-app-${self:provider.stage}-user-pool-client
        UserPoolId: { Ref: UserPool }
        ExplicitAuthFlows:
          - ALLOW_USER_PASSWORD_AUTH
          - ALLOW_ADMIN_USER_PASSWORD_AUTH
          - ALLOW_REFRESH_TOKEN_AUTH
        RefreshTokenValidity: 3650
        GenerateSecret: false
        AccessTokenValidity: 1
        IdTokenValidity: 1
        # WriteAttributes:
        #   - "email"
        #   - "custom:email"
        #   - "custom:firstName"
        #   - "custom:lastName"
        #   - "custom:phoneNumber"
        #   - "custom:userType"
        # ReadAttributes:
        #   - "email"
        #   - "custom:email"
        #   - "custom:firstName"
        #   - "custom:lastName"
        #   - "custom:phoneNumber"
        #   - "custom:userType"

    UsersTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:custom.usersTable}

        AttributeDefinitions:
          - AttributeName: id
            AttributeType: S
        KeySchema:
          - AttributeName: id
            KeyType: HASH
        BillingMode: PAY_PER_REQUEST
        StreamSpecification:
          StreamViewType: NEW_AND_OLD_IMAGES

    ClubsTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:custom.clubsTable}
        AttributeDefinitions:
          - AttributeName: id
            AttributeType: S

        KeySchema:
          - AttributeName: id
            KeyType: HASH
        BillingMode: PAY_PER_REQUEST

    TeamsTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:custom.teamsTable}
        AttributeDefinitions:
          - AttributeName: id
            AttributeType: S
          - AttributeName: clubId
            AttributeType: S

        KeySchema:
          - AttributeName: id
            KeyType: HASH
          - AttributeName: clubId
            KeyType: RANGE
        BillingMode: PAY_PER_REQUEST

        GlobalSecondaryIndexes:
          - IndexName: teams-indexing
            KeySchema:
              - AttributeName: clubId
                KeyType: HASH
              - AttributeName: id
                KeyType: RANGE
            Projection:
              ProjectionType: ALL

    SeasonsTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:custom.seasonsTable}
        AttributeDefinitions:
          - AttributeName: id
            AttributeType: S

        KeySchema:
          - AttributeName: id
            KeyType: HASH
        BillingMode: PAY_PER_REQUEST

    MatchesTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:custom.matchesTable}
        AttributeDefinitions:
          - AttributeName: id
            AttributeType: S

        KeySchema:
          - AttributeName: id
            KeyType: HASH
        BillingMode: PAY_PER_REQUEST

    HighlightTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:custom.highlightTable}
        AttributeDefinitions:
          - AttributeName: id
            AttributeType: S
          - AttributeName: userId
            AttributeType: S
          - AttributeName: updatedAt
            AttributeType: S
          - AttributeName: createdAt
            AttributeType: S
          - AttributeName: feedStatus
            AttributeType: S
          - AttributeName: timeStamp
            AttributeType: S

        KeySchema:
          - AttributeName: id
            KeyType: HASH
          - AttributeName: userId
            KeyType: RANGE

        GlobalSecondaryIndexes:
          - IndexName: userId-index
            KeySchema:
              - AttributeName: userId
                KeyType: HASH
            Projection:
              ProjectionType: ALL

          - IndexName: updated-at
            KeySchema:
              - AttributeName: id
                KeyType: HASH
              - AttributeName: updatedAt
                KeyType: RANGE
            Projection:
              ProjectionType: ALL

          - IndexName: feedStatus-createdAt-index
            KeySchema:
              - AttributeName: feedStatus
                KeyType: HASH
              - AttributeName: createdAt
                KeyType: RANGE
            Projection:
              ProjectionType: ALL

          - IndexName: feedVideo-timeStamp-index
            KeySchema:
              - AttributeName: timeStamp
                KeyType: HASH
              - AttributeName: feedStatus
                KeyType: RANGE
            Projection:
              ProjectionType: ALL
        BillingMode: PAY_PER_REQUEST

    LeaguesTableName:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:custom.leaguesTableName}
        AttributeDefinitions:
          - AttributeName: id
            AttributeType: S

        KeySchema:
          - AttributeName: id
            KeyType: HASH
        BillingMode: PAY_PER_REQUEST

    ExperienceTableName:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:custom.experienceTableName}
        AttributeDefinitions:
          - AttributeName: id
            AttributeType: S

        KeySchema:
          - AttributeName: id
            KeyType: HASH
        BillingMode: PAY_PER_REQUEST

    P2PChatTableName:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:custom.p2pChatTableName}
        AttributeDefinitions:
          - AttributeName: id
            AttributeType: S

        KeySchema:
          - AttributeName: id
            KeyType: HASH
        BillingMode: PAY_PER_REQUEST

    P2PUnreadChatTableName:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:custom.p2pUnreadChatTableName}
        AttributeDefinitions:
          - AttributeName: id
            AttributeType: S

        KeySchema:
          - AttributeName: id
            KeyType: HASH
        BillingMode: PAY_PER_REQUEST

    AnnouncementTableName:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:custom.announcementTableName}
        AttributeDefinitions:
          - AttributeName: id
            AttributeType: S

        KeySchema:
          - AttributeName: id
            KeyType: HASH
        BillingMode: PAY_PER_REQUEST

    ReferenceTableName:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:custom.referenceTableName}
        AttributeDefinitions:
          - AttributeName: id
            AttributeType: S

        KeySchema:
          - AttributeName: id
            KeyType: HASH
        BillingMode: PAY_PER_REQUEST

    ReportsTableName:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:custom.reportsTableName}
        AttributeDefinitions:
          - AttributeName: id
            AttributeType: S

        KeySchema:
          - AttributeName: id
            KeyType: HASH
        BillingMode: PAY_PER_REQUEST

    UserPhysicalDataTableName:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:custom.userPhysicalDataTableName}
        AttributeDefinitions:
          - AttributeName: id
            AttributeType: S

        KeySchema:
          - AttributeName: id
            KeyType: HASH
        BillingMode: PAY_PER_REQUEST

    FeaturedSquadTableName:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:custom.featuredSquadTableName}
        AttributeDefinitions:
          - AttributeName: id
            AttributeType: S

        KeySchema:
          - AttributeName: id
            KeyType: HASH
        BillingMode: PAY_PER_REQUEST

    UsersBlockTableName:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:custom.usersBlockTableName}
        AttributeDefinitions:
          - AttributeName: id
            AttributeType: S
          - AttributeName: blockedId
            AttributeType: S
          - AttributeName: createdAt
            AttributeType: S

        KeySchema:
          - AttributeName: id
            KeyType: HASH
        GlobalSecondaryIndexes:
          - IndexName: blockedId-index
            KeySchema:
              - AttributeName: blockedId
                KeyType: HASH
              - AttributeName: createdAt
                KeyType: RANGE
            Projection:
              ProjectionType: ALL

        BillingMode: PAY_PER_REQUEST

    signupEmailsUsersTableName:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:custom.signupEmailsUsersTableName}
        AttributeDefinitions:
          - AttributeName: id
            AttributeType: S

        KeySchema:
          - AttributeName: id
            KeyType: HASH
        BillingMode: PAY_PER_REQUEST

    FollowTableName:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:custom.followTableName}
        AttributeDefinitions:
          - AttributeName: id
            AttributeType: S
          - AttributeName: follow
            AttributeType: S

        KeySchema:
          - AttributeName: id
            KeyType: HASH
          - AttributeName: follow
            KeyType: RANGE
        BillingMode: PAY_PER_REQUEST

    MobileVersionManager:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:custom.mobileVersionManager}
        AttributeDefinitions:
          - AttributeName: id
            AttributeType: S
          - AttributeName: createdAt
            AttributeType: N
        KeySchema:
          - AttributeName: id
            KeyType: HASH
          - AttributeName: createdAt
            KeyType: RANGE
        BillingMode: PAY_PER_REQUEST
    # HighlightsVideoBucket:
    #   Type: AWS::S3::Bucket
    #   Properties:
    #     BucketName: highlights-video-bucket
    #     AccessControl: PublicRead

    # HighlightsPhotoBucket:
    #   Type: AWS::S3::Bucket
    #   Properties:
    #     BucketName: highlights-photo-bucket
    #     AccessControl: PublicRead
