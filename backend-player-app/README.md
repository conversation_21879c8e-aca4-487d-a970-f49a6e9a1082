## Player app backend

## Details

[Nest](https://github.com/nestjs/nest) framework TypeScript

## Getting started

### Install dependencies

`yarn install`

### Configure env variables

Copy `.env.example` to `.env` and fill in the values.

### Configure serverless

#### Login

`yarn run serverless login`

#### Point to local database

Ensure `IS_LOCAL=true` in `.env`.

#### Install dynamodb

`sls dynamodb install`

If you encounter an issue here, you can resolve it by changing `http` to `https` in this path "node_modules/serverless-dynamodb-local/node_modules/dynamodb-localhost/dynamodb/installer.js" and running the command again:
`sls dynamodb install`

### Configure [AWS CLI](https://www.serverless.com/framework/docs/providers/aws/guide/credentials/)

To set them up through the aws-cli install it first then run `aws configure` to configure the aws-cli and credentials:

```
$ aws configure
AWS Access Key ID [None]: AKIAIOSFODNN7EXAMPLE
AWS Secret Access Key [None]: wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY
Default region name [None]: us-west-2
Default output format [None]: ENTER
```

Credentials are stored in INI format in ~/.aws/credentials, which you can edit directly if needed. You can change the path to the credentials file via the AWS_SHARED_CREDENTIALS_FILE environment variable. Read more about that file in the [AWS documentation](http://docs.aws.amazon.com/cli/latest/userguide/cli-chap-getting-started.html#cli-config-files)

## Running the app

```bash
# development
$ yarn run start

# watch mode
$ yarn run start:dev

# production mode
$ yarn run start:prod
```

### Troubleshooting

In anycase, the app is not starting because of dynamodb you can put the following line in the custom section of serverless.yml file:

```bash
custom:
  dynamodb:
    stages:
      - local
```

## Test

```bash
# unit tests
$ yarn run test

# e2e tests
$ yarn run test:e2e

# test coverage
$ yarn run test:cov
```

## Deploy

**To deploy to development**, squash and merge your branch or PR into the `development` branch. A GitHub action will automatically deploy the changes.

**To deploy to production**, merge the `development` branch into `main`. Then manually trigger the deployment in the GitHub Actions tab.
