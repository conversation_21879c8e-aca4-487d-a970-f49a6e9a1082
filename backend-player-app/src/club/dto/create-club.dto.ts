import {
  Is<PERSON><PERSON>y,
  IsBoolean,
  IsNotE<PERSON>y,
  IsO<PERSON>al,
  IsString,
} from 'class-validator';

export class CreateClubDto {
  @IsString()
  @IsOptional()
  id?: string;

  @IsString()
  @IsNotEmpty()
  clubLogoUrl: string;

  @IsString()
  @IsOptional()
  typeOfOrg?: 'CLUB' | 'COMPANY';

  @IsOptional()
  claimedStatus?: boolean;

  @IsString()
  managerId: string;

  @IsArray()
  @IsOptional()
  teamsIds: [];

  @IsString()
  @IsNotEmpty()
  clubName: string;

  @IsBoolean()
  @IsNotEmpty()
  isPrivate: boolean;
}
