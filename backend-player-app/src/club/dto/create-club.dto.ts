import {
  Is<PERSON><PERSON>y,
  IsBoolean,
  IsN<PERSON>Empty,
  IsOptional,
  IsString,
} from 'class-validator';

export const enum TypeOfOrg {
  CLUB = 'CLUB',
  COMPANY = 'COMPANY',
}

export class CreateClubDto {
  @IsString()
  @IsOptional()
  id?: string;

  @IsString()
  @IsNotEmpty()
  clubLogoUrl: string;

  @IsString()
  @IsOptional()
  typeOfOrg?: TypeOfOrg;

  @IsOptional()
  claimedStatus?: boolean;

  @IsString()
  managerId: string;

  @IsArray()
  @IsOptional()
  teamsIds: [];

  @IsString()
  @IsNotEmpty()
  clubName: string;

  @IsBoolean()
  @IsNotEmpty()
  isPrivate: boolean;
}
