import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { CreateClubDto, TypeOfOrg } from './dto/create-club.dto';
import { UpdateClubDto } from './dto/update-club.dto';
import { dbConfig } from 'src/config';
import { envVars } from 'src/config/constants';
import { randomUUID } from 'crypto';
import { response } from 'src/utils/response';
import { ReturnValue } from '@aws-sdk/client-dynamodb';
import { dbPartialQueryExecution } from 'src/config/database';
import { CreateTeamDto } from 'src/teams/dto/create-team.dto';
import { TeamsService } from 'src/teams/teams.service';
import { UserType } from 'src/users/dto/create-user.dto';

@Injectable()
export class ClubService {
  dbName = envVars.dynamoDB.CLUBS_TABLE_NAME;

  constructor(private readonly teamsService: TeamsService) {}

  async create(createClubDto: CreateClubDto) {
    try {
      const id = randomUUID();
      const data = await dbConfig.writeItemsToDatabase(
        envVars.dynamoDB.CLUBS_TABLE_NAME,
        {
          ...createClubDto,
          id,
          isPrivate: createClubDto.isPrivate || false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      );

      return response({
        message: 'Club created successfully',
        status: 1,
        data: { ...data, id, input: createClubDto },
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async findAll({ isPrivate, userType = UserType.PLAYER }) {
    try {
      const statement = `SELECT * FROM \"${
        envVars.dynamoDB.CLUBS_TABLE_NAME
      }\" WHERE \"isPrivate\" = ${Boolean(isPrivate) || false}`;

      const statementIgnorePrivate = `SELECT * FROM \"${envVars.dynamoDB.CLUBS_TABLE_NAME}\"`;

      const data = await dbPartialQueryExecution(
        isPrivate === undefined ? statementIgnorePrivate : statement,
      );

      if (userType === UserType.PLAYER) {
        // Filter out all companies in this list
        return response({
          message: 'Success',
          status: 1,
          data: {
            Items: data.Items?.filter(
              (item) =>
                !Boolean(item?.typeOfOrg) ||
                Boolean(item?.typeOfOrg !== TypeOfOrg.COMPANY),
            ),
            lastEvaluatedKey: data?.LastEvaluatedKey || null,
          },
        });
      }

      return response({ message: 'Success', status: 1, data });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async findOne(id: string) {
    try {
      const data = await dbConfig.getItemFromDatabase(
        envVars.dynamoDB.CLUBS_TABLE_NAME,
        { id },
      );

      return response({ message: 'Success', status: 1, data });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async update(updateClubDto: UpdateClubDto) {
    const { id, ...others } = updateClubDto;
    const dbName = envVars.dynamoDB.CLUBS_TABLE_NAME;

    try {
      const { Item: existingClubData } = await dbConfig.getItemFromDatabase(
        dbName,
        {
          id,
        },
      );

      const updateParams = {
        UpdateExpression:
          'set clubName = :cn, isPrivate = :pv, claimedStatus = :cSts, clubLogoUrl = :clUrl, updatedAt = :uat, #ts = list_append(#ts, :ts)',
        ExpressionAttributeValues: {
          ':cn': others.clubName || existingClubData?.clubName || '',
          ':pv': others.isPrivate || existingClubData?.isPrivate || false,
          ':cSts':
            others.claimedStatus || existingClubData?.claimedStatus || '',
          ':clUrl': others.clubLogoUrl || existingClubData?.clubLogoUrl || '',
          ':ts': others.teamsIds || existingClubData?.teamsIds || [],
          ':uat': new Date().toISOString(),
        },
        ExpressionAttributeNames: {
          '#ts': 'teamsIds',
        },
      };

      const data = await dbConfig.updateItemsToDatabase(
        dbName,
        { id },
        updateParams,
        ReturnValue.ALL_NEW,
      );

      return response({
        message: 'Updated successfully',
        status: 1,
        data: data.Attributes,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async remove(id: string) {
    try {
      const data = await dbConfig.removeItemFromDatabase(
        envVars.dynamoDB.CLUBS_TABLE_NAME,
        { id },
      );

      return response({ message: 'Deleted successfully', status: 1, data });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async createClubAndTeams(
    data: Array<CreateClubDto> | Array<Partial<CreateTeamDto>>,
  ) {
    if (data.length > 20) {
      return response({
        message: 'Maximum 10 clubs and teams allowed',
        status: 0,
        data: null,
      });
    }

    try {
      const listOfClubPromises = await Promise.all(
        data.map((teamAndClubData: CreateClubDto) => {
          return this.create({
            clubLogoUrl: teamAndClubData?.clubLogoUrl,
            clubName: teamAndClubData.clubName,
            isPrivate: teamAndClubData?.isPrivate,
            managerId: teamAndClubData?.managerId,
            teamsIds: teamAndClubData?.teamsIds,
          });
        }),
      );
      console.log(listOfClubPromises);

      const listOfTeamsPromises = await Promise.all(
        listOfClubPromises.map(
          (clubResponse: {
            status: number;
            data: { id: string; input: CreateClubDto };
            message: string;
          }) => {
            if (clubResponse.status !== 0) {
              return this.teamsService.create({
                clubId: clubResponse.data?.id,
                isPrivate: false,
                logoUrl: clubResponse.data?.input.clubLogoUrl,
                teamName: clubResponse.data?.input.clubName,
              });
            }
          },
        ),
      );

      return response({
        data: listOfTeamsPromises,
        message: 'Clubs and teams created successfully',
        status: 1,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async fixTeamIssues(teamName: string, clubId: string) {
    // Find the UIs clubs and remove them
    // Then use the clubId to find the teams and update their clubId
    try {
      const listOfClubs = await dbConfig.getAllItemFromDatabase(this.dbName, {
        TableName: this.dbName,
        FilterExpression: 'contains(#tN, :query)',
        ExpressionAttributeValues: {
          ':query': teamName,
        },
        ExpressionAttributeNames: {
          '#tN': 'clubName',
        },
      });

      listOfClubs.Items.forEach(async (club) => {
        const params = {
          TableName: envVars.dynamoDB.TEAMS_TABLE_NAME,
          FilterExpression: '#cbId = :query',
          ExpressionAttributeValues: {
            ':query': club.id,
          },
          ExpressionAttributeNames: {
            '#cbId': 'clubId',
          },
        };

        const foundTeam = await dbConfig.getAllItemFromDatabase(
          envVars.dynamoDB.TEAMS_TABLE_NAME,
          params,
        );

        // update team table with new clubId but then since clubId is sort key is it possible to update ?
        if (foundTeam.Items.length > 0) {
          foundTeam.Items.forEach(async (team: CreateTeamDto) => {
            await this.teamsService.remove(team.id, team.clubId);
          });

          foundTeam.Items.forEach(async (team: CreateTeamDto) => {
            await this.teamsService.create({ ...team, clubId });
          });
        }
      });

      listOfClubs.Items.forEach(async (club) => {
        await this.remove(club.id);
      });

      return response({
        data: null,
        message: 'Clubs and teams created successfully',
        status: 1,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }
}
