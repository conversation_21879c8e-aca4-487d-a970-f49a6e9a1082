import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Put,
  Query,
} from '@nestjs/common';
import { ClubService } from './club.service';
import { CreateClubDto } from './dto/create-club.dto';
import { UpdateClubDto } from './dto/update-club.dto';
import { CreateTeamDto } from 'src/teams/dto/create-team.dto';

@Controller('club')
export class ClubController {
  constructor(private readonly clubService: ClubService) {}

  @Post()
  create(@Body() createClubDto: CreateClubDto) {
    return this.clubService.create(createClubDto);
  }

  @Post('create-club-teams')
  createClubsWithTeams(
    @Body() createClubDto: Array<CreateClubDto> | Array<Partial<CreateTeamDto>>,
  ) {
    return this.clubService.createClubAndTeams(createClubDto);
  }

  // @Post('fix-issues')
  // fixIssues(@Body() body: { teamName: string; clubId: string }) {
  //   return this.clubService.fixTeamIssues(body.teamName, body.clubId);
  // }

  @Get()
  findAll(
    @Query('isPrivate') isPrivate: string,
    @Query('type') userType: string,
  ) {
    return this.clubService.findAll({ isPrivate, userType });
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.clubService.findOne(id);
  }

  @Put('update')
  update(@Body() updateClubDto: UpdateClubDto) {
    return this.clubService.update(updateClubDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.clubService.remove(id);
  }
}
