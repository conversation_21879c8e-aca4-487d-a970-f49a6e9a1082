import {
  MiddlewareConsumer,
  Module,
  NestModule,
  RequestMethod,
} from '@nestjs/common';
import { ClubService } from './club.service';
import { ClubController } from './club.controller';
import { TeamsService } from 'src/teams/teams.service';
import { AuthMiddleware } from 'src/auth/guards/auth.middleware';

@Module({
  controllers: [ClubController],
  providers: [ClubService, TeamsService],
})
// export class ClubModule {}
export class ClubModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(AuthMiddleware).forRoutes('club');
  }
}
