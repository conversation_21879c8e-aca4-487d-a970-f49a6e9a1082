import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { CreateMatchDto } from './dto/create-match.dto';
import { UpdateMatchDto } from './dto/update-match.dto';
import { envVars } from 'src/config/constants';
import { randomUUID } from 'crypto';
import { dbConfig } from 'src/config';
import { SeasonsService } from 'src/seasons/seasons.service';
import { response } from 'src/utils/response';
import { getAllItemFromDatabase } from 'src/config/database';
import { ScanCommandInput } from '@aws-sdk/lib-dynamodb';
import { PUBLISHSTATUS } from 'src/leagues/dto/create-league.dto';

@Injectable()
export class MatchesService {
  constructor(private readonly seasonsService: SeasonsService) {}

  dbName = envVars.dynamoDB.MATCHES_TABLE_NAME;

  async create(createMatchDto: CreateMatchDto) {
    const dbParams = {
      ...createMatchDto,
      matchHighLights: createMatchDto.matchHighLights || [],
      homeTeam: createMatchDto.homeTeam || null,
      awayTeam: createMatchDto.awayTeam || null,
      publishStatus: PUBLISHSTATUS.DRAFT,
      id: randomUUID(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    try {
      // save match to db
      const data = await dbConfig.writeItemsToDatabase(this.dbName, dbParams);

      // update the season table with the matches and associated teams
      if (data.$metadata.httpStatusCode < 400) {
        try {
          await this.seasonsService.update({
            id: createMatchDto.seasonId,
            matches: [dbParams],
            teamsIds: [
              createMatchDto.awayTeam.data.teamId,
              createMatchDto.homeTeam.data.teamId,
            ],
          });
        } catch (error) {
          Logger.error(error.message);
          throw new InternalServerErrorException(
            response({
              message: `${error.message}. Although the match was created but not updated in season`,
              status: 0,
              data: null,
            }),
          );
        }
      }

      return response({
        message: 'Match created successfully',
        status: 1,
        data,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async findAll() {
    try {
      const queryParams: ScanCommandInput = {
        TableName: this.dbName,
        ExpressionAttributeValues: {
          ':null': null,
        },
        FilterExpression:
          '(homeTeam.score <> :null) and (awayTeam.score <> :null)',
      };

      const data = await dbConfig.getAllItemFromDatabase(
        this.dbName,
        queryParams,
      );

      return response({ message: 'Success', status: 1, data });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async getMatchesBySeasonId(seasonId, teamId) {
    try {
      const queryParams: ScanCommandInput = {
        TableName: this.dbName,
        ExpressionAttributeNames: { '#ssID': 'seasonId', '#data': 'data' },
        ExpressionAttributeValues: {
          ':ssID': seasonId,
          ':teamsId': teamId,
          ':null': null,
        },
        FilterExpression: seasonId
          ? '(#ssID = :ssID and homeTeam.#data.teamId = :teamsId and homeTeam.score <> :null) or (awayTeam.#data.teamId = :teamsId and #ssID = :ssID and awayTeam.score <> :null)'
          : '(homeTeam.#data.teamId = :teamsId and homeTeam.score <> :null)  or  (awayTeam.#data.teamId = :teamsId and awayTeam.score <> :null) ',
      };

      if (!seasonId) {
        delete queryParams.ExpressionAttributeValues[':ssID'];
        delete queryParams.ExpressionAttributeNames['#ssID'];
      }
      const data = await getAllItemFromDatabase(this.dbName, queryParams);

      return response({
        message: 'Successfully',
        status: 1,
        data: data.Items,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async getFixturesByTeam(teamId) {
    try {
      const queryParams: ScanCommandInput = {
        TableName: this.dbName,
        ExpressionAttributeNames: { '#data': 'data' },
        ExpressionAttributeValues: {
          ':teamId': teamId,
          ':null': null,
        },
        FilterExpression:
          '(homeTeam.#data.teamId = :teamId and homeTeam.score = :null) or  (awayTeam.#data.teamId = :teamId and awayTeam.score = :null)',
      };

      const data = await getAllItemFromDatabase(this.dbName, queryParams);

      return response({
        message: 'Success',
        status: 1,
        data: data.Items,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async remove(id: string) {
    try {
      const data = await dbConfig.removeItemFromDatabase(this.dbName, {
        id,
      });

      return response({ message: 'Deleted successfully', status: 1, data });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }
}
