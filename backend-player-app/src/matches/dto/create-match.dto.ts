import {
  IsA<PERSON>y,
  IsBoolean,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
export interface PlayersLineUps {
  players: Array<any>;
  substitutes: Array<any>;
}

export interface MatchHighLights {
  url: string;
  comment: string;
}

export interface MatchSquad {
  data: {
    teamId: string;
    name: string;
    logo: string;
  };
  score: number;
  goalScorers: Array<any>;
  assists: Array<any>;
  playersOfTheMatch: Array<any>;
  lineUPs: PlayersLineUps;
}

export class CreateMatchDto {
  @IsString()
  @IsOptional()
  id?: string;

  @IsString()
  @IsUUID()
  @IsNotEmpty()
  seasonId: string;

  @IsString()
  @IsNotEmpty()
  seasonTitle: string;

  @IsString()
  location: string;

  @IsString()
  dateTimePlayed: string;

  @IsObject()
  awayTeam: MatchSquad;

  @IsObject()
  homeTeam: MatchSquad;

  @IsString()
  @IsOptional()
  createdAt?: string;

  @IsString()
  @IsOptional()
  updatedAt?: string;

  @IsArray()
  matchHighLights: Array<MatchHighLights>;
}
