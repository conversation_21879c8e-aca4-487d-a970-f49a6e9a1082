import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { MatchesService } from './matches.service';
import { MatchesController } from './matches.controller';
import { SeasonsService } from 'src/seasons/seasons.service';
import { TeamsService } from 'src/teams/teams.service';
import { AdminAuthMiddleware } from 'src/auth/guards/auth.middleware';

@Module({
  controllers: [MatchesController],
  providers: [MatchesService, SeasonsService, TeamsService],
})
// export class MatchesModule {}
export class MatchesModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(AdminAuthMiddleware).forRoutes('matches'); // Blocking this routes with admin for now until we need them
  }
}