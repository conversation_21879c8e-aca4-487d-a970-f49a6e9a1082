import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from '@nestjs/common';
import { MatchesService } from './matches.service';
import { CreateMatchDto } from './dto/create-match.dto';
import { UpdateMatchDto } from './dto/update-match.dto';

@Controller('matches')
export class MatchesController {
  constructor(private readonly matchesService: MatchesService) {}

  @Post()
  create(@Body() createMatchDto: CreateMatchDto) {
    return this.matchesService.create(createMatchDto);
  }

  @Post('fixtures')
  createFixtures(@Body() createMatchDto: CreateMatchDto) {
    return this.matchesService.create(createMatchDto);
  }

  @Get('fixtures')
  getFixturesByTeam(@Query('teamId') teamId: string) {
    return this.matchesService.getFixturesByTeam(teamId);
  }

  @Get()
  findAll() {
    return this.matchesService.findAll();
  }

  @Get('by-season-team')
  getMatchesBySeasonIdTeamId(
    @Query('seasonId') seasonId: string,
    @Query('teamId') teamId: string,
  ) {
    return this.matchesService.getMatchesBySeasonId(seasonId, teamId);
  }

  // @Patch(':id')
  // update(@Param('id') id: string, @Body() updateMatchDto: UpdateMatchDto) {
  //   return this.matchesService.update(+id, updateMatchDto);
  // }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.matchesService.remove(id);
  }
}
