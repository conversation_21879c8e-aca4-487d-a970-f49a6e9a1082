import {
  IsArray,
  IsBoolean,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUrl,
} from 'class-validator';

export class CreateTeamDto {
  @IsString()
  @IsOptional()
  id?: string;

  @IsString()
  @IsNotEmpty()
  clubId: string;

  @IsString()
  @IsNotEmpty()
  teamName: string;

  @IsBoolean()
  @IsNotEmpty()
  isPrivate: boolean;

  @IsUrl()
  @IsNotEmpty()
  logoUrl: string;

  @IsArray()
  playersIds?: Array<string>;

  @IsArray()
  seasonsIds?: Array<string>;

  @IsArray()
  seasonsStats?: Array<any>;

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;
}
