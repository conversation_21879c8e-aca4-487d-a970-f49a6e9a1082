import {
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { CreateTeamDto } from './dto/create-team.dto';
import { UpdateTeamDto } from './dto/update-team.dto';
import { randomUUID } from 'crypto';
import { response } from 'src/utils/response';
import { dbConfig } from 'src/config';
import { envVars } from 'src/config/constants';
import { ReturnValue } from '@aws-sdk/client-dynamodb';
import {
  dbPartialQueryExecution,
  getAllItemFromDatabase,
  queryItemFromDatabase,
} from 'src/config/database';
import { ScanCommandInput } from '@aws-sdk/lib-dynamodb';
import _groupBy from 'lodash.groupby';

@Injectable()
export class TeamsService {
  dbName = envVars.dynamoDB.TEAMS_TABLE_NAME;

  async create(createTeamDto: CreateTeamDto) {
    const dbParams = {
      ...createTeamDto,
      playersIds: createTeamDto.playersIds || [],
      seasonsIds: createTeamDto.seasonsIds || [],
      seasonsStats: createTeamDto.seasonsStats || [],
      isPrivate: createTeamDto.isPrivate || false,
      isActive: createTeamDto.isActive || true,
      id: createTeamDto.id || randomUUID(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    try {
      const data = await dbConfig.writeItemsToDatabase(this.dbName, dbParams);

      return response({
        message: 'Team created successfully',
        status: 1,
        data: { ...data, id: dbParams.id },
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async findAllWithPrivate(clubId: string, isPrivate) {
    try {
      if (clubId) {
        return this.getTeamsByClubID(clubId);
      }
      const statement = `SELECT * FROM \"${
        this.dbName
      }\" WHERE \"isPrivate\" = ${Boolean(isPrivate) || false} `;

      const statementIgnorePrivate = `SELECT * FROM \"${this.dbName}\"`;

      const data = await dbPartialQueryExecution(
        isPrivate === undefined ? statement : statementIgnorePrivate,
      );

      return response({ message: 'Success', status: 1, data });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async findAllTeams() {
    try {
      const data = await dbConfig.getAllItemFromDatabase(this.dbName);

      return response({ message: 'Success', status: 1, data });
    } catch (error) {
      console.log(error, 'there is an error');
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async findOne({ id, clubId }: { id: string; clubId: string }) {
    try {
      const data = await dbConfig.getItemFromDatabase(this.dbName, {
        id,
        clubId,
      });

      const { Item: clubData } = await dbConfig.getItemFromDatabase(
        envVars.dynamoDB.CLUBS_TABLE_NAME,
        {
          id: clubId,
        },
      );

      return response({
        message: 'Success',
        status: 1,
        data: { ...data, clubData },
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async update(updateTeamDto: UpdateTeamDto) {
    const { id, clubId } = updateTeamDto;

    try {
      const { Item: existingTeamData } = await dbConfig.getItemFromDatabase(
        this.dbName,
        {
          id,
          clubId,
        },
      );

      const uniqueSeasonsIds = new Set([
        ...(existingTeamData.seasonsIds || []),
        ...(updateTeamDto.seasonsIds || []),
      ]);

      const uniquePlayerIds = new Set([
        ...(existingTeamData.playersIds || []),
        ...(updateTeamDto.playersIds || []),
      ]);

      const updateParams = {
        UpdateExpression:
          'set isActive = :isA, teamName = :tn, isPrivate = :pv, logoUrl = :lUrl, updatedAt = :uat, #ss = :ss, #plys = :plys, seasonsStats = list_append(#seasonsStats, :seasonsStats)',
        ExpressionAttributeValues: {
          ':tn': updateTeamDto.teamName || existingTeamData?.teamName || '',
          ':lUrl': updateTeamDto.logoUrl || existingTeamData?.logoUrl || '',
          ':pv':
            updateTeamDto.isPrivate || existingTeamData?.isPrivate || false,
          ':plys': Array.from(uniquePlayerIds) || [],
          ':seasonsStats':
            updateTeamDto.seasonsStats || existingTeamData?.seasonsStats || [],
          ':ss': Array.from(uniqueSeasonsIds) || [],
          ':isA': updateTeamDto.isActive || existingTeamData?.isActive || true,
          ':uat': new Date().toISOString(),
        },
        ExpressionAttributeNames: {
          '#ss': 'seasonsIds',
          '#plys': 'playersIds',
          '#seasonsStats': 'seasonsStats',
        },
      };

      const data = await dbConfig.updateItemsToDatabase(
        this.dbName,
        { id, clubId },
        updateParams,
        ReturnValue.ALL_NEW,
      );

      return response({
        message: 'Updated successfully',
        status: 1,
        data: data.Attributes,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async getTeamsByClubID(clubId) {
    try {
      const allTeamsFromClub = await queryItemFromDatabase(this.dbName, {
        TableName: this.dbName,
        IndexName: 'teams-indexing', // GSI name
        KeyConditionExpression: 'clubId = :clubId',
        ExpressionAttributeValues: {
          ':clubId': clubId,
        },
      });

      return response({
        message: 'Successfully',
        status: 1,
        data: allTeamsFromClub.Items,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async getPlayersByTeamId(teamId: string, groupBy?: string) {
    try {
      const queryParams: ScanCommandInput = {
        TableName: envVars.dynamoDB.USERS_TABLE_NAME,
        ExpressionAttributeNames: { '#teamId': 'teamId' },
        ExpressionAttributeValues: {
          ':teamId': teamId,
        },
        FilterExpression: '#teamId = :teamId',
      };
      const data = await getAllItemFromDatabase(
        envVars.dynamoDB.USERS_TABLE_NAME,
        queryParams,
      );

      if (!data.Items) {
        throw new NotFoundException(
          response({
            message:
              'The team Id is not found or no players belongs to this team',
            status: 0,
            data: null,
          }),
        );
      }

      let groupedData;
      if (groupBy) {
        groupedData = _groupBy(data.Items, (item) => item[groupBy]);
      }

      return response({
        message: 'Successfully',
        status: 1,
        data: groupBy ? groupedData : data.Items,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async findAllByProjection({
    projectionExpression,
  }: {
    projectionExpression: string;
  }) {
    const projectionArray = projectionExpression
      ? projectionExpression.replace(' ', '').split(',')
      : 'id,clubId,teamName,logoUrl,leagueName'.replace(' ', '').split(',');

    const expressionAttributeNames = projectionArray?.reduce((acc, curr) => {
      acc[`#${curr}`] = curr;
      return acc;
    }, {});

    try {
      const data = await dbConfig.getAllItemFromDatabase(this.dbName, {
        ProjectionExpression: Object.keys(expressionAttributeNames).join(','),
        ExpressionAttributeNames: expressionAttributeNames,
        TableName: this.dbName,
      });

      return response({ message: 'Success', data, status: 1 });
    } catch (error) {
      console.log('error', error);
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({
          message: `${error.message}. Also make sure you formed your projectionExpression very well. see example: \n 'id,clubId,teamName,logoUrl,leagueName'`,
          status: 0,
          data: null,
        }),
      );
    }
  }

  async searchTeams({ query }: { query: string }) {
    if (query?.length === 0) {
      return response({ message: 'Success', data: [], status: 1 });
    }

    try {
      const data = await dbConfig.getAllItemFromDatabase(this.dbName, {
        TableName: this.dbName,
        FilterExpression:
          'contains(#tN, :query) OR contains(#lU, :query) OR contains(#lNm, :query) OR contains(#tN, :query2) OR contains(#lU, :query2) OR contains(#lNm, :query2) ',
        ExpressionAttributeNames: {
          '#tN': 'teamName',
          '#lU': 'logoUrl',
          '#lNm': 'leagueName',
        },
        ExpressionAttributeValues: {
          ':query':
            String(query).charAt(0).toUpperCase() +
            String(query).slice(1).toLowerCase(),
          ':query2': String(query).toLowerCase(),
        },
      });

      return response({ message: 'Success', data: data.Items, status: 1 });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async remove(id: string, clubId: string) {
    try {
      const data = await dbConfig.removeItemFromDatabase(this.dbName, {
        id,
        clubId,
      });

      return response({ message: 'Deleted successfully', status: 1, data });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }
}
