import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Query,
  Put,
} from '@nestjs/common';
import { TeamsService } from './teams.service';
import { CreateTeamDto } from './dto/create-team.dto';
import { UpdateTeamDto } from './dto/update-team.dto';

@Controller('teams')
export class TeamsController {
  constructor(private readonly teamsService: TeamsService) {}

  @Post()
  create(@Body() createTeamDto: CreateTeamDto) {
    return this.teamsService.create(createTeamDto);
  }

  @Get()
  findAllWithPrivate(
    @Query('clubId') clubId: string,
    @Query('isPrivate') isPrivate: string,
  ) {
    return this.teamsService.findAllWithPrivate(clubId, isPrivate);
  }

  @Get('get-by-projection')
  findAllByProjection(@Query('projectionExpression') projectionExpression) {
    return this.teamsService.findAllByProjection({ projectionExpression });
  }

  @Get('search')
  searchUsers(@Query('query') query) {
    return this.teamsService.searchTeams({ query });
  }

  @Get('all')
  findAllTeams() {
    return this.teamsService.findAllTeams();
  }

  @Get('players')
  getPlayersByTeamId(
    @Query('teamId') teamId: string,
    @Query('groupBy') groupBy: string,
  ) {
    return this.teamsService.getPlayersByTeamId(teamId, groupBy);
  }

  @Get(':id')
  findOne(@Param('id') id: string, @Query('clubId') clubId: string) {
    return this.teamsService.findOne({ id, clubId });
  }

  @Put('update')
  update(@Body() updateTeamDto: UpdateTeamDto) {
    return this.teamsService.update(updateTeamDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string, @Query('clubId') clubId) {
    return this.teamsService.remove(id, clubId);
  }
}
