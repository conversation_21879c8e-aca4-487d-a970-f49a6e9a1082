import {
  MiddlewareConsumer,
  Module,
  NestModule,
  RequestMethod,
} from '@nestjs/common';
import { TeamsService } from './teams.service';
import { TeamsController } from './teams.controller';
import {
  AdminAuthMiddleware,
  AuthMiddleware,
} from 'src/auth/guards/auth.middleware';

@Module({
  controllers: [TeamsController],
  providers: [TeamsService],
})
export class TeamsModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(AuthMiddleware)
      .forRoutes('teams')
      .apply(AdminAuthMiddleware)
      .forRoutes(
        {
          path: 'team/:id',
          method: RequestMethod.DELETE,
        },
        {
          path: 'teams',
          method: RequestMethod.POST,
        },
        {
          path: 'teams/update',
          method: RequestMethod.PUT,
        },
      );
  }
}
