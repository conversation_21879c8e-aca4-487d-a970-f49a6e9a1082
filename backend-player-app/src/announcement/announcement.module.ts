import {
  MiddlewareConsumer,
  Module,
  NestModule,
  RequestMethod,
} from '@nestjs/common';
import {
  AdminAuthMiddleware,
  AuthMiddleware,
} from 'src/auth/guards/auth.middleware';
import { CommentsModule } from '../comments/comments.module';
import { AnnouncementController } from './announcement.controller';
import { AnnouncementService } from './announcement.service';

@Module({
  imports: [CommentsModule],
  controllers: [AnnouncementController],
  providers: [AnnouncementService],
})
// export class AnnouncementModule {}
export class AnnouncementModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(AdminAuthMiddleware)
      .forRoutes(
        { method: RequestMethod.POST, path: 'announcement' },
        { method: RequestMethod.DELETE, path: 'announcement' },
      )
      .apply(AuthMiddleware)
      .forRoutes({ method: RequestMethod.PUT, path: 'announcement' });
  }
}
