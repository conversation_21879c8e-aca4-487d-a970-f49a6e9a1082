import {
  MiddlewareConsumer,
  Module,
  NestModule,
  RequestMethod,
} from '@nestjs/common';
import { AnnouncementService } from './announcement.service';
import { AnnouncementController } from './announcement.controller';
import { AdminAuthMiddleware, AuthMiddleware } from 'src/auth/guards/auth.middleware';

@Module({
  controllers: [AnnouncementController],
  providers: [AnnouncementService],
})
// export class AnnouncementModule {}
export class AnnouncementModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(AdminAuthMiddleware)
      .forRoutes(
        { method: RequestMethod.POST, path: 'announcement' },
        { method: RequestMethod.DELETE, path: 'announcement' },
      )
      .apply(AuthMiddleware)
      .forRoutes({ method: RequestMethod.PUT, path: 'announcement' });
  }
}
