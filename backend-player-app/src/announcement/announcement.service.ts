import { ReturnValue } from '@aws-sdk/client-dynamodb';
import { GetObjectCommand, GetObjectCommandInput } from '@aws-sdk/client-s3';
import { UpdateCommandInput } from '@aws-sdk/lib-dynamodb';
import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { randomUUID } from 'crypto';
import { dbConfig } from 'src/config';
import { envVars } from 'src/config/constants';
import { s3Client } from 'src/config/s3Config';
import { VIDEO_STATUS } from 'src/highlights/dto/create-highlight.dto';
import { response } from 'src/utils/response';
import { CommentsService } from '../comments/comments.service';
import {
  ANNOUNCEMENT_TYPE,
  CreateAnnouncementDto,
} from './dto/create-announcement.dto';
import { UpdateAnnouncementDto } from './dto/update-announcement.dto';

@Injectable()
export class AnnouncementService {
  private readonly dbName: string = envVars.dynamoDB.ANNOUNCEMENT_TABLE_NAME;

  constructor(private readonly commentsService: CommentsService) {}

  public checkIfAnnoumcementExpired = (data) => {
    const currentDate = new Date().getTime();
    const endDate = new Date(data.endDateTime).getTime();

    if (currentDate > endDate) {
      return true;
    }
    return false;
  };

  public checkIfAnnoumcementIsActive = (data) => {
    const currentDate = new Date().getTime();
    const startDate = new Date(data.startDateTime).getTime();
    const endDate = new Date(data.endDateTime).getTime();

    if (currentDate >= startDate && currentDate <= endDate) {
      return true;
    }
    return false;
  };

  private async validateUploadStatus(streamUrl, videoTimeStamp) {
    let queueProcessed = false;
    let videoStatus = VIDEO_STATUS.PROCESSING;
    // let videoTimeStamp = '';
    // Get the video object from S3 and see if it exists
    if (streamUrl?.key) {
      const s3Params: GetObjectCommandInput = {
        Bucket: envVars.aws.streamingBucket,
        Key: `${videoTimeStamp}/thumbnail.png`,
      };
      // videoTimeStamp = streamUrl.key.split('--')[1];
      try {
        const s3Response = await s3Client.send(new GetObjectCommand(s3Params));
        if (s3Response.Body) {
          queueProcessed = true;
          videoStatus = VIDEO_STATUS.SUCCESSFUL;
        }
      } catch (error) {
        console.log('======S3 OBJECT NOT FOUND========', error);
      }
    }
    return { queueProcessed, videoStatus };
  }

  async create({
    createAnnouncementDto,
    user,
  }: {
    createAnnouncementDto: CreateAnnouncementDto;
    user: Record<string, any>;
  }) {
    if (
      createAnnouncementDto.startDateTime > createAnnouncementDto.endDateTime
    ) {
      throw new BadRequestException(
        response({
          message: 'End date must be greater than Start date',
          status: 0,
          data: null,
        }),
      );
    }

    try {
      const newAnnouncementId = randomUUID();
      const now = new Date().toISOString();
      const payload = {
        ...createAnnouncementDto,
        id: newAnnouncementId,
        createdAt: now,
        updatedAt: now,
        reactedByUsers: [],
      };

      const commentBoardId = await this.commentsService.createCommentBoard({
        customId: newAnnouncementId,
        user,
      });
      payload.commentBoardId = commentBoardId;

      if (
        createAnnouncementDto?.streamUrl?.key &&
        createAnnouncementDto.announcementType !== ANNOUNCEMENT_TYPE.VOTE
      ) {
        const { queueProcessed, videoStatus } = await this.validateUploadStatus(
          createAnnouncementDto.streamUrl,
          createAnnouncementDto?.videoTimeStamp,
        );

        payload.queueProcessed = queueProcessed;
        payload.videoStatus = videoStatus;
      } else if (
        createAnnouncementDto.announcementType === ANNOUNCEMENT_TYPE.VOTE
      ) {
        const videoData = [];
        for (const item of createAnnouncementDto.voteSubmittedAssets) {
          const { queueProcessed, videoStatus } =
            await this.validateUploadStatus(
              item.streamUrl,
              item.videoTimeStamp,
            );
          videoData.push({
            ...item,
            queueProcessed,
            videoStatus,
          });
        }

        payload.voteSubmittedAssets = videoData;
      }

      // TODO: Check if the item is already processed in the s3 stream and then toggle the queue processed to true
      const data = await dbConfig.writeItemsToDatabase(this.dbName, payload);

      return response({
        message: 'Success',
        data,
        status: 1,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async findAll({
    limit = 10,
    lastEvaluatedKey,
  }: {
    limit?: number;
    lastEvaluatedKey?: Record<string, string>;
  }) {
    try {
      const data = await dbConfig.getAllItemFromDatabase(this.dbName, {
        Limit: +limit,
        TableName: this.dbName,
        ExclusiveStartKey: lastEvaluatedKey,
      });

      return response({
        message: 'Success',
        data: {
          ...data,
          Items: data.Items?.map((item) => ({
            ...item,
            expired: this.checkIfAnnoumcementExpired(item),
            isActive: this.checkIfAnnoumcementIsActive(item),
          })),
        },
        status: 1,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async findOne({ id }: { id: string }) {
    try {
      const data = await dbConfig.getItemFromDatabase(this.dbName, {
        id,
      });

      return response({
        message: 'Success',
        status: 1,
        data: {
          ...data,
          Item: {
            ...data.Item,
            expired: this.checkIfAnnoumcementExpired(data.Item),
            isActive: this.checkIfAnnoumcementIsActive(data.Item),
          },
        },
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async update(updateAnnouncementDto: UpdateAnnouncementDto) {
    try {
      const { id, ...others } = updateAnnouncementDto;

      const { Item: existingData } = await dbConfig.getItemFromDatabase(
        this.dbName,
        {
          id,
        },
      );

      if (!existingData?.id) {
        throw new BadRequestException(
          response({
            message: 'Not a valid update, provided ID is not found',
            status: 0,
            data: null,
          }),
        );
      }

      let payload: UpdateAnnouncementDto;
      // Get the video object from S3 and see if it exists
      if (
        others?.streamUrl?.key &&
        others.announcementType !== ANNOUNCEMENT_TYPE.VOTE
      ) {
        const { queueProcessed, videoStatus } = await this.validateUploadStatus(
          others.streamUrl,
          others?.videoTimeStamp,
        );

        payload = {
          ...others,
          queueProcessed,
          videoStatus,
        };
      } else if (others.announcementType === ANNOUNCEMENT_TYPE.VOTE) {
        const videoData = [];
        for (const item of others.voteSubmittedAssets) {
          const { queueProcessed, videoStatus } =
            await this.validateUploadStatus(
              item.streamUrl,
              item.videoTimeStamp,
            );
          videoData.push({
            ...item,
            queueProcessed,
            videoStatus,
          });
        }

        payload = {
          ...others,
          voteSubmittedAssets: videoData,
        };
      } else {
        payload = others;
      }

      const updateParams: Partial<UpdateCommandInput> = {
        UpdateExpression: `set #typeOfAsset = :ty, 
                                #assetUrl= :assetUrl, 
                                updatedAt = :uat, 
                                #title = :title, 
                                commentBoardId = :cmBId, 
                                videoTimeStamp = :videoTimeStamp, 
                                videoStatus = :videoStatus, 
                                totalCommentCount = :totalCoCt, 
                                startDateTime = :startDateTime, 
                                endDateTime = :endDateTime, 
                                subtitle = :subtitle,
                                userId = :userId, 
                                logoUrl = :logoUrl,
                                streamUrl = :streamUrl,
                                reactedByUsers = :reactedByUsers,
                                announcementType = :announcementType,
                                voteSubmittedAssets = :voteSubmittedAssets,
                                winnerHandle = :winnerHandle,
                                queueProcessed = :queueProcessed`,

        ExpressionAttributeValues: {
          ':ty': payload?.type || existingData?.type || '',
          ':totalCoCt':
            payload?.totalCommentCount || existingData?.totalCommentCount || 0,
          ':assetUrl': payload?.assetUrl || existingData?.assetUrl || '',
          ':announcementType':
            payload?.announcementType || existingData?.announcementType || '',
          ':voteSubmittedAssets':
            payload?.voteSubmittedAssets ||
            existingData?.voteSubmittedAssets ||
            [],
          ':winnerHandle':
            payload?.winnerHandle || existingData?.winnerHandle || {},
          ':queueProcessed': Boolean(payload?.queueProcessed),
          ':videoTimeStamp':
            payload?.videoTimeStamp || existingData?.videoTimeStamp || 0,
          ':title': payload?.title || existingData?.title || '',
          ':cmBId':
            payload?.commentBoardId || existingData?.commentBoardId || '',
          ':videoStatus':
            payload?.videoStatus ||
            existingData?.videoStatus ||
            VIDEO_STATUS.PROCESSING,
          ':endDateTime':
            payload?.endDateTime || existingData?.endDateTime || '',
          ':startDateTime':
            payload?.startDateTime || existingData?.startDateTime || '',
          ':subtitle': payload?.subtitle || existingData?.subtitle || '',
          ':userId': payload?.userId || existingData?.userId || '',
          ':logoUrl': payload?.logoUrl || existingData?.logoUrl || '',
          ':streamUrl': payload?.streamUrl || existingData?.streamUrl || '',
          ':reactedByUsers': payload?.reactedByUsers?.length
            ? payload.reactedByUsers
            : existingData?.reactedByUsers || [],
          ':uat': new Date().toISOString(),
        },
        ExpressionAttributeNames: {
          '#typeOfAsset': 'type',
          '#assetUrl': 'assetUrl',
          '#title': 'title',
        },
      };

      const data = await dbConfig.updateItemsToDatabase(
        this.dbName,
        { id },
        updateParams,
        ReturnValue.ALL_NEW,
      );

      return response({
        message: 'Announcement updated successfully',
        data: {
          ...data,
          Attributes: {
            ...data.Attributes,
            expired: this.checkIfAnnoumcementExpired(data.Attributes),
            isActive: this.checkIfAnnoumcementIsActive(data.Attributes),
          },
        },
        status: 1,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async remove({ id }: { id: string }) {
    if (!id) {
      throw new BadRequestException(
        response({
          message: 'id not provided',
          status: 0,
          data: null,
        }),
      );
    }
    const highlight = await this.findOne({ id });

    if (!highlight.data) {
      throw new NotFoundException(
        response({
          message: 'id not found',
          status: 0,
          data: null,
        }),
      );
    }
    try {
      const data = await dbConfig.removeItemFromDatabase(this.dbName, {
        id,
      });

      return response({ message: 'Deleted successfully', status: 1, data });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }
}
