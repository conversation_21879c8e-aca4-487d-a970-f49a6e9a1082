import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Request,
} from '@nestjs/common';
import { AnnouncementService } from './announcement.service';
import { CreateAnnouncementDto } from './dto/create-announcement.dto';
import { UpdateAnnouncementDto } from './dto/update-announcement.dto';

@Controller('announcement')
export class AnnouncementController {
  constructor(private readonly announcementService: AnnouncementService) {}

  @Post()
  create(
    @Body() createAnnouncementDto: CreateAnnouncementDto,
    @Request() req: any,
  ) {
    return this.announcementService.create({
      createAnnouncementDto,
      user: req.user,
    });
  }

  @Post('get')
  findAll(
    @Body()
    announcementQueryDetails: {
      limit?: number;
      lastEvaluatedKey?: Record<string, string>;
    },
  ) {
    return this.announcementService.findAll({
      limit: announcementQueryDetails.limit,
      lastEvaluatedKey: announcementQueryDetails.lastEvaluatedKey,
    });
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.announcementService.findOne({ id });
  }

  @Put(':id')
  update(@Body() updateAnnouncementDto: UpdateAnnouncementDto) {
    return this.announcementService.update(updateAnnouncementDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.announcementService.remove({ id });
  }
}
