import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>E<PERSON>,
  IsNotEmpty,
  <PERSON>N<PERSON>ber,
  IsOptional,
  IsString,
  IsUUID,
  IsUrl,
} from 'class-validator';
import {
  ASSEST_TYPE,
  VIDEO_STATUS,
} from 'src/highlights/dto/create-highlight.dto';

export enum ANNOUNCEMENT_TYPE {
  ANNOUNCEMENT = 'ANNOUNCEMENT',
  COMPETITION = 'COMPETITION',
  VOTE = 'VOTE',
  WINNER = 'WINNER',
  PINNED_POST = 'PINNED_POST',
}

export interface VOTE_SUBMITTED_ASSETS {
  streamUrl: { key: string; baseUrl: string };
  assetType: ASSEST_TYPE;
  userId: string;
  userName?: string;
  orderIndex: number;
  shouldNotifyUser?: boolean;
  text?: string;
  videoTimeStamp?: string;
}

export class CreateAnnouncementDto {
  @IsUUID()
  @IsOptional()
  id: string;

  @IsString()
  @IsUrl()
  @IsOptional()
  assetUrl: string;

  @IsString()
  @IsOptional()
  userId: string;

  @IsString()
  @IsNotEmpty()
  title: string;

  @IsString()
  @IsOptional()
  subtitle: string;

  @IsString()
  @IsOptional()
  commentBoardId: string;

  @IsNumber()
  @IsOptional()
  totalCommentCount: number;

  @IsEnum(ASSEST_TYPE, { message: 'type can be either (VIDEO | PHOTO' })
  type: ASSEST_TYPE;

  @IsNumber()
  @IsOptional()
  startDateTime: number;

  @IsNumber()
  @IsOptional()
  endDateTime: number;

  @IsArray()
  @IsOptional()
  reactedByUsers?: object[];

  @IsString()
  @IsUrl()
  @IsOptional()
  logoUrl?: string;

  @IsOptional()
  winnerHandle?: any;

  @IsOptional()
  streamUrl?: any;

  @IsOptional()
  videoTimeStamp?: string;

  @IsEnum(ANNOUNCEMENT_TYPE, {
    message:
      'type can be either (COMPETITION | VOTE | WINNER | ANNOUNCEMENT | PINNED_POST)',
  })
  @IsOptional()
  announcementType?: ANNOUNCEMENT_TYPE;

  @IsArray()
  @IsOptional()
  voteSubmittedAssets?: VOTE_SUBMITTED_ASSETS[];

  @IsOptional()
  videoStatus?: VIDEO_STATUS;

  @IsOptional()
  updatedAt: string;

  @IsOptional()
  queueProcessed: boolean;
}
