import { ReturnValue } from '@aws-sdk/client-dynamodb';
import { envVars } from './config/constants';
import { dbPartialQueryExecution } from './config/database';
import { dbConfig } from './config';

export const handleOnUserTableChange = async (event: any, context) => {
  for (const record of event.Records) {
    if (record.eventName === 'MODIFY') {
      const newImage = record.dynamodb.NewImage;
      const oldImage = record.dynamodb.OldImage;

      // Ensure the profile photo changed
      if (
        newImage.photoUrl.S !== oldImage.photoUrl.S ||
        newImage.lastName.S !== oldImage.lastName.S ||
        newImage.firstName.S !== oldImage.firstName.S
      ) {
        const userId = newImage.id.S;
        const photoUrl = newImage.photoUrl.S;
        const lastName = newImage.lastName.S;
        const firstName = newImage.firstName.S;

        try {
          await updateReferencesByUserId({
            userId,
            photoUrl,
            lastName,
            firstName,
          });
          console.log(`Updated References table for user ${userId}`);
          return {
            statusCode: 200,
            body: `Updated References table for user ${userId}`,
          };
        } catch (error) {
          console.error('Error updating References table:', error);
          return { statusCode: 500, body: 'Error updating References table' };
        }
      }
    }
  }
};

const updateReferencesByUserId = async ({
  userId,
  lastName,
  firstName,
  photoUrl,
}) => {
  const dbName = envVars.dynamoDB.REFERENCE_TABLE_NAME;
  try {
    const statement = `select * from \"${dbName}\"  where \"fromProfileId\" = '${userId}'`;
    const statement2 = `select * from \"${dbName}\"  where \"toProfileId\" = '${userId}'`;

    const given = await dbPartialQueryExecution(statement);
    const received = await dbPartialQueryExecution(statement2);

    const allUserReferences = [...given.Items, ...received.Items];

    if (allUserReferences.length > 0) {
      for (const reference of allUserReferences) {
        const copyOfReference = { ...reference };
        if (copyOfReference.fromProfileId === userId) {
          copyOfReference.fromProfilePhoto = photoUrl;
          copyOfReference.fromProfileName = firstName + ' ' + lastName;
        } else if (copyOfReference.toProfileId === userId) {
          copyOfReference.toProfilePhoto = photoUrl;
          copyOfReference.toProfileName = firstName + ' ' + lastName;
        }
        await updateReference(copyOfReference);
      }
    }
  } catch (error) {
    console.log(error);
    throw new Error('Error while getting references');
  }
};

async function updateReference(reference: any) {
  const dbName = envVars.dynamoDB.REFERENCE_TABLE_NAME;

  const { id, ...ref } = reference;

  try {
    const updateExpression = Object.keys(ref)
      .map((key) => `#${key} = :${key}`)
      .join(', ');

    const expressionAttributeValues = Object.keys(ref).reduce((acc, key) => {
      acc[`:${key}`] = ref[key];
      return acc;
    }, {});
    const expressionAttributeNames = Object.keys(ref).reduce((acc, key) => {
      acc[`#${key}`] = key;
      return acc;
    }, {});

    const updateParams = {
      UpdateExpression: `set ${updateExpression}`,
      ExpressionAttributeValues: expressionAttributeValues,
      ExpressionAttributeNames: expressionAttributeNames,
    };

    const result = await dbConfig.updateItemsToDatabase(
      dbName,
      { id },
      updateParams,
      ReturnValue.ALL_NEW,
    );

    console.log('UPDATED REFERENCE');

    return result;
  } catch (error) {
    console.log(error);
    throw new Error(error);
  }
}
