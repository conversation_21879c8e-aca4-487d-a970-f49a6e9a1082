import { Controller, Get, Post, Body } from '@nestjs/common';
import { ReportsService } from './reports.service';
import { CreateReportDto } from './dto/create-report.dto';
import { OwnerOrAdminCanAccess } from 'src/shared/decorators/shouldAllowAccess';

@Controller('reports')
export class ReportsController {
  constructor(private readonly reportsService: ReportsService) {}

  @Post()
  @OwnerOrAdminCanAccess('reporterUserId')
  create(@Body() createReportDto: CreateReportDto) {
    return this.reportsService.create(createReportDto);
  }

  @Get('all-reports')
  findAll() {
    return this.reportsService.findAll();
  }
}
