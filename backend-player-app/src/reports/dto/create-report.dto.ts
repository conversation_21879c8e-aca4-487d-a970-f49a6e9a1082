import { IsEnum, IsObject, IsOptional, IsString } from 'class-validator';

enum REPORT_TYPE {
  MESSAGE = 'MESSAGE',
  COMMENT = 'COMMENT',
  POST = 'POST',
  USER = 'USER',
}

export class CreateReportDto {
  @IsString()
  @IsOptional()
  id: string;

  @IsString()
  reporterUserId: string;

  @IsString()
  reporterFullName: string;

  @IsString()
  reportedUserId: string;

  @IsString()
  reportedFullName: string;

  @IsString()
  @IsOptional()
  reason: string;

  @IsString()
  @IsOptional()
  contendId: string;

  @IsString()
  reportedContent: string; // The content being reported. can be message, comment, post. in the case of a user

  @IsString()
  @IsOptional()
  createdAt: string;

  @IsString()
  @IsOptional()
  updatedAt: string;

  @IsString()
  @IsOptional()
  reportedPhotoUrl: string;

  @IsString()
  @IsOptional()
  reporterPhotoUrl: string;

  @IsString()
  @IsEnum(REPORT_TYPE)
  reportType: REPORT_TYPE;

  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;
}
