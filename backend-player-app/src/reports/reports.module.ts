import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { ReportsService } from './reports.service';
import { ReportsController } from './reports.controller';
import { SlackNotifierService } from 'src/utils/slackNotifier';
import {
  AdminAuthMiddleware,
  AuthMiddleware,
} from 'src/auth/guards/auth.middleware';

@Module({
  controllers: [ReportsController],
  providers: [ReportsService, SlackNotifierService],
})

export class ReportsModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(AuthMiddleware)
      .forRoutes('reports')
      .apply(AdminAuthMiddleware)
      .forRoutes('reports/all-reports');
  }
}
