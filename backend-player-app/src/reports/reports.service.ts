import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { CreateReportDto } from './dto/create-report.dto';
import { envVars } from 'src/config/constants';
import { dbConfig } from 'src/config';
import { randomUUID } from 'crypto';
import { response } from 'src/utils/response';
import { SlackNotifierService } from 'src/utils/slackNotifier';
import { getClientSideDomainUrl } from 'src/utils/helpers';

@Injectable()
export class ReportsService {
  dbName = envVars.dynamoDB.REPORTS_TABLE_NAME;

  constructor(private readonly slackNotifier: SlackNotifierService) {}

  async create(createReportDto: CreateReportDto) {
    try {
      const data = await dbConfig.writeItemsToDatabase(this.dbName, {
        ...createReportDto,
        id: randomUUID(),
        updatedAt: new Date().toISOString(),
        createdAt: new Date().toISOString(),
      });

      this.sendReportToSlack(createReportDto);

      return response({ message: 'Success', data, status: 1 });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async sendReportToSlack(createReportDto: CreateReportDto) {
    let contentUrl;

    const {
      reportType = '',
      reporterUserId,
      reporterFullName,
      reportedFullName,
      reportedUserId,
      contendId,
      reportedContent,
      metadata = {},
    } = createReportDto;

    const baseUrl = getClientSideDomainUrl();

    switch (reportType) {
      case 'POST':
        contentUrl = `${baseUrl}/user/comment?highlightId=${contendId}&userId=${
          metadata?.userId || reportedUserId
        }`;
        break;

      case 'COMMENT':
        contentUrl = metadata?.highlightId
          ? `${baseUrl}/user/comment?highlightId=${
              metadata?.highlightId
            }&userId=${metadata?.userId || reportedUserId}`
          : metadata?.announcementId
          ? `${baseUrl}/user/announcement?announcementId=${metadata?.announcementId}`
          : '';
        break;

      // case 'MESSAGE':
      case 'USER':
        contentUrl = `${baseUrl}/profile?id=${reportedUserId}`;
        break;

      default:
        contentUrl = '';
        break;
    }

    const reportNotifications = [
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*Reporter Full Name*: <${baseUrl}/profile?id=${reporterUserId}|${reporterFullName}>`,
        },
      },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*Reported Full Name*: <${baseUrl}/profile?id=${reportedUserId}|${reportedFullName}>`,
        },
      },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*Reported Content*: ${reportedContent}`,
        },
      },
      ...(contentUrl && [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `*Reported Content URL*: <${contentUrl}|${contentUrl}>`,
          },
        },
      ]),
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*Report Type*: ${reportType}`,
        },
      },
    ];

    const slackNotificationBlocks = [
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: `:bell: Report Notification from ${reporterFullName} for ${reportedFullName}`,
          emoji: true,
        },
      },
      ...reportNotifications,
    ];

    await this.slackNotifier.sendBlocks(slackNotificationBlocks);
  }

  async findAll() {
    try {
      const data = await dbConfig.getAllItemFromDatabase(this.dbName);

      return response({
        message: 'Success',
        data: data.Items,
        status: 1,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  // findOne(id: number) {
  //   return `This action returns a #${id} report`;
  // }

  // update(id: number, updateReportDto: UpdateReportDto) {
  //   return `This action updates a #${id} report`;
  // }

  // remove(id: number) {
  //   return `This action removes a #${id} report`;
  // }
}
