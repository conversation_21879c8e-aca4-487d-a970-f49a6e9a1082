import { Test, TestingModule } from '@nestjs/testing';
import { MobileVersionManagerService } from './mobile-version-manager.service';

describe('MobileVersionManagerService', () => {
  let service: MobileVersionManagerService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [MobileVersionManagerService],
    }).compile();

    service = module.get<MobileVersionManagerService>(
      MobileVersionManagerService,
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
