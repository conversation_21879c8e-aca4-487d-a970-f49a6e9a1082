import { Controller, Get, Post, Body, Param, Delete } from '@nestjs/common';
import { MobileVersionManagerService } from './mobile-version-manager.service';
import {
  CreateMobileVersionManagerDto,
  PLATFORM_TYPE,
} from './dto/create-mobile-version-manager.dto';
import { CurrentProfile } from 'src/shared/decorators/currentProfile';
import { DeleteMobileVersionManagerDto } from './dto/delete-mobile-version-manager.dto';

@Controller('mobile-version-manager')
export class MobileVersionManagerController {
  constructor(
    private readonly mobileVersionManagerService: MobileVersionManagerService,
  ) {}

  @Post()
  create(
    @Body() createMobileVersionManagerDto: CreateMobileVersionManagerDto,
    @CurrentProfile() user,
  ) {
    return this.mobileVersionManagerService.create({
      ...createMobileVersionManagerDto,
      updatedByAdminId: user.id,
    });
  }

  @Get('/all-versions')
  findAll() {
    return this.mobileVersionManagerService.findAll();
  }

  @Get('/:platformType')
  getLatest(@Param('platformType') platformType: PLATFORM_TYPE) {
    return this.mobileVersionManagerService.getLatestVersion(platformType);
  }

  @Delete()
  remove(@Body() playload: DeleteMobileVersionManagerDto[]) {
    return this.mobileVersionManagerService.remove(playload);
  }
}
