import { Test, TestingModule } from '@nestjs/testing';
import { MobileVersionManagerController } from './mobile-version-manager.controller';
import { MobileVersionManagerService } from './mobile-version-manager.service';

describe('MobileVersionManagerController', () => {
  let controller: MobileVersionManagerController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [MobileVersionManagerController],
      providers: [MobileVersionManagerService],
    }).compile();

    controller = module.get<MobileVersionManagerController>(
      MobileVersionManagerController,
    );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
