import { IsEnum, <PERSON>NotEmpty, IsOptional, IsString } from 'class-validator';

export enum PLATFORM_TYPE {
  IOS = 'IOS',
  ANDROID = 'ANDROID',
}

export class CreateMobileVersionManagerDto {
  @IsNotEmpty()
  @IsString()
  version: string;

  @IsEnum(PLATFORM_TYPE, {
    message: 'Platform type must be either IOS or ANDROID',
  })
  platformType: PLATFORM_TYPE;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  updatedByAdminId?: string;
}
