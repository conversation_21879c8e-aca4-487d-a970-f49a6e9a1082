import { MiddlewareConsumer, Module, NestModule, RequestMethod } from '@nestjs/common';
import { MobileVersionManagerService } from './mobile-version-manager.service';
import { MobileVersionManagerController } from './mobile-version-manager.controller';
import {
  AdminAuthMiddleware,
  AuthMiddleware,
} from 'src/auth/guards/auth.middleware';

@Module({
  controllers: [MobileVersionManagerController],
  providers: [MobileVersionManagerService],
})
export class MobileVersionManagerModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(AdminAuthMiddleware).forRoutes(
      {
        method: RequestMethod.POST,
        path: 'mobile-version-manager',
      },
      {
        method: RequestMethod.DELETE,
        path: 'mobile-version-manager',
      },
    );
    consumer.apply(AuthMiddleware).forRoutes({
      method: RequestMethod.GET,
      path: 'mobile-version-manager',
    });
  }
}
