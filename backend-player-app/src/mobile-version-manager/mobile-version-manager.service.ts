import {
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import {
  CreateMobileVersionManagerDto,
  PLATFORM_TYPE,
} from './dto/create-mobile-version-manager.dto';
import { dbConfig } from 'src/config';
import { envVars } from 'src/config/constants';
import { response } from 'src/utils/response';

@Injectable()
export class MobileVersionManagerService {
  dbName = envVars.dynamoDB.MOBILE_VERSION_MANAGER_TABLE_NAME;
  async create(createMobileVersionManagerDto: CreateMobileVersionManagerDto) {
    try {
      const data = await dbConfig.writeItemsToDatabase(this.dbName, {
        ...createMobileVersionManagerDto,
        id: createMobileVersionManagerDto.platformType,
        createdAt: Date.now(),
      });

      return response({ message: 'Success', data, status: 1 });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async getLatestVersion(platformType: PLATFORM_TYPE) {
    try {
      const result = await dbConfig.queryItemFromDatabase(this.dbName, {
        TableName: this.dbName,
        KeyConditionExpression: 'id = :platform',
        ExpressionAttributeValues: {
          ':platform': platformType,
        },
        ScanIndexForward: false,
        Limit: 1,
      });

      if (!result.Items?.[0]) {
        return new NotFoundException(
          response({
            message: 'No version found',
            status: 0,
            data: null,
          }),
        );
      }

      return response({
        message: 'Success',
        data: result.Items?.[0],
        status: 1,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async findAll() {
    try {
      const result = await dbConfig.getAllItemFromDatabase(this.dbName);
      return response({ data: result.Items, message: 'success', status: 1 });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async remove(
    payload: {
      platformType: PLATFORM_TYPE;
      createdAt: number;
    }[],
  ) {
    try {
      const delObj = payload.map((item) => {
        return dbConfig.removeItemFromDatabase(this.dbName, {
          id: item.platformType,
          createdAt: item.createdAt,
        });
      });

      const result = await Promise.all(delObj);

      return response({
        message: 'Success',
        data: result,
        status: 1,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }
}
