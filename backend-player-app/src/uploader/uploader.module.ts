import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { UploaderService } from './uploader.service';
import { UploaderController } from './uploader.controller';
import { AuthMiddleware } from 'src/auth/guards/auth.middleware';

@Module({
  controllers: [UploaderController],
  providers: [UploaderService],
})
export class UploaderModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(AuthMiddleware).forRoutes('uploader');
  }
}
