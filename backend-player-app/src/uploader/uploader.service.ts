import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { CreateUploaderDto } from './dto/create-uploader.dto';
import { s3Client } from 'src/config/s3Config';
import {
  CreateMultipartUploadCommand,
  UploadPartCommand,
  CompleteMultipartUploadCommand,
  PutObjectCommand,
  AbortMultipartUploadCommand,
  ObjectCannedACL,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { response } from 'src/utils/response';
import { envVars } from 'src/config/constants';

@Injectable()
export class UploaderService {
  currentAcl =
    envVars.environment === 'dev'
      ? ObjectCannedACL.public_read
      : ObjectCannedACL.bucket_owner_full_control;

  async create(createUploaderDto: CreateUploaderDto) {
    const {
      bucket,
      key,
      contentType,
      metaData,
      isMultiPart,
      partNumber,
      uploadId,
      totalParts,
    } = createUploaderDto;

    if (!isMultiPart) {
      const putParams = {
        Bucket: bucket,
        Key: key,
        ACL: this.currentAcl,
        ContentType: contentType,
        Metadata: metaData,
      };

      try {
        const command = new PutObjectCommand(putParams);
        const signedUrl = await getSignedUrl(s3Client, command, {
          expiresIn: 3600,
        });

        return response({
          status: 1,
          message: 'Signed URL generated successfully',
          data: {
            url: signedUrl,
            isMultiPart: false,
            expiresIn: Date.now() + 3600 * 1000,
          },
        });
      } catch (error) {
        Logger.error(error.message);
        throw new InternalServerErrorException(
          response({ message: error.message, status: 0, data: null }),
        );
      }
    }
    try {
      if (!uploadId && isMultiPart) {
        const createMultipartParams = {
          Bucket: bucket,
          Key: key,
          ACL: this.currentAcl,
          ContentType: contentType,
          Metadata: metaData,
        };
        const createCommand = new CreateMultipartUploadCommand(
          createMultipartParams,
        );
        const multiPartresponse = await s3Client.send(createCommand);
        return response({
          status: 1,
          message: 'Upload ID generated successfully',
          data: {
            uploadId: multiPartresponse.UploadId,
            key: multiPartresponse.Key,
            isMultiPart: true,
          },
        });
      }

      if (
        uploadId &&
        partNumber &&
        isMultiPart &&
        !createUploaderDto?.completedParts
      ) {
        const uploadPartCommand = new UploadPartCommand({
          Bucket: bucket,
          Key: key,
          PartNumber: partNumber,
          UploadId: uploadId,
          ContentLength: createUploaderDto.contentLength,
        });

        const signedUrl = await getSignedUrl(s3Client, uploadPartCommand, {
          expiresIn: 3600,
        });

        return response({
          status: 1,
          message: 'Multipart Signed URL generated successfully',
          data: {
            url: signedUrl,
            isMultiPart: true,
            partNumber,
            uploadId,
            expiresIn: Date.now() + 3600 * 1000,
          },
        });
      }

      if (
        uploadId &&
        isMultiPart &&
        createUploaderDto.completedParts &&
        createUploaderDto.completedParts.length >= totalParts
      ) {
        const completeCommand = new CompleteMultipartUploadCommand({
          Bucket: bucket,
          Key: key,
          UploadId: uploadId,
          MultipartUpload: {
            Parts: createUploaderDto.completedParts,
          },
        });

        const completeResult = await s3Client.send(completeCommand);
        return response({
          message: 'File uploaded successfully',
          status: 1,
          data: {
            message: 'Multipart upload completed',
            location: completeResult.Location,
            bucket: completeResult.Bucket,
            key: completeResult.Key,
            etag: completeResult.ETag,
          },
        });
      }

      return response({
        message: 'File upload failed',
        status: 0,
        data: null,
      });
    } catch (error) {
      console.log(error);

      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async abordMultipartUpload(details: Partial<CreateUploaderDto>) {
    try {
      // Abort the multipart upload if something goes wrong
      await s3Client.send(
        new AbortMultipartUploadCommand({
          Bucket: details.bucket,
          Key: details.key,
          UploadId: details.uploadId,
        }),
      );
      return response({
        message: 'File aborted successfully',
        status: 1,
        data: null,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }
}
