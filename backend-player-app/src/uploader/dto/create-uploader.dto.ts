import { ObjectCannedACL } from '@aws-sdk/client-s3';
import { IsBoolean, IsOptional, IsString } from 'class-validator';

export class CreateUploaderDto {
  @IsString()
  bucket: string;

  @IsString()
  key: string;

  @IsOptional()
  @IsString()
  contentType?: string;

  @IsOptional()
  metaData?: Record<string, string>;

  @IsBoolean()
  isMultiPart: boolean;

  @IsOptional()
  partNumber?: number;

  @IsOptional()
  uploadId?: string;

  @IsOptional()
  contentLength?: number;

  @IsOptional()
  completedParts?: { ETag: string; PartNumber: number }[];

  @IsOptional()
  totalParts?: number;
}
