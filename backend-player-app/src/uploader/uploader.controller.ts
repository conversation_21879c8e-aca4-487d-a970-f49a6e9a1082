import { <PERSON>, Post, Body } from '@nestjs/common';
import { UploaderService } from './uploader.service';
import { CreateUploaderDto } from './dto/create-uploader.dto';

@Controller('uploader')
export class UploaderController {
  constructor(private readonly uploaderService: UploaderService) {}

  @Post('get-upload-url')
  create(@Body() createUploaderDto: CreateUploaderDto) {
    return this.uploaderService.create(createUploaderDto);
  }

  @Post('abort-upload')
  abordMultipartUpload(@Body() uploaderDto: Partial<CreateUploaderDto>) {
    return this.uploaderService.abordMultipartUpload(uploaderDto);
  }
}
