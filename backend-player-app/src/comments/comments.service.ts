import LiveLike from '@livelike/javascript';
import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import dayjs from 'dayjs';
import { response } from 'src/utils/response';
import { initLiveLike } from '../utils/LiveLike';

@Injectable()
export class CommentsService {
  async createCommentBoard({
    customId,
    user,
  }: {
    customId: string;
    user: Record<string, any>;
  }) {
    await initLiveLike(user);

    try {
      const commentBoard = await LiveLike.createCommentBoard({
        customId,
        repliesDepth: 2,
        allowComments: true,
      });
      return commentBoard.id;
    } catch (error) {
      Logger.error(
        `Error creating comment board with custom ID ${customId}: ${error.message}`,
      );
      throw new InternalServerErrorException(
        response({
          message: 'Error creating comment board',
          status: 0,
          data: null,
        }),
      );
    }
  }

  async getComments({
    commentBoardId,
    cursor,
    user,
  }: {
    commentBoardId: string;
    cursor?: string;
    user: Record<string, any>;
  }) {
    await initLive<PERSON>ike(user);

    try {
      const { results: comments, done } = await LiveLike.getComments({
        commentBoardId,
        sorting: LiveLike.CommentSort.NEWEST,
        excludeDeletedCommentWithNoReplies: true,
        ...(cursor && { until: cursor }),
      });

      if (!comments.length) {
        Logger.debug(
          `No comments not found for comment board ID ${commentBoardId}`,
        );
      }

      const nextCursor =
        comments.length > 0 && !done
          ? dayjs(
              new Date(comments[comments.length - 1].created_at),
            ).toISOString()
          : null;

      return {
        comments: comments.map((comment) => {
          const parsedCustomData = JSON.parse(comment.custom_data || '{}');
          return {
            id: comment.id,
            text: comment.text,
            user: parsedCustomData,
            createdAt: comment.created_at,
          };
        }),
        pagination: {
          nextCursor,
        },
      };
    } catch (error) {
      Logger.error(error);
      throw new InternalServerErrorException(
        response({ message: 'Error fetching comments', status: 0, data: null }),
      );
    }
  }

  async createComment({
    commentBoardId,
    comment,
    user,
  }: {
    commentBoardId: string;
    comment: string;
    user: Record<string, any>;
  }) {
    await initLiveLike(user);

    try {
      const customData = {
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        clubName: user.clubName,
        teamName: user.teamName,
        photoUrl: user.photoUrl,
      };

      const createdComment = await LiveLike.addComment({
        text: comment,
        customData: JSON.stringify(customData),
        commentBoardId,
      });

      return {
        id: createdComment.id,
        text: createdComment.text,
        user: customData,
        createdAt: createdComment.created_at,
      };
    } catch (error) {
      Logger.error(error);
      throw new InternalServerErrorException(
        response({ message: 'Error creating comment', status: 0, data: null }),
      );
    }
  }

  async deleteComment({
    commentBoardId,
    commentId,
    user,
  }: {
    commentBoardId: string;
    commentId: string;
    user: Record<string, any>;
  }) {
    await initLiveLike(user);

    try {
      await LiveLike.deleteComment({
        commentBoardId,
        commentId,
      });
    } catch (error) {
      Logger.error(error);
      throw new InternalServerErrorException(
        response({ message: 'Error deleting comment', status: 0, data: null }),
      );
    }
  }
}
