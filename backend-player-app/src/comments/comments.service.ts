import LiveLike from '@livelike/javascript';
import {
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import dayjs from 'dayjs';
import { response } from 'src/utils/response';
import { initLiveLike } from '../utils/LiveLike';

@Injectable()
export class CommentsService {
  async createCommentBoard(customId: string) {
    try {
      const commentBoard = await LiveLike.createCommentBoard({
        customId,
        repliesDepth: 2,
        allowComments: true,
      });
      return commentBoard.id;
    } catch (error) {
      Logger.error(
        `Error creating comment board with custom ID ${customId}: ${error.message}`,
      );
    }
  }

  async getComments({
    commentBoardId,
    cursor,
    user,
  }: {
    commentBoardId: string;
    cursor?: string;
    user: Record<string, any>;
  }) {
    await initLiveLike(user);

    // pagination

    try {
      const { results: comments, done } = await LiveLike.getComments({
        commentBoardId,
        sorting: LiveLike.CommentSort.NEWEST,
        excludeDeletedCommentWithNoReplies: true,
        ...(cursor && { until: cursor }),
      });

      console.log('***', comments.length);

      if (!comments.length) {
        Logger.debug(
          `Comments not found for comment board ID ${commentBoardId}`,
        );
        return new NotFoundException(
          response({ message: 'Comments not found', status: 0, data: null }),
        );
      }

      const nextCursor =
        comments.length > 0 && !done
          ? dayjs(
              new Date(comments[comments.length - 1].created_at),
            ).toISOString()
          : null;

      return {
        comments: comments.map((comment) => {
          const parsedCustomData = JSON.parse(comment.custom_data || '{}');
          return {
            id: comment.id,
            text: comment.text,
            user: parsedCustomData, // comment.author
            createdAt: comment.created_at,
          };
        }),
        pagination: {
          nextCursor,
        },
      };
    } catch (error) {
      Logger.error(error);
      throw new InternalServerErrorException(
        response({ message: 'Error fetching comments', status: 0, data: null }),
      );
    }
  }

  async createComment(commentBoardId: string) {
    return commentBoardId;
  }

  async deleteComment({
    commentBoardId,
    commentId,
    user,
  }: {
    commentBoardId: string;
    commentId: string;
    user: Record<string, any>;
  }) {
    await initLiveLike(user);

    try {
      const response = await LiveLike.deleteComment({
        commentBoardId,
        commentId,
      });
      console.log('***', response);
    } catch (error) {
      Logger.error(error);
      throw new InternalServerErrorException(
        response({ message: 'Error deleting comment', status: 0, data: null }),
      );
    }
  }
}
