import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Query,
  Request,
} from '@nestjs/common';
import { CommentsService } from './comments.service';
import { CreateCommentDto } from './dto/create-comment.dto';
import { GetCommentsQueryDto } from './dto/get-comments-query.dto';

@Controller('comments')
export class CommentsController {
  constructor(private readonly commentsService: CommentsService) {}

  @Get(':commentBoardId')
  getComments(
    @Param('commentBoardId') commentBoardId: string,
    @Request() req: any,
    @Query() query: GetCommentsQueryDto,
  ) {
    return this.commentsService.getComments({
      commentBoardId,
      cursor: query?.cursor,
      user: req.user,
    });
  }

  @Post(':commentBoardId')
  createComment(
    @Param('commentBoardId') commentBoardId: string,
    @Request() req: any,
    @Body() createCommentDto: CreateCommentDto,
  ) {
    return this.commentsService.createComment({
      commentBoardId,
      comment: createCommentDto.comment,
      user: req.user,
    });
  }

  @Delete(':commentBoardId/:commentId')
  deleteComment(
    @Param('commentBoardId') commentBoardId: string,
    @Param('commentId') commentId: string,
    @Request() req: any,
  ) {
    return this.commentsService.deleteComment({
      commentBoardId,
      commentId,
      user: req.user,
    });
  }
}
