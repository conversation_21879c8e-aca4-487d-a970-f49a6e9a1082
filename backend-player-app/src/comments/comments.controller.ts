import {
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Query,
  Request,
} from '@nestjs/common';
import { CommentsService } from './comments.service';
import { GetCommentsQueryDto } from './dto/get-comments-query.dto';

@Controller('comments')
export class CommentsController {
  constructor(private readonly commentsService: CommentsService) {}

  // - Create comment board - currently if a video detail page is loaded and no comment board exists it creates one (do this automatically on highlight upload/ready?)

  @Get(':commentBoardId')
  getComments(
    @Param('commentBoardId') commentBoardId: string,
    @Request() req: any,
    @Query() query: GetCommentsQueryDto,
  ) {
    return this.commentsService.getComments({
      commentBoardId,
      cursor: query?.cursor,
      user: req.user,
    });
  }

  @Post(':commentBoardId')
  createComment(@Param('commentBoardId') commentBoardId: string) {
    return this.commentsService.createComment(commentBoardId);
  }

  @Delete(':commentBoardId/:commentId')
  deleteComment(
    @Param('commentBoardId') commentBoardId: string,
    @Param('commentId') commentId: string,
    @Request() req: any,
  ) {
    return this.commentsService.deleteComment({
      commentBoardId,
      commentId,
      user: req.user,
    });
  }
}
