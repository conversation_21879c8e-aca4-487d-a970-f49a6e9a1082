import {
  Injectable,
  InternalServerErrorException,
  Logger,
  NestMiddleware,
  UnauthorizedException,
} from '@nestjs/common';
import { NextFunction, Request, Response } from 'express';
import jwt from 'jsonwebtoken';
import jwkToPem from 'jwk-to-pem';
import fetch from 'node-fetch';
import { envVars, isLocal } from 'src/config/constants';
import { ADMIN_GROUP } from 'src/users/dto/create-user.dto';
import { response } from 'src/utils/response';
import { getItemFromDatabase } from '../../config/database';

const mockUser = {
  id: 'local-user-id',
  firstName: 'firstName',
  lastName: 'lastName',
  email: '<EMAIL>',
};

const mockJwtPayload = {
  sub: mockUser.id,
  email: mockUser.email,
  email_verified: true,
  'cognito:groups': [ADMIN_GROUP.PlayerAppAdmins],
};

export abstract class BaseAuthMiddleware {
  protected pems = {};

  async setUp() {
    if (isLocal) {
      // Use dummy PEM in local
      this.pems['local-kid'] = 'mock-local-pem';
      return;
    }

    const region = envVars.aws.awsRegion!;
    const userPoolId = envVars.aws.awsUserPoolId!;
    const url = `https://cognito-idp.${region}.amazonaws.com/${userPoolId}/.well-known/jwks.json`;

    const res = await fetch(url);
    if (!res.ok) throw new InternalServerErrorException('Failed to fetch JWKS');

    const { keys } = await res.json();
    keys.forEach((key: any) => {
      this.pems[key.kid] = jwkToPem(key);
    });
  }

  decodeToken(token: string): any {
    if (isLocal) {
      return {
        header: { kid: 'local-kid' },
        payload: mockJwtPayload,
      };
    }

    return jwt.decode(token, { complete: true });
  }

  async verifyToken(token: string, pem: string): Promise<any> {
    if (isLocal) {
      return mockJwtPayload;
    }

    return jwt.verify(token, pem);
  }

  async getUserFromDb(userId: string) {
    if (isLocal) {
      return { Item: mockUser };
    }

    return getItemFromDatabase(envVars.dynamoDB.USERS_TABLE_NAME, {
      id: userId,
    });
  }
}

@Injectable()
export class AuthMiddleware
  extends BaseAuthMiddleware
  implements NestMiddleware
{
  async use(req: Request & { user?: any }, res: Response, next: NextFunction) {
    await this.setUp();
    const auth = req.header('Authorization');
    if (!auth)
      throw new UnauthorizedException(
        response({ message: 'UnauthorizedException', status: 0, data: null }),
      );

    const authArray = auth.split(' ');
    if (authArray[0] !== 'Bearer')
      throw new UnauthorizedException(
        response({ message: 'UnauthorizedException', status: 0, data: null }),
      );

    const decoded: any = this.decodeToken(authArray[1]);
    const pem = this.pems[decoded.header.kid];
    if (!pem)
      throw new UnauthorizedException(
        response({ message: 'UnauthorizedException', status: 0, data: null }),
      );

    let verifiedPayload: any;
    try {
      verifiedPayload = await this.verifyToken(authArray[1], pem);
    } catch (error) {
      Logger.debug(`Auth token verification error: ${error.message}`);
      throw new UnauthorizedException(
        response({ message: 'Invalid token', status: 0, data: null }),
      );
    }

    const { Item: user } = await this.getUserFromDb(verifiedPayload.sub);

    if (!user)
      throw new UnauthorizedException(
        response({ message: 'UnauthorizedException', status: 0, data: null }),
      );

    req.user = { ...user, emailVerified: verifiedPayload.email_verified };
    next();
  }
}

@Injectable()
export class AdminAuthMiddleware
  extends BaseAuthMiddleware
  implements NestMiddleware
{
  async use(req: Request & { user?: any }, res: Response, next: NextFunction) {
    await this.setUp();
    const auth = req.header('Authorization');
    if (!auth)
      throw new UnauthorizedException(
        response({ message: 'UnauthorizedException', status: 0, data: null }),
      );

    const authArray = auth.split(' ');
    if (authArray[0] !== 'Bearer')
      throw new UnauthorizedException(
        response({ message: 'UnauthorizedException', status: 0, data: null }),
      );

    const decoded: any = this.decodeToken(authArray[1]);
    const pem = this.pems[decoded.header.kid];
    if (!pem)
      throw new UnauthorizedException(
        response({ message: 'UnauthorizedException', status: 0, data: null }),
      );

    let verifiedPayload: any;
    try {
      verifiedPayload = await this.verifyToken(authArray[1], pem);
    } catch (error) {
      Logger.debug(`Auth token verification error: ${error.message}`);
      throw new UnauthorizedException(
        response({ message: 'Invalid token', status: 0, data: null }),
      );
    }

    if (
      !verifiedPayload['cognito:groups']?.includes(ADMIN_GROUP.PlayerAppAdmins)
    ) {
      throw new UnauthorizedException(
        response({ message: 'UnauthorizedException', status: 0, data: null }),
      );
    }

    const { Item: user } = await this.getUserFromDb(verifiedPayload.sub);
    if (!user)
      throw new UnauthorizedException(
        response({ message: 'UnauthorizedException', status: 0, data: null }),
      );

    req.user = { ...user, emailVerified: verifiedPayload.email_verified };
    next();
  }
}

@Injectable()
export class GuestAuthMiddleware
  extends BaseAuthMiddleware
  implements NestMiddleware
{
  allowedEndpoints = ['/api/highlights/get'];

  async use(req: Request & { user?: any }, res: Response, next: NextFunction) {
    if (!this.allowedEndpoints.includes(req.url)) {
      throw new UnauthorizedException(
        response({
          message: 'This url is not allowed as guest, you must be logged in',
          status: 0,
          data: null,
        }),
      );
    }

    await this.setUp();
    const auth = req.header('Authorization');
    const authArray = auth?.split(' ') || [];

    if (auth && authArray[1] !== 'undefined' && authArray[0] === 'Bearer') {
      const decoded: any = this.decodeToken(authArray[1]);
      const pem = this.pems[decoded.header.kid];
      if (!pem)
        throw new UnauthorizedException(
          response({ message: 'UnauthorizedException', status: 0, data: null }),
        );

      let verifiedPayload: any;
      try {
        verifiedPayload = await this.verifyToken(authArray[1], pem);
      } catch (error) {
        Logger.debug(`Auth token verification error: ${error.message}`);
        throw new UnauthorizedException(
          response({ message: 'Invalid token', status: 0, data: null }),
        );
      }

      const { Item: user } = await this.getUserFromDb(verifiedPayload.sub);
      req.user = { ...user, emailVerified: verifiedPayload.email_verified };
    }

    next();
  }
}
