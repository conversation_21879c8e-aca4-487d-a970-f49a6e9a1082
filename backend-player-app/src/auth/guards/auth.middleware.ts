import {
  Injectable,
  InternalServerErrorException,
  Logger,
  NestMiddleware,
  UnauthorizedException,
} from '@nestjs/common';
import { NextFunction, Request, Response } from 'express';
import jwt from 'jsonwebtoken';
import jwkToPem from 'jwk-to-pem';
import fetch from 'node-fetch';
import { ADMIN_GROUP } from 'src/users/dto/create-user.dto';
import { response } from 'src/utils/response';
import { envVars } from '../../config/constants';
import { getItemFromDatabase } from '../../config/database';

export abstract class BaseAuthMiddleware {
  protected pems = {};

  async setUp() {
    const url = `https://cognito-idp.${envVars.aws.awsRegion}.amazonaws.com/${envVars.aws.awsUserPoolId}/.well-known/jwks.json`;
    try {
      const res = await fetch(url);
      if (!res.ok) {
        throw new InternalServerErrorException('Failed to fetch JWKS');
      }

      const { keys } = await res.json();
      keys.forEach((key: any) => {
        this.pems[key.kid] = jwkToPem(key);
      });
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }
}
@Injectable()
export class AuthMiddleware
  extends BaseAuthMiddleware
  implements NestMiddleware
{
  async use(req: Request & { user: any }, res: Response, next: NextFunction) {
    try {
      await this.setUp();
      const auth = req.header('Authorization');

      if (!auth)
        throw new UnauthorizedException(
          response({
            message: 'UnauthorizedException',
            status: 0,
            data: null,
          }),
        );
      const authArray = auth.split(' ');
      if (authArray[0] === 'Bearer') {
        const decodeJwt: any = jwt.decode(authArray[1], { complete: true });
        if (!decodeJwt)
          throw new UnauthorizedException(
            response({
              message: 'UnauthorizedException',
              status: 0,
              data: null,
            }),
          );

        const kid = decodeJwt.header.kid;
        const pem = this.pems[kid];

        if (!pem) {
          throw new UnauthorizedException(
            response({
              message: 'UnauthorizedException',
              status: 0,
              data: null,
            }),
          );
        }
        await jwt.verify(authArray[1], pem);

        const { Item: user } = await getItemFromDatabase(
          envVars.dynamoDB.USERS_TABLE_NAME,
          {
            id: decodeJwt.payload.sub,
          },
        );
        if (!user) {
          Logger.error(`User exists in cognito but not found in database`, {
            sub: decodeJwt.payload.sub,
            email: decodeJwt.payload.email,
          });
          throw new UnauthorizedException(
            response({
              message: 'UnauthorizedException',
              status: 0,
              data: null,
            }),
          );
        }

        req.user = {
          ...user,
          emailVerified: decodeJwt.payload.email_verified,
        };
        next();
      } else {
        throw new UnauthorizedException(
          response({
            message: 'UnauthorizedException',
            status: 0,
            data: null,
          }),
        );
      }
    } catch (error) {
      Logger.debug(error);
      throw new UnauthorizedException(
        response({
          message: error.message,
          status: 0,
          data: null,
        }),
      );
    }
  }
}

@Injectable()
export class AdminAuthMiddleware
  extends BaseAuthMiddleware
  implements NestMiddleware
{
  async use(req: Request & { user: any }, res: Response, next: NextFunction) {
    try {
      await this.setUp();
      const auth = req.header('Authorization');

      if (!auth)
        throw new UnauthorizedException(
          response({
            message: 'UnauthorizedException',
            status: 0,
            data: null,
          }),
        );
      const authArray = auth.split(' ');
      if (authArray[0] === 'Bearer') {
        const decodeJwt: any = jwt.decode(authArray[1], { complete: true });
        if (!decodeJwt)
          throw new UnauthorizedException(
            response({
              message: 'UnauthorizedException',
              status: 0,
              data: null,
            }),
          );

        const kid = decodeJwt.header.kid;
        const pem = this.pems[kid];

        if (!pem) {
          throw new UnauthorizedException(
            response({
              message: 'UnauthorizedException',
              status: 0,
              data: null,
            }),
          );
        }
        await jwt.verify(authArray[1], pem);

        if (
          !Boolean(decodeJwt.payload['cognito:groups']) ||
          !decodeJwt.payload['cognito:groups'].includes(
            ADMIN_GROUP.PlayerAppAdmins,
          )
        ) {
          throw new UnauthorizedException(
            response({
              message: 'UnauthorizedException',
              status: 0,
              data: null,
            }),
          );
        }

        const { Item: user } = await getItemFromDatabase(
          envVars.dynamoDB.USERS_TABLE_NAME,
          {
            id: decodeJwt.payload.sub,
          },
        );
        if (!user) {
          Logger.error(
            `Admin user exists in cognito but not found in database`,
            {
              sub: decodeJwt.payload.sub,
              email: decodeJwt.payload.email,
            },
          );
          throw new UnauthorizedException(
            response({
              message: 'UnauthorizedException',
              status: 0,
              data: null,
            }),
          );
        }

        req.user = {
          ...user,
          emailVerified: decodeJwt.payload.email_verified,
        };
        next();
      } else {
        throw new UnauthorizedException(
          response({
            message: 'UnauthorizedException',
            status: 0,
            data: null,
          }),
        );
      }
    } catch (error) {
      Logger.debug(error);
      throw new UnauthorizedException(
        response({
          message: error.message,
          status: 0,
          data: null,
        }),
      );
    }
  }
}

@Injectable()
export class GuestAuthMiddleware
  extends BaseAuthMiddleware
  implements NestMiddleware
{
  allowedEndpoints = ['/api/highlights/get'];

  async use(req: Request & { user: any }, res: Response, next: NextFunction) {
    try {
      if (!this.allowedEndpoints.includes(req.url)) {
        throw new UnauthorizedException(
          response({
            message: 'This url is not allowed as guest, you must be logged in',
            status: 0,
            data: null,
          }),
        );
      }

      const auth = req.header('Authorization');
      const authArray = auth?.split(' ') || [];

      await this.setUp();

      if (auth && authArray[1] !== 'undefined') {
        if (authArray[0] === 'Bearer') {
          const decodeJwt: any = jwt.decode(authArray[1], { complete: true });
          if (!decodeJwt) {
            throw new UnauthorizedException(
              response({
                message: 'UnauthorizedException',
                status: 0,
                data: null,
              }),
            );
          }

          const kid = decodeJwt.header.kid;
          const pem = this.pems[kid];

          if (!pem) {
            throw new UnauthorizedException(
              response({
                message: 'UnauthorizedException',
                status: 0,
                data: null,
              }),
            );
          }
          await jwt.verify(authArray[1], pem);

          const { Item: user } = await getItemFromDatabase(
            envVars.dynamoDB.USERS_TABLE_NAME,
            {
              id: decodeJwt.payload.sub,
            },
          );
          if (!user) {
            Logger.warn(
              `Guest user exists in cognito but not found in database`,
              {
                sub: decodeJwt.payload.sub,
                email: decodeJwt.payload.email,
              },
            );
          }

          req.user = {
            ...user,
            emailVerified: decodeJwt.payload.email_verified,
          };
          next();
        } else {
          throw new UnauthorizedException(
            response({
              message: 'UnauthorizedException',
              status: 0,
              data: null,
            }),
          );
        }
      } else {
        next();
      }
    } catch (error) {
      Logger.debug(error);
      throw new UnauthorizedException(
        response({
          message: error.message,
          status: 0,
          data: null,
        }),
      );
    }
  }
}
