import {
  AdminAddUserToGroupCommand,
  AdminConfirmSignUpCommand,
  AdminDeleteUserCommand,
  AdminSetUserPasswordCommand,
  AdminSetUserPasswordCommandInput,
  AdminUpdateUserAttributesCommand,
  AuthFlowType,
  ChangePasswordCommand,
  CognitoIdentityProviderClient,
  ConfirmForgotPasswordCommand,
  ConfirmSignUpCommand,
  DeleteUserCommand,
  ForgotPasswordCommand,
  InitiateAuthCommand,
  ResendConfirmationCodeCommand,
  SignUpCommand,
  UserNotConfirmedException,
} from '@aws-sdk/client-cognito-identity-provider';
import {
  BadRequestException,
  HttpException,
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import fetch from 'node-fetch';
import { useUpdate } from 'src/config/util';
import { HighlightsService } from 'src/highlights/highlights.service';
import { UserType } from 'src/users/dto/create-user.dto';
import { UsersService } from 'src/users/users.service';
import { EmailSender } from 'src/utils/emailSender';
import { getAllowedSignupUsers } from 'src/utils/getGoogleSheetData';
import { envVars, isProduction } from '../config/constants';
import { writeItemsToDatabase } from '../config/database';
import { response } from '../utils/response';
import { ALLOWED_USERS as staticAllowedUsers } from './constants';
import { ChangePassword } from './validators/change-password.dto';
import { UserDto } from './validators/create-user.dto';
import { ResetPasswordDto } from './validators/reset-password.dto';
import { VerifyUserDto } from './validators/verify-user.dto';

/**
 * Auth service to control all the activities relating to auth
 */
@Injectable()
export class AuthService {
  private cognitoIdentity: CognitoIdentityProviderClient;
  private secret: string;
  private ClientId: string;
  emailSender: EmailSender;

  constructor(
    private readonly logger: Logger,
    private jwtService: JwtService,
    private userService: UsersService,
    private highlightsService: HighlightsService,
  ) {
    this.cognitoIdentity = new CognitoIdentityProviderClient({
      region: envVars.aws.awsRegion,
    });

    this.secret = envVars.aws.awsCognitoClientSecret;
    this.ClientId = envVars.aws.awsCognitoClientId;
    this.emailSender = new EmailSender();
  }

  handleAwsException({
    error,
    message,
    data = null,
  }: {
    error: any;
    message: string;
    data?: any;
  }) {
    const detail = error?.message || 'Unexpected error';
    const statusCode = error?.$metadata?.httpStatusCode;

    if (statusCode < 500) {
      this.logger.log(`${message}: ${detail}`);
      throw new HttpException(
        response({ message: detail, status: 0, data }),
        statusCode,
      );
    } else {
      this.logger.error(`${message}: ${detail}`, error);
      throw new InternalServerErrorException(
        response({
          message: detail,
          status: 0,
          data,
        }),
      );
    }
  }

  /**
   * Sign up function
   * @param userDto
   * @returns
   */
  async signUp(userDto: UserDto): Promise<any> {
    const ALLOWED_USERS = await getAllowedSignupUsers();

    const listOfAllowedUsers =
      ALLOWED_USERS.length > 0 ? ALLOWED_USERS : staticAllowedUsers;

    const isAllowedUser = listOfAllowedUsers
      .filter((email) => Boolean(email))
      .find((item) => userDto.email.toLowerCase() === item.toLowerCase());

    const isAdmin = userDto.userType === UserType.ADMIN;
    // whitelist signup for staging and dev but block signup for only allowed list for productions
    if (isProduction && !isAllowedUser && !isAdmin) {
      throw new BadRequestException(
        response({
          message: `${userDto.email} is not allowed to signup, join the wait list`,
          status: 0,
          data: null,
        }),
      );
    }

    try {
      const command = new SignUpCommand({
        ClientId: this.ClientId,
        Username: userDto.email?.toLowerCase(),
        Password: userDto.password,
        UserAttributes: [
          { Name: 'email', Value: userDto.email?.toLowerCase() },
          { Name: 'given_name', Value: userDto.firstName },
          { Name: 'family_name', Value: userDto.lastName },
          {
            Name: 'profile',
            Value: userDto.userType || UserType.PLAYER,
          },
        ],
      });

      const adminConfirm = new AdminConfirmSignUpCommand({
        Username: userDto.email?.toLowerCase(),
        UserPoolId: envVars.aws.awsUserPoolId,
      });

      const cognitoUser = await this.cognitoIdentity.send(command);

      const livelikeRespone = await this.createLiveLikeProfile(
        userDto,
        cognitoUser.UserSub,
      );

      const savedUser: any = {
        id: cognitoUser.UserSub,
        createdDate: new Date().toISOString(),
        gender: '',
        birthday: '',
        cludId: '',
        location: '',
        position: '',
        teamId: '',
        preferredFoot: '',
        height: '',
        userType: userDto.userType || UserType.PLAYER,
        photoUrl: '',
        liveLikeProfileId: livelikeRespone.id,
        liveLikeProfileToken: livelikeRespone.access_token,
        allowPrivateClubsTeams: false,
        crown: null,
        banner: '',
        isManager: false,
        nonPlayerRole: userDto.nonPlayerRole || [],
        ...userDto,
        email: userDto.email?.toLowerCase(),
        invitedBy: userDto.playerId || '',
      };

      delete savedUser.password;

      await writeItemsToDatabase(envVars.dynamoDB.USERS_TABLE_NAME, savedUser);
      await this.cognitoIdentity.send(adminConfirm);

      if (isAdmin) {
        const input = {
          UserPoolId: envVars.aws.awsUserPoolId,
          Username: userDto.email,
          GroupName: 'PlayerAppAdmins',
        };
        const command = new AdminAddUserToGroupCommand(input);
        const response = await this.cognitoIdentity.send(command);
        Logger.debug('AdminAddUserToGroupCommand response: ', response);
      }

      // verify user emails automatically since we invite them manually for now
      const verificationCommand = new AdminUpdateUserAttributesCommand({
        UserPoolId: envVars.aws.awsUserPoolId,
        Username: userDto.email.toLowerCase(),
        UserAttributes: [
          {
            Name: 'email_verified',
            Value: 'true', //TODO: this should be false when user invite is no longer manual
          },
        ],
      });

      await this.cognitoIdentity.send(verificationCommand);

      // await this.emailSender.sendEmail({
      //   emailAdresses: [userDto.email],
      //   emailSubject: 'Welcome to playerapp',
      //   htmlEmailTemplate: `<p>Hi ${userDto.firstName},</p>
      //   <h4>Welcome to PlayerApp Beta!</h4>
      //   <p>We’ve created an account for you to be able to access the platform. Your email is ${userDto.email} and the password we’ve created for you is ${tempPass}</p>
      //   <p>You can change your password at any time in the profile settings section.</p>
      //   <p><a href="https://${envVars.environment}-app.playerapp.co" >Click here</a>  to log in to your account.</p>
      //   <p>We hope you enjoy the platform. As you know, it’s in Beta phase, so you might experience some glitches.</p>
      //   <p>We’d love to get your feedback so if there is anything that you like, dislike, feel is missing or if you have any questions or issues, please send <NAME_EMAIL>.</p>
      //   <p>Thanks,</p>
      //   <p>PlayerApp Team</p>`,
      // });

      this.logger.debug('Sign up success');

      return response({
        message: 'Signed up sucessfully',
        status: 1,
        data: savedUser,
      });
    } catch (error) {
      this.handleAwsException({ error, message: 'Sign up error' });
    }
  }

  // Helper function to create LiveLike profile
  async createLiveLikeProfile(
    user: UserDto,
    userId: string,
  ): Promise<{
    access_token: string;
    id: string;
  }> {
    const url = `https://cf-blast.livelikecdn.com/api/v1/profile-by-custom-id/${envVars.liveLike.LIVELIKE_CLIENT_ID}/${userId}`;

    const customeData = JSON.stringify({
      firstName: user?.firstName,
      lastName: user?.lastName,
      userId: userId,
    });

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${envVars.liveLike.LIVELIKE_TOKEN}`,
      },
      body: JSON.stringify({ custom_data: customeData }),
    });

    if (!response.ok) {
      await this.deleteUser({
        requestFrom: UserType.ADMIN,
        email: user.email,
        userId,
      });

      throw new Error(
        `Failed to create LiveLike profile: ${response.statusText}`,
      );
    }

    return await response.json();
  }

  /**
   * A function that logs users in
   * @param email
   * @param password
   * @returns
   */
  async signInUser(
    email: string,
    password: string,
  ): Promise<{ message: string; data: unknown; status: number }> {
    try {
      const command = new InitiateAuthCommand({
        AuthFlow: AuthFlowType.USER_PASSWORD_AUTH,
        AuthParameters: {
          USERNAME: email?.toLowerCase(),
          PASSWORD: password,
        },
        ClientId: this.ClientId,
      });

      const auth = await this.cognitoIdentity.send(command);

      const tokens = {
        access: auth.AuthenticationResult.AccessToken,
        refresh: auth.AuthenticationResult.RefreshToken,
        id: auth.AuthenticationResult.IdToken,
        expiresIn: auth.AuthenticationResult.ExpiresIn,
      };

      const user: Record<string, any> = this.jwtService.decode(
        auth.AuthenticationResult.IdToken,
      ) as Record<string, any>;

      const userDetails: any = await this.userService.findOne({
        id: user.sub,
      });

      this.logger.debug('Login success');

      await useUpdate({
        dbName: envVars.dynamoDB.USERS_TABLE_NAME,
        id: user.sub,
        data: {
          lastLogin: new Date().toISOString(),
        },
      });

      return response({
        message: 'Signed in successfully!',
        status: 1,
        data: {
          ...userDetails.data,
          emailVerified: user.email_verified,
          tokens,
        },
      });
    } catch (error) {
      const isUserNotConfirmedException =
        error.name === UserNotConfirmedException.name;

      this.handleAwsException({
        error,
        message: 'Login error',
        data: {
          name: isUserNotConfirmedException
            ? UserNotConfirmedException.name
            : null,
        },
      });
    }
  }

  /**
   * Resend verification function
   * @param email
   * @returns {Promise} an object of the response instanse
   */
  async resendVerification(
    email: string,
  ): Promise<{ message: string; data: unknown; status: number }> {
    try {
      const command = new ResendConfirmationCodeCommand({
        ClientId: this.ClientId,
        Username: email,
      });

      await this.cognitoIdentity.send(command);
      this.logger.log('Resend verification success');

      return response({
        message: 'Confirmation has been resent!',
        data: null,
        status: 1,
      });
    } catch (error) {
      this.handleAwsException({ error, message: 'Resend verification error' });
    }
  }

  /**
   * A function that verifies emails
   * @param verifyData
   * @returns
   */
  async verifyEmail(
    verifyData: VerifyUserDto,
  ): Promise<{ message: string; data: unknown; status: number }> {
    try {
      const command = new ConfirmSignUpCommand({
        ClientId: this.ClientId,
        Username: verifyData.email,
        ConfirmationCode: verifyData.verificationCode,
      });

      await this.cognitoIdentity.send(command);
      this.logger.log('Verify email success');

      return response({
        message: 'User has been successfully confirmed!',
        data: null,
        status: 1,
      });
    } catch (error) {
      this.handleAwsException({ error, message: 'Verify email error' });
    }
  }

  /**
   * A function used to reset password when a user is not logged in
   * @param verifyData
   * @returns
   */
  async forgotPassword(verifyData: {
    email: string;
  }): Promise<{ message: string; data: unknown; status: number }> {
    try {
      const command = new ForgotPasswordCommand({
        ClientId: this.ClientId,
        Username: verifyData.email,
      });

      const forgotPassResponse = await this.cognitoIdentity.send(command);
      this.logger.log('Forgot password success');

      return response({
        message:
          'Verification code has been sent to your email!, use it to change your password',
        status: 1,
        data: forgotPassResponse,
      });
    } catch (error) {
      this.handleAwsException({ error, message: 'Forgot password error' });
    }
  }

  /**
   * A function used to reset password when the user is logged in
   * @param verifyData
   * @returns
   */
  async resetPassword(
    verifyData: ResetPasswordDto,
  ): Promise<{ message: string; data: unknown; status: number }> {
    try {
      const command = new ConfirmForgotPasswordCommand({
        ClientId: this.ClientId,
        Username: verifyData.email,
        Password: verifyData.password,
        ConfirmationCode: verifyData.verificationCode,
      });

      await this.cognitoIdentity.send(command);
      this.logger.debug('Reset password success');

      return response({
        message: 'Password has been changed successfully!',
        status: 1,
      });
    } catch (error) {
      this.handleAwsException({ error, message: 'Reset password error' });
    }
  }

  /**
   * Chnage password
   */
  async changePassword({
    accessToken,
    oldPassword,
    newPassword,
  }: ChangePassword) {
    try {
      const command = new ChangePasswordCommand({
        PreviousPassword: oldPassword,
        ProposedPassword: newPassword,
        AccessToken: accessToken.split(' ')[1],
      });

      await this.cognitoIdentity.send(command);
      this.logger.log('Change password success');

      return response({
        message: 'Password has been changed successfully!',
        status: 1,
      });
    } catch (error) {
      this.handleAwsException({ error, message: 'Change password error' });
    }
  }

  /**
   * Delete user and its data from the system. Also delete user from cognito
   */
  async deleteUser({
    accessToken,
    requestFrom,
    email,
    userId,
  }: {
    accessToken?: string;
    requestFrom: UserType;
    email: string;
    userId: string;
  }) {
    try {
      let command;

      if (requestFrom === UserType.ADMIN && email) {
        command = new AdminDeleteUserCommand({
          UserPoolId: envVars.aws.awsUserPoolId,
          Username: email,
        });
      } else {
        command = new DeleteUserCommand({
          AccessToken: accessToken.replace('Bearer ', ''),
        });
      }

      // Delete all user highlights
      await this.highlightsService.deleteAllByUserID({ userId });

      // Fetch User Details Before Delete
      const user: any = (await this.userService.findOne({ id: userId }))
        ?.data ?? {
        liveLikeProfileId: null,
      };

      // Remove user from user database
      await this.userService.removeUser(userId);

      await this.cognitoIdentity.send(command);
      this.logger.debug('Delete user success');

      // Delete LiveLike Profile
      if (user?.liveLikeProfileId) {
        await this.deleteUserAccountFromLiveLike(user.liveLikeProfileId);
      }

      return response({
        message: 'User has been deleted successfully!',
        status: 1,
      });
    } catch (error) {
      this.handleAwsException({ error, message: 'Delete user error' });
    }
  }

  async refreshToken(refreshToken: string) {
    try {
      const authParams = {
        REFRESH_TOKEN: refreshToken,
      };

      const command = new InitiateAuthCommand({
        AuthFlow: 'REFRESH_TOKEN_AUTH',
        ClientId: this.ClientId,
        AuthParameters: authParams,
      });

      const result = await this.cognitoIdentity.send(command);

      if (result.AuthenticationResult) {
        const tokens = {
          access: result.AuthenticationResult.AccessToken,
          refresh: result.AuthenticationResult.RefreshToken,
          id: result.AuthenticationResult.IdToken,
          expiresIn: result.AuthenticationResult.ExpiresIn,
        };
        this.logger.debug('Refresh token success');
        return response({
          message: 'Token renewed!',
          status: 1,
          data: tokens,
        });
      }
      return response({
        message: 'Failed to refresh token: No authentication result',
        status: 1,
        data: null,
      });
    } catch (error) {
      this.handleAwsException({ error, message: 'Refresh token error' });
    }
  }

  async allowedToSignup(email: string) {
    try {
      const emails = await getAllowedSignupUsers();
      const foundEmail = emails
        .filter((item) => Boolean(item))
        .find((item) => email.toLowerCase() === item.toLowerCase());

      if (!isProduction) {
        return response({
          message: 'Email found!',
          status: 1,
          data: email,
        });
      }

      if (!foundEmail && isProduction) {
        this.logger.debug('Allowed to signup denied');
        return response({
          message: 'Email not allowed to signup!',
          status: 0,
          data: null,
        });
      }

      this.logger.debug('Allowed to signup success');

      return response({
        message: 'Email found!',
        status: 1,
        data: foundEmail,
      });
    } catch (error) {
      this.handleAwsException({ error, message: 'Allowed to signup error' });
    }
  }

  async adminResetUserPassword({
    email,
    newPassword,
  }: {
    email: string;
    newPassword: string;
  }) {
    const params: AdminSetUserPasswordCommandInput = {
      UserPoolId: envVars.aws.awsUserPoolId,
      Username: email.toLowerCase(),
      Password: newPassword,
      Permanent: true,
    };

    try {
      const data = await this.cognitoIdentity.send(
        new AdminSetUserPasswordCommand(params),
      );

      this.logger.debug('Admin reset user password success');

      return response({
        message: 'Password reset successful!',
        status: 1,
        data: data,
      });
    } catch (error) {
      this.handleAwsException({
        error,
        message: 'Admin reset user password error',
      });
    }
  }

  async deleteUserAccountFromLiveLike(livelikeId) {
    try {
      const url = `https://cf-blast.livelikecdn.com/api/v1/profiles/${livelikeId}`;

      const response = await fetch(url, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${envVars.liveLike.LIVELIKE_TOKEN}`,
        },
      });

      if (!response.ok) {
        throw new Error(
          `Failed to delete LiveLike profile: ${response.statusText}`,
        );
      }
    } catch (error) {
      this.logger.debug(error.message);
    }
  }
}
