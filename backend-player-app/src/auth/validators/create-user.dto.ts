import {
  IsEmail,
  IsNotEmpty,
  IsString,
  Length,
  IsPhoneNumber,
  IsOptional,
  IsEnum,
  IsArray,
} from 'class-validator';
import { UserType } from 'src/users/dto/create-user.dto';

export class UserDto {
  @IsString()
  @IsNotEmpty()
  firstName: string;

  @IsString()
  @IsNotEmpty()
  lastName: string;

  @IsEnum(UserType)
  @IsOptional()
  userType: UserType;

  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsString()
  @IsNotEmpty()
  @Length(6)
  password: string;

  @IsOptional()
  @IsString()
  @IsPhoneNumber()
  phoneNumber: string;

  @IsOptional()
  @IsArray()
  nonPlayerRole: Array<string>;

  @IsOptional()
  @IsString()
  playerId: string;
}
