import {
  Logger,
  MiddlewareConsumer,
  Module,
  NestModule,
  RequestMethod,
} from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { ExperienceStatsService } from 'src/experience-stats/experience-stats.service';
import { HighlightsService } from 'src/highlights/highlights.service';
import { P2pChatService } from 'src/p2p-chat/p2p-chat.service';
import { UsersService } from 'src/users/users.service';
import { FollowModule } from '../follow/follow.module';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { AdminAuthMiddleware, AuthMiddleware } from './guards/auth.middleware';

@Module({
  imports: [ConfigModule, FollowModule],
  controllers: [AuthController],
  providers: [
    AuthService,
    ConfigService,
    Logger,
    JwtService,
    UsersService,
    ExperienceStatsService,
    HighlightsService,
    P2pChatService,
  ],
})
// export class AuthModule {}
export class AuthModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(AuthMiddleware)
      .forRoutes(
        { path: 'auth/change-password', method: RequestMethod.POST },
        { path: 'auth/delete-account', method: RequestMethod.POST },
      )
      .apply(AdminAuthMiddleware)
      .forRoutes({
        path: 'admin-reset-user-password',
        method: RequestMethod.POST,
      });
  }
}
