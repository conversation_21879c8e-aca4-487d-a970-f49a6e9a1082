import { Body, Controller, Headers, Post } from '@nestjs/common';
import { AuthService } from './auth.service';
import { UserDto } from './validators/create-user.dto';
import { LoginUserDto } from './validators/login-user.dto';
import { ResetPasswordDto } from './validators/reset-password.dto';
import { VerifyUserDto } from './validators/verify-user.dto';
import { OwnerOrAdminCanAccess } from 'src/shared/decorators/shouldAllowAccess';

@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('signup')
  create(@Body() createUserDto: UserDto) {
    return this.authService.signUp(createUserDto);
  }

  @Post('login')
  async login(@Body() payload: LoginUserDto) {
    const authResult = await this.authService.signInUser(
      payload.email,
      payload.password,
    );
    if (authResult) {
      return authResult;
    }
    return 'Auth failed';
  }

  @Post('verify')
  async verify(@Body() payload: VerifyUserDto) {
    return await this.authService.verifyEmail(payload);
  }

  @Post('resend-verification')
  async resendVerification(@Body() payload: { email: string }) {
    return await this.authService.resendVerification(payload.email);
  }

  @Post('forgot-password')
  async forgotPassword(@Body() payload: { email: string }) {
    return await this.authService.forgotPassword(payload);
  }

  @Post('reset-password')
  async resetPassword(@Body() payload: ResetPasswordDto) {
    return await this.authService.resetPassword(payload);
  }
  @Post('refresh-token')
  async refreshToken(@Body() payload: { refreshToken: string }) {
    return await this.authService.refreshToken(payload.refreshToken);
  }

  @Post('change-password')
  async changePassword(
    @Body() payload,
    @Headers('Authorization') token: string,
  ) {
    return await this.authService.changePassword({
      accessToken: token,
      newPassword: payload.newPassword,
      oldPassword: payload.oldPassword,
    });
  }

  @Post('delete-account')
  @OwnerOrAdminCanAccess('userId')
  async deleteAccount(
    @Body() payload,
    @Headers('Authorization') token: string,
  ) {
    return await this.authService.deleteUser({
      accessToken: token,
      requestFrom: payload.requestFrom,
      email: payload.email,
      userId: payload.userId,
    });
  }

  @Post('allowed-to-signup')
  async allowedToSignup(@Body() payload: { email: string }) {
    return await this.authService.allowedToSignup(payload.email);
  }

  @Post('admin-reset-user-password')
  async adminResetUserPassword(
    @Body() payload: { email: string; newPassword: string },
  ) {
    return await this.authService.adminResetUserPassword(payload);
  }
}
