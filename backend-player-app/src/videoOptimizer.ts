import fs from 'fs';
import {
  S3Client,
  GetObjectCommand,
  GetObjectCommandInput,
  GetObjectCommandOutput,
  PutObjectCommand,
  HeadObjectCommand,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

import { envVars } from './config/constants';
import {
  ensureDirectoryExists,
  generateThumbnail,
  optimizeVideo,
  setFFPath,
} from './utils/helpers';
import {
  SQSClient,
  DeleteMessageCommand,
  DeleteMessageCommandInput,
  SendMessageCommand,
} from '@aws-sdk/client-sqs';
import { dbConfig } from './config';
import { VIDEO_STATUS } from './highlights/dto/create-highlight.dto';
import { ANNOUNCEMENT_TYPE } from './announcement/dto/create-announcement.dto';

const s3Client = new S3Client({ region: envVars.aws.awsRegion });

const sqsClient = new SQSClient({ region: envVars.aws.awsRegion });

async function getSignedS3Url(bucket: string, key: string): Promise<string> {
  const command = new GetObjectCommand({ Bucket: bucket, Key: key });
  return getSignedUrl(s3Client, command, { expiresIn: 3600 }); // URL valid for 1 hour
}

export const videoOptimizerFunc = async (event: any, context) => {
  let videoPath = '';
  let retryCount = 0;
  let videoTimeStamp = '';
  try {
    await setFFPath();

    const sqsRecord = event.Records[0];
    const body = JSON.parse(sqsRecord.body);
    const s3Records = body.Records[0].s3;
    const bucketName = s3Records.bucket.name;
    const key = s3Records.object.key;

    // Extract retry count from message attributes, default to 0 if not present
    retryCount = sqsRecord.messageAttributes?.retryCount?.stringValue
      ? parseInt(sqsRecord.messageAttributes.retryCount.stringValue, 10)
      : 0;

    // Process the video
    const fileName = key.split('/').pop();
    const localFilePath = `/tmp/${fileName}`;
    const newPath = key.split('--')[1];
    videoPath = newPath;

    const s3Params: GetObjectCommandInput = { Bucket: bucketName, Key: key };
    const { Body }: GetObjectCommandOutput = await s3Client.send(
      new GetObjectCommand(s3Params),
    );

    // GET THE METADATA OF THE VIDEO
    const data = await s3Client.send(
      new HeadObjectCommand({
        Bucket: bucketName,
        Key: key,
      }),
    );

    console.log('THE VIDEO METADATA:', data.Metadata);

    const videoResolution = data?.Metadata?.width
      ? `${data?.Metadata.width}x${data?.Metadata.height}`
      : '';
    videoTimeStamp = data?.Metadata?.timeStamp;
    console.log('VIDEO RESOLUTION: ', videoResolution);

    // BLOCK TO PROCESS THE VIDEO
    const outputDir = '/tmp/output';
    fs.writeFileSync(localFilePath, await Body.transformToByteArray());
    ensureDirectoryExists(outputDir);
    await optimizeVideo(localFilePath, outputDir, fileName);
    await handleUploadSegments({ outputDir, newPath });
    console.log('Optimization complete');

    const thumbnailPath = '/tmp/images';
    ensureDirectoryExists(thumbnailPath);
    await generateThumbnail(
      '/tmp/output/segment0.ts',
      thumbnailPath,
      videoResolution,
    );
    console.log('Thumbnail generation complete');

    await handleThumbnailUploads(newPath);

    // Clean up
    fs.unlinkSync('/tmp/output/segment0.ts');
    fs.rmdirSync(outputDir);
    fs.rmdirSync(thumbnailPath);
    fs.unlinkSync(localFilePath);
    // fs.rmdirSync(thumbnailPath, { recursive: true });

    await updateDb(newPath, videoTimeStamp, 'queueProcessed');

    // REMOVE ITEM FROM QUEUE
    await removeItemFromSQS(sqsRecord.receiptHandle);
    return { statusCode: 200, body: 'Processing complete' };
  } catch (error) {
    console.error('Processing error:', error);

    if (retryCount < 2) {
      console.log('RETRYING TO PROCESS THE VIDEO', retryCount);

      // Increment retry count and requeue the message
      await requeueMessage(event.Records[0], retryCount + 1);
    } else {
      // Update DB as failed and remove from SQS
      await updateDb(videoPath, videoTimeStamp, 'videoProcessingFailed');
      await removeItemFromSQS(event.Records[0].receiptHandle);
      console.log('AFTER 3 RETRIALS TO PROCESS THE VIDEO, IT FAILED');
    }

    return { statusCode: 500, body: 'Processing failed' };
  }
};

async function requeueMessage(record: any, retryCount: any) {
  try {
    const params = {
      QueueUrl: envVars.aws.sqsUrl,
      MessageBody: record.body,
      DelaySeconds: 5,
      MessageAttributes: {
        retryCount: {
          DataType: 'Number',
          StringValue: retryCount.toString(),
        },
      },
    };
    await sqsClient.send(new SendMessageCommand(params));
    console.log(`Requeued message with retryCount: ${retryCount}`);
  } catch (error) {
    console.error('Error requeuing message:', error);
  }
}

async function handleThumbnailUploads(newPath: string) {
  try {
    const pngFiles = fs.readdirSync('/tmp/images');

    const thumbnailFile = pngFiles.map(async (file) => {
      const filePath = `/tmp/images/${file}`;
      const fileData = fs.readFileSync(filePath);

      const uploadParams = {
        Bucket: envVars.aws.streamingBucket,
        Key: `${newPath}/thumbnail.png`,
        Body: fileData,
        ContentType: 'image/png',
      };
      await s3Client.send(new PutObjectCommand(uploadParams));
      console.log('Uploaded thumbnail:', filePath);
      fs.unlinkSync(filePath); // Delete the processed thumbnail file from the file system
    });

    await Promise.all(thumbnailFile);
  } catch (error) {
    console.log('Error uploading thumbnail:', error);
    throw new Error(`Error uploading thumbnail ${error}`);
  }
}

/**
 * The function to handle the upload of video segments to S3
 */
async function handleUploadSegments({ newPath, outputDir }) {
  console.log(
    envVars.aws.streamingBucket,
    '=========================>>>>>>>>>>>>>',
  );

  try {
    // Upload optimized video segments to S3
    const files = fs.readdirSync(outputDir);
    const uploadPromises = files.map(async (file, idx) => {
      const filePath = `${outputDir}/${file}`;
      const fileData = fs.readFileSync(filePath);
      const uploadParams = {
        Bucket: envVars.aws.streamingBucket,
        Key: `${newPath}/${file}`,
        Body: fileData,
        ContentType: filePath.includes('.m3u8')
          ? 'application/vnd.apple.mpegurl'
          : 'video/mp2t',
      };
      await s3Client.send(new PutObjectCommand(uploadParams));
      console.log('Uploaded segment:', idx, filePath);
      if (!file.includes('segment0.ts')) {
        fs.unlinkSync(filePath); // Delete the processed segment file from the file system and leave segment 1 since we use it to generate the thumbnail
      }
    });
    await Promise.all(uploadPromises);
  } catch (error) {
    console.log(error, 'Error uploading segments to S3');
    throw new Error(`Error uploading segments to S3 ${error}`);
  }
}

/**
 * The function to remove the item from sqs queue
 * @param receiptHandle - receipt handle of the sqs queue
 */
async function removeItemFromSQS(receiptHandle: any) {
  // remove item from sqs queue
  const sqsDeleteInputParams: DeleteMessageCommandInput = {
    QueueUrl: envVars.aws.sqsUrl,
    ReceiptHandle: receiptHandle,
  };
  try {
    // Done with db tasks
    const command = new DeleteMessageCommand(sqsDeleteInputParams);
    const response = await sqsClient.send(command);

    console.log('Message deleted from SQS:', response);
  } catch (error) {
    console.log('ERROR REMOVING ITEM FROM SQS', error);
    // throw new Error(`Error uploading segments to S3 ${error}`);
  }
}

/**
 * The function to update the db with with the status of the video
 * @param newPath - the path of the optimized video
 */
async function updateDb(
  newPath: string,
  videoTimeStamp: string,
  status = 'queueProcessed',
) {
  try {
    console.log('ASSET PATH TO SEARCH IN DB', newPath);
    console.log('STATUS TO UPDATE IN THE FOUND DATA', status);

    // Getting the item from db
    const dbName = envVars.dynamoDB.HIGHLIGHT_TABLE_NAME;
    const statement = `select * from \"${dbName}\" where contains(url, '${newPath}')`;
    console.log('STARTED TO GET THE DATA FROM HIGHLIGHT DB');

    const data = await dbConfig.dbPartialQueryExecution(statement);

    console.log('DONE TO GET THE DATA FROM HIGHLIGHT DB', data.Items);

    if (data.Items.length > 0) {
      // update the db
      const highlightsUpdateParams = {
        UpdateExpression: `set ${status} = :${status}, videoStatus = :videoStatus`,
        ExpressionAttributeValues: {
          [`:${status}`]: true,
          ':videoStatus':
            status === 'queueProcessed'
              ? VIDEO_STATUS.SUCCESSFUL
              : VIDEO_STATUS.FAILED,
        },
      };

      const currentHighlight = data.Items[0];
      await dbConfig.updateItemsToDatabase(
        dbName,
        {
          userId: currentHighlight.user.id,
          id: currentHighlight.id,
        },
        highlightsUpdateParams,
      );
      console.log('DONE TO UPDATE THE DATA TO DB HIGHLIGHTS');
    } else {
      console.log(
        'NOT FOUND IN HIGHLIGHT, STARTED TO GET THE DATA FROM ANNOUNCEMENT DB',
      );
      const statement2 = `select * from \"${
        envVars.dynamoDB.ANNOUNCEMENT_TABLE_NAME
      }\" where contains(videoTimeStamp, '${videoTimeStamp || newPath}')`;

      console.log('STARTED TO GET THE DATA FROM DB ANNOUCEMENTS', statement2);
      const data2 = await dbConfig.dbPartialQueryExecution(statement2);
      const items = await data2.Items;
      console.log('DONE TO GET THE DATA FROM DB ANNOUCEMENTS', items);

      if (items.length > 0) {
        await updateAnnouncement(items[0], status, videoTimeStamp || newPath);
      }
    }
    console.log('DONE TO UPDATE THE DATA TO DB ANNOUNCEMENTS');
  } catch (error) {
    console.log('ERROR IN PROCESSING THE DATA TO DB ', error);
    throw new Error(`ERROR IN PROCESSING THE DATA TO DB ${error}`);
  }
}

const updateAnnouncement = async (
  createAnnouncementDto,
  status,
  videoTimeStamp,
) => {
  // Get the video object from S3 and see if it exists
  if (
    createAnnouncementDto?.streamUrl?.key &&
    createAnnouncementDto.announcementType !== ANNOUNCEMENT_TYPE.VOTE
  ) {
    const updatePayload = {
      UpdateExpression: `set ${status} = :${status}, videoStatus = :videoStatus, updatedAt = :updatedAt`,
      ExpressionAttributeValues: {
        [`:${status}`]: true,
        ':videoStatus':
          status === 'queueProcessed'
            ? VIDEO_STATUS.SUCCESSFUL
            : VIDEO_STATUS.FAILED,
        ':updatedAt': new Date().toISOString(),
      },
    };

    await dbConfig.updateItemsToDatabase(
      envVars.dynamoDB.ANNOUNCEMENT_TABLE_NAME,
      {
        id: createAnnouncementDto.id,
      },
      updatePayload,
    );
  } else if (
    createAnnouncementDto.announcementType === ANNOUNCEMENT_TYPE.VOTE
  ) {
    const allvoteSubmittedAssets = await Promise.all(
      createAnnouncementDto.voteSubmittedAssets.map(async (item) => {
        if (item.videoTimeStamp === videoTimeStamp) {
          return {
            ...item,
            queueProcessed: status === 'queueProcessed',
            videoStatus:
              status === 'queueProcessed'
                ? VIDEO_STATUS.SUCCESSFUL
                : VIDEO_STATUS.FAILED,
          };
        } else {
          return item;
        }
      }),
    );

    const updatePayload = {
      UpdateExpression: `set ${status} = :${status}, videoStatus = :videoStatus, updatedAt = :updatedAt, voteSubmittedAssets = :voteSubmittedAssets`,
      ExpressionAttributeValues: {
        ':voteSubmittedAssets': allvoteSubmittedAssets,
        [`:${status}`]: true,
        ':videoStatus':
          status === 'queueProcessed'
            ? VIDEO_STATUS.SUCCESSFUL
            : VIDEO_STATUS.FAILED,
        ':updatedAt': new Date().toISOString(),
      },
    };

    await dbConfig.updateItemsToDatabase(
      envVars.dynamoDB.ANNOUNCEMENT_TABLE_NAME,
      { id: createAnnouncementDto.id },
      updatePayload,
    );
  }
};
