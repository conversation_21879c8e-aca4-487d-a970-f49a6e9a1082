import { ReturnValue } from '@aws-sdk/client-dynamodb';
import { dbConfig } from '.';

/**
 * This is function is used to update the data in the database
 * @param param0 { dbName, id, data }
 * @returns {Promise<UpdateCommandOutput>}
 */
export async function useUpdate({
  dbName,
  id,
  data,
}: {
  dbName: string;
  id: string;
  data: Record<string, any>;
}) {
  const dataCopy = { ...data };

  try {
    const updateExpression = Object.keys(dataCopy)
      .map((key) => `#${key} = :${key}`)
      .join(', ');

    const expressionAttributeValues = Object.keys(dataCopy).reduce(
      (acc, key) => {
        acc[`:${key}`] = dataCopy[key];
        return acc;
      },
      {},
    );
    const expressionAttributeNames = Object.keys(dataCopy).reduce(
      (acc, key) => {
        acc[`#${key}`] = key;
        return acc;
      },
      {},
    );

    const updateParams = {
      UpdateExpression: `set ${updateExpression}`,
      ExpressionAttributeValues: expressionAttributeValues,
      ExpressionAttributeNames: expressionAttributeNames,
    };

    const result = await dbConfig.updateItemsToDatabase(
      dbName,
      { id },
      updateParams,
      ReturnValue.ALL_NEW,
    );

    return result;
  } catch (error) {
    console.log(error);
    throw new Error(error);
  }
}
