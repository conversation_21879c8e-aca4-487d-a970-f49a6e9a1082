import { AttributeValue } from '@aws-sdk/client-dynamodb';
import { randomUUID } from 'crypto';
import { CreateUserDto } from 'src/users/dto/create-user.dto';

/**
 * @deprecated not in use as we are now using 'lib/dynamodb'
 */
export const dbUserParams = (createUserDto: CreateUserDto) => {
  const param: Record<string, AttributeValue> = {
    firstName: {
      S: createUserDto.firstName,
    },
    id: {
      S: randomUUID(),
    },
    lastName: {
      S: createUserDto.lastName,
    },
    gender: {
      S: createUserDto.gender,
    },
    birthday: {
      S: createUserDto.birthday,
    },
    email: {
      S: createUserDto.email,
    },
    cludId: {
      S: createUserDto.cludId,
    },
    isManager: {
      BOOL: createUserDto.isManager,
    },
    location: {
      S: createUserDto.location,
    },
    phone: {
      S: createUserDto.phoneNumber,
    },
    position: {
      S: createUserDto.position,
    },
    teamId: {
      S: createUserDto.teamId,
    },
    seasons: {
      L: createUserDto.experiences,
    },
  };

  for (const key in param) {
    if (Object.prototype.hasOwnProperty.call(param, key)) {
      const element = createUserDto[key];

      if (!element && key !== 'id') {
        delete param[key];
      }
    }
  }

  return param;
};

export const envVars = {
  liveLike: {
    LIVELIKE_CLIENT_ID: process.env.LIVELIKE_CLIENT_ID,
    LIVELIKE_TOKEN: process.env.LIVELIKE_TOKEN,
  },
  dynamoDB: {
    USERS_TABLE_NAME: process.env.USERS_TABLE_NAME,
    CLUBS_TABLE_NAME: process.env.CLUBS_TABLE_NAME,
    TEAMS_TABLE_NAME: process.env.TEAMS_TABLE_NAME,
    SEASONS_TABLE_NAME: process.env.SEASONS_TABLE_NAME,
    MATCHES_TABLE_NAME: process.env.MATCHES_TABLE_NAME,
    HIGHLIGHT_TABLE_NAME: process.env.HIGHLIGHT_TABLE_NAME,
    LEAGUES_TABLE_NAME: process.env.LEAGUES_TABLE_NAME,
    EXPERIENCE_TABLE_NAME: process.env.EXPERIENCE_TABLE_NAME,
    P2P_CHAT_TABLE_NAME: process.env.P2P_CHAT_TABLE_NAME,
    P2P_UNREAD_CHAT_TABLE_NAME: process.env.P2P_UNREAD_CHAT_TABLE_NAME,
    ANNOUNCEMENT_TABLE_NAME: process.env.ANNOUNCEMENT_TABLE_NAME,
    FOLLOW_TABLE_NAME: process.env.FOLLOW_TABLE_NAME,
    REFERENCE_TABLE_NAME: process.env.REFERENCE_TABLE_NAME,
    REPORTS_TABLE_NAME: process.env.REPORTS_TABLE_NAME,
    USERS_PHYSICAL_DATA_TABLE_NAME: process.env.USERS_PHYSICAL_DATA_TABLE_NAME,
    BLOCK_USERS_TABLE_NAME: process.env.BLOCK_USERS_TABLE_NAME,
    MOBILE_VERSION_MANAGER_TABLE_NAME:
      process.env.MOBILE_VERSION_MANAGER_TABLE_NAME,
  },
  aws: {
    awsRegion: process.env.PLAYER_AWS_REGION,
    awsCognitoClientSecret: process.env.AWS_COGNITO_CLIENT_SECRET,
    awsCognitoClientId: process.env.AWS_COGNITO_CLIENT_ID,
    awsUserPoolId: process.env.USER_POOL_ID,
    streamingBucket: process.env.STREAMING_BUCKET,
    sqsUrl: process.env.SQS_URL,
    imageBucket: process.env.IMAGE_BUCKET,
    videoBucket: process.env.VIDEO_BUCKET,
    backendLambdaServiceName: process.env.AWS_BACKEND_LAMBDA_SERVICE_NAME,
  },
  environment: process.env.NODE_ENV,
  isLocal: process.env.IS_LOCAL,
};

export const isProduction = envVars.environment === 'prod';

export const isLocal = envVars.isLocal === 'TRUE';
