import { DynamoDBClient, ReturnValue } from '@aws-sdk/client-dynamodb';
import {
  BatchGetCommand,
  BatchGetCommandInput,
  BatchGetCommandOutput,
  DeleteCommand,
  DeleteCommandInput,
  DeleteCommandOutput,
  DynamoDBDocumentClient,
  ExecuteStatementCommand,
  ExecuteStatementCommandInput,
  ExecuteStatementCommandOutput,
  GetCommand,
  GetCommandInput,
  GetCommandOutput,
  PutCommand,
  PutCommandInput,
  PutCommandOutput,
  QueryCommand,
  QueryCommandInput,
  QueryCommandOutput,
  ScanCommand,
  ScanCommandInput,
  ScanCommandOutput,
  UpdateCommand,
  UpdateCommandInput,
  UpdateCommandOutput,
} from '@aws-sdk/lib-dynamodb';
import { envVars, isLocal } from './constants';

let client;

if (!isLocal) {
  client = new DynamoDBClient({
    region: process.env.PLAYER_AWS_REGION,
  });
} else {
  client = new DynamoDBClient({
    region: 'localhost',
    endpoint: 'http://localhost:8000',
  });
}

const marshallOptions = {
  // Whether to automatically convert empty strings, blobs, and sets to `null`.
  convertEmptyValues: false, // false, by default.
  // Whether to remove undefined values while marshalling.
  removeUndefinedValues: false, // false, by default.
  // Whether to convert typeof object to map attribute.
  convertClassInstanceToMap: false, // false, by default.
};

const unmarshallOptions = {
  // Whether to return numbers as a string instead of converting them to native JavaScript numbers.
  wrapNumbers: false, // false, by default.
};

const translateConfig = { marshallOptions, unmarshallOptions };

// Create the DynamoDB Document client.
const dbDocClient = DynamoDBDocumentClient.from(client, translateConfig);

type DynamoDBKeys = keyof typeof envVars.dynamoDB;

/**
 * A function that inserts items to the database
 * @param tableName The table name you want to write to. example is the users table
 * @param params The table params you want to use.
 * @example <caption>Example usage</caption>
 * const dataResponse = await writeItemsToDatabase('users', {'name': 'eric', age: 20}})
 * @returns {PutItemCommandOutput}
 */
const writeItemsToDatabase = async (
  tableName: string,
  params: Record<string, any>,
): Promise<PutCommandOutput> => {
  try {
    const dbParams: PutCommandInput = {
      TableName: tableName,
      Item: params,
    };
    return await client.send(new PutCommand(dbParams));
  } catch (error) {
    throw new Error(error);
  }
};

/**
 * A function that updates items to the database
 * @param tableName The table name you want to write to. example is the users table
 * @param key The key used for the search/filter
 * @param updateParams The table params you want to use.
 * @param shouldAllowEmptyValues The condition to allow saving of empty field values
 * @param {ReturnValue} returnValue If the update should return new values or ...
 * @example <caption>Example usage</caption>
 * const dataResponse = await updateItemsToDatabase('users',
 *  tableName: "TABLE_NAME",
 *  key: {
 *    title: "MOVIE_NAME",
 *    year: "MOVIE_YEAR",
 *  },
 * {
 *   ProjectionExpression: "#r",
 *    ExpressionAttributeNames: { "#r": "rank" },
 *    UpdateExpression: "set info.plot = :p, info.#r = :r",
 *    ExpressionAttributeValues: {
 *      ":p": "MOVIE_PLOT",
 *      ":r": "MOVIE_RANK",
 *    },
 * "ALL_NEW"
 *}
 *
 *@link See how expressions works https://docs.aws.amazon.com/amazondynamodb/latest/developerguide/Expressions.Attributes.html
 * @link See example usage  https://docs.aws.amazon.com/amazondynamodb/latest/developerguide/example_dynamodb_UpdateItem_section.html
 * @returns {UpdateCommandOutput}
 */
const updateItemsToDatabase = async (
  tableName: string,
  key: Record<string, any>,
  updateParams?: Record<string, any>,
  returnValue?: ReturnValue,
  shouldAllowEmptyValues?: boolean,
): Promise<UpdateCommandOutput> => {
  try {
    const dbParams: UpdateCommandInput = {
      TableName: tableName,
      Key: key,
      /**Amazon DynamoDB returns all the item attributes by default. To get only some, rather than all of the attributes, use a projection expression.
A projection expression is a string that identifies the attributes that you want. To retrieve a single attribute, specify its name. For multiple attributes, the names must be comma-separated. */
      UpdateExpression: updateParams.UpdateExpression,
      ExpressionAttributeNames: updateParams.ExpressionAttributeNames,
      ExpressionAttributeValues: updateParams.ExpressionAttributeValues,
      ReturnValues: returnValue,
    };

    // delete the  empty keys
    if (!shouldAllowEmptyValues) {
      for (const key in dbParams) {
        if (Object.prototype.hasOwnProperty.call(dbParams, key)) {
          const element = dbParams[key];

          if (!element && key in dbParams) {
            delete dbParams[key];
          }
        }
      }
    }

    return await client.send(new UpdateCommand(dbParams));
  } catch (error) {
    throw new Error(error);
  }
};

/**
 * A function that gets an item from the database
 * @param tableName The table name you want to get item from. example is the users table
 * @param params The table params you want to use.
 * @example <caption>Example usage</caption>
 * const dataResponse = await getItemFromDatabase('users', {'id': '123', clubIdTeamId: '321})
 * @returns {GetItemCommandOutput}
 */
const getItemFromDatabase = async (
  tableName: string,
  params: Record<string, any>,
): Promise<GetCommandOutput> => {
  try {
    const dbParams: GetCommandInput = {
      TableName: tableName,
      Key: params,
    };
    return await client.send(new GetCommand(dbParams));
  } catch (error) {
    throw new Error(error);
  }
};
/**
 * A function that gets an item from the database by projection expression
 * @param tableName The table name you want to get item from. example is the users table
 * @param params The table params you want to use.
 * @example <caption>Example usage</caption>
 * const dataResponse = await getItemFromDatabase('users', {'id': '123', clubIdTeamId: '321})
 * @returns {GetItemCommandOutput}
 */
const getItemFromDatabaseByProjection = async (
  tableName: DynamoDBKeys,
  params: Omit<GetCommandInput, 'TableName'>,
): Promise<GetCommandOutput> => {
  try {
    const dbParams: GetCommandInput = {
      TableName: envVars.dynamoDB[tableName],
      ...params,
    };
    return await client.send(new GetCommand(dbParams));
  } catch (error) {
    throw new Error(error);
  }
};

/**
 * A function that gets all items from the database
 * @param tableName The table name you want to get item from. example is the users table
 * @example <caption>Example usage</caption>
 * const dataResponse = await getAllItemFromDatabase('users')
 * @returns {ScanCommandOutput}
 */
const getAllItemFromDatabase = async (
  tableName: string,
  params?: ScanCommandInput,
): Promise<ScanCommandOutput> => {
  try {
    const dbParams: ScanCommandInput = params
      ? {
          TableName: tableName,
          ...params,
        }
      : {
          TableName: tableName,
        };

    for (const key in dbParams) {
      if (Object.prototype.hasOwnProperty.call(dbParams, key)) {
        const element = dbParams[key];

        if (!element && key in dbParams) {
          delete dbParams[key];
        }
      }
    }

    const data = await dbDocClient.send(new ScanCommand(dbParams));
    return data;
  } catch (error) {
    throw new Error(error);
  }
};

/**
 * A function that removes an items from the database
 * @param tableName The table name you want to get item from. example is the users table
 * @example <caption>Example usage</caption>
 * const dataResponse = await removeItemFromDatabase('users', {})
 * @returns {DeleteCommandOutput}
 */
const removeItemFromDatabase = async (
  tableName: string,
  param: Record<string, any>,
): Promise<DeleteCommandOutput> => {
  try {
    const dbParams: DeleteCommandInput = {
      TableName: tableName,
      Key: param,
    };
    const data = await dbDocClient.send(new DeleteCommand(dbParams));
    return data;
  } catch (error) {
    throw new Error(error);
  }
};

/**
 * A function that queries an items from the database
 * @param tableName The table name you want to get item from. example is the users table
 * @example <caption>Example usage</caption>
 * const dataResponse = await queryItemFromDatabase('users', {})
 * @returns {QueryCommandOutput}
 */
const queryItemFromDatabase = async (
  tableName: string,
  param: QueryCommandInput,
): Promise<QueryCommandOutput> => {
  try {
    const dbParams: QueryCommandInput = {
      TableName: tableName,
      ...param,
    };
    const data = await dbDocClient.send(new QueryCommand(dbParams));
    return data;
  } catch (error) {
    throw new Error(error);
  }
};

/**
 * A function that queries an items from the database with partial sql queries
 * @param statement The partial sql query to use
 * @example <caption>Example usage</caption>
 * const dataResponse = await dbPartialQueryExecution('select * from tableName')
 * @returns {QueryCommandOutput}
 */
const dbPartialQueryExecution = async (
  statement: string,
): Promise<ExecuteStatementCommandOutput> => {
  try {
    const dbParams: ExecuteStatementCommandInput = {
      Statement: statement,
    };
    const data = await dbDocClient.send(new ExecuteStatementCommand(dbParams));
    return data;
  } catch (error) {
    throw new Error(error);
  }
};

/**
 * A function that get items from the database based on batch request in one call
 * @param statement The partial sql query to use
 * @example <caption>Example usage</caption>
 * const dataResponse = await dbBatchGetItemsRequest('users', [{ id: '1' }, { id: '2' }])
 * @returns {BatchGetItemCommandOutput}
 */
const dbBatchGetItemsRequest = async (
  tableName: string,
  keys: Array<Record<string, any>>,
  ProjectionExpression?: string,
): Promise<BatchGetCommandOutput> => {
  try {
    const dbParams: BatchGetCommandInput = {
      RequestItems: {
        [tableName]: {
          Keys: keys,
          ProjectionExpression,
        },
      },
    };

    if (
      dbParams.RequestItems?.[tableName]?.ProjectionExpression === undefined
    ) {
      delete dbParams.RequestItems[tableName].ProjectionExpression;
    }

    const command = new BatchGetCommand(dbParams);
    const response = await client.send(command);

    return response;
  } catch (error) {
    throw new Error(error);
  }
};

export function formatFilterExpressionForArray(values: string[]) {
  const expressionAttributeValues = values.reduce(
    (acc, value, index) => ({
      ...acc,
      [`:value${index}`]: value,
    }),
    {},
  );
  const filterExpression = Object.keys(expressionAttributeValues).toString();

  return { expressionAttributeValues, filterExpression };
}

export {
  dbBatchGetItemsRequest,
  dbDocClient,
  dbPartialQueryExecution,
  DynamoDBKeys,
  getAllItemFromDatabase,
  getItemFromDatabase,
  getItemFromDatabaseByProjection,
  queryItemFromDatabase,
  removeItemFromDatabase,
  updateItemsToDatabase,
  writeItemsToDatabase,
};
