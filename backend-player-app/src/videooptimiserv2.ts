import fs from 'fs';
import {
  S3Client,
  GetObjectCommand,
  GetObjectCommandInput,
  GetObjectCommandOutput,
  PutObjectCommand,
} from '@aws-sdk/client-s3';

import { envVars } from './config/constants';
import {
  ensureDirectoryExists,
  generateThumbnail,
  generateThumbnailV2,
  optimizeVideo,
  optimizeVideoV2,
  setFFPath,
} from './utils/helpers';
import {
  SQSClient,
  DeleteMessageCommand,
  DeleteMessageCommandInput,
} from '@aws-sdk/client-sqs';
import { dbConfig } from './config';

const s3Client = new S3Client({ region: envVars.aws.awsRegion });

const sqsClient = new SQSClient({ region: envVars.aws.awsRegion });

export const videoOptimizerFuncV2 = async (event: any, context) => {
  try {
    // Set up ffmpeg path
    await setFFPath();
    console.log('THE EVENT', event);
    console.log('THE BODY', event.Records[0].body);

    // Extracting information from the S3 event
    const s3Records = JSON.parse(event.Records[0].body).Records[0].s3;
    const bucketName = s3Records.bucket.name;
    const key = s3Records.object.key;
    console.log('the current bucket bucketName', bucketName);
    console.log('the current file key', key);

    // Extract the filename and extension
    const fileName = key.split('/').pop();
    const fileExtension = fileName.split('.').pop();

    // Get the video object from S3
    const s3Params: GetObjectCommandInput = { Bucket: bucketName, Key: key };
    const { Body }: GetObjectCommandOutput = await s3Client.send(
      new GetObjectCommand(s3Params),
    );

    // Transform the S3 object to a byte array and save it locally
    // Optimize the video
    console.log('transform of s3 item started');
    const transformedBody = await Body.transformToByteArray();
    const localFilePath = `/tmp/${fileName}`;
    fs.writeFileSync(localFilePath, transformedBody);

    // Create a unique output directory for each video processing request
    const outputDir = '/tmp/output';
    ensureDirectoryExists(outputDir);

    // Optimize the video
    console.log('Optimization started');
    await optimizeVideoV2(localFilePath, outputDir, fileName);
    console.log('Optimization complete');

    // Generate thumbnails
    // const thumbnailPath = '/tmp/images';
    // ensureDirectoryExists(thumbnailPath);
    // await generateThumbnailV2(localFilePath, thumbnailPath);
    // console.log('Thumbnail generation complete');

    // Upload optimized video segments to S3
    const newPath = key.split('--')[1];
    const files = fs.readdirSync(outputDir);
    const uploadPromises = files.map(async (file, idx) => {
      const filePath = `${outputDir}/${file}`;
      const fileData = fs.readFileSync(filePath);
      const uploadParams = {
        Bucket: envVars.aws.streamingBucket,
        Key: `${newPath}/${file}`,
        Body: fileData,
        ContentType: filePath.includes('.m3u8')
          ? 'application/vnd.apple.mpegurl'
          : filePath.includes('.ts')
          ? 'video/mp2t'
          : 'image/png',
      };
      await s3Client.send(new PutObjectCommand(uploadParams));
      console.log('Uploaded segment:', idx, filePath);
      fs.unlinkSync(filePath); // Delete the processed segment file from the file system
    });
    await Promise.all(uploadPromises);

    // Upload the thumbnail to S3
    // const pngFiles = fs.readdirSync('/tmp/images');

    // const thumbnailFile = pngFiles.map(async (file) => {
    //   const filePath = `/tmp/images/${file}`;
    //   const fileData = fs.readFileSync(filePath);

    //   const uploadParams = {
    //     Bucket: envVars.aws.streamingBucket,
    //     Key: `${newPath}/thumbnail.png`,
    //     Body: fileData,
    //     ContentType: 'image/png',
    //   };
    //   await s3Client.send(new PutObjectCommand(uploadParams));
    //   console.log('Uploaded thumbnail:', filePath);
    //   fs.unlinkSync(filePath); // Delete the processed thumbnail file from the file system
    // });

    // await Promise.all(thumbnailFile);

    // console.log('Thumbnail uploaded');

    // Clean up local files
    fs.unlinkSync(localFilePath);
    fs.rmdirSync(outputDir);
    // fs.rmdirSync(thumbnailPath);

    // remove item from sqs queue
    const sqsDeleteInputParams: DeleteMessageCommandInput = {
      QueueUrl: envVars.aws.sqsUrl,
      ReceiptHandle: event.Records[0].receiptHandle,
    };

    // Getting the item from db
    const dbName = envVars.dynamoDB.HIGHLIGHT_TABLE_NAME;
    const statement = `select * from \"${dbName}\" where contains(url, '${newPath}')`;
    console.log('Started to get the data from db');

    const data = await dbConfig.dbPartialQueryExecution(statement);

    console.log('Done to get the data from db', data.Items);
    // update the db
    const highlightsUpdateParams = {
      UpdateExpression: 'set queueProcessed = :queueProcessed',
      ExpressionAttributeValues: {
        ':queueProcessed': true,
      },
    };

    if (data.Items.length > 0) {
      const currentHighlight = data.Items[0];
      await dbConfig.updateItemsToDatabase(
        dbName,
        {
          userId: currentHighlight.user.id,
          id: currentHighlight.id,
        },
        highlightsUpdateParams,
      );
      console.log('Done to process the data to db highlights');
    } else {
      // Getting the item from db
      const statement2 = `select * from \"${envVars.dynamoDB.ANNOUNCEMENT_TABLE_NAME}\" where contains(assetUrl, '${newPath}')`;
      console.log('Started to get the data from db annoucements');

      const data2 = await dbConfig.dbPartialQueryExecution(statement2);

      console.log('Done to get the data from db annoucements', data.Items);

      if (data2.Items.length > 0) {
        // update the db. TODO: On save of the announcement of the highlight, check if the item is already in s3 and then update the db
        const annoucementsUpdateParams = {
          UpdateExpression: 'set queueProcessed = :queueProcessed',
          ExpressionAttributeValues: {
            ':queueProcessed': true,
          },
        };

        const currentAnnoucement = data.Items[0];
        await dbConfig.updateItemsToDatabase(
          envVars.dynamoDB.ANNOUNCEMENT_TABLE_NAME,
          {
            id: currentAnnoucement.id,
          },
          annoucementsUpdateParams,
        );
        console.log('Done to process the data to db annoucements');
      }
    }
    // Done with db tasks
    const command = new DeleteMessageCommand(sqsDeleteInputParams);
    const response = await sqsClient.send(command);

    console.log('Message deleted from SQS:', response);

    return {
      statusCode: 200,
      body: 'Video optimization and processing complete!',
    };
  } catch (error) {
    console.error('Error:', error);
    return {
      statusCode: 500,
      body: `Video optimization and processing failed. ${JSON.stringify(
        error,
      )}`,
    };
  }
};
