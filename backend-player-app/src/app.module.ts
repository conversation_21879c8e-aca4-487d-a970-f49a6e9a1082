import { Module } from '@nestjs/common';
import { AnnouncementModule } from './announcement/announcement.module';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './auth/auth.module';
import { ClubModule } from './club/club.module';
import { CommentsModule } from './comments/comments.module';
import { ExperienceStatsModule } from './experience-stats/experience-stats.module';
import { FollowModule } from './follow/follow.module';
import { HighlightsModule } from './highlights/highlights.module';
import { LeaguesModule } from './leagues/leagues.module';
import { MatchesModule } from './matches/matches.module';
import { MobileVersionManagerModule } from './mobile-version-manager/mobile-version-manager.module';
import { P2pChatModule } from './p2p-chat/p2p-chat.module';
import { ReferencesModule } from './references/references.module';
import { ReportsModule } from './reports/reports.module';
import { SeasonsModule } from './seasons/seasons.module';
import { TeamsModule } from './teams/teams.module';
import { UploaderModule } from './uploader/uploader.module';
import { UsersModule } from './users/users.module';

@Module({
  imports: [
    UsersModule,
    AuthModule,
    ClubModule,
    TeamsModule,
    SeasonsModule,
    MatchesModule,
    LeaguesModule,
    HighlightsModule,
    ExperienceStatsModule,
    P2pChatModule,
    AnnouncementModule,
    FollowModule,
    ReferencesModule,
    ReportsModule,
    MobileVersionManagerModule,
    UploaderModule,
    CommentsModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
