import { MiddlewareConsumer, Module, NestModule, RequestMethod } from '@nestjs/common';
import { LeaguesService } from './leagues.service';
import { LeaguesController } from './leagues.controller';
import { AdminAuthMiddleware } from 'src/auth/guards/auth.middleware';

@Module({
  controllers: [LeaguesController],
  providers: [LeaguesService],
})
export class LeaguesModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(AdminAuthMiddleware).forRoutes('leagues'); // Blocking these routes with admin auth middleware pending when its needed to be used for players
  }
}
