import {
  Is<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
  IsUrl,
} from 'class-validator';

interface LeagueTable {
  teamName: string;
  numberOfMatchPlayed: number;
  winnings: number;
  drawers: number;
  loss: number;
  numberOfGoals: number;
  totalPoints: number;
  teamId: string;
}

interface LeagueStats {
  topScorers: Array<any>;
  topAssists: Array<any>;
  topCleanSheet: Array<any>;
  topAppearance: Array<any>;
}

export enum PUBLISHSTATUS {
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED',
  PREVIEW = 'PREVIEW',
}

export class CreateLeagueDto {
  @IsString()
  @IsOptional()
  logoUrl?: string;

  @IsString()
  @IsNotEmpty()
  leagueName: string;

  @IsString()
  @IsOptional()
  id: string;

  @IsArray()
  leagueTable: Array<LeagueTable>;

  @IsObject()
  @IsOptional()
  leagueStats: LeagueStats;

  @IsString()
  seasonTitle: string;

  @IsString()
  seasonId: string;

  @IsOptional()
  @IsEnum(PUBLISHSTATUS, {
    message: 'publishStatus can be either (DRAFT | PUBLISHED | PREVIEW)',
  })
  publishStatus: PUBLISHSTATUS;

  @IsString()
  @IsOptional()
  publisherId?: string;
}
