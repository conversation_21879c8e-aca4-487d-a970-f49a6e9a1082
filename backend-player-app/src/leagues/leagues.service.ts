import {
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { CreateLeagueDto, PUBLISHSTATUS } from './dto/create-league.dto';
import { UpdateLeagueDto } from './dto/update-league.dto';
import { dbConfig } from 'src/config';
import { response } from 'src/utils/response';
import { envVars } from 'src/config/constants';
import { ReturnValue } from '@aws-sdk/client-dynamodb';
import { randomUUID } from 'crypto';
import uniqWith from 'lodash.uniqwith';
import iseEqual from 'lodash.isequal';

@Injectable()
export class LeaguesService {
  dbName = envVars.dynamoDB.LEAGUES_TABLE_NAME;

  async create(createLeagueDto: CreateLeagueDto) {
    const dbParams = {
      ...createLeagueDto,
      id: randomUUID(),
      publishStatus: createLeagueDto.publishStatus || PUBLISHSTATUS.DRAFT,
      leagueName: createLeagueDto.leagueName,
      leagueStats: createLeagueDto.leagueStats || {},
      leagueTable: createLeagueDto.leagueTable || [],
      logoUrl: createLeagueDto.logoUrl || '',
      seasonId: createLeagueDto.seasonId || '',
      seasonTitle: createLeagueDto.seasonTitle || '',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    try {
      const data = await dbConfig.writeItemsToDatabase(this.dbName, dbParams);

      return response({
        message: 'League created successfully',
        status: 1,
        data: { ...data, ...dbParams },
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async findAll() {
    try {
      const data = await dbConfig.getAllItemFromDatabase(this.dbName);

      return response({ message: 'Success', status: 1, data });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async findOne(id: string) {
    try {
      const data = await dbConfig.getItemFromDatabase(this.dbName, {
        id,
      });

      return response({ message: 'Success', status: 1, data });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async update(updateLeagueDto: UpdateLeagueDto) {
    const { Item: existingLeagueData } = await dbConfig.getItemFromDatabase(
      this.dbName,
      {
        id: updateLeagueDto.id,
      },
    );

    if (!existingLeagueData) {
      throw new NotFoundException('The league is not found');
    }

    const topScorers = uniqWith(
      [
        ...(existingLeagueData?.leagueStats?.topScorers || []),
        ...(updateLeagueDto.leagueStats?.topScorers || []),
      ],
      iseEqual,
    );

    const topAssists = uniqWith(
      [
        ...(existingLeagueData?.leagueStats?.topAssists || []),
        ...(updateLeagueDto.leagueStats?.topAssists || []),
      ],
      iseEqual,
    );

    const topCleanSheet = uniqWith(
      [
        ...(existingLeagueData?.leagueStats?.topCleanSheet || []),
        ...(updateLeagueDto.leagueStats?.topCleanSheet || []),
      ],
      iseEqual,
    );

    const topAppearance = uniqWith(
      [
        ...(existingLeagueData?.leagueStats?.topAppearance || []),
        ...(updateLeagueDto.leagueStats?.topAppearance || []),
      ],
      iseEqual,
    );

    const leagueTable = uniqWith(
      [
        ...(existingLeagueData?.leagueTable || []),
        ...(updateLeagueDto?.leagueTable || []),
      ],
      iseEqual,
    );

    try {
      const updateParams = {
        UpdateExpression: `
          set leagueName = :ln, 
          logoUrl = :lUrl, 
          seasonTitle = :st, 
          seasonId = :sId, 
          publishStatus = :publishStatus, 
          publisherId = :publisherId, 
          updatedAt = :uat, 
          leagueStats.topScorers = :topScorers,     
          leagueStats.topAssists = :topAssists,
          leagueStats.topCleanSheet = :topCleanSheet,
          leagueStats.topAppearance = :topAppearance,
          leagueTable = :leagueTable`,
        ExpressionAttributeValues: {
          ':ln':
            updateLeagueDto.leagueName || existingLeagueData?.leagueName || '',
          ':lUrl': updateLeagueDto.logoUrl || existingLeagueData?.logoUrl || '',
          ':topScorers': topScorers,
          ':topAssists': topAssists,
          ':topCleanSheet': topCleanSheet,
          ':sId': updateLeagueDto.seasonId || existingLeagueData?.seasonId,
          ':publishStatus':
            updateLeagueDto.publishStatus ||
            existingLeagueData?.publishStatus ||
            '',
          ':publisherId':
            updateLeagueDto.publisherId ||
            existingLeagueData?.publisherId ||
            '',
          ':st': updateLeagueDto.seasonTitle || existingLeagueData?.seasonTitle,
          ':topAppearance': topAppearance,
          ':leagueTable': leagueTable || [],
          ':uat': new Date().toISOString(),
        },
        // ExpressionAttributeNames: {
        // '#topScorers': 'topScorers',
        // '#topAssists': 'topAssists',
        // '#topCleanSheet': 'topCleanSheet',
        // '#topAppearance': 'topAppearance',
        // '#leagueTable': 'leagueTable',
        // },
      };

      const data = await dbConfig.updateItemsToDatabase(
        this.dbName,
        { id: updateLeagueDto.id },
        updateParams,
        ReturnValue.ALL_NEW,
      );

      return response({
        message: 'Updated successfully',
        status: 1,
        data: data.Attributes,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async remove(id: string) {
    try {
      const data = await dbConfig.removeItemFromDatabase(this.dbName, {
        id,
      });

      return response({ message: 'Deleted successfully', status: 1, data });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }
}
