import LiveLike from '@livelike/javascript';
import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { dbConfig } from 'src/config';
import { envVars } from 'src/config/constants';
import { response } from 'src/utils/response';
import { initLiveLike } from '../utils/LiveLike';

@Injectable()
export class FollowService {
  dbName = envVars.dynamoDB.FOLLOW_TABLE_NAME;

  formatUser(liveLikeProfile: any) {
    let customData: any = {};

    if (liveLikeProfile.custom_data) {
      try {
        customData = JSON.parse(liveLikeProfile.custom_data);
      } catch (error) {
        Logger.warn(
          `Failed to parse custom_data for LiveLike profile ${liveLikeProfile.id}: ${error.message}`,
        );
      }
    }

    return {
      id: customData?.userId || null,
      liveLikeProfileId: liveLikeProfile.id,
      firstName: customData?.firstName || null,
      lastName: customData?.lastName || null,
      photoUrl: customData?.photoUrl || null,
      clubName: customData?.clubName || null,
      teamName: customData?.teamName || null,
    };
  }

  async getFollowers(user: Record<string, any>) {
    await initLiveLike(user);

    try {
      const followers = await LiveLike.getProfileRelationships({
        relationshipTypeKey: 'follow',
        toProfileId: user.liveLikeProfileId,
      });

      return followers.results.map((follower) =>
        this.formatUser(follower.from_profile),
      );
    } catch (error) {
      Logger.error(`Get followers error: ${error.message}`);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async getFollowing(user: Record<string, any>) {
    await initLiveLike(user);

    try {
      const following = await LiveLike.getProfileRelationships({
        relationshipTypeKey: 'follow',
        fromProfileId: user.liveLikeProfileId,
      });

      return following.results.map((follower) =>
        this.formatUser(follower.to_profile),
      );
    } catch (error) {
      Logger.error(`Get following error: ${error.message}`);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async follow({
    user,
    followingUserId,
  }: {
    user: Record<string, any>;
    followingUserId: string;
  }) {
    let otherUser: Record<string, any>;

    try {
      const { Item } = await dbConfig.getItemFromDatabase(
        envVars.dynamoDB.USERS_TABLE_NAME,
        {
          id: followingUserId,
        },
      );

      if (!Item) {
        return new NotFoundException(
          response({
            message: 'Following user not found',
            status: 0,
            data: null,
          }),
        );
      }

      otherUser = Item;
    } catch (error) {
      Logger.error(`Follow error: ${error.message}`);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }

    await initLiveLike(user);

    try {
      const profileRelationship = await LiveLike.createProfileRelationship({
        relationshipTypeKey: 'follow',
        fromProfileId: user.liveLikeProfileId,
        toProfileId: otherUser.liveLikeProfileId,
      });

      Logger.debug('Follow success', {
        followingUserId: user.id,
        followingLiveLikeId: user.liveLikeProfileId,
        followedUserId: otherUser.id,
        followedLiveLikeId: otherUser.liveLikeProfileId,
      });

      return {
        followingUser: this.formatUser(profileRelationship.from_profile),
        followedUser: this.formatUser(profileRelationship.to_profile),
      };
    } catch (error) {
      Logger.error(`Follow error: ${error.message}`);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async unfollow({
    user,
    followingUserId,
  }: {
    user: Record<string, any>;
    followingUserId: string;
  }) {
    let otherUser: Record<string, any>;

    try {
      const { Item } = await dbConfig.getItemFromDatabase(
        envVars.dynamoDB.USERS_TABLE_NAME,
        {
          id: followingUserId,
        },
      );

      if (!Item) {
        return new NotFoundException(
          response({
            message: 'Following user not found',
            status: 0,
            data: null,
          }),
        );
      }

      otherUser = Item;
    } catch (error) {
      Logger.error(`Unfollow error: ${error.message}`);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }

    await initLiveLike(user);

    try {
      const profileRelationships = await LiveLike.getProfileRelationships({
        relationshipTypeKey: 'follow',
        fromProfileId: user.liveLikeProfileId,
        toProfileId: otherUser.liveLikeProfileId,
      });

      if (profileRelationships.count === 0) {
        return new BadRequestException(
          response({
            message: 'You do not follow this user',
            status: 0,
            data: null,
          }),
        );
      }

      const profileRelationship = profileRelationships.results[0];
      await LiveLike.deleteProfileRelationship({
        relationshipId: profileRelationship.id,
      });

      Logger.debug('Unfollow success', {
        unfollowingUserId: user.id,
        unfollowingLiveLikeId: user.liveLikeProfileId,
        unfollowedUserId: otherUser.id,
        unfollowedLiveLikeId: otherUser.liveLikeProfileId,
      });

      return {
        unfollowingUser: this.formatUser(profileRelationship.from_profile),
        unfollowedUser: this.formatUser(profileRelationship.to_profile),
      };
    } catch (error) {
      Logger.error(`Unfollow error: ${error.message}`);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }
}
