import { Body, Controller, Delete, Param, Post, Request } from '@nestjs/common';
import { FollowDto } from './dto/follow.dto';
import { FollowService } from './follow.service';

@Controller('follow')
export class FollowController {
  constructor(private readonly followService: FollowService) {}

  @Post()
  follow(@Body() followDto: FollowDto, @Request() req: any) {
    return this.followService.follow({
      user: req.user,
      followingUserId: followDto.followingUserId,
    });
  }

  @Delete(':followingUserId')
  unfollow(
    @Param('followingUserId') followingUserId: string,
    @Request() req: any,
  ) {
    return this.followService.unfollow({
      user: req.user,
      followingUserId,
    });
  }
}
