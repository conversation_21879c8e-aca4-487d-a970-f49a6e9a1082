import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Put,
} from '@nestjs/common';
import { ExperienceStatsService } from './experience-stats.service';
import { CreateExperienceStatDto } from './dto/create-experience-stat.dto';
import { UpdateExperienceStatDto } from './dto/update-experience-stat.dto';
import { CurrentProfile } from 'src/shared/decorators/currentProfile';
import {
  OwnerOrAdminCanAccess,
  OwnerOrAdminCanAccessByResource,
} from 'src/shared/decorators/shouldAllowAccess';

@Controller('experience-stats')
export class ExperienceStatsController {
  constructor(
    private readonly experienceStatsService: ExperienceStatsService,
  ) {}

  @Post()
  @OwnerOrAdminCanAccess('userId')
  create(
    @Body() createExperienceStatDto: CreateExperienceStatDto,
    @CurrentProfile() user,
  ) {
    return this.experienceStatsService.create(createExperienceStatDto, user);
  }

  @Get(':userId')
  findAllByUserId(@Param('userId') userId: string) {
    return this.experienceStatsService.findAllByUserId(userId);
  }

  @Get('all-experiences')
  findAll() {
    return this.experienceStatsService.findAll();
  }

  @Put('manual')
  @OwnerOrAdminCanAccess('userId')
  async updateManual(@Body() allnewExperiencesDto: any) {
    try {
      const allPromises = allnewExperiencesDto.map((exp) => {
        return this.experienceStatsService.updateManual(exp.id, exp);
      });

      await Promise.all(allPromises);

      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  @Put(':id')
  @OwnerOrAdminCanAccess('userId')
  update(
    @Param('id') id: string,
    @Body() updateExperienceStatDto: UpdateExperienceStatDto,
  ) {
    return this.experienceStatsService.update(id, updateExperienceStatDto);
  }

  @Delete(':id')
  @OwnerOrAdminCanAccessByResource({
    key: 'id',
    resourceName: 'EXPERIENCE_TABLE_NAME',
  })
  remove(@Param('id') id: string) {
    return this.experienceStatsService.remove(id);
  }
}
