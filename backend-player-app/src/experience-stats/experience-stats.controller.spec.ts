import { Test, TestingModule } from '@nestjs/testing';
import { ExperienceStatsController } from './experience-stats.controller';
import { ExperienceStatsService } from './experience-stats.service';

describe('ExperienceStatsController', () => {
  let controller: ExperienceStatsController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ExperienceStatsController],
      providers: [ExperienceStatsService],
    }).compile();

    controller = module.get<ExperienceStatsController>(
      ExperienceStatsController,
    );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
