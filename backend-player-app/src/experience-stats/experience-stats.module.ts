import {
  MiddlewareConsumer,
  Module,
  NestModule,
  RequestMethod,
} from '@nestjs/common';
import { ExperienceStatsService } from './experience-stats.service';
import { ExperienceStatsController } from './experience-stats.controller';
import {
  AuthMiddleware,
  AdminAuthMiddleware,
} from 'src/auth/guards/auth.middleware';

@Module({
  controllers: [ExperienceStatsController],
  providers: [ExperienceStatsService],
})
export class ExperienceStatsModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(AuthMiddleware)
      .forRoutes('experience-stats')
      .apply(AdminAuthMiddleware)
      .forRoutes('experience-stats/all-experiences');
  }
}
