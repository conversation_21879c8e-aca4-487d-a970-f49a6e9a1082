import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { CreateExperienceStatDto } from './dto/create-experience-stat.dto';
import { UpdateExperienceStatDto } from './dto/update-experience-stat.dto';
import { envVars } from 'src/config/constants';
import { dbConfig } from 'src/config';
import { randomUUID } from 'crypto';
import { response } from 'src/utils/response';
import { ReturnValue } from '@aws-sdk/client-dynamodb';

@Injectable()
export class ExperienceStatsService {
  dbName = envVars.dynamoDB.EXPERIENCE_TABLE_NAME;

  async create(createExperienceStatDto: CreateExperienceStatDto, user) {
    try {
      const data = await dbConfig.writeItemsToDatabase(this.dbName, {
        ...createExperienceStatDto,
        userId: createExperienceStatDto?.userId || user.id,
        teamId: createExperienceStatDto?.teamId || '',
        clubId: createExperienceStatDto?.clubId || '',
        id: randomUUID(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });

      return response({ message: 'Success', data, status: 1 });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async findAll() {
    try {
      const allExp = await dbConfig.getAllItemFromDatabase(this.dbName);
      return response({ data: allExp.Items, message: 'success', status: 1 });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async findAllByUserId(userId: string) {
    try {
      const statement = `select * from \"${this.dbName}\"  where \"userId\" = '${userId}'`;

      const data = await dbConfig.dbPartialQueryExecution(statement);

      return response({
        message: 'Successfully',
        status: 1,
        data: data.Items,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async update(id: string, updateExperienceStatDto: UpdateExperienceStatDto) {
    try {
      const existingExperience = await dbConfig.getItemFromDatabase(
        this.dbName,
        {
          id,
        },
      );

      if (!existingExperience?.Item) {
        return response({ message: 'Experience not founf', status: 0 });
      }

      const updateParams = {
        UpdateExpression:
          'set teamId = :tId, clubId = :cId, teamName = :tn, minsPlayed = :minsPlayed, teamLogo = :tl, appearances = :appr,  goals = :gls, assists = :assists, cleanSheets = :cns, seasonName = :seasonName, updatedAt = :uat',
        ExpressionAttributeValues: {
          ':tn':
            updateExperienceStatDto.teamName ||
            existingExperience?.Item?.teamName ||
            '',
          ':tId':
            updateExperienceStatDto.teamId ||
            existingExperience?.Item?.teamId ||
            '',
          ':cId':
            updateExperienceStatDto.clubId ||
            existingExperience?.Item?.clubId ||
            '',
          ':tl':
            updateExperienceStatDto.teamLogo ||
            existingExperience?.Item?.teamLogo ||
            '',
          ':appr':
            updateExperienceStatDto.appearances !== undefined
              ? updateExperienceStatDto.appearances
              : existingExperience?.Item?.appearances || '',
          ':gls':
            updateExperienceStatDto.goals !== undefined
              ? updateExperienceStatDto.goals
              : existingExperience?.Item?.goals || '',
          ':minsPlayed':
            updateExperienceStatDto.minsPlayed !== undefined
              ? updateExperienceStatDto.minsPlayed
              : existingExperience?.Item?.minsPlayed || '',

          ':assists':
            updateExperienceStatDto.assists !== undefined
              ? updateExperienceStatDto.assists
              : existingExperience?.Item?.assists || '',
          ':cns':
            updateExperienceStatDto.cleanSheets !== undefined
              ? updateExperienceStatDto.cleanSheets
              : existingExperience?.Item?.cleanSheets || '',
          ':seasonName':
            updateExperienceStatDto.seasonName ||
            existingExperience?.Item?.seasonName ||
            '',
          ':uat': new Date().toISOString(),
        },
      };

      const data = await dbConfig.updateItemsToDatabase(
        this.dbName,
        { id },
        updateParams,
        ReturnValue.ALL_NEW,
      );

      return response({
        message: 'Updated experience',
        data: data.Attributes,
        status: 1,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async updateManual(
    id: string,
    updateExperienceStatDto: UpdateExperienceStatDto,
  ) {
    try {
      const updateParams = {
        UpdateExpression:
          'set teamId = :tId, clubId = :cId, teamName = :tn, minsPlayed = :minsPlayed, teamLogo = :tl, appearances = :appr,  goals = :gls, assists = :assists, cleanSheets = :cns, seasonName = :seasonName, updatedAt = :uat',
        ExpressionAttributeValues: {
          ':tn': updateExperienceStatDto.teamName || '',
          ':tId': updateExperienceStatDto.teamId || '',
          ':cId': updateExperienceStatDto.clubId || '',
          ':tl': updateExperienceStatDto.teamLogo || '',
          ':appr':
            updateExperienceStatDto.appearances !== undefined
              ? updateExperienceStatDto.appearances
              : '',
          ':gls':
            updateExperienceStatDto.goals !== undefined
              ? updateExperienceStatDto.goals
              : '',
          ':minsPlayed':
            updateExperienceStatDto.minsPlayed !== undefined
              ? updateExperienceStatDto.minsPlayed
              : 0,

          ':assists':
            updateExperienceStatDto.assists !== undefined
              ? updateExperienceStatDto.assists
              : '',
          ':cns':
            updateExperienceStatDto.cleanSheets !== undefined
              ? updateExperienceStatDto.cleanSheets
              : '',
          ':seasonName': updateExperienceStatDto.seasonName || '',
          ':uat': new Date().toISOString(),
        },
      };

      const data = await dbConfig.updateItemsToDatabase(
        this.dbName,
        { id },
        updateParams,
        ReturnValue.ALL_NEW,
      );

      return response({
        message: 'Updated experience',
        data: data.Attributes,
        status: 1,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async remove(id: string) {
    try {
      const data = await dbConfig.removeItemFromDatabase(this.dbName, {
        id,
      });

      return response({ message: 'Deleted successfully', status: 1, data });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }
}
