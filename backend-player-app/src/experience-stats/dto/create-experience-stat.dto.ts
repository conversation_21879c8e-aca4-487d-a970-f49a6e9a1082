import { IsNotEmpty, <PERSON>N<PERSON><PERSON>, IsOptional, IsString } from 'class-validator';
export class CreateExperienceStatDto {
  @IsNotEmpty()
  @IsString()
  teamName: string;

  @IsString()
  @IsOptional()
  teamId?: string;

  @IsOptional()
  @IsString()
  @IsOptional()
  clubId?: string;

  @IsOptional()
  teamLogo: string;

  @IsNumber()
  @IsOptional()
  appearances;

  @IsNumber()
  @IsOptional()
  goals: number;

  @IsNumber()
  @IsOptional()
  assists;

  @IsNumber()
  @IsOptional()
  cleanSheets;

  @IsNumber()
  @IsOptional()
  minsPlayed: number;

  @IsString()
  @IsNotEmpty()
  seasonName;

  @IsString()
  @IsOptional()
  userId?: string;
}
