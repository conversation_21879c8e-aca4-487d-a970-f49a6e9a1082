import { ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { UserType } from 'src/users/dto/create-user.dto';
import {
  OwnerAndAdminCanAccess,
  OwnerAndAdminCanAccessByResource,
} from './shouldAllowAccess';
import * as db from 'src/config/database';

jest.mock('src/config/database', () => ({
  getItemFromDatabaseByProjection: jest.fn(),
}));

describe('OwnerAndAdminCanAccess', () => {
  let guard: OwnerAndAdminCanAccess;
  let reflector: Reflector;

  beforeEach(() => {
    reflector = new Reflector();
    guard = new OwnerAndAdminCanAccess(reflector);
  });

  const getContextMock = (
    user: any,
    sourceId: any,
    paramKey = 'userId',
  ): ExecutionContext =>
    ({
      switchToHttp: () => ({
        getRequest: () => ({
          user,
          params: { [paramKey]: sourceId },
          body: {},
          query: {},
        }),
      }),
      getHandler: () => jest.fn(),
    } as unknown as ExecutionContext);

  it('should throw if no user', () => {
    jest.spyOn(reflector, 'get').mockReturnValue('userId');
    expect(() => guard.canActivate(getContextMock(null, '123'))).toThrow(
      ForbiddenException,
    );
  });

  it('should throw if no sourceId', () => {
    jest.spyOn(reflector, 'get').mockReturnValue('userId');
    const ctx = getContextMock({ id: '123', userType: UserType.PLAYER }, null);
    expect(() => guard.canActivate(ctx)).toThrow(ForbiddenException);
  });

  it('should allow if user is owner', () => {
    jest.spyOn(reflector, 'get').mockReturnValue('userId');
    const ctx = getContextMock({ id: '123', userType: UserType.PLAYER }, '123');
    expect(guard.canActivate(ctx)).toBe(true);
  });

  it('should allow if user is admin', () => {
    jest.spyOn(reflector, 'get').mockReturnValue('userId');
    const ctx = getContextMock({ id: '456', userType: UserType.ADMIN }, '123');
    expect(guard.canActivate(ctx)).toBe(true);
  });

  it('should forbid if not owner or admin', () => {
    jest.spyOn(reflector, 'get').mockReturnValue('userId');
    const ctx = getContextMock({ id: '999', userType: UserType.PLAYER }, '123');
    expect(() => guard.canActivate(ctx)).toThrow(ForbiddenException);
  });
});

describe('OwnerAndAdminCanAccessByResource', () => {
  let guard: OwnerAndAdminCanAccessByResource;
  let reflector: Reflector;

  beforeEach(() => {
    reflector = new Reflector();
    guard = new OwnerAndAdminCanAccessByResource(reflector);
  });

  const mockRequest = (user, resourceKey, resourceValue, customKeys = {}) => ({
    user,
    params: { [resourceKey]: resourceValue },
    body: {},
    query: {},
    ...customKeys,
  });

  const getContextMock = (user, resource, value, item?, customKeys = {}) =>
    ({
      switchToHttp: () => ({
        getRequest: () => mockRequest(user, resource.key, value, customKeys),
      }),
      getHandler: () => jest.fn(),
    } as unknown as ExecutionContext);

  it('should allow if user is owner', async () => {
    jest.spyOn(reflector, 'get').mockReturnValue({
      key: 'highlightId',
      resourceName: 'HIGHLIGHTS',
    });

    (db.getItemFromDatabaseByProjection as jest.Mock).mockResolvedValue({
      Item: { id: 'highlightId', userId: '123' },
    });

    const ctx = getContextMock(
      { id: '123', userType: UserType.PLAYER },
      { key: 'highlightId', resourceName: 'HIGHLIGHTS' },
      'highlightId',
    );
    await expect(guard.canActivate(ctx)).resolves.toBe(true);
  });

  it('should allow if user is admin', async () => {
    jest.spyOn(reflector, 'get').mockReturnValue({
      key: 'highlightId',
      resourceName: 'HIGHLIGHTS',
    });

    (db.getItemFromDatabaseByProjection as jest.Mock).mockResolvedValue({
      Item: { id: 'highlightId', userId: '456' },
    });

    const ctx = getContextMock(
      { id: '123', userType: UserType.ADMIN },
      { key: 'highlightId', resourceName: 'HIGHLIGHTS' },
      'highlightId',
    );
    await expect(guard.canActivate(ctx)).resolves.toBe(true);
  });

  it('should forbid if user is neither owner nor admin', async () => {
    jest.spyOn(reflector, 'get').mockReturnValue({
      key: 'highlightId',
      resourceName: 'HIGHLIGHTS',
    });

    (db.getItemFromDatabaseByProjection as jest.Mock).mockResolvedValue({
      Item: { id: 'highlightId', userId: 'ownerId' },
    });

    const ctx = getContextMock(
      { id: 'notOwner', userType: UserType.PLAYER },
      { key: 'highlightId', resourceName: 'HIGHLIGHTS' },
      'highlightId',
    );
    await expect(guard.canActivate(ctx)).rejects.toThrow(ForbiddenException);
  });

  it('should throw if no user', async () => {
    jest.spyOn(reflector, 'get').mockReturnValue({
      key: 'highlightId',
      resourceName: 'HIGHLIGHTS',
    });

    const ctx = getContextMock(
      null,
      { key: 'highlightId', resourceName: 'HIGHLIGHTS' },
      'highlightId',
    );
    await expect(guard.canActivate(ctx)).rejects.toThrow(ForbiddenException);
  });

  it('should allow access using customKeys if user is owner', async () => {
    jest.spyOn(reflector, 'get').mockReturnValue({
      resourceName: 'HIGHLIGHTS',
      customKeys: {
        params: 'id',
        query: 'userId',
      },
    });

    const mockUser = { id: '123', userType: UserType.PLAYER };

    (db.getItemFromDatabaseByProjection as jest.Mock).mockResolvedValue({
      Item: { id: 'abc', userId: '123' },
    });

    const context = {
      switchToHttp: () => ({
        getRequest: () => ({
          user: mockUser,
          params: { id: '123' },
          body: {},
          query: { userId: 'abc' },
        }),
      }),
      getHandler: () => jest.fn(),
    } as unknown as ExecutionContext;

    const result = await guard.canActivate(context);

    expect(db.getItemFromDatabaseByProjection).toHaveBeenCalledWith(
      'HIGHLIGHTS',
      {
        Key: {
          id: '123',
          userId: 'abc',
        },
        ProjectionExpression: 'id, userId',
      },
    );

    expect(result).toBe(true);
  });
});
