import {
  ExecutionContext,
  ForbiddenException,
  CanActivate,
  Injectable,
  applyDecorators,
  UseGuards,
  SetMetadata,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import {
  DynamoDBKeys,
  getItemFromDatabaseByProjection,
} from 'src/config/database';
import { UserType } from 'src/users/dto/create-user.dto';

@Injectable()
export class OwnerAndAdminCanAccess implements CanActivate {
  constructor(private reflector: Reflector) {}
  canActivate(context: ExecutionContext): boolean {
    const paramKey = this.reflector.get<string>(
      'user_owns_param_key',
      context.getHandler(),
    );

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    const sourceId =
      request.params?.[paramKey] ??
      request.body?.[paramKey] ??
      request.query?.[paramKey];

    if (!user) {
      throw new ForbiddenException('User not authenticated');
    }

    if (!sourceId) {
      throw new ForbiddenException(`Missing key "${paramKey}" in request`);
    }

    if (user.id !== sourceId && user.userType !== UserType.ADMIN) {
      throw new ForbiddenException(
        'You are not authorized to access this resource',
      );
    }

    return true;
  }
}

@Injectable()
export class OwnerAndAdminCanAccessByResource implements CanActivate {
  constructor(private reflector: Reflector) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const resource = this.reflector.get<{
      key: string;
      resourceName: DynamoDBKeys;
      keyToCompare?: string;
      customKeys?: Record<string, string>;
    }>('resource_key', context.getHandler());

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    let compositeKeys: Record<string, string> = null,
      resourceId: string;

    if (resource.customKeys) {
      compositeKeys = this.getCustomKeys(resource, request);
    } else {
      resourceId =
        request.params?.[resource.key] ??
        request.body?.[resource.key] ??
        request.query?.[resource.key];
    }

    let ownerId;

    if (resourceId || compositeKeys) {
      const foundResource = await getItemFromDatabaseByProjection(
        resource.resourceName,
        {
          Key: resource.customKeys
            ? compositeKeys
            : { [resource.key]: resourceId },
          ProjectionExpression: resource.keyToCompare
            ? `id, ${resource.keyToCompare}`
            : 'id, userId',
        },
      );

      ownerId =
        foundResource?.Item?.[resource.keyToCompare] ||
        foundResource?.Item?.userId;
    }

    if (!user) {
      throw new ForbiddenException('User not authenticated');
    }

    if (!ownerId) {
      throw new ForbiddenException(`Missing key "${resource.key}" in request`);
    }

    if (user.id !== ownerId && user.userType !== UserType.ADMIN) {
      throw new ForbiddenException(
        'You are not authorized to access this resource',
      );
    }

    return true;
  }

  getCustomKeys(resource, request) {
    return Object.keys(resource.customKeys).reduce((result, current) => {
      const keySource = request[current];
      const value = keySource?.[resource.customKeys[current]];

      if (Boolean(value)) {
        result[resource.customKeys[current]] = value;
      }
      return result;
    }, {});
  }
}
/**
 * Function that checks if the user is the owner of the resource or is an admin accessing,
 * it grabs the current user ID from the request and the resource ID from the request params, body or query,
 * it then checks if the user is the owner of the resource or an admin and returns true if they are, otherwise it throws a ForbiddenException
 * @param paramKey
 * @returns
 */
export function OwnerOrAdminCanAccess(paramKey: string) {
  return applyDecorators(
    SetMetadata('user_owns_param_key', paramKey),
    UseGuards(OwnerAndAdminCanAccess),
  );
}

/**
 * Same as @function OwnerOrAdminCanAccess - the difference is that this one takes a resource object with a key and a resourceName.
 * the key is the key of the resource in the request params, body or query, and the resourceName is the name of the table in the database.
 * @param resource the resource to check
 * @param resource.keyToCompare the resource key to compare with the key passed. If not provided, it will use userId.
 */
export function OwnerOrAdminCanAccessByResource(resource: {
  key: string;
  resourceName: DynamoDBKeys;
  keyToCompare?: string;
  customKeys?: Record<string, string>;
}) {
  return applyDecorators(
    SetMetadata('resource_key', resource),
    UseGuards(OwnerAndAdminCanAccessByResource),
  );
}
