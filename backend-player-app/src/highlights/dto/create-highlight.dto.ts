import {
  Is<PERSON>rray,
  IsBoolean,
  IsE<PERSON>,
  IsNotEmpty,
  <PERSON>N<PERSON>ber,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';

export enum ASSEST_TYPE {
  PHOTO = 'PHOTO',
  VIDEO = 'VIDEO',
  RESULT = 'RESULT',
}

export enum FEED_STATUS {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
}
export enum VIDEO_STATUS {
  PROCESSING = 'PROCESSING',
  SUCCESSFUL = 'SUCCESSFUL',
  FAILED = 'FAILED',
}

export class CreateHighlightDto {
  @IsUUID()
  @IsOptional()
  id: string;

  @IsString()
  url: string;

  @IsString()
  @IsNotEmpty()
  userId: string;

  @IsString()
  @IsOptional()
  teamId?: string;

  @IsString()
  comment: string;

  @IsString()
  @IsOptional()
  commentBoardId: string;

  @IsNumber()
  @IsOptional()
  totalCommentCount: number;

  @IsEnum(ASSEST_TYPE, {
    message: 'type can be either (VIDEO | PHOTO | RESULT)',
  })
  type: ASSEST_TYPE;

  @IsBoolean()
  @IsOptional()
  allowMatchOnFeeds?: boolean;

  @IsArray()
  @IsOptional()
  reactedByUsers?: object[];

  @IsOptional()
  streamUrl?: any;

  @IsOptional()
  feedStatus?: FEED_STATUS;

  @IsOptional()
  queueProcessed?: string;

  @IsOptional()
  videoStatus?: VIDEO_STATUS;
}
