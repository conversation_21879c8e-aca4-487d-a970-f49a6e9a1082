import {
  Controller,
  Get,
  Post,
  Body,
  Delete,
  Query,
  Put,
  Param,
} from '@nestjs/common';
import { HighlightsService } from './highlights.service';
import { CreateHighlightDto } from './dto/create-highlight.dto';
import { UpdateHighlightDto } from './dto/update-highlight.dto';
import { CurrentProfile } from 'src/shared/decorators/currentProfile';
import {
  OwnerOrAdminCanAccess,
  OwnerOrAdminCanAccessByResource,
} from 'src/shared/decorators/shouldAllowAccess';

@Controller('highlights')
export class HighlightsController {
  constructor(private readonly highlightsService: HighlightsService) {}

  @Post()
  @OwnerOrAdminCanAccess('userId')
  create(@Body() createHighlightDto: CreateHighlightDto) {
    return this.highlightsService.create(createHighlightDto);
  }

  @Post('get')
  findAll(@Body() highlightsQueryDetails, @CurrentProfile() currentUser) {
    return this.highlightsService.findAll({
      currentUser,
      limit: highlightsQueryDetails.limit,
      lastEvaluatedKey: highlightsQueryDetails.lastEvaluatedKey,
      filter: highlightsQueryDetails.filter,
    });
  }

  @Post('publish-result-to-highlights')
  publishResultOfMatch(@Body() resultDetails) {
    return this.highlightsService.publishResultOfMatch(resultDetails);
  }

  @Get(':id')
  findOne(@Param('id') id: string, @Query('userId') userId: string) {
    return this.highlightsService.findOneFromAUser({ id, userId });
  }

  @Post('user-highlights/:userId')
  findAllByUserId(
    @Param('userId') userId: string,
    @Body() body: any,
    @CurrentProfile() currentUser,
  ) {
    return this.highlightsService.findAllByUserId(userId, body, currentUser);
  }

  @Get('team-highlights/:teamId')
  findAllByTeamId(@Param('teamId') teamId: string) {
    return this.highlightsService.findAllByTeamId(teamId);
  }

  @Put('update')
  update(@Body() updateHighlightDto: UpdateHighlightDto) {
    return this.highlightsService.update(updateHighlightDto);
  }

  @Delete(':id')
  @OwnerOrAdminCanAccessByResource({
    key: 'id',
    resourceName: 'HIGHLIGHT_TABLE_NAME',
    customKeys: { params: 'id', query: 'userId' },
  })
  remove(@Param('id') id: string, @Query('userId') userId: string) {
    return this.highlightsService.remove({ id, userId });
  }
}
