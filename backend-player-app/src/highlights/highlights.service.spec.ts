import { Test, TestingModule } from '@nestjs/testing';
import {
  BadRequestException,
  NotFoundException,
  InternalServerErrorException,
} from '@nestjs/common';
import {
  DeleteObjectCommand,
  DeleteObjectsCommand,
  ListObjectsV2Command,
} from '@aws-sdk/client-s3';
import { HighlightsService } from './highlights.service';

jest.mock('src/config/s3Config', () => ({
  s3Client: {
    send: jest.fn(),
  },
}));

jest.mock('src/config', () => ({
  dbConfig: {
    removeItemFromDatabase: jest.fn(),
  },
}));

jest.mock('src/utils/response', () => ({
  response: jest.fn(({ message, status, data }) => ({ message, status, data })),
}));

describe('HighlightsService', () => {
  let service: HighlightsService;
  const mockDbConfig = require('src/config').dbConfig;
  const mockS3Client = require('src/config/s3Config').s3Client;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [HighlightsService],
    }).compile();

    service = module.get<HighlightsService>(HighlightsService);
  });

  it('should throw BadRequestException if id or userId is missing', async () => {
    await expect(service.remove({ id: '', userId: '' })).rejects.toThrow(
      BadRequestException,
    );
  });

  it('should throw NotFoundException if highlight not found', async () => {
    jest.spyOn(service, 'findOneFromAUser').mockResolvedValueOnce({
      message: 'highlight not found',
      data: null,
      status: 0,
    });

    await expect(service.remove({ id: '1', userId: '2' })).rejects.toThrow(
      NotFoundException,
    );
  });

  it('should delete a video highlight and its HLS files', async () => {
    const highlight = {
      data: {
        type: 'video',
        streamUrl: { key: 'video--folder/video.mp4' },
      },
      message: 'test success',
      status: 1,
    };

    const listObjectsOutput = {
      Contents: [{ Key: 'folder/seg1.ts' }, { Key: 'folder/seg2.ts' }],
    };

    jest.spyOn(service, 'findOneFromAUser').mockResolvedValueOnce(highlight);
    mockDbConfig.removeItemFromDatabase.mockResolvedValueOnce({
      success: true,
    });

    // List objects, delete HLS segments, delete video origin
    mockS3Client.send
      .mockResolvedValueOnce(listObjectsOutput)
      .mockResolvedValueOnce({})
      .mockResolvedValueOnce({});

    const result = await service.remove({ id: '1', userId: '2' });

    expect(result.status).toBe(1);
    expect(mockS3Client.send).toHaveBeenCalledWith(
      expect.any(ListObjectsV2Command),
    );
    expect(mockS3Client.send).toHaveBeenCalledWith(
      expect.any(DeleteObjectsCommand),
    );
    expect(mockS3Client.send).toHaveBeenCalledWith(
      expect.any(DeleteObjectCommand),
    );
  });

  it('should delete an image highlight', async () => {
    const highlight = {
      data: {
        type: 'image',
        url: 'https://dev-player-asset.s3.us-east-1.amazonaws.com/1747131115203.jpg',
        streamUrl: { key: '1747131115203.jpg' },
      },
      status: 1,
      message: 'Deleted successfully',
    };

    jest.spyOn(service, 'findOneFromAUser').mockResolvedValueOnce(highlight);
    mockDbConfig.removeItemFromDatabase.mockResolvedValueOnce({
      success: true,
    });
    mockS3Client.send.mockResolvedValueOnce({});

    const result = await service.remove({ id: '1', userId: '2' });

    expect(result.status).toBe(1);
    expect(mockS3Client.send).toHaveBeenCalledWith(
      expect.any(DeleteObjectCommand),
    );
  });

  it('should throw InternalServerErrorException if something goes wrong', async () => {
    const highlight = {
      data: {
        type: 'image',
        url: 'https://dev-player-asset.s3.us-east-1.amazonaws.com/image.jpg',
        streamUrl: { key: 'image.jpg' },
      },
      status: 0,
      message: 'something went wrong',
    };

    jest.spyOn(service, 'findOneFromAUser').mockResolvedValueOnce(highlight);
    mockDbConfig.removeItemFromDatabase.mockRejectedValueOnce(
      new Error('DB error'),
    );

    await expect(service.remove({ id: '1', userId: '2' })).rejects.toThrow(
      InternalServerErrorException,
    );
  });
});
