import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import {
  ASSEST_TYPE,
  CreateHighlightDto,
  FEED_STATUS,
  VIDEO_STATUS,
} from './dto/create-highlight.dto';
import { UpdateHighlightDto } from './dto/update-highlight.dto';
import { dbConfig } from 'src/config';
import { envVars } from 'src/config/constants';
import { randomUUID } from 'crypto';
import { response } from 'src/utils/response';
import { ReturnValue } from '@aws-sdk/client-dynamodb';
import {
  QueryCommandInput,
  ScanCommandInput,
  UpdateCommandInput,
} from '@aws-sdk/lib-dynamodb';
import { PUBLISHSTATUS } from 'src/leagues/dto/create-league.dto';
import {
  DeleteObjectCommand,
  DeleteObjectsCommand,
  GetObjectCommand,
  GetObjectCommandInput,
  ListObjectsV2Command,
} from '@aws-sdk/client-s3';
import { s3Client } from 'src/config/s3Config';
import { extractFileKeyFromUrl } from 'src/utils/helpers';
import { useUpdate } from 'src/config/util';
import dayjs from 'dayjs';

@Injectable()
export class HighlightsService {
  dbName = envVars.dynamoDB.HIGHLIGHT_TABLE_NAME;

  async create(createHighlightDto: CreateHighlightDto) {
    try {
      let user;

      try {
        const { Item } = await dbConfig.getItemFromDatabase(
          envVars.dynamoDB.USERS_TABLE_NAME,
          {
            id: createHighlightDto.userId,
          },
        );
        user = Item;
      } catch (error) {
        throw new NotFoundException(
          response({
            message: 'user id provided is not found',
            status: 0,
            data: null,
          }),
        );
      }

      const { queueProcessed, ...highlightDto } = createHighlightDto;
      const isVideo = createHighlightDto.type === ASSEST_TYPE.VIDEO;

      let queueProcessedStatus = false;
      let videoStatus = VIDEO_STATUS.PROCESSING;
      // Get the video object from S3 and see if it exists
      if (createHighlightDto?.streamUrl?.key && isVideo) {
        const s3Params: GetObjectCommandInput = {
          Bucket: envVars.aws.streamingBucket,
          Key: `${
            createHighlightDto.streamUrl.key.split('--')[1]
          }/thumbnail.png`,
        };

        try {
          const s3Response = await s3Client.send(
            new GetObjectCommand(s3Params),
          );
          if (s3Response.Body) {
            queueProcessedStatus = true;
            videoStatus = VIDEO_STATUS.SUCCESSFUL;
          }
        } catch (error) {
          console.log('======S3 OBJECT NOT FOUND========', error);
        }
      }

      const newHighlight = {
        ...highlightDto,
        totalCommentCount: createHighlightDto.totalCommentCount || 0,
        user: {
          id: user?.id,
          firstName: user?.firstName || '',
          lastName: user?.lastName || '',
          teamName: user?.teamName || '',
          clubName: user?.clubName || '',
          photoUrl: user?.photoUrl || '',
        },
        timeStamp: isVideo
          ? createHighlightDto.streamUrl.key.split('--')[1]
          : '', // timestamp from the key of the s3 object. might be needed to easily find the thumbnail
        teamId: createHighlightDto.teamId || '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        feedStatus: FEED_STATUS.PENDING,
        queueProcessed: isVideo ? queueProcessedStatus : true,
        videoStatus,
        id: randomUUID(),
      };

      const data = await dbConfig.writeItemsToDatabase(
        this.dbName,
        newHighlight,
      );

      return response({
        message: 'Success',
        data: { newHighlight, ...data },
        status: 1,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async update(updateHighlightDto: UpdateHighlightDto) {
    try {
      const { id, userId, ...others } = updateHighlightDto;

      const { Item: existingUserData } = await dbConfig.getItemFromDatabase(
        this.dbName,
        {
          id,
          userId,
        },
      );

      const updateParams: Partial<UpdateCommandInput> = {
        UpdateExpression:
          'set allowMatchOnFeeds = :allowMatchOnFeeds, #typeOfAsset = :ty, #assetUrl= :url, updatedAt = :uat, #comment = :cm, commentBoardId = :cmBId, totalCommentCount = :totalCoCt, reactedByUsers = :reactedByUsers, streamUrl = :streamUrl, feedStatus = :feedStatus',
        ExpressionAttributeValues: {
          ':ty': others?.type || existingUserData?.type || '',
          ':totalCoCt':
            others?.totalCommentCount ||
            existingUserData?.totalCommentCount ||
            0,
          ':url': others?.url || existingUserData?.url || '',
          ':feedStatus':
            others?.feedStatus ||
            existingUserData?.feedStatus ||
            FEED_STATUS.PENDING,
          ':streamUrl': others?.streamUrl || existingUserData?.streamUrl || '',
          ':cm': others?.comment || existingUserData?.comment || '',
          ':cmBId':
            others?.commentBoardId || existingUserData?.commentBoardId || '',
          ':reactedByUsers':
            others?.reactedByUsers || existingUserData?.reactedByUsers || [],
          ':allowMatchOnFeeds':
            others?.allowMatchOnFeeds ||
            existingUserData?.allowMatchOnFeeds ||
            false,
          ':uat': new Date().toISOString(),
        },
        ExpressionAttributeNames: {
          '#typeOfAsset': 'type',
          '#assetUrl': 'url',
          '#comment': 'comment',
        },
      };

      const data = await dbConfig.updateItemsToDatabase(
        this.dbName,
        { id, userId },
        updateParams,
        ReturnValue.ALL_NEW,
      );

      return response({
        message: 'Highlight updated successfully',
        data: data.Attributes,
        status: 1,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async findOneFromAUser({ id, userId }: { id: string; userId: string }) {
    if (!id || !userId) {
      throw new BadRequestException(
        response({
          message: 'id and or userId not provided',
          status: 0,
          data: null,
        }),
      );
    }

    try {
      const params = { id, userId };

      const data = await dbConfig.getItemFromDatabase(this.dbName, params);

      return response({ message: 'Success', data: data.Item, status: 1 });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async findAll({
    currentUser,
    limit = 10,
    lastEvaluatedKey,
    filter = FEED_STATUS.APPROVED,
  }: {
    currentUser?: { id: string; lastTimeViewedHighlights?: string };
    limit?: number;
    lastEvaluatedKey?: Record<string, string>;
    filter: FEED_STATUS;
  }) {
    try {
      // let filterExpression: string;

      // switch (filter) {
      //   case FEED_STATUS.APPROVED:
      //     filterExpression =
      //       'attribute_exists(feedStatus) and #feedStatus = :feedStatus';
      //     break;
      //   case FEED_STATUS.REJECTED:
      //     filterExpression =
      //       'attribute_exists(feedStatus) and #feedStatus = :feedStatus';
      //     break;
      //   case FEED_STATUS.PENDING:
      //     filterExpression =
      //       'attribute_not_exists(feedStatus) or #feedStatus = :feedStatus';
      //     break;

      //   default:
      //     break;
      // }

      // const { Items: highlightsItems, LastEvaluatedKey } =
      //   await dbConfig.getAllItemFromDatabase(this.dbName, {
      //     Limit: +limit,
      //     TableName: this.dbName,
      //     ExclusiveStartKey: lastEvaluatedKey,
      //     IndexName: 'updated-at',
      //     ExpressionAttributeNames: {
      //       '#feedStatus': 'feedStatus',
      //     },
      //     ExpressionAttributeValues: {
      //       ':feedStatus': filter,
      //     },
      //     FilterExpression: filterExpression, //Syntax https://docs.aws.amazon.com/amazondynamodb/latest/developerguide/Expressions.OperatorsAndFunctions.html#Expressions.OperatorsAndFunctions.Syntax
      //   });

      const { Items: highlightsItems, LastEvaluatedKey } =
        await dbConfig.queryItemFromDatabase(this.dbName, {
          TableName: this.dbName,
          IndexName: 'feedStatus-createdAt-index',
          KeyConditionExpression: '#feedStatus = :feedStatus',
          ExpressionAttributeNames: {
            '#feedStatus': 'feedStatus',
          },
          ExpressionAttributeValues: {
            ':feedStatus': filter,
          },
          Limit: +limit,
          ExclusiveStartKey: lastEvaluatedKey,
          ScanIndexForward: false, // descending order by updatedAt
        });

      let filteredHighlightsItems;

      // TODO: Consider a GSI on blockedUsers or a reverse mapping table (blockedBy entries)
      // If this is being access by a logged in user, we need to filter out highlights where the current user is blocked by another user in the highlight
      if (currentUser) {
        // HANDLE CASE FOR LOGGED IN USERS
        //approach: Filter out highlights where the current user is blocked by another user in the highlight
        // This means that we will need to get list of all users where this profile is not blocked
        // then return only highlights from this the list above
        const allusers = await dbConfig.getAllItemFromDatabase(
          envVars.dynamoDB.USERS_TABLE_NAME,
        );

        const usersWhoAllowedCurrentProfile = allusers.Items.map(
          (user: { id: string; blockedUsers: string }) => {
            if (
              !(
                user?.blockedUsers &&
                user?.blockedUsers?.includes(currentUser.id)
              )
            ) {
              return user.id;
            }
          },
        );

        filteredHighlightsItems = highlightsItems?.filter(
          (highlight: CreateHighlightDto) => {
            return usersWhoAllowedCurrentProfile?.includes(highlight?.userId); // check if the highlight is created by the user who allowed the current user
          },
        );

        // Only update this every 2 minutes, that way we don't update it too often
        if (
          !currentUser?.lastTimeViewedHighlights ||
          (currentUser?.lastTimeViewedHighlights &&
            dayjs(currentUser?.lastTimeViewedHighlights).isBefore(
              dayjs().subtract(2, 'minute'),
            ))
        ) {
          await useUpdate({
            dbName: envVars.dynamoDB.USERS_TABLE_NAME,
            id: currentUser.id,
            data: { lastTimeViewedHighlights: new Date().toISOString() },
          });
        }
      } else {
        // HANDLE CASE FOR GUEST USERS
        // Get allBlockedUsers from the blocked user tabled
        const allBlockedUsers = await dbConfig.getAllItemFromDatabase(
          envVars.dynamoDB.BLOCK_USERS_TABLE_NAME,
        );

        // Get the Ids of all blocked users
        const blockedUsersIds = allBlockedUsers.Items.map(
          (user) => user.blockedId,
        );

        // Filter out any content created by blocked users
        filteredHighlightsItems = highlightsItems?.filter(
          (highlight: CreateHighlightDto) => {
            return !blockedUsersIds?.includes(highlight?.userId); // check if the highlight is created by a user who blocked
          },
        );
      }

      return response({
        message: 'Success',
        data: {
          Items: filteredHighlightsItems || highlightsItems,
          Count: filteredHighlightsItems?.length || highlightsItems?.length,
          LastEvaluatedKey,
        },
        status: 1,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  // CONSIDER QUERING ONLY HIGHLIGHTS WITH TYPE RESULT AND THEN RETURN THEM ALONG THE RESULTS FROM THE FINDALL
  // async findAll({
  //   limit = 10,
  //   lastEvaluatedKey,
  // }: {
  //   limit?: number;
  //   lastEvaluatedKey?: Record<string, string>;
  // }) {
  //   try {
  //     // const data = await dbConfig.getAllItemFromDatabase(this.dbName, {
  //     //   Limit: +limit,
  //     //   TableName: this.dbName,
  //     //   ExclusiveStartKey: lastEvaluatedKey,
  //     //   IndexName: 'updated-at',
  //     // });

  //     const today = new Date().toISOString();
  //     // last3Months.setMonth(last3Months.getMonth() - 3);
  //     // const statement = `select * from \"${this.dbName}\" where updatedAt < '${today}' order by updatedAt desc limit = '${limit}' LastEvaluatedKey = '${lastEvaluatedKey}'`;
  //     const statement = `select * from \"${this.dbName}\" where updatedAt < '${today}' order by updatedAt desc limit = '${limit}' LastEvaluatedKey = '${lastEvaluatedKey}'`;

  //     // const statement = `select * from \"${this.dbName}\" limit = '${limit}' LastEvaluatedKey = '${lastEvaluatedKey}' order by `;
  //     const highlights = await dbConfig.dbPartialQueryExecution(statement);

  //     return response({ message: 'Success', data: highlights, status: 1 });
  //   } catch (error) {
  //     Logger.error(error.message);
  //     throw new InternalServerErrorException(
  //       response({ message: error.message, status: 0, data: null }),
  //     );
  //   }
  // }

  async findAllByUserId(
    userId: string,
    {
      limit = 5,
      lastEvaluatedKey,
      internal = false,
    }: {
      limit?: number;
      lastEvaluatedKey?: Record<string, string>;
      internal: boolean;
    },
    currentUser?: Record<string, any>,
  ) {
    try {
      const params: QueryCommandInput = {
        TableName: this.dbName,
        KeyConditionExpression: 'userId = :userId',
        ExpressionAttributeValues: {
          ':userId': userId,
        },
        IndexName: 'userId-index',
        Limit: +limit,
        ExclusiveStartKey: lastEvaluatedKey,
        ProjectionExpression:
          '#id, #type, #assetUrl, createdAt, updatedAt, #comment, commentBoardId, totalCommentCount, userId, streamUrl, reactedByUsers, queueProcessed, videoProcessingFailed',
        ExpressionAttributeNames: {
          '#type': 'type',
          '#assetUrl': 'url',
          '#id': 'id',
          '#comment': 'comment',
        },
      };

      const data = await dbConfig.queryItemFromDatabase(this.dbName, params);

      // HANDLE CASE WHERE THE CURRENT USER IS BEING BLOCKED BY THE OWNER OF THE POST
      if (!internal && currentUser?.id !== userId) {
        const userData = await dbConfig.getItemFromDatabase(
          envVars.dynamoDB.USERS_TABLE_NAME,
          { id: userId },
        );

        if (
          userData.Item?.blockedUsers &&
          userData.Item?.blockedUsers?.includes(currentUser?.id)
        ) {
          return response({
            message: 'User blocked you',
            status: 0,
            data: { Items: [] },
          });
        }
      }

      return internal
        ? data.Items
        : response({ message: 'Success', data: data, status: 1 });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async findAllByTeamId(teamId: string) {
    try {
      const params: ScanCommandInput = {
        TableName: this.dbName,
        FilterExpression: `teamId = :tId`,
        ExpressionAttributeValues: {
          ':tId': teamId,
        },
      };

      const data = await dbConfig.getAllItemFromDatabase(this.dbName, params);

      return response({ message: 'Success', data: data.Items, status: 1 });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async publishResultOfMatch({
    teamId,
    matchId,
    userId,
    url = '',
    publishStatus,
  }: {
    teamId: string;
    matchId: string;
    userId: string;
    url?: string;
    publishStatus?: PUBLISHSTATUS;
  }) {
    try {
      let user, result;

      try {
        const { Item } = await dbConfig.getItemFromDatabase(
          envVars.dynamoDB.USERS_TABLE_NAME,
          {
            id: userId,
          },
        );
        user = Item;
      } catch (error) {
        throw new NotFoundException(
          response({
            message: 'user id provided is not found',
            status: 0,
            data: null,
          }),
        );
      }

      try {
        const { Item: Match } = await dbConfig.getItemFromDatabase(
          envVars.dynamoDB.MATCHES_TABLE_NAME,
          {
            id: matchId,
          },
        );

        if (!Match) {
          throw new NotFoundException(
            response({
              message: 'match id provided is not found',
              status: 0,
              data: null,
            }),
          );
        }
        result = Match;
        const updateParams: Partial<UpdateCommandInput> = {
          UpdateExpression:
            'set matchHighLights = :matchHighLights, homeTeam = :homeTeam, awayTeam = :awayTeam, publishStatus = :publishStatus, updatedAt = :updatedAt',
          ExpressionAttributeValues: {
            ':matchHighLights': Match.matchHighLights || [],
            ':homeTeam': Match.homeTeam || null,
            ':awayTeam': Match.awayTeam || null,
            ':publishStatus': publishStatus,
            ':updatedAt': new Date().toISOString(),
          },
        };

        await dbConfig.updateItemsToDatabase(
          envVars.dynamoDB.MATCHES_TABLE_NAME,
          {
            id: matchId,
          },
          updateParams,
          ReturnValue.ALL_NEW,
        );
      } catch (error) {
        throw new NotFoundException(
          response({
            message: `match id provided is not found ${error.message}`,
            status: 0,
            data: null,
          }),
        );
      }

      const newResultHighlight = {
        totalCommentCount: 0,
        user: {
          id: user?.id,
          firstName: user?.firstName || '',
          lastName: user?.lastName || '',
          teamName: user?.teamName || '',
          clubName: user?.clubName || '',
          photoUrl: user?.photoUrl || '',
        },
        teamId: teamId || '',
        result,
        userId,
        allowMatchOnFeeds: PUBLISHSTATUS.DRAFT === publishStatus ? false : true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        type: ASSEST_TYPE.RESULT,
        url,
        matchId,
        id: randomUUID(),
      };

      const statement = `select * from \"${this.dbName}\" where \"matchId\" = '${matchId}' and \"userId\" = '${userId}'`;
      const matchFeed = await dbConfig.dbPartialQueryExecution(statement);

      if (matchFeed.Items.length > 0) {
        const updateParamsForMatchInFeeds: Partial<UpdateCommandInput> = {
          UpdateExpression:
            'set allowMatchOnFeeds = :allowMatchOnFeeds, updatedAt = :updatedAt',
          ExpressionAttributeValues: {
            ':allowMatchOnFeeds':
              PUBLISHSTATUS.DRAFT === publishStatus ? false : true,
            ':updatedAt': new Date().toISOString(),
          },
        };
        const data = await dbConfig.updateItemsToDatabase(
          this.dbName,
          { id: matchFeed.Items[0].id, userId },
          updateParamsForMatchInFeeds,
          ReturnValue.ALL_NEW,
        );

        return response({
          message: 'Success',
          data,
          status: 1,
        });
      }

      const data = await dbConfig.writeItemsToDatabase(
        this.dbName,
        newResultHighlight,
      );

      return response({
        message: 'Success',
        data: { newResultHighlight, ...data },
        status: 1,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async remove({ id, userId }: { id: string; userId: string }) {
    if (!id || !userId) {
      throw new BadRequestException(
        response({
          message: 'id and or userId not provided',
          status: 0,
          data: null,
        }),
      );
    }
    const highlight: any = await this.findOneFromAUser({ id, userId });

    if (!highlight.data) {
      throw new NotFoundException(
        response({
          message: 'id and or userId not found',
          status: 0,
          data: null,
        }),
      );
    }
    try {
      const data = await dbConfig.removeItemFromDatabase(this.dbName, {
        id,
        userId,
      });

      if (highlight?.data?.type) {
        const type = highlight.data.type.toLowerCase();
        const deleteOperations = [];

        if (type === 'video') {
          const hlsFolderKey = highlight.data.streamUrl.key.split('--')[1];
          const hlsPrefix = hlsFolderKey.endsWith('/')
            ? hlsFolderKey
            : `${hlsFolderKey}/`;

          Logger.debug('Deleting video from streaming bucket', hlsPrefix);

          // 1. List all HLS segment objects under the folder
          const listedObjects = await s3Client.send(
            new ListObjectsV2Command({
              Bucket: envVars.aws.streamingBucket,
              Prefix: hlsPrefix,
            }),
          );

          // 2. Add delete commands for all HLS objects
          if (listedObjects.Contents?.length) {
            const objectsToDelete = listedObjects.Contents.map((obj) => ({
              Key: obj.Key,
            }));

            deleteOperations.push(
              s3Client.send(
                new DeleteObjectsCommand({
                  Bucket: envVars.aws.streamingBucket,
                  Delete: { Objects: objectsToDelete },
                }),
              ),
            );
          }

          Logger.debug(
            'Deleting video from origin bucket',
            highlight.data.streamUrl.key,
          );

          // 3. Delete the original video file
          deleteOperations.push(
            s3Client.send(
              new DeleteObjectCommand({
                Bucket: envVars.aws.videoBucket,
                Key: highlight.data.streamUrl.key,
              }),
            ),
          );
        } else if (type === 'image') {
          const url = highlight.data.url;
          const key = url?.substring(url?.lastIndexOf('/') + 1);
          Logger.debug(
            'Deleting image from origin bucket',
            highlight.data.streamUrl.key,
          );

          // Delete the image file
          deleteOperations.push(
            s3Client.send(
              new DeleteObjectCommand({
                Bucket: envVars.aws.imageBucket,
                Key: key,
              }),
            ),
          );
        }

        await Promise.all(deleteOperations);
      }

      return response({ message: 'Deleted successfully', status: 1, data });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async deleteAllByUserID({ userId }: { userId: string }) {
    try {
      console.log(userId);
      const allUserHighlights = (await this.findAllByUserId(userId, {
        internal: true,
        limit: 1000,
      })) as Array<CreateHighlightDto | any>;

      const allUserHighlightsPromise = allUserHighlights.flatMap(
        (highlight) => {
          const deleteOperations = [
            dbConfig.removeItemFromDatabase(this.dbName, {
              id: highlight.id,
              userId,
            }),
          ];

          if (highlight.data?.streamUrl?.key) {
            deleteOperations.push(
              s3Client.send(
                new DeleteObjectCommand({
                  Bucket:
                    highlight.data?.type === 'VIDEO'
                      ? envVars.aws.streamingBucket
                      : envVars.aws.imageBucket,
                  Key:
                    highlight.data?.type === 'VIDEO'
                      ? highlight?.data?.streamUrl?.key
                      : extractFileKeyFromUrl(highlight?.data?.url),
                }),
              ),
            );
          }

          // Also delete from base bucket
          if (highlight.data?.type?.toLowerCase() === 'video') {
            deleteOperations.push(
              s3Client.send(
                new DeleteObjectCommand({
                  Bucket: envVars.aws.videoBucket,
                  Key: highlight.data.streamUrl.key,
                }),
              ),
            );
          }

          return deleteOperations;
        },
      );

      const data = await Promise.all(allUserHighlightsPromise);

      return data;
    } catch (error) {
      throw new Error(error);
    }
  }
}
