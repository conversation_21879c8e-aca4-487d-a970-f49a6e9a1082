import {
  MiddlewareConsumer,
  Module,
  NestModule,
  RequestMethod,
} from '@nestjs/common';
import {
  AuthMiddleware,
  GuestAuthMiddleware,
} from 'src/auth/guards/auth.middleware';
import { CommentsModule } from '../comments/comments.module';
import { HighlightsController } from './highlights.controller';
import { HighlightsService } from './highlights.service';

@Module({
  imports: [CommentsModule],
  controllers: [HighlightsController],
  providers: [HighlightsService],
})
// export class HighlightsModule {}
export class HighlightsModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(GuestAuthMiddleware).forRoutes({
      method: RequestMethod.POST,
      path: 'highlights/get',
    });
    consumer.apply(AuthMiddleware).forRoutes(
      {
        method: RequestMethod.POST,
        path: 'highlights',
      },
      {
        method: RequestMethod.PUT,
        path: 'highlights',
      },
      {
        method: RequestMethod.DELETE,
        path: 'highlights/:id',
      },
    );
  }
}
