import {
  MiddlewareConsumer,
  Module,
  NestModule,
  RequestMethod,
} from '@nestjs/common';
import { HighlightsService } from './highlights.service';
import { HighlightsController } from './highlights.controller';
import {
  GuestAuthMiddleware,
  AuthMiddleware,
} from 'src/auth/guards/auth.middleware';

@Module({
  controllers: [HighlightsController],
  providers: [HighlightsService],
})
// export class HighlightsModule {}
export class HighlightsModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(GuestAuthMiddleware).forRoutes({
      method: RequestMethod.POST,
      path: 'highlights/get',
    });
    consumer.apply(AuthMiddleware).forRoutes(
      {
        method: RequestMethod.POST,
        path: 'highlights',
      },
      {
        method: RequestMethod.PUT,
        path: 'highlights',
      },
      {
        method: RequestMethod.DELETE,
        path: 'highlights/:id',
      },
    );
  }
}
