import { IsArray, IsOptional, IsString, IsUUID } from 'class-validator';

export class CreateReferenceDto {
  @IsUUID()
  @IsOptional()
  id: string;

  @IsString()
  fromProfileName: string;

  @IsString()
  fromProfilePhoto: string;

  @IsString()
  fromProfileId: string;

  @IsString()
  toProfileName: string;

  @IsString()
  toProfileId: string;

  @IsString()
  relationship: string;

  @IsString()
  toProfilePhoto: string;

  @IsString()
  message: string;

  @IsArray()
  @IsOptional()
  toRole: Array<string>;

  @IsArray()
  @IsOptional()
  fromRole: Array<string>;
}
