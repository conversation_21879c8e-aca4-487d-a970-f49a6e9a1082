import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Put,
} from '@nestjs/common';
import { ReferencesService } from './references.service';
import { CreateReferenceDto } from './dto/create-reference.dto';
import { UpdateReferenceDto } from './dto/update-reference.dto';
import {
  OwnerOrAdminCanAccess,
  OwnerOrAdminCanAccessByResource,
} from 'src/shared/decorators/shouldAllowAccess';

@Controller('references')
export class ReferencesController {
  constructor(private readonly referencesService: ReferencesService) {}

  @Post()
  @OwnerOrAdminCanAccess('fromProfileId')
  create(@Body() createReferenceDto: CreateReferenceDto) {
    return this.referencesService.create(createReferenceDto);
  }

  @Get('all-users-references')
  getAll() {
    return this.referencesService.findAll();
  }

  @Get(':id')
  findAllByUserId(@Param('id') id: string) {
    return this.referencesService.findAllReferenceByUserId(id);
  }

  @Get('single/:id')
  findAllId(@Param('id') id: string) {
    return this.referencesService.findById(id);
  }

  @Put(':id')
  @OwnerOrAdminCanAccessByResource({
    resourceName: 'REFERENCE_TABLE_NAME',
    key: 'id',
    keyToCompare: 'fromProfileId',
  })
  updateReference(
    @Param('id') id: string,
    @Body() updateReferenceDto: UpdateReferenceDto,
  ) {
    return this.referencesService.updateReference(id, updateReferenceDto);
  }

  @Delete(':id')
  @OwnerOrAdminCanAccessByResource({
    resourceName: 'REFERENCE_TABLE_NAME',
    key: 'id',
    keyToCompare: 'fromProfileId',
  })
  deleteReference(@Param('id') id: string) {
    return this.referencesService.deleteReference(id);
  }
}
