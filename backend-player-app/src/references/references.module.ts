import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { ReferencesService } from './references.service';
import { ReferencesController } from './references.controller';
import {
  AuthMiddleware,
  AdminAuthMiddleware,
} from 'src/auth/guards/auth.middleware';

@Module({
  controllers: [ReferencesController],
  providers: [ReferencesService],
})
export class ReferencesModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(AuthMiddleware)
      .forRoutes('references')
      .apply(AdminAuthMiddleware)
      .forRoutes('references/all-users-references');
  }
}
