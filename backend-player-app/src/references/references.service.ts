import {
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { CreateReferenceDto } from './dto/create-reference.dto';
import { UpdateReferenceDto } from './dto/update-reference.dto';
import { dbConfig } from 'src/config';
import { envVars } from 'src/config/constants';
import { response } from 'src/utils/response';
import { dbPartialQueryExecution } from 'src/config/database';
import { randomUUID } from 'crypto';
import { ReturnValue } from '@aws-sdk/client-dynamodb';

@Injectable()
export class ReferencesService {
  dbName = envVars.dynamoDB.REFERENCE_TABLE_NAME;
  async create(createReferenceDto: CreateReferenceDto) {
    try {
      const data = await dbConfig.writeItemsToDatabase(this.dbName, {
        ...createReferenceDto,
        id: randomUUID(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });

      return response({
        message: 'Success',
        data,
        status: 1,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async findAllReferenceByUserId(id: string) {
    try {
      const statement = `select * from \"${this.dbName}\"  where \"fromProfileId\" = '${id}'`;
      const statement2 = `select * from \"${this.dbName}\"  where \"toProfileId\" = '${id}'`;

      const given = await dbPartialQueryExecution(statement);
      const received = await dbPartialQueryExecution(statement2);

      return response({
        message: 'Successfully',
        status: 1,
        data: {
          given: given.Items,
          received: received.Items,
        },
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async findAll() {
    try {
      const responseData = await dbConfig.getAllItemFromDatabase(this.dbName);
      return response({
        data: responseData.Items,
        message: 'success',
        status: 1,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }
  async findById(id: string) {
    try {
      const responseData = await dbConfig.getItemFromDatabase(this.dbName, {
        id,
      });
      return response({
        data: responseData.Item,
        message: 'success',
        status: 1,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async updateReference(id: string, updateData: UpdateReferenceDto) {
    try {
      const foundReference = await this.findById(id);

      if (!foundReference.data) {
        throw new NotFoundException(
          response({ message: 'Reference not found', status: 0, data: null }),
        );
      }

      const updateExpression = Object.keys(updateData)
        .map((key) => `#${key} = :${key}`)
        .join(', ');

      const expressionAttributeValues = Object.keys(updateData).reduce(
        (acc, key) => {
          acc[`:${key}`] = updateData[key];
          return acc;
        },
        {},
      );
      const expressionAttributeNames = Object.keys(updateData).reduce(
        (acc, key) => {
          acc[`#${key}`] = key;
          return acc;
        },
        {},
      );

      const updateParams = {
        UpdateExpression: `set ${updateExpression}`,
        ExpressionAttributeValues: expressionAttributeValues,
        ExpressionAttributeNames: expressionAttributeNames,
      };

      const result = await dbConfig.updateItemsToDatabase(
        this.dbName,
        { id },
        updateParams,
        ReturnValue.ALL_NEW,
      );

      return response({
        message: 'Successfully Updated',
        status: 1,
        data: result.Attributes,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async deleteReference(id: string) {
    try {
      await dbConfig.removeItemFromDatabase(this.dbName, {
        id,
      });

      return response({
        message: 'Successfully deleted',
        data: null,
        status: 1,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }
}
