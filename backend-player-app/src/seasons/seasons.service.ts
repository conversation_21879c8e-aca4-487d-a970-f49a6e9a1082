import {
  Injectable,
  InternalServerErrorException,
  NotFoundException,
  Logger,
} from '@nestjs/common';
import { CreateSeasonDto } from './dto/create-season.dto';
import { UpdateSeasonDto } from './dto/update-season.dto';
import { envVars } from 'src/config/constants';
import { randomUUID } from 'crypto';
import { dbConfig } from 'src/config';
import { response } from 'src/utils/response';
import { ReturnValue } from '@aws-sdk/client-dynamodb';
import { TeamsService } from 'src/teams/teams.service';
import uniqWith from 'lodash.uniqwith';
import iseEqual from 'lodash.isequal';

@Injectable()
export class SeasonsService {
  constructor(private readonly teamsService: TeamsService) {}
  dbName = envVars.dynamoDB.SEASONS_TABLE_NAME;

  async create(createSeasonDto: CreateSeasonDto) {
    const dbParams = {
      ...createSeasonDto,
      teamsIds: createSeasonDto.teamsIds || [],
      matches: createSeasonDto.matches || [],
      id: randomUUID(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    try {
      const data = await dbConfig.writeItemsToDatabase(this.dbName, dbParams);

      if (createSeasonDto.teamsIds.length > 0) {
        const listOfTeams = createSeasonDto.teamsIds.map((teamId) =>
          this.teamsService.update({
            clubId: createSeasonDto.clubId,
            id: teamId,
            seasonsIds: [dbParams.id],
          }),
        );
        await Promise.all(listOfTeams);
      }

      return response({
        message: 'Season created successfully',
        status: 1,
        data,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async findAll() {
    try {
      const data = await dbConfig.getAllItemFromDatabase(this.dbName);

      return response({ message: 'Success', status: 1, data });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async findOne(id: string) {
    try {
      const data = await dbConfig.getItemFromDatabase(this.dbName, {
        id,
      });

      return response({ message: 'Success', status: 1, data });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async update(updateSeasonDto: UpdateSeasonDto) {
    const { id } = updateSeasonDto;
    try {
      const { Item: existingSeasonData } = await dbConfig.getItemFromDatabase(
        this.dbName,
        {
          id,
        },
      );

      const updatedAt = new Date().toISOString();

      if (!existingSeasonData) {
        throw new NotFoundException(
          response({
            message: 'The season id is not found',
            status: 0,
            data: null,
          }),
        );
      }
      const uniqueTeamsIds = new Set([
        ...existingSeasonData.teamsIds,
        ...updateSeasonDto.teamsIds,
      ]);

      const uniqueMatches = uniqWith(
        [...updateSeasonDto.matches, ...existingSeasonData.matches],
        iseEqual,
      );

      const updateParams = {
        UpdateExpression:
          'set startDate = :sd, endDate = :ed, updatedAt = :uat, #mts = :mts, #tms = :tms',
        ExpressionAttributeValues: {
          ':sd':
            updateSeasonDto.startDate || existingSeasonData?.startDate || '',
          ':ed': updateSeasonDto.endDate || existingSeasonData?.endDate || '',
          ':mts': uniqueMatches || [],
          ':tms': Array.from(uniqueTeamsIds) || [],
          ':uat': updatedAt,
        },
        ExpressionAttributeNames: {
          '#mts': 'matches',
          '#tms': 'teamsIds',
        },
      };

      const data = await dbConfig.updateItemsToDatabase(
        this.dbName,
        { id },
        updateParams,
        ReturnValue.ALL_NEW,
      );

      return response({
        message: 'Updated successfully',
        status: 1,
        data: data.Attributes,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async remove(id: string) {
    try {
      const data = await dbConfig.removeItemFromDatabase(this.dbName, {
        id,
      });

      return response({ message: 'Deleted successfully', status: 1, data });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }
}
