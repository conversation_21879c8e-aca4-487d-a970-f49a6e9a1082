import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { SeasonsService } from './seasons.service';
import { SeasonsController } from './seasons.controller';
import { TeamsService } from 'src/teams/teams.service';
import { AdminAuthMiddleware } from 'src/auth/guards/auth.middleware';

@Module({
  controllers: [SeasonsController],
  providers: [SeasonsService, TeamsService],
})
export class SeasonsModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(AdminAuthMiddleware)
      .forRoutes('seasons'); // Blocking all seasons routes to admin for now. to be scoped properly when needed
  }
}