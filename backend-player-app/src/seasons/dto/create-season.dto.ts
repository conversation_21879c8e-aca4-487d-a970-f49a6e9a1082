import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>NotEmpty, <PERSON>Optional, IsString } from 'class-validator';

export class CreateSeasonDto {
  @IsString()
  @IsOptional()
  id?: string;

  @IsString()
  clubId: string;

  @IsString()
  @IsOptional()
  createdAt?: string;

  @IsString()
  @IsOptional()
  updatedAt?: string;

  @IsString()
  @IsNotEmpty()
  startDate: string;

  @IsString()
  @IsNotEmpty()
  endDate: string;

  @IsArray()
  teamsIds?: Array<string>;

  @IsArray()
  matches?: Array<object>;
}
