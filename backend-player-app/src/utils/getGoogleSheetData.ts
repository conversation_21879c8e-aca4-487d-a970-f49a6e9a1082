import fetch from 'node-fetch';
const SPREADSHEET_ID = '1GAM4r4Zd302a7LchrrTf2l3B2UftMnexCZheX5QlakM';
const KEY = 'AIzaSyDxCGSOIKxQRXsg758Iwqz4hUE7Pa3_qvI';

const RANGE = 'signup!A:A';

export const getAllowedSignupUsers = async (): Promise<Array<string>> => {
  try {
    const response = await fetch(
      `https://sheets.googleapis.com/v4/spreadsheets/${SPREADSHEET_ID}/values/${RANGE}?key=${KEY}`,
    );
    const data = await response.json();

    if (data.values.length) {
      const emails = data.values.map((row: string) => row[0]?.trim());
      return emails;
    } else {
      console.log('No data found.');
      return [];
    }
  } catch (error) {
    console.log(error);

    return [];
  }
};
