import {
  SESClient,
  SendEmailCommand,
  SendEmailCommandInput,
  SendEmailCommandOutput,
} from '@aws-sdk/client-ses';

export class EmailSender {
  private client: SESClient;
  constructor() {
    this.client = new SESClient({ region: 'eu-west-2' });
  }

  /**
   * A function that sends emails to given email addresses
   * @param {Object} options the params
   * @param options.emailAdresses an array of email addresses to receive the email
   * @param options.emailSubject the email subject
   * @param options.plainTextEmailBody the plain text email
   * @param options.htmlEmailTemplate html format for sending email
   * @param options.bccAddresses bcc
   * @returns {SendEmailCommandOutput}
   */
  async sendEmail({
    emailAdresses,
    emailSubject,
    plainTextEmailBody,
    htmlEmailTemplate,
    bccAddresses,
  }: {
    emailAdresses: Array<string>;
    emailSubject: string;
    plainTextEmailBody?: string;
    htmlEmailTemplate?: string;
    bccAddresses?: Array<string>;
  }): Promise<SendEmailCommandOutput> {
    const params: SendEmailCommandInput = {
      Destination: { ToAddresses: emailAdresses, BccAddresses: bccAddresses },
      Source: '<EMAIL>',
      Message: {
        Body: {
          Html: htmlEmailTemplate
            ? {
                Data: htmlEmailTemplate,
              }
            : null,
          Text: plainTextEmailBody
            ? {
                Data: plainTextEmailBody,
              }
            : null,
        },
        Subject: {
          Data: emailSubject,
        },
      },
    };
    const command = new SendEmailCommand(params);
    try {
      return await this.client.send(command);
    } catch (error: any) {
      throw new Error(error);
    }
  }
}
