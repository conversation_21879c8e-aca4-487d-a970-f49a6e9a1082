import { ReturnValue } from '@aws-sdk/client-dynamodb';
import { UpdateCommandInput } from '@aws-sdk/lib-dynamodb';
import LiveLike from '@livelike/javascript';
import { InternalServerErrorException, Logger } from '@nestjs/common';
import { dbConfig } from '../config';
import { envVars } from '../config/constants';
import { response } from './response';

export async function initLiveLike(user: Record<string, any>) {
  try {
    const profile = await LiveLike.init({
      clientId: envVars.liveLike.LIVELIKE_CLIENT_ID,
      ...(user.liveLikeProfileToken && {
        accessToken: user.liveLikeProfileToken,
      }),
      logger: true,
    });

    if (!user.liveLikeProfileToken) {
      Logger.warn(
        `Creating new LiveLike token for user ${user.id} as none exists`,
      );
      const updateParams: Partial<UpdateCommandInput> = {
        UpdateExpression:
          'set liveLikeProfileId = :liveLikeProfileId, liveLikeProfileToken = :liveLikeProfileToken',
        ExpressionAttributeValues: {
          ':liveLikeProfileId': profile.id,
          ':liveLikeProfileToken': profile.access_token,
        },
      };

      await dbConfig.updateItemsToDatabase(
        envVars.dynamoDB.USERS_TABLE_NAME,
        { id: user.id },
        updateParams,
        ReturnValue.ALL_NEW,
      );
    }
  } catch (error) {
    Logger.error(error.message);
    throw new InternalServerErrorException(
      response({ message: error.message, status: 0, data: null }),
    );
  }
}

export function shouldUpdateLiveLikeProfile({
  user,
  updatedUser,
}: {
  user: Record<string, any>;
  updatedUser: Record<string, any>;
}) {
  const keysToCompare = [
    'firstName',
    'lastName',
    'photoUrl',
    'teamName',
    'clubName',
  ];
  return keysToCompare.some((key) => user[key] !== updatedUser[key]);
}
