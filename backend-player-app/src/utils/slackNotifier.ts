import { Injectable, Logger } from '@nestjs/common';
import fetch from 'node-fetch';
import { isProduction } from 'src/config/constants';

export interface SlackMessage {
  text?: string;
  blocks?: any[];
  attachments?: any[];
  channel?: string;
  username?: string;
  icon_emoji?: string;
}

@Injectable()
export class SlackNotifierService {
  private readonly logger = new Logger(SlackNotifierService.name);
  private readonly webhookUrl: string;
  private readonly defaultChannel: string;

  constructor() {
    this.webhookUrl = process.env.SLACK_WEBHOOK_URL;
    this.defaultChannel = isProduction ? '#tech-reports' : '#tech-reports-dev';

    if (!this.webhookUrl) {
      this.logger.warn(
        'SLACK_WEBHOOK_URL is not set. Slack notifications will not work.',
      );
    }
  }

  /**
   * Send a simple text message to Slack
   * @param message The text message to send
   * @param channel Optional channel override
   */
  async sendMessage(message: string, channel?: string): Promise<void> {
    await this.send({
      text: message,
      channel: channel || this.defaultChannel,
    });
  }

  /**
   * Send a message with blocks (rich formatting)
   * @param blocks Array of block elements
   * @param channel Optional channel override
   */
  async sendBlocks(
    blocks: {
      type: string;
      text: {
        type: string;
        text: string;
        emoji?: boolean;
      };
    }[],
    channel?: string,
  ): Promise<void> {
    await this.send({
      blocks,
      channel: channel || this.defaultChannel,
    });
  }

  /**
   * Send a message with attachments
   * @param attachments Array of attachment objects
   * @param channel Optional channel override
   */
  async sendAttachments(attachments: any[], channel?: string): Promise<void> {
    await this.send({
      attachments,
      channel: channel || this.defaultChannel,
    });
  }

  /**
   * Send a custom formatted message
   * @param message SlackMessage object with custom formatting
   */
  async send(message: SlackMessage): Promise<void> {
    if (!this.webhookUrl) {
      this.logger.error(
        'Cannot send Slack message: SLACK_WEBHOOK_URL is not configured',
      );
      return;
    }

    try {
      await fetch(this.webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...message,
          username: message.username || 'Notification Bot',
          icon_emoji: message.icon_emoji || ':bell:',
        }),
      });
    } catch (error) {
      this.logger.error(
        `Failed to send Slack message: ${error.message}`,
        error.stack,
      );
      // Silently ignore
    }
  }

  /**
   * Send an error notification
   * @param error The error object or message
   * @param context Additional context about the error
   * @param channel Optional channel override
   */
  async sendError(
    error: Error | string,
    context?: string,
    channel?: string,
  ): Promise<void> {
    const errorMessage = error instanceof Error ? error.message : error;
    const errorStack = error instanceof Error ? error.stack : '';

    const blocks = [
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: '🚨 Error Alert',
          emoji: true,
        },
      },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*Error:* ${errorMessage}`,
        },
      },
    ];

    if (context) {
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*Context:* ${context}`,
        },
      });
    }

    if (errorStack) {
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: '```' + errorStack + '```',
        },
      });
    }

    await this.sendBlocks(blocks, channel);
  }

  /**
   * Send a success notification
   * @param message Success message
   * @param context Additional context
   * @param channel Optional channel override
   */
  async sendSuccess(
    message: string,
    context?: string,
    channel?: string,
  ): Promise<void> {
    const blocks = [
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: '✅ Success',
          emoji: true,
        },
      },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*Message:* ${message}`,
        },
      },
    ];

    if (context) {
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*Context:* ${context}`,
        },
      });
    }

    await this.sendBlocks(blocks, channel);
  }
}
