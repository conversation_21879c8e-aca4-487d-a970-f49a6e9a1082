import ffmpeg from 'fluent-ffmpeg';
import fs from 'fs';
import { envVars } from 'src/config/constants';
// import ffmpegPath from 'ffmpeg-static';

// ffmpegPath = require("@ffmpeg-installer/ffmpeg").path,
// ffmpeg = require("fluent-ffmpeg"),

// // set ffmpeg package path
// ffmpeg.setFfmpegPath(ffmpegPath);

export function ensureDirectoryExists(directoryPath: string) {
  if (!fs.existsSync(directoryPath)) {
    fs.mkdirSync(directoryPath, { recursive: true });
  }
}

/**
 * A function that picks properties from an object
 * @param obj the object to pick properties from
 * @param props the array of properties to pick from the object
 * @returns the object with the picked properties
 */
export function pickObjectProperties(obj: object, props: Array<string>) {
  return props.reduce(function (result, prop) {
    result[prop] = obj[prop];
    return result;
  }, {});
}

export async function setFFPath() {
  await ffmpeg.setFfmpegPath('/opt/bin/ffmpeg');
  // await ffmpeg.setFfprobePath('/opt/bin/ffprobe');
  console.log('Done with path setting');

  // await new ffmpeg.FfmpegCommand('/opt/bin/ffmpeg').addOption();
}

/**
 * A function that optimizes a video file
 * @param {string} inputPath the url of the file to be optimized
 * @param {string} outputPath the url of the output file
 * @returns {Promise<void>} a promise that resolves when the optimization is complete
 */
export function optimizeVideo(
  inputPath: string,
  outputPath: string,
  key: string,
  time = '00:00:02',
  videoBitrate = '3000k', // Lower bitrate for better streaming
  audioBitrate = '96k', // Higher audio bitrate for better sound quality
  segmentDuration = '4', // Slightly longer segment duration
): Promise<void> {
  return new Promise<void>((resolve, reject) => {
    ffmpeg(inputPath)
      .inputOptions(['-re'])
      .outputOptions([
        '-c:v libx264', // Use H.264 codec for video
        '-preset veryfast', // Use 'veryfast' preset for quicker encoding and decent quality
        // '-tune zerolatency', // Tune for zero latency streaming
        '-profile:v main', // Use 'main' profile for compatibility across devices
        '-level 3.1', // Level 3.1 is widely compatible and suited for most web video
        '-pix_fmt yuv420p', // Convert to 8-bit for high10 compatibility
        '-c:a aac', // Use AAC codec for audio
        `-b:v ${videoBitrate}`, // Adjust the video bitrate as needed
        `-b:a ${audioBitrate}`, // Adjust the audio bitrate as needed
        '-hls_segment_filename',
        `${outputPath}/segment%d.ts`,
        `-hls_time ${segmentDuration}`, // Duration of each segment in seconds
        '-hls_list_size 0', // Keep all segments in the playlist
        '-hls_playlist_type vod', // VOD mode for adaptive streaming
        '-f hls',
      ])
      .output(`${outputPath}/index.m3u8`)
      .on('end', () => resolve())
      .on('error', (err: any, stdout, stderr) => {
        console.error('An error occurred on optimizeVideo:', err);
        console.error('An error message:', err.message);
        console.log('stdout:\n' + stdout);
        console.log('stderr:\n' + stderr);
        return reject(err);
      })
      .run();
  });
}

/**
 * A function that optimizes a video file
 * @param {string} inputPath the url of the file to be optimized
 * @param {string} outputPath the url of the output file
 * @returns {Promise<void>} a promise that resolves when the optimization is complete
 */
function getRecommendedBitrate(resolution: number): string {
  if (resolution <= 480) return '1500k';
  if (resolution <= 720) return '3500k';
  if (resolution <= 1080) return '5500k';
  if (resolution <= 1440) return '10000k';
  return '20000k'; // For 4K
}

export function optimizeVideoV2(
  inputPath: string,
  outputPath: string,
  key: string,
  audioBitrate = '128k',
  segmentDuration = '4',
  thumbnailTime = '00:00:03',
  videoBitrate = '3000k',
  size = '1024x768',
): Promise<void> {
  return new Promise<void>((resolve, reject) => {
    ffmpeg.ffprobe(inputPath, (err, metadata) => {
      if (err) {
        console.error('FFprobe error:', err);
        return reject(err);
      }

      const videoStream = metadata.streams.find(
        (s) => s.codec_type === 'video',
      );
      if (!videoStream) {
        return reject(new Error('No video stream found'));
      }

      const resolution = videoStream.height || 720; // Default to 720p if unknown
      const width = videoStream.width || 1024;
      const height = videoStream.height || 768;
      const videoBitrate = getRecommendedBitrate(resolution);

      console.log(
        `Using bitrate ${videoBitrate} for resolution ${resolution}p`,
      );
      console.log(
        `Generating thumbnail at ${width}x${height} from ${thumbnailTime}`,
      );

      ffmpeg(inputPath)
        .outputOptions([
          '-c:v libx264',
          '-preset fast',
          '-tune zerolatency',
          '-profile:v main',
          '-level 4.2',
          `-b:v ${videoBitrate}`,
          '-maxrate ' + videoBitrate,
          '-bufsize ' + parseInt(videoBitrate) * 1.5 + 'k',
          '-c:a aac',
          `-b:a ${audioBitrate}`,
          '-hls_segment_filename',
          `${outputPath}/segment%d.ts`,
          `-hls_time ${segmentDuration}`,
          '-hls_list_size 0',
          '-hls_playlist_type vod',
          '-f hls',
        ])
        .output(`${outputPath}/index.m3u8`) // HLS Output
        .screenshots({
          timestamps: [thumbnailTime],
          folder: outputPath,
          filename: 'thumbnail.png',
          size, // Correct resolution
        }) // Generate Thumbnail
        .on('end', () => {
          console.log(
            'Video processing & thumbnail generation completed successfully',
          );
          resolve();
        })
        .on('error', (err) => {
          console.error('Error:', err);
          reject(err);
        })
        .run();
    });
  });
}

export function generateThumbnail(
  videoPath: string,
  outputPath: string,
  size: string,
  time = '00:00:00',
): Promise<void> {
  return new Promise((resolve, reject) => {
    const payload = {
      timestamps: [time],
      folder: outputPath,
      size: size,
      filename: 'thumbnail.png',
    };

    if (!size) {
      delete payload.size;
    }

    ffmpeg(videoPath)
      // .inputOptions(['-re']) // Read the video at normal speed
      .screenshots(payload)
      .on('end', () => resolve(console.log('Thumbnail generated successfully')))
      .on('error', (err, stdout, stderr) => {
        console.error('Error generating thumbnail:', err);
        console.error('An error message:', err.message);
        console.log('stdout:\n' + stdout);
        console.log('stderr:\n' + stderr);
        return reject(err);
      });
  });
}

export function generateThumbnailV2(
  videoPath: string,
  outputPath: string,
  time = '00:00:03',
): Promise<void> {
  return new Promise((resolve, reject) => {
    ffmpeg.ffprobe(videoPath, (err, metadata) => {
      if (err) {
        console.error('FFprobe error:', err);
        return reject(err);
      }

      const videoStream = metadata.streams.find(
        (s) => s.codec_type === 'video',
      );
      if (!videoStream) {
        return reject(new Error('No video stream found'));
      }

      const width = videoStream.width || 1024;
      const height = videoStream.height || 768;
      const size = `${width}x${height}`;

      console.log(`Generating thumbnail at ${size} from time ${time}`);

      ffmpeg(videoPath)
        .screenshots({
          timestamps: [time],
          folder: outputPath,
          filename: 'thumbnail.png',
          size: size, // Use actual resolution
        })
        .on('end', () => {
          console.log('Thumbnail generated successfully');
          resolve();
        })
        .on('error', (err) => {
          console.error('Error generating thumbnail:', err);
          reject(err);
        });
    });
  });
}

/**
 * Function that splits large list of clubs to small manageable chunks
 * @param allClubs array of json objects of the clubs to be created
 */
export function createSlicedFilesForBulkTeamClubCreation(allClubs) {
  const formattedClubs = allClubs.map((club) => {
    const obj = JSON.parse(club);
    return {
      claimedStatus: false,
      isPrivate: true,
      clubLogoUrl: obj.imgUrl,
      clubName: obj.clubName,
      managerId: '',
      teamsIds: [],
      id: obj.id,
      abr: obj.abr,
      createdAt: obj.createdAt,
      seasonsStats: obj.seasonsStats,
      updatedAt: obj.updatedAt,
    };
  });

  if (!fs.existsSync('./clubs')) {
    fs.mkdirSync('./clubs', { recursive: true });
  }

  const totalPages = formattedClubs.length / 20;
  for (let i = 0; i < totalPages; i++) {
    const clubs = formattedClubs.slice(i * 10, (i + 1) * 10);
    fs.writeFile(
      `./clubs/clubs-${i + 1}.json`,
      JSON.stringify(clubs),
      (err) => {
        if (err) {
          console.error(err);
        }
      },
    );
  }
}

export function extractFileKeyFromUrl(fileUrl: string) {
  const url = new URL(fileUrl);
  const fileName = url.pathname.split('/').pop();
  return fileName;
}

export function convertPubnubTimetokenToDate(timetoken: string) {
  return new Date(Number(timetoken) / 10000);
}

export function getClientSideDomainUrl() {
  const env = envVars.environment;

  let url = null;
  switch (env) {
    case 'local':
      url = 'http://localhost:5134';
      break;

    case 'dev':
      url = 'https://d2upejw2mgdjoj.cloudfront.net';
      break;

    case 'staging':
      url = 'https://d2upejw2mgdjoj.cloudfront.net';
      break;

    case 'production':
      url = 'http://playerapp.co';
      break;

    default:
      url = 'https://localhost:5134';
      break;
  }

  return url;
}
