import { Logger } from '@aws-lambda-powertools/logger';
import { Injectable, LoggerService } from '@nestjs/common';
import { envVars } from '../config/constants';

export const lambdaLogger = new Logger({
  serviceName: envVars.aws.backendLambdaServiceName,
});

@Injectable()
export class AppLogger implements LoggerService {
  verbose(message: any, ...meta: any[]) {
    lambdaLogger.trace(message, ...meta);
  }

  debug(message: any, ...meta: any[]) {
    lambdaLogger.debug(message, ...meta);
  }

  log(message: any, ...meta: any[]) {
    lambdaLogger.info(message, ...meta);
  }

  warn(message: any, ...meta: any[]) {
    lambdaLogger.warn(message, ...meta);
  }

  error(message: any, ...meta: any[]) {
    lambdaLogger.error(message, ...meta);
  }

  fatal(message: any, ...meta: any[]) {
    lambdaLogger.critical(message, ...meta);
  }
}
