import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { AuthMiddleware } from '../auth/guards/auth.middleware';
import { P2pChatController } from './p2p-chat.controller';
import { P2pChatService } from './p2p-chat.service';

@Module({
  controllers: [P2pChatController],
  providers: [P2pChatService],
})
export class P2pChatModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(AuthMiddleware).forRoutes('p2p-chat');
  }
}
