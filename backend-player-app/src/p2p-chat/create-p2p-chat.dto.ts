import { IsOptional, IsString } from 'class-validator';

export class CreateP2pChatDto {
  @IsString()
  @IsOptional()
  id: string;

  @IsString()
  chatInitiatorName: string;

  @IsString()
  chatInitiatorUserId: string;

  @IsString()
  chatJoinerName: string;

  @IsString()
  chatJoinerUserId: string;

  @IsString()
  chatroomId: string;

  @IsString()
  @IsOptional()
  createdAt: string;

  @IsString()
  @IsOptional()
  updatedAt: string;
}
