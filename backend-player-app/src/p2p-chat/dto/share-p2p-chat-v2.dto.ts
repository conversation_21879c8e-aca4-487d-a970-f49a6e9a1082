import {
  ArrayMaxSize,
  ArrayMinSize,
  IsArray,
  IsEnum,
  IsString,
} from 'class-validator';

export enum SHARE_TYPE {
  Highlight = 'highlight',
  Announcement = 'announcement',
  Post = 'post',
  Profile = 'profile',
}

export class ShareP2pChatV2Dto {
  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(1)
  @ArrayMaxSize(10)
  recipientIds: string[];

  @IsEnum(SHARE_TYPE, {
    message: 'Unsupported share type',
  })
  type: SHARE_TYPE;

  @IsString()
  id: string;
}
