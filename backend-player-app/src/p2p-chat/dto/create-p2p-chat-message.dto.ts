import { IsEnum, IsString, Matches, ValidateIf } from 'class-validator';

export enum MESSAGE_TYPE {
  Text = 'text',
  Image = 'image',
  Video = 'video',
}

export class CreateP2pChatMessageDto {
  @IsEnum(MESSAGE_TYPE, {
    message: 'Unsupported message type',
  })
  type: MESSAGE_TYPE;

  @ValidateIf((o) => o.type === MESSAGE_TYPE.Text)
  @IsString({ message: 'message must be a string when type is text' })
  @Matches(/\S/, { message: 'message cannot be empty or whitespace' })
  message?: string;

  @ValidateIf((o) => o.type === MESSAGE_TYPE.Image)
  @IsString({ message: 'imageUrl must be a string when type is image' })
  imageUrl?: string;

  @ValidateIf((o) => o.type === MESSAGE_TYPE.Video)
  @IsString({ message: 'videoUrl must be a string when type is video' })
  videoUrl?: string;
}
