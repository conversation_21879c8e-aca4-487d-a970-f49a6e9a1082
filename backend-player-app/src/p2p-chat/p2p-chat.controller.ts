import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Put,
  Query,
  Request,
} from '@nestjs/common';
import { CreateP2pChatMessageDto } from './dto/create-p2p-chat-message.dto';
import { CreateP2pChatV2Dto } from './dto/create-p2p-chat-v2.dto';
import { CreateP2pChatDto } from './dto/create-p2p-chat.dto';
import { ShareP2pChatV2Dto } from './dto/share-p2p-chat-v2.dto';
import { UpdateP2pChatDto } from './dto/update-p2p-chat.dto';
import { P2pChatService } from './p2p-chat.service';
import { GetChatQueryDto } from './dto/get-chat-query.dto';

@Controller('p2p-chat')
export class P2pChatController {
  constructor(private readonly p2pChatService: P2pChatService) {}

  @Get('v2')
  getChats(@Request() req: any) {
    return this.p2pChatService.getChats({
      user: req.user,
    });
  }

  @Post('v2')
  createChat(
    @Body() createP2pChatDto: CreateP2pChatV2Dto,
    @Request() req: any,
  ) {
    return this.p2pChatService.createChat({
      user: req.user,
      recipientId: createP2pChatDto.recipientId,
    });
  }

  @Get('v2/unread')
  getUnreadMessageCount(@Request() req: any) {
    return this.p2pChatService.getUnreadMessageCount({
      user: req.user,
    });
  }

  @Post('v2/share')
  share(@Body() shareP2pChatDto: ShareP2pChatV2Dto, @Request() req: any) {
    return this.p2pChatService.share({
      user: req.user,
      data: shareP2pChatDto,
    });
  }

  @Get('v2/recipient/:recipientId')
  getChatByRecipient(
    @Param('recipientId') recipientId: string,
    @Request() req: any,
  ) {
    return this.p2pChatService.getChatByRecipient({
      user: req.user,
      recipientId,
    });
  }

  @Get('v2/:chatroomId')
  getChat(
    @Param('chatroomId') chatroomId: string,
    @Request() req: any,
    @Query() query: GetChatQueryDto,
  ) {
    return this.p2pChatService.getChat({
      user: req.user,
      chatroomId,
      cursor: query?.cursor,
      limit: query?.limit,
    });
  }

  @Post('v2/:chatroomId/message')
  createMessage(
    @Param('chatroomId') chatroomId: string,
    @Body() createP2pChatMessageDto: CreateP2pChatMessageDto,
    @Request() req: any,
  ) {
    return this.p2pChatService.createMessage({
      user: req.user,
      chatroomId,
      data: createP2pChatMessageDto,
    });
  }

  @Post()
  create(@Body() createP2pChatDto: CreateP2pChatDto) {
    return this.p2pChatService.create(createP2pChatDto);
  }

  @Get(':userId')
  findOne(@Param('userId') userId: string) {
    return this.p2pChatService.findAllByUserId(userId);
  }

  @Put()
  update(@Body() updateP2pChatDto: UpdateP2pChatDto) {
    return this.p2pChatService.update(updateP2pChatDto);
  }
}
