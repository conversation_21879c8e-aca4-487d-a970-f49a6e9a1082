import { Test, TestingModule } from '@nestjs/testing';
import { P2pChatController } from './p2p-chat.controller';
import { P2pChatService } from './p2p-chat.service';

describe('P2pChatController', () => {
  let controller: P2pChatController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [P2pChatController],
      providers: [P2pChatService],
    }).compile();

    controller = module.get<P2pChatController>(P2pChatController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
