import { ReturnValue } from '@aws-sdk/client-dynamodb';
import { UpdateCommandInput } from '@aws-sdk/lib-dynamodb';
import LiveLike, {
  IChatUserMessageResponsePayload,
} from '@livelike/javascript';
import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { randomUUID } from 'crypto';
import _ from 'lodash';
import merge from 'lodash.merge';
import { dbConfig } from 'src/config';
import { envVars } from 'src/config/constants';
import dayjs from 'src/utils/dayjs';
import { response } from 'src/utils/response';
import { ANNOUNCEMENT_TYPE } from '../announcement/dto/create-announcement.dto';
import { convertPubnubTimetokenToDate } from '../utils/helpers';
import { initLiveLike } from '../utils/LiveLike';
import {
  CreateP2pChatMessageDto,
  MESSAGE_TYPE,
} from './dto/create-p2p-chat-message.dto';
import { CreateP2pChatDto } from './dto/create-p2p-chat.dto';
import { SHARE_TYPE, ShareP2pChatV2Dto } from './dto/share-p2p-chat-v2.dto';
import { UpdateP2pChatDto } from './dto/update-p2p-chat.dto';

const PLAYER_LOGO_URL = 'https://playerapp.co/images/favicon.jpeg';

@Injectable()
export class P2pChatService {
  dbName = envVars.dynamoDB.P2P_CHAT_TABLE_NAME;
  unreadDbName = envVars.dynamoDB.P2P_UNREAD_CHAT_TABLE_NAME;
  usersDbName = envVars.dynamoDB.USERS_TABLE_NAME;

  async create(createP2pChatDto: CreateP2pChatDto) {
    try {
      const requesPayload = {
        ...createP2pChatDto,
        id: createP2pChatDto?.id || randomUUID(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      await dbConfig.writeItemsToDatabase(this.dbName, requesPayload);

      return response({ message: 'Success', data: requesPayload, status: 1 });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async findAllByUserId(userId: string) {
    try {
      const statement = `select * from \"${this.dbName}\"  where \"chatInitiatorUserId\" = '${userId}' or \"chatJoinerUserId\" = '${userId}'`;

      const data = await dbConfig.dbPartialQueryExecution(statement);

      return response({
        message: 'Successfully',
        status: 1,
        data: data.Items,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async update(updateP2pChatDto: UpdateP2pChatDto) {
    const { id, ...others } = updateP2pChatDto;

    try {
      const { Item: existingP2pChat } = await dbConfig.getItemFromDatabase(
        this.dbName,
        {
          id,
        },
      );

      const updateParams = {
        UpdateExpression:
          'set chatInitiatorName = :chatIn, chatJoinerName = :chatJo, chatInitiatorUserId = :chatInId, chatJoinerUserId = :chatJoId, chatInitiatorPhotoUrl = :chatInPh, chatJoinerPhotoUrl = :chatJoPh, updatedAt = :updatedAt, lastMessageTime = :msgTime, lastMessage = :msg',
        ExpressionAttributeValues: {
          ':chatIn':
            others.chatInitiatorName ||
            existingP2pChat?.chatInitiatorName ||
            '',
          ':chatJo':
            others.chatJoinerName || existingP2pChat?.chatJoinerName || '',
          ':chatInId':
            others.chatInitiatorUserId ||
            existingP2pChat?.chatInitiatorUserId ||
            '',
          ':chatJoId':
            others.chatJoinerUserId || existingP2pChat?.chatJoinerUserId || '',
          ':chatInPh':
            others.chatInitiatorPhotoUrl ||
            existingP2pChat?.chatInitiatorPhotoUrl ||
            '',
          ':chatJoPh':
            others.chatJoinerPhotoUrl ||
            existingP2pChat?.chatJoinerPhotoUrl ||
            '',
          ':updatedAt': new Date().toISOString(),
          ':msgTime':
            others.lastMessageTime || existingP2pChat?.lastMessageTime || '',
          ':msg': others.lastMessage || existingP2pChat?.lastMessage || '',
        },
      };

      const data = await dbConfig.updateItemsToDatabase(
        this.dbName,
        { id },
        updateParams,
        ReturnValue.ALL_NEW,
      );

      return response({
        message: 'Updated successfully',
        status: 1,
        data: data.Attributes,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async getRecipient(recipientId: string) {
    const { Item: otherUser } = await dbConfig.getItemFromDatabase(
      envVars.dynamoDB.USERS_TABLE_NAME,
      {
        id: recipientId,
      },
    );

    if (!otherUser) {
      throw new NotFoundException(
        response({ message: 'Recipient not found', status: 0, data: null }),
      );
    }
    if (!otherUser.liveLikeProfileId) {
      throw new InternalServerErrorException(
        response({
          message: 'Recipient does not have LiveLike profile',
          status: 0,
          data: null,
        }),
      );
    }

    return otherUser;
  }

  async getOtherUserInChat({
    user,
    chatroomId,
  }: {
    user: Record<string, any>;
    chatroomId: string;
  }) {
    const { results: chatroomMemberships } =
      await LiveLike.getChatRoomMemberships({
        roomId: chatroomId,
        profileIds: [],
      });

    const otherUserChatroomMembership = chatroomMemberships.find(
      (chatroomMembership) =>
        chatroomMembership.profile.id !== user.liveLikeProfileId,
    );

    if (otherUserChatroomMembership) {
      const otherLiveLikeProfileId = otherUserChatroomMembership.profile
        .custom_data
        ? JSON.parse(otherUserChatroomMembership.profile.custom_data)?.userId
        : null;

      if (otherLiveLikeProfileId) {
        const { Item: otherUser } = await dbConfig.getItemFromDatabase(
          envVars.dynamoDB.USERS_TABLE_NAME,
          {
            id: otherLiveLikeProfileId,
          },
        );

        return otherUser;
      }
    }
  }

  async getCommonChat(liveLikeProfileIds: string[]) {
    const { results: chatroomMemberships } =
      await LiveLike.getProfileChatRoomMemberships({
        profileIds: liveLikeProfileIds,
      });

    if (!chatroomMemberships.length) {
      return null;
    }

    const uniqueChatroomIds = chatroomMemberships
      .map((chatroomMembership) => chatroomMembership.chat_room)
      .sort(
        (a, b) =>
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime(),
      )
      .map((chatroom) => chatroom.id);
    return uniqueChatroomIds[0];
  }

  async getChatsForUser({ user }: { user: Record<string, any> }) {
    const { results: chatroomMemberships } =
      await LiveLike.getProfileChatRoomMemberships({
        profileIds: [user.liveLikeProfileId],
      });

    const chats = [];
    for (const chatroomMembership of chatroomMemberships) {
      const chatroomId = chatroomMembership.chat_room.id;

      const otherUser = await this.getOtherUserInChat({ user, chatroomId });

      if (!otherUser) {
        continue;
      }

      chats.push({
        chatroomId,
        createdAt: chatroomMembership.chat_room.created_at,
        otherUser: {
          id: otherUser.id,
          firstName: otherUser.firstName,
          lastName: otherUser.lastName,
          photoUrl: otherUser.photoUrl,
          teamName: otherUser.teamName,
        },
      });
    }

    // Helps avoid LiveLike edge cases
    const deduplicatedChats = _(chats)
      .groupBy((c) => c.otherUser.id)
      .map((group) => _.maxBy(group, 'createdAt'))
      .value();

    return deduplicatedChats;
  }

  async getChats({ user }: { user: Record<string, any> }) {
    await initLiveLike(user);

    try {
      const { Items: unreadChatroomCounts } =
        await dbConfig.getAllItemFromDatabase(this.unreadDbName, {
          TableName: this.unreadDbName,
          FilterExpression: '#userId = :userId',
          ExpressionAttributeValues: {
            ':userId': user.id,
          },
          ExpressionAttributeNames: {
            '#userId': 'userId',
          },
        });

      const chats = await this.getChatsForUser({ user });
      const chatsWithMessageCount = await Promise.all(
        chats.map(async (chat) => {
          const lastMessageList = await LiveLike.getMessageList(
            chat.chatroomId,
            {
              count: 1,
            },
          );
          const lastMessage = lastMessageList
            .messages[0] as IChatUserMessageResponsePayload & {
            message: string;
            pubnub_timetoken: string;
          };

          const unreadChatroom = unreadChatroomCounts.find(
            (ucc) => ucc.chatroomId === chat.chatroomId,
          );
          const lastReadAt = unreadChatroom?.lastReadAt;
          const unreadMessageCount = (
            await LiveLike.getMessageCount(
              chat.chatroomId,
              lastReadAt
                ? {
                    since: lastReadAt,
                  }
                : undefined,
            )
          ).count;

          return {
            ...chat,
            lastMessage: lastMessage ? lastMessage.message : null,
            lastMessageSentAt: lastMessage ? lastMessage.created_at : null,
            unreadMessageCount,
          };
        }),
      );

      return {
        unreadMessageCount: chatsWithMessageCount.reduce(
          (acc, chat) => acc + chat.unreadMessageCount,
          0,
        ),
        chats: chatsWithMessageCount.sort(
          (a, b) =>
            dayjs(b.lastMessageSentAt || b.createdAt).valueOf() -
            dayjs(a.lastMessageSentAt || a.createdAt).valueOf(),
        ),
      };
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async getChat({
    user,
    chatroomId,
    cursor,
    limit = 25,
  }: {
    user: Record<string, any>;
    chatroomId: string;
    cursor?: string;
    limit?: number;
  }) {
    // Return access denied if user not one of users in chat
    await initLiveLike(user);

    try {
      const filter = {
        includeFilteredMessages: true,
        count: limit,
        ...(cursor && { start: cursor }),
      };
      const [data, total] = await Promise.all([
        LiveLike.getMessageList(chatroomId, filter),
        LiveLike.getMessageCount(chatroomId),
      ]);
      const { messages: messageList, done } = data;

      const nextCursor =
        messageList.length > 0 && !done
          ? dayjs(new Date(messageList[0].created_at))
              .subtract(1, 'millisecond')
              .toISOString()
          : null;

      await this.markChatAsRead({
        user,
        chatroomId,
      });

      return {
        messages: messageList,
        pagination: {
          nextCursor,
          hasMore: !done,
          total: total?.count || 0,
        },
      };
    } catch (error) {
      Logger.error(error);
      throw new NotFoundException(
        response({ message: 'Chat not found', status: 0, data: null }),
      );
    }
  }

  async getChatByRecipient({
    user,
    recipientId,
  }: {
    user: Record<string, any>;
    recipientId: string;
  }) {
    if (user.id === recipientId) {
      throw new BadRequestException(
        response({
          message: 'Recipient ID must be different from user ID',
          status: 0,
          data: null,
        }),
      );
    }

    const otherUser = await this.getRecipient(recipientId);

    await initLiveLike(user);

    try {
      const chatroomId = await this.getCommonChat([
        user.liveLikeProfileId,
        otherUser.liveLikeProfileId,
      ]);

      if (chatroomId) {
        const { messages: messageList } = await LiveLike.getMessageList(
          chatroomId,
          {
            includeFilteredMessages: true,
          },
        );

        await this.markChatAsRead({
          user,
          chatroomId,
        });

        return {
          chatroomId,
          messages: messageList,
        };
      }
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }

    throw new NotFoundException(
      response({ message: 'Chat not found', status: 0, data: null }),
    );
  }

  async getUnreadMessageCount({ user }: { user: Record<string, any> }) {
    await initLiveLike(user);

    try {
      const chats = await this.getChatsForUser({ user });

      const { Items: unreadChatroomCounts } =
        await dbConfig.getAllItemFromDatabase(this.unreadDbName, {
          TableName: this.unreadDbName,
          FilterExpression: '#userId = :userId',
          ExpressionAttributeValues: {
            ':userId': user.id,
          },
          ExpressionAttributeNames: {
            '#userId': 'userId',
          },
        });

      let totalUnreadMessageCount = 0;
      for (const chat of chats) {
        const unreadChatroom = unreadChatroomCounts.find(
          (ucc) => ucc.chatroomId === chat.chatroomId,
        );
        const lastReadAt = unreadChatroom?.lastReadAt;

        const messageCount = (
          await LiveLike.getMessageCount(
            chat.chatroomId,
            lastReadAt
              ? {
                  since: lastReadAt,
                }
              : undefined,
          )
        ).count;

        totalUnreadMessageCount += messageCount;

        if (unreadChatroom) {
          const updateParams: Partial<UpdateCommandInput> = {
            UpdateExpression: 'set updatedAt = :updatedAt',
            ExpressionAttributeValues: {
              ':updatedAt': new Date().toISOString(),
            },
          };
          await dbConfig.updateItemsToDatabase(
            this.unreadDbName,
            {
              id: unreadChatroom.id,
            },
            updateParams,
            ReturnValue.ALL_NEW,
          );
        } else {
          await dbConfig.writeItemsToDatabase(this.unreadDbName, {
            id: randomUUID(),
            userId: user.id,
            chatroomId: chat.chatroomId,
            lastReadAt: null,
            updatedAt: new Date().toISOString(),
          });
        }
      }

      const removedChatrooms = unreadChatroomCounts.filter(
        (ucc) => !chats.find((chat) => chat.chatroomId === ucc.chatroomId),
      );
      for (const removedChatroom of removedChatrooms) {
        await dbConfig.removeItemFromDatabase(this.unreadDbName, {
          id: removedChatroom.id,
        });
      }

      return totalUnreadMessageCount;
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async markChatAsRead({
    user,
    chatroomId,
    lastReadAt = new Date().toISOString(),
  }: {
    user: Record<string, any>;
    chatroomId: string;
    lastReadAt?: string;
  }) {
    try {
      const { Items: unreadChatroomCounts } =
        await dbConfig.getAllItemFromDatabase(this.unreadDbName, {
          TableName: this.unreadDbName,
          FilterExpression: '#userId = :userId and #chatroomId = :chatroomId',
          ExpressionAttributeValues: {
            ':userId': user.id,
            ':chatroomId': chatroomId,
          },
          ExpressionAttributeNames: {
            '#userId': 'userId',
            '#chatroomId': 'chatroomId',
          },
        });

      if (unreadChatroomCounts.length) {
        const unreadChatroomCount = unreadChatroomCounts[0];

        if (
          !unreadChatroomCount.lastReadAt ||
          dayjs(lastReadAt).isAfter(dayjs(unreadChatroomCount.lastReadAt))
        ) {
          const updateParams: Partial<UpdateCommandInput> = {
            UpdateExpression:
              'set lastReadAt = :lastReadAt, updatedAt = :updatedAt',
            ExpressionAttributeValues: {
              ':lastReadAt': lastReadAt,
              ':updatedAt': lastReadAt,
            },
          };
          await dbConfig.updateItemsToDatabase(
            this.unreadDbName,
            {
              id: unreadChatroomCount.id,
            },
            updateParams,
            ReturnValue.ALL_NEW,
          );
        } else {
          Logger.warn(
            `Skipping chat read update: new timestamp ${lastReadAt} is not after existing timestamp ${unreadChatroomCount.lastReadAt}`,
          );
        }
      } else {
        await dbConfig.writeItemsToDatabase(this.unreadDbName, {
          id: randomUUID(),
          userId: user.id,
          chatroomId,
          lastReadAt,
          updatedAt: lastReadAt,
        });
      }
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async createChat({
    user,
    recipientId,
  }: {
    user: Record<string, any>;
    recipientId: string;
  }) {
    if (user.id === recipientId) {
      throw new BadRequestException(
        response({
          message: 'Recipient ID must be different from user ID',
          status: 0,
          data: null,
        }),
      );
    }

    const otherUser = await this.getRecipient(recipientId);

    await initLiveLike(user);

    const chatroomId = await this.getCommonChat([
      user.liveLikeProfileId,
      otherUser.liveLikeProfileId,
    ]);
    if (chatroomId) {
      throw new BadRequestException(
        response({
          message: 'Chat already exists',
          status: 0,
          data: null,
        }),
      );
    }

    try {
      const chatroom = await LiveLike.createChatRoom({
        title: `${user.firstName}/${otherUser.firstName}`,
        visibility: 'everyone',
      });
      await LiveLike.joinChatRoom({ roomId: chatroom.id });
      await LiveLike.addNewMemberToChatRoom({
        roomId: chatroom.id,
        profileId: otherUser.liveLikeProfileId,
      });

      const data = {
        chatroomId: chatroom.id,
        lastReadAt: null,
        updatedAt: chatroom.created_at,
      };
      await dbConfig.writeItemsToDatabase(this.unreadDbName, {
        ...data,
        id: randomUUID(),
        userId: user.id,
      });
      await dbConfig.writeItemsToDatabase(this.unreadDbName, {
        ...data,
        id: randomUUID(),
        userId: otherUser.id,
      });

      // Legacy code start //
      await dbConfig.writeItemsToDatabase(this.dbName, {
        id: randomUUID(),
        chatInitiatorName: `${user.firstName} ${user.lastName}`,
        chatInitiatorUserId: user.id,
        chatJoinerName: `${otherUser.firstName} ${otherUser.lastName}`,
        chatJoinerUserId: otherUser.id,
        chatroomId: chatroom.id,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        chatInitiatorPhotoUrl: user.photoUrl,
        chatJoinerPhotoUrl: otherUser.photoUrl,
      });
      // Legacy code end //

      return { chatroomId: chatroom.id };
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async createMessage({
    user,
    chatroomId,
    data,
  }: {
    user: Record<string, any>;
    chatroomId: string;
    data: CreateP2pChatMessageDto;
  }) {
    await initLiveLike(user);

    try {
      await LiveLike.getChatRoom({
        roomId: chatroomId,
      });
    } catch (error) {
      Logger.error(error);
      throw new NotFoundException(
        response({ message: 'Chat not found', status: 0, data: null }),
      );
    }

    try {
      const message = (await LiveLike.sendMessage({
        roomId: chatroomId,
        sender_image_url: user.photoUrl,
        message_metadata: {
          playerAppUserId: user.id,
          ...(data.type === MESSAGE_TYPE.Video && {
            video_url: data.videoUrl,
          }),
        },
        ...(data.type === MESSAGE_TYPE.Text && {
          message: data.message,
        }),
        ...(data.type === MESSAGE_TYPE.Image && {
          message: 'Image attachment',
          image_url: data.imageUrl,
        }),
        ...(data.type === MESSAGE_TYPE.Video && {
          message: 'Video attachment',
        }),
      })) as LiveLike.IMessageResponsePayload & { pubnub_timetoken: string };

      const lastReadAt = dayjs
        .max(
          dayjs(message.created_at),
          dayjs(convertPubnubTimetokenToDate(message.pubnub_timetoken)),
        )
        .add(1, 'second') // Prevents inconsistent response from LiveLike.getMessageCount()
        .toISOString();

      await this.markChatAsRead({
        user,
        chatroomId,
        lastReadAt,
      });

      // Legacy code start //
      const statement = `select * from \"${this.dbName}\"  where \"chatroomId\" = '${chatroomId}'`;
      const p2pChat = (await dbConfig.dbPartialQueryExecution(statement))
        .Items[0];

      if (p2pChat) {
        await dbConfig.updateItemsToDatabase(
          this.dbName,
          {
            id: p2pChat.id,
          },
          {
            UpdateExpression:
              'set updatedAt = :updatedAt, lastMessageTime = :msgTime, lastMessage = :msg',
            ExpressionAttributeValues: {
              ':updatedAt': new Date().toISOString(),
              ':msgTime': lastReadAt,
              ':msg':
                data.type === MESSAGE_TYPE.Text
                  ? message.message
                  : 'Attachment',
            },
          },
        );
      }

      // Legacy code end //

      return message;
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async share({
    user,
    data,
  }: {
    user: Record<string, any>;
    data: ShareP2pChatV2Dto;
  }) {
    if (data.recipientIds.some((recipientId) => recipientId === user.id)) {
      throw new BadRequestException(
        response({
          message: 'Recipient ID must be different from user ID',
          status: 0,
          data: null,
        }),
      );
    }

    await initLiveLike(user);

    try {
      for (const recipientId of data.recipientIds) {
        const otherUser = await this.getRecipient(recipientId);

        let chatroomId = await this.getCommonChat([
          user.liveLikeProfileId,
          otherUser.liveLikeProfileId,
        ]);
        let p2pChatId;

        if (!chatroomId) {
          const chatroom = await LiveLike.createChatRoom({
            title: `${user.firstName}/${otherUser.firstName}`,
            visibility: 'everyone',
          });
          chatroomId = chatroom.id;
          await LiveLike.joinChatRoom({ roomId: chatroom.id });
          await LiveLike.addNewMemberToChatRoom({
            roomId: chatroom.id,
            profileId: otherUser.liveLikeProfileId,
          });

          const unreadChatData = {
            chatroomId: chatroom.id,
            lastReadAt: null,
            updatedAt: chatroom.created_at,
          };
          await dbConfig.writeItemsToDatabase(this.unreadDbName, {
            ...unreadChatData,
            id: randomUUID(),
            userId: user.id,
          });
          await dbConfig.writeItemsToDatabase(this.unreadDbName, {
            ...unreadChatData,
            id: randomUUID(),
            userId: otherUser.id,
          });
        }

        // Legacy code start //
        const statement = `select * from \"${this.dbName}\"  where \"chatroomId\" = '${chatroomId}'`;
        const p2pChat = (await dbConfig.dbPartialQueryExecution(statement))
          .Items[0];

        if (p2pChat) {
          p2pChatId = p2pChat.id;
        } else {
          const createdP2pChatId = randomUUID();
          await dbConfig.writeItemsToDatabase(this.dbName, {
            id: createdP2pChatId,
            chatInitiatorName: `${user.firstName} ${user.lastName}`,
            chatInitiatorUserId: user.id,
            chatJoinerName: `${otherUser.firstName} ${otherUser.lastName}`,
            chatJoinerUserId: otherUser.id,
            chatroomId: chatroomId,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            chatInitiatorPhotoUrl: user.photoUrl,
            chatJoinerPhotoUrl: otherUser.photoUrl,
          });
          p2pChatId = createdP2pChatId;
        }
        // Legacy code end //

        let messagePayload;
        if (
          data.type === SHARE_TYPE.Announcement ||
          data.type === SHARE_TYPE.Post
        ) {
          const { Item: announcementToShare } =
            await dbConfig.getItemFromDatabase(
              envVars.dynamoDB.ANNOUNCEMENT_TABLE_NAME,
              {
                id: data.id,
              },
            );
          if (!announcementToShare) {
            throw new NotFoundException(
              response({
                message: 'Announcement to share not found',
                status: 0,
                data: null,
              }),
            );
          }

          if (
            announcementToShare.announcementType ===
              ANNOUNCEMENT_TYPE.ANNOUNCEMENT ||
            announcementToShare.announcementType ===
              ANNOUNCEMENT_TYPE.PINNED_POST
          ) {
            messagePayload = {
              message: 'Check out this post by PLAYER:',
              message_metadata: {
                sharingType: 'PINNED_POST',
                shareContentDetails: {
                  id: data.id,
                  text: announcementToShare.title,
                  mediaUrl: PLAYER_LOGO_URL,
                },
              },
            };
          } else {
            messagePayload = {
              message: 'Check out this PLAYER competition:',
              message_metadata: {
                sharingType: 'SHARE_ANNOUNCEMENT',
                shareContentDetails: {
                  id: data.id,
                  text: announcementToShare.title,
                  mediaUrl: PLAYER_LOGO_URL,
                },
              },
            };
          }
        }
        if (data.type === SHARE_TYPE.Highlight) {
          const statement = `select * from \"${envVars.dynamoDB.HIGHLIGHT_TABLE_NAME}\"  where \"id\" = '${data.id}'`;
          const highlightToShare = (
            await dbConfig.dbPartialQueryExecution(statement)
          ).Items[0];

          if (!highlightToShare) {
            throw new NotFoundException(
              response({
                message: 'Highlight to share not found',
                status: 0,
                data: null,
              }),
            );
          }

          messagePayload = {
            message: `Check out this post by ${highlightToShare.user.firstName} ${highlightToShare.user.lastName}:`,
            message_metadata: {
              sharingType: 'SHARE_HIGHLIGHT',
              shareContentDetails: {
                id: data.id,
                text: highlightToShare.comment,
                userId: highlightToShare.userId,
                mediaUrl: highlightToShare.user.photoUrl,
              },
            },
          };
        }
        if (data.type === SHARE_TYPE.Profile) {
          const { Item: userToShare } = await dbConfig.getItemFromDatabase(
            envVars.dynamoDB.USERS_TABLE_NAME,
            {
              id: data.id,
            },
          );
          if (!userToShare) {
            throw new NotFoundException(
              response({
                message: 'User to share not found',
                status: 0,
                data: null,
              }),
            );
          }

          messagePayload = {
            message: 'Check out this profile:',
            message_metadata: {
              sharingType: 'SHARE_PROFILE',
              shareContentDetails: {
                id: data.id,
                text: userToShare.firstName + ' ' + userToShare.lastName,
                userId: data.id,
                mediaUrl: userToShare.photoUrl,
                teamName: userToShare.teamName,
              },
            },
          };
        }

        const message = (await LiveLike.sendMessage(
          merge(
            {
              roomId: chatroomId,
              sender_image_url: user.photoUrl,
              message_metadata: {
                playerAppUserId: user.id,
              },
            },
            messagePayload,
          ),
        )) as LiveLike.IMessageResponsePayload & { pubnub_timetoken: string };

        const lastReadAt = dayjs
          .max(
            dayjs(message.created_at),
            dayjs(convertPubnubTimetokenToDate(message.pubnub_timetoken)),
          )
          .add(1, 'second') // Prevents inconsistent response from LiveLike.getMessageCount()
          .toISOString();

        await this.markChatAsRead({
          user,
          chatroomId,
          lastReadAt,
        });

        // Legacy code start //
        await dbConfig.updateItemsToDatabase(
          this.dbName,
          {
            id: p2pChatId,
          },
          {
            UpdateExpression:
              'set updatedAt = :updatedAt, lastMessageTime = :msgTime, lastMessage = :msg',
            ExpressionAttributeValues: {
              ':updatedAt': new Date().toISOString(),
              ':msgTime': lastReadAt,
              ':msg': message.message,
            },
          },
        );
        // Legacy code end //
      }
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }
}
