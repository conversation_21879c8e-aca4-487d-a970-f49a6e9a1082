import { ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import serverlessExpress from '@vendia/serverless-express';
import { Callback, Context, Handler } from 'aws-lambda';
import { json, urlencoded } from 'express';
import helmet from 'helmet';
import { AppModule } from './app.module';
import { isLocal } from './config/constants';
import { handleOnUserTableChange } from './userTableChange';
import { AppLogger, lambdaLogger } from './utils/logger';
import { videoOptimizerFunc } from './videoOptimizer';

let server: Handler;

async function bootstrap(): Promise<Handler> {
  const app = await NestFactory.create(AppModule, {
    ...(!isLocal && { logger: new AppLogger() }),
  });
  app.useGlobalPipes(new ValidationPipe());
  app.setGlobalPrefix('api');
  app.enableCors();
  app.use(helmet());
  app.use(json({ limit: '50mb' }));
  app.use(urlencoded({ limit: '50mb', extended: true }));
  await app.init();

  const expressApp = app.getHttpAdapter().getInstance();

  return serverlessExpress({ app: expressApp });
}

export const handler: Handler = async (
  event: any,
  context: Context,
  callback: Callback,
) => {
  lambdaLogger.addContext(context);

  if (event.requestContext) {
    lambdaLogger.appendKeys({
      apiRequestId: event.requestContext.requestId,
      apiPath: event.requestContext.path,
      apiHttpMethod: event.requestContext.httpMethod,
    });
  }

  try {
    server = server ?? (await bootstrap());
    return await server(event, context, callback);
  } finally {
    lambdaLogger.resetKeys();
  }
};

export const videoOptimizer: Handler = async (event: any, context: Context) => {
  try {
    await videoOptimizerFunc(event, context);
    return {
      statusCode: 200,
      body: 'Video optimization and processing complete!',
    };
  } catch (error) {
    console.log(error);
    return {
      statusCode: 500,
      body: 'Video optimization and processing failed!',
    };
  }
};

export const processUserDBStream: Handler = async (
  event: any,
  context: Context,
) => {
  try {
    await handleOnUserTableChange(event, context);
    return {
      statusCode: 200,
      body: 'Video optimization and processing complete!',
    };
  } catch (error) {
    console.log(error);
    return {
      statusCode: 500,
      body: 'Video optimization and processing failed!',
    };
  }
};
