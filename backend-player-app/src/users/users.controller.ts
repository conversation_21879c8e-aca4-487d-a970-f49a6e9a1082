import {
  Body,
  Controller,
  Get,
  Post,
  Put,
  Query,
  Request,
  Param,
} from '@nestjs/common';
import { CurrentProfile } from 'src/shared/decorators/currentProfile';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { CreateUserPhysicalData } from './dto/user-physical-data.dto';
import { UsersService } from './users.service';
import { OneUserByProjectionDto } from './dto/get-one-user-by-expression.dto';
import { OwnerOrAdminCanAccess } from 'src/shared/decorators/shouldAllowAccess';

@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Post('create')
  create(@Body() createUserDto: CreateUserDto) {
    return this.usersService.create(createUserDto);
  }

  @Post('physical-data')
  @OwnerOrAdminCanAccess('userId')
  createUserPhysicalData(@Body() physicalData: CreateUserPhysicalData) {
    return this.usersService.createUserPhysical(physicalData);
  }

  @Put('physical-data')
  @OwnerOrAdminCanAccess('userId')
  updateUserPhysicalData(@Body() physicalData: CreateUserPhysicalData) {
    return this.usersService.updateUserPhysical(physicalData);
  }

  @Put('update')
  @OwnerOrAdminCanAccess('id')
  update(
    @Body() updateUserDto: UpdateUserDto,
    @Query('remove-from-team') removeTeam,
  ) {
    return this.usersService.updateUser(updateUserDto, removeTeam);
  }

  // BLOCK USER ROUTE
  @Post('block-user')
  blockUser(
    @Body() details: { blockedUserId: string; reason?: string },
    @CurrentProfile() user: any,
  ) {
    return this.usersService.blockUser({ ...details, currentUser: user });
  }

  // UNBLOCK USER ROUTE
  @Post('unblock-user')
  unblockUser(
    @Body() details: { blockedUserId: string },
    @CurrentProfile() user: any,
  ) {
    return this.usersService.unblockUser({
      ...details,
      currentUser: user,
    });
  }

  // ALL BLOCKED USER ROUTE
  @Get('list-blocked-users')
  listBlockedUsers(@CurrentProfile() user: any) {
    return this.usersService.getProfileAllBlockedUsers(user);
  }

  // GET ALL BLOCK USER IN THE SYSTEM
  @Get('getall-blocked-users')
  getAllBlock() {
    return this.usersService.listAllBlockedUsers();
  }

  @Get('get')
  findOne(
    @Query('id') id: string,
    @Request() req: any,
    @Query('clubIDteamID') clubIDteamID?: string,
  ) {
    return this.usersService.findOne({
      id,
      clubIDteamID,
      currentUser: req.user,
    });
  }

  @Get('all')
  findAll(
    @Request() req: any,
    @Query('startLastTimeViewedHighlights') startLastTimeViewedHighlights: Date,
    @Query('endLastTimeViewedHighlights') endLastTimeViewedHighlights: Date,
  ) {
    return this.usersService.findAll({
      currentUser: req.user,
      startLastTimeViewedHighlights,
      endLastTimeViewedHighlights,
    });
  }

  @Get('search')
  searchUsers(@Query('query') query: string, @Request() req: any) {
    return this.usersService.searchUser({ query, currentUser: req.user });
  }

  @Get('physical-data')
  getPhysical(@Query('userId') id: string) {
    return this.usersService.getUserPhysical(id);
  }

  @Get('get-by-projection')
  findAllByProjection(
    @Query('projectionExpression') projectionExpression,
    @CurrentProfile() user: any,
  ) {
    return this.usersService.findAllByProjection({
      projectionExpression,
      currentUser: user,
    });
  }

  @Get(':userId/followers')
  getFollowers(@Param('userId') userId: string) {
    return this.usersService.getFollowers(userId);
  }

  @Get(':userId/following')
  getFollowing(@Param('userId') userId: string) {
    return this.usersService.getFollowing(userId);
  }

  @Get('user-by-projection/:userId')
  findOneByProjection(
    @Query() query: OneUserByProjectionDto,
    @Param('userId') userId: string,
    @CurrentProfile() user: any,
  ) {
    return this.usersService.findOneByProjection({
      id: userId,
      projectionExpression: query.projectionExpression,
      currentUser: user,
    });
  }
}
