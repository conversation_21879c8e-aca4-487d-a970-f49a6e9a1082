import {
  MiddlewareConsumer,
  Module,
  NestModule,
  RequestMethod,
} from '@nestjs/common';
import {
  AdminAuthMiddleware,
  AuthMiddleware,
} from 'src/auth/guards/auth.middleware';
import { ExperienceStatsService } from 'src/experience-stats/experience-stats.service';
import { HighlightsService } from 'src/highlights/highlights.service';
import { P2pChatService } from 'src/p2p-chat/p2p-chat.service';
import { CommentsModule } from '../comments/comments.module';
import { FollowModule } from '../follow/follow.module';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';

@Module({
  imports: [FollowModule, CommentsModule],
  controllers: [UsersController],
  providers: [
    UsersService,
    ExperienceStatsService,
    HighlightsService,
    P2pChatService,
  ],
})
export class UsersModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(AuthMiddleware)
      .forRoutes('users')
      .apply(AdminAuthMiddleware)
      .forRoutes(
        { path: 'users/create', method: RequestMethod.POST },
        { path: 'users/all', method: RequestMethod.GET },
        { path: 'users/getall-blocked-users', method: RequestMethod.GET },
      );
  }
}
