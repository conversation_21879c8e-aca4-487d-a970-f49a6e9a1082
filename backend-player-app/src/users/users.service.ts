import { ReturnValue } from '@aws-sdk/client-dynamodb';
import { DeleteObjectCommand } from '@aws-sdk/client-s3';
import LiveLike from '@livelike/javascript';
import {
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { randomUUID } from 'crypto';
import dayjs from 'dayjs';
import { dbConfig } from 'src/config';
import { envVars } from 'src/config/constants';
import { s3Client } from 'src/config/s3Config';
import { ExperienceStatsService } from 'src/experience-stats/experience-stats.service';
import { HighlightsService } from 'src/highlights/highlights.service';
import { P2pChatService } from 'src/p2p-chat/p2p-chat.service';
import { extractFileKeyFromUrl, pickObjectProperties } from 'src/utils/helpers';
import { response } from 'src/utils/response';
import { formatFilterExpressionForArray } from '../config/database';
import { FollowService } from '../follow/follow.service';
import { initLiveLike, shouldUpdateLiveLikeProfile } from '../utils/LiveLike';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { CreateUserPhysicalData } from './dto/user-physical-data.dto';

@Injectable()
export class UsersService {
  constructor(
    private experienceStatsService: ExperienceStatsService,
    private highlightsService: HighlightsService,
    private p2pChatService: P2pChatService,
    private followService: FollowService,
  ) {}
  async create(createUserDto: CreateUserDto) {
    try {
      const data = await dbConfig.writeItemsToDatabase(
        envVars.dynamoDB.USERS_TABLE_NAME,
        {
          ...createUserDto,
          id: createUserDto?.id || randomUUID(),
        },
      );

      return response({ message: 'Success', data, status: 1 });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async updateUser(updateUserDto: UpdateUserDto, removeTeam: any) {
    const { id, ...others } = updateUserDto;
    let user, updatedUser;

    try {
      const dbName = envVars.dynamoDB.USERS_TABLE_NAME;

      const { Item: existingUserData } = await dbConfig.getItemFromDatabase(
        dbName,
        {
          id,
        },
      );

      if (!existingUserData) {
        throw new NotFoundException(
          response({
            message: 'User account is not found',
            data: null,
            status: 0,
          }),
        );
      }

      user = existingUserData;

      const updateParams = {
        UpdateExpression: `set verifiedBy = :verifiedBy, 
        verified = :verified, 
        nonPlayerRole = :nonPlayerRole, 
        banner = :banner, 
        crown = :crown, 
        height = :heht, 
        preferredFoot = :prf, 
        wingNumber = :wn, 
        allowPrivateClubsTeams = :pv, 
        teamName = :tNm, 
        clubName = :clN,  
        photoUrl = :ph, 
        lastName = :ln, 
        firstName = :fn, 
        gender = :gn, 
        birthday = :bd, 
        cludId = :cId, 
        userType = :ut, 
        #lc = :lc, 
        #ps = :ps, 
        teamId = :tId, 
        bio = :bio, 
        updatedAt = :uat, 
        liveLikeProfileId = :liveLikeProfileId, 
        liveLikeProfileToken = :liveLikeProfileToken, 
        followingsIDs = :ffIds, 
        representedBy= :representedBy,
        contractExpiry= :contractExpiry`,

        ExpressionAttributeValues: {
          ':ln': others.lastName || user?.lastName || '',
          ':fn': others.firstName || user?.firstName || '',
          ':tNm': others.teamName || user?.teamName || '',
          ':clN': others.clubName || user?.clubName || '',
          ':crown': others.crown || user?.crown || null,
          ':banner': others.banner || user?.banner || '',
          ':gn': others.gender || user?.gender || '',
          ':bd': others.birthday || user?.birthday || '',
          ':verified': others.verified || user?.verified || false,
          ':verifiedBy': others.verifiedById || user?.verifiedById || '',
          ':cId': removeTeam ? '' : others.cludId || user?.cludId || '',
          ':tId': removeTeam ? '' : others.teamId || user?.teamId || '',
          ':prf': others.preferredFoot || user?.preferredFoot || '',
          ':heht': others.height || user?.height || '',
          ':ut': others.userType || user?.userType || '',
          ':ps': others.position || user?.position || '',
          ':lc': others.location || user?.location || '',
          ':wn': others.wingNumber || user?.wingNumber || '',
          ':representedBy':
            others.representedBy !== undefined
              ? others.representedBy
              : user?.representedBy || '',
          ':contractExpiry':
            others.contractExpiry !== undefined
              ? others.contractExpiry
              : user?.contractExpiry || '',
          ':ffIds': others.followingsIDs || user?.followingsIDs || [],
          ':pv':
            others.allowPrivateClubsTeams ||
            user?.allowPrivateClubsTeams ||
            false,
          ':ph': others.photoUrl || user?.photoUrl || '',
          ':bio': others.bio !== undefined ? others.bio : user?.bio || '',
          ':nonPlayerRole': others.nonPlayerRole || user?.nonPlayerRole || [],
          ':liveLikeProfileId':
            others?.liveLikeProfileId || user?.liveLikeProfileId || '',
          ':liveLikeProfileToken':
            others.liveLikeProfileToken || user?.liveLikeProfileToken || '',
          ':uat': new Date().toISOString(),
        },
        ExpressionAttributeNames: {
          '#lc': 'location',
          '#ps': 'position',
        },
      };

      // check if the below properties are in the 'others' object
      const shouldUpdatedHighlights = Object.keys(others).some((key) =>
        ['firstName', 'lastName', 'photoUrl', 'teamName', 'clubName'].includes(
          key,
        ),
      );

      const data = await dbConfig.updateItemsToDatabase(
        dbName,
        { id },
        updateParams,
        ReturnValue.ALL_NEW,
        removeTeam,
      );
      updatedUser = data.Attributes;

      // TODO: this implementation is not efficient, we need a better way to update all highlights with the user data
      if (shouldUpdatedHighlights) {
        const allUserHighlts = await this.highlightsService.findAllByUserId(
          id,
          { limit: 1000, internal: true },
        );
        const allUserHighltsArray = allUserHighlts as Array<any>;

        if (allUserHighltsArray?.length > 0) {
          const higlightsUpdatePromises = allUserHighltsArray?.map(
            async (item) => {
              const highlightsUpdateParams = {
                UpdateExpression: 'set #user = :usr',
                ExpressionAttributeValues: {
                  ':usr': pickObjectProperties(updatedUser, [
                    'firstName',
                    'lastName',
                    'photoUrl',
                    'teamName',
                    'clubName',
                    'id',
                  ]),
                },
                ExpressionAttributeNames: {
                  '#user': 'user',
                },
              };
              await dbConfig.updateItemsToDatabase(
                envVars.dynamoDB.HIGHLIGHT_TABLE_NAME,
                { userId: id, id: item.id },
                highlightsUpdateParams,
              );
            },
          );

          await Promise.all(higlightsUpdatePromises);
        }
      }

      try {
        const deleteFromS3Operation = [];

        // Should delete banner from s3 bucket
        if (
          user.banner &&
          updateUserDto?.banner &&
          updateUserDto.banner !== user.banner
        ) {
          deleteFromS3Operation.push(
            s3Client.send(
              new DeleteObjectCommand({
                Bucket: envVars.aws.imageBucket,
                Key: extractFileKeyFromUrl(user.banner),
              }),
            ),
          );
        }

        // Should delete photoUrl from s3 bucket
        if (
          user.photoUrl &&
          updateUserDto?.photoUrl &&
          updateUserDto.photoUrl !== user.photoUrl
        ) {
          deleteFromS3Operation.push(
            s3Client.send(
              new DeleteObjectCommand({
                Bucket: envVars.aws.imageBucket,
                Key: extractFileKeyFromUrl(user.photoUrl),
              }),
            ),
          );
        }

        if (deleteFromS3Operation?.length) {
          await Promise.all(deleteFromS3Operation);
        }
      } catch (error) {
        // Silently log error from s3 deletion
        Logger.error(`User update S3 error: ${error.message}`, {
          ...error,
          data: {
            existingBanner: user.banner,
            updateBanner: updateUserDto?.banner,
            existingPhotoUrl: user.photoUrl,
            updatePhotoUrl: updateUserDto?.photoUrl,
          },
        });
      }
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }

    if (
      shouldUpdateLiveLikeProfile({
        user,
        updatedUser,
      })
    ) {
      try {
        await initLiveLike(updatedUser);
        await LiveLike.updateUserProfile({
          accessToken: updatedUser.liveLikeProfileToken,
          options: {
            nickname: `${updatedUser.firstName}${
              updatedUser.lastName ? ` ${updatedUser.lastName}` : ''
            }`,
            custom_data: JSON.stringify({
              photoUrl: updatedUser.photoUrl || '/images/profile.png',
              firstName: updatedUser.firstName,
              lastName: updatedUser.lastName,
              userId: updatedUser.id,
              clubName: updatedUser.clubName,
              teamName: updatedUser.teamName,
              nextUpdate: dayjs().add(5, 'days').unix(),
            }),
          },
        });
        Logger.debug('LiveLike profile update success');
      } catch (error) {
        Logger.error(`LiveLike profile update error: ${error.message}`);
      }
    }

    return response({
      message: 'Updated successfully',
      data: updatedUser,
      status: 1,
    });
  }

  async blockUser(details: {
    blockedUserId: string;
    reason?: string;
    currentUser: Record<string, any>;
  }) {
    try {
      const { blockedUserId, reason, currentUser } = details;

      const updateParams = {
        ExpressionAttributeNames: {
          '#blockedUsers': 'blockedUsers',
          '#reason': 'reason',
        },
        ExpressionAttributeValues: {
          ':reason': reason || '',
          ':blockedUsers': [blockedUserId],
          ':emptyList': [],
        },
        UpdateExpression:
          'set #reason = :reason, #blockedUsers = list_append(if_not_exists(#blockedUsers, :emptyList), :blockedUsers)',
      };

      if (
        currentUser?.blockedUsers &&
        currentUser?.blockedUsers?.includes(blockedUserId)
      ) {
        return response({
          message: 'Already blocked',
          data: null,
          status: 1,
        });
      }

      await dbConfig.updateItemsToDatabase(
        envVars.dynamoDB.USERS_TABLE_NAME,
        {
          id: currentUser.id,
        },
        updateParams,
      );

      // Add to blocked users table
      await dbConfig.writeItemsToDatabase(
        envVars.dynamoDB.BLOCK_USERS_TABLE_NAME,
        {
          id: randomUUID(),
          blockerId: currentUser.id,
          blockedId: blockedUserId,
        },
      );

      return response({
        message: 'Blocked successfully',
        data: null,
        status: 1,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async unblockUser({
    blockedUserId,
    currentUser,
  }: {
    blockedUserId: string;
    currentUser: Record<string, any>;
  }) {
    try {
      const unblockedByUserDetails = await this.findOne({
        id: currentUser.id,
        currentUser,
      });
      const userBlockList = unblockedByUserDetails?.data as any;

      if (!userBlockList?.blockedUsers || !userBlockList.blockedUsers.length) {
        return response({
          message: 'No Blocked user not found',
          status: 0,
          data: null,
        });
      }

      const indexOfBlockedUser = userBlockList.blockedUsers.findIndex(
        (userId: string) => blockedUserId === userId,
      );

      if (indexOfBlockedUser === -1) {
        return response({
          message: 'Blocked user not found',
          status: 0,
          data: null,
        });
      }

      const updateParams = {
        ExpressionAttributeNames: {
          '#blockedUsers': 'blockedUsers',
        },
        UpdateExpression: `remove #blockedUsers[${indexOfBlockedUser}]`,
      };

      await dbConfig.updateItemsToDatabase(
        envVars.dynamoDB.USERS_TABLE_NAME,
        {
          id: currentUser.id,
        },
        updateParams,
      );

      // Find and remove blocked record in the blocking table
      const foundtItems = await dbConfig.getAllItemFromDatabase(
        envVars.dynamoDB.BLOCK_USERS_TABLE_NAME,
        {
          TableName: envVars.dynamoDB.BLOCK_USERS_TABLE_NAME,
          FilterExpression: 'blockerId = :blockerId and blockedId = :blockedId',
          ExpressionAttributeValues: {
            ':blockerId': currentUser.id,
            ':blockedId': blockedUserId,
          },
        },
      );

      if (foundtItems.Items.length > 0) {
        await dbConfig.removeItemFromDatabase(
          envVars.dynamoDB.BLOCK_USERS_TABLE_NAME,
          {
            id: foundtItems.Items[0].id,
          },
        );
      }

      return response({
        message: 'Unblocked successfully',
        data: null,
        status: 1,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async getProfileAllBlockedUsers(currentUser) {
    try {
      const blockedUsersIds: [] = currentUser?.blockedUsers || [];

      if (blockedUsersIds.length > 0) {
        const userKeys = blockedUsersIds.map((id) => ({ id }));

        const blockedUsers = await dbConfig.dbBatchGetItemsRequest(
          envVars.dynamoDB.USERS_TABLE_NAME,
          userKeys,
          'id,firstName,lastName,teamName,clubName,photoUrl',
        );

        return response({
          message: 'Success',
          data: blockedUsers.Responses?.[envVars.dynamoDB.USERS_TABLE_NAME],
          status: 1,
        });
      }

      return response({
        message: 'Success',
        data: [],
        status: 1,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async findOne({
    id,
    clubIDteamID,
    currentUser,
  }: {
    id: string;
    clubIDteamID?: string;
    currentUser?: Record<string, any>;
  }) {
    try {
      const params = { id, clubIDteamID };
      if (!clubIDteamID) {
        delete params.clubIDteamID;
      }

      const data = await dbConfig.getItemFromDatabase(
        envVars.dynamoDB.USERS_TABLE_NAME,
        params,
      );

      if (
        data.Item?.blockedUsers &&
        data.Item?.blockedUsers?.includes(currentUser?.id)
      ) {
        return response({
          message: 'User blocked you',
          status: 0,
          data: null,
        });
      }

      const experiences = await this.experienceStatsService.findAllByUserId(id);
      const p2pChats = await this.p2pChatService.findAllByUserId(id);

      return response({
        message: 'Success',
        data: {
          ...data.Item,
          experiences: experiences.data,
          p2pChats: p2pChats.data,
        },
        status: 1,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async findOneByProjection({
    id,
    currentUser,
    projectionExpression,
  }: {
    id: string;
    projectionExpression?: string;
    currentUser?: Record<string, any>;
  }) {
    try {
      const projectionArray = projectionExpression
        ? `blockedUsers,${projectionExpression}`.replace(' ', '').split(',')
        : 'id,firstName,lastName,teamName,clubName,blockedUsers,photoUrl,clubId,clubName,cludId,teamId,teamName,userType,experiences,gender,position,location,createdDate,birthday,banner'
            .replace(' ', '')
            .split(',');

      const expressionAttributeNames = projectionArray
        .filter(
          (item) =>
            !['email', 'liveLikeProfileToken', 'experiences'].includes(item),
        ) //remove the fields we don't want to return
        .reduce((acc, curr) => {
          acc[`#${curr}`] = curr;
          return acc;
        }, {});

      const responseData = await dbConfig.getItemFromDatabaseByProjection(
        'USERS_TABLE_NAME',
        {
          Key: { id },
          ProjectionExpression: Object.keys(expressionAttributeNames).join(','),
          ExpressionAttributeNames: expressionAttributeNames,
        },
      );

      if (
        responseData.Item?.blockedUsers &&
        responseData.Item?.blockedUsers?.includes(currentUser?.id)
      ) {
        return response({
          message: 'User blocked you',
          status: 0,
          data: null,
        });
      }

      if (projectionArray.includes('experiences')) {
        const experiences = await this.experienceStatsService.findAllByUserId(
          id,
        );

        return response({
          message: 'Success',
          data: { ...responseData.Item, experiences: experiences.data },
          status: 1,
        });
      }

      return response({
        message: 'Success',
        data: responseData.Item,
        status: 1,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async findAll({ currentUser }: { currentUser: Record<string, any> }) {
    try {
      const data = await dbConfig.getAllItemFromDatabase(
        envVars.dynamoDB.USERS_TABLE_NAME,
      );

      const filterUsersWhoBlockCurrentProfile = data.Items.filter(
        (user) =>
          !(user?.blockedUsers && user?.blockedUsers?.includes(currentUser.id)),
      );

      return response({
        message: 'Success',
        data: { Items: filterUsersWhoBlockCurrentProfile },
        status: 1,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }
  async listAllBlockedUsers() {
    try {
      const data = await dbConfig.getAllItemFromDatabase(
        envVars.dynamoDB.BLOCK_USERS_TABLE_NAME,
      );

      return response({
        message: 'Success',
        data: data.Items,
        status: 1,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async findAllByProjection({
    projectionExpression,
    currentUser,
  }: {
    projectionExpression: string;
    currentUser: Record<string, any>;
  }) {
    const projectionArray = projectionExpression
      ? `blockedUsers,${projectionExpression}`.replace(' ', '').split(',')
      : 'id,firstName,lastName,teamName,clubName,blockedUsers'
          .replace(' ', '')
          .split(',');

    const expressionAttributeNames = projectionArray?.reduce((acc, curr) => {
      acc[`#${curr}`] = curr;
      return acc;
    }, {});

    try {
      const data = await dbConfig.getAllItemFromDatabase(
        envVars.dynamoDB.USERS_TABLE_NAME,
        {
          ProjectionExpression: Object.keys(expressionAttributeNames).join(','),
          ExpressionAttributeNames: expressionAttributeNames,
          TableName: envVars.dynamoDB.USERS_TABLE_NAME,
        },
      );

      const filterUsersWhoBlockCurrentProfile = data.Items.filter(
        (user) =>
          !(user?.blockedUsers && user?.blockedUsers?.includes(currentUser.id)),
      );

      return response({
        message: 'Success',
        data: { Items: filterUsersWhoBlockCurrentProfile },
        status: 1,
      });
    } catch (error) {
      console.log('error', error);
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({
          message: `${error.message}. Also make sure you formed your projectionExpression very well. see example: \n 'id, firstName, lastName, teamName, clubName'`,
          status: 0,
          data: null,
        }),
      );
    }
  }

  async verifyPlayer(userId: string, verifiedById: string) {
    return await this.updateUser(
      {
        id: userId,
        verified: true,
        verifiedById: verifiedById,
      },
      false,
    );
  }

  async searchUser({
    query,
    currentUser,
  }: {
    query: string;
    currentUser: Record<string, any>;
  }) {
    if (query?.length === 0) {
      return response({ message: 'Success', data: [], status: 1 });
    }
    const splitedQuery = query.split(' ')?.[1] || '';
    let filterExpression =
      'contains(#fn, :query) OR contains(#ln, :query) OR contains(#fn, :query2) OR contains(#ln, :query2) OR contains(#email, :query2)  OR contains(#fn, :query3) OR contains(#ln, :query3)';

    const expressionAttributeValues = {
      ':query':
        String(query).charAt(0).toUpperCase() +
        String(query).slice(1).toLowerCase(),
      ':query2': String(query).toLowerCase(),
    };

    if (splitedQuery) {
      expressionAttributeValues[':query3'] =
        String(splitedQuery).charAt(0).toUpperCase() +
        String(splitedQuery).slice(1).toLowerCase();
    } else {
      filterExpression =
        'contains(#fn, :query) OR contains(#ln, :query) OR contains(#fn, :query2) OR contains(#ln, :query2) OR contains(#email, :query2)';
    }

    try {
      const data = await dbConfig.getAllItemFromDatabase(
        envVars.dynamoDB.USERS_TABLE_NAME,
        {
          TableName: envVars.dynamoDB.USERS_TABLE_NAME,
          FilterExpression: filterExpression,
          // FilterExpression:
          //   'contains(#fn, :query) OR contains(#ln, :query) OR contains(#tNm, :query) OR contains(#clNm, :query) OR contains(#fn, :query2) OR contains(#ln, :query2) OR contains(#tNm, :query2) OR contains(#clNm, :query2) OR contains(#email, :query2)',
          ExpressionAttributeNames: {
            '#fn': 'firstName',
            '#ln': 'lastName',
            '#email': 'email',
          },
          ExpressionAttributeValues: expressionAttributeValues,
        },
      );

      const filterUsersWhoBlockCurrentProfile = data.Items.filter(
        (user) =>
          !(user?.blockedUsers && user?.blockedUsers?.includes(currentUser.id)),
      );

      return response({
        message: 'Success',
        data: filterUsersWhoBlockCurrentProfile,
        status: 1,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async getUserPhysical(userId: string) {
    try {
      const data = await dbConfig.getItemFromDatabase(
        envVars.dynamoDB.USERS_PHYSICAL_DATA_TABLE_NAME,
        { id: userId },
      );

      return response({
        message: 'Success',
        data: data.Item,
        status: 1,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async createUserPhysical(createUserPhysicalData: CreateUserPhysicalData) {
    try {
      const data = await dbConfig.writeItemsToDatabase(
        envVars.dynamoDB.USERS_PHYSICAL_DATA_TABLE_NAME,
        {
          ...createUserPhysicalData,
          id: createUserPhysicalData.userId,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      );

      return response({
        message: 'Success',
        data,
        status: 1,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async updateUserPhysical(
    createUserPhysicalData: Partial<CreateUserPhysicalData>,
  ) {
    const updateExpression = Object.keys(createUserPhysicalData)
      .map((key) => `#${key} = :${key}`)
      .join(', ');

    const expressionAttributeValues = Object.keys(
      createUserPhysicalData,
    ).reduce((acc, key) => {
      acc[`:${key}`] = createUserPhysicalData[key];
      return acc;
    }, {});

    const expressionAttributeNames = Object.keys(createUserPhysicalData).reduce(
      (acc, key) => {
        acc[`#${key}`] = key;
        return acc;
      },
      {},
    );

    const updateParams = {
      UpdateExpression: `set ${updateExpression}`,
      ExpressionAttributeValues: expressionAttributeValues,
      ExpressionAttributeNames: expressionAttributeNames,
    };

    try {
      const data = await dbConfig.updateItemsToDatabase(
        envVars.dynamoDB.USERS_PHYSICAL_DATA_TABLE_NAME,
        { id: createUserPhysicalData.userId },
        updateParams,
      );

      return response({
        message: 'Success',
        data,
        status: 1,
      });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async removeUser(userId: string) {
    try {
      const data = await dbConfig.removeItemFromDatabase(
        envVars.dynamoDB.USERS_TABLE_NAME,
        {
          id: userId,
        },
      );

      return response({ message: 'Deleted successfully', status: 1, data });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async getFollowers(userId: string) {
    const { Item: user } = await dbConfig.getItemFromDatabase(
      envVars.dynamoDB.USERS_TABLE_NAME,
      {
        id: userId,
      },
    );

    if (!user) {
      throw new NotFoundException(
        response({
          message: 'User not found',
          status: 0,
          data: null,
        }),
      );
    }

    const followers = await this.followService.getFollowers(user);

    if (!followers.length) {
      return {
        count: 0,
        followers: [],
      };
    }

    const { expressionAttributeValues, filterExpression } =
      formatFilterExpressionForArray(
        followers.map((follow) => follow.liveLikeProfileId),
      );
    const { Items: existingUsers } = await dbConfig.getAllItemFromDatabase(
      envVars.dynamoDB.USERS_TABLE_NAME,
      {
        TableName: envVars.dynamoDB.USERS_TABLE_NAME,
        FilterExpression: 'liveLikeProfileId IN (' + filterExpression + ')',
        ExpressionAttributeValues: expressionAttributeValues,
      },
    );
    const filteredFollowers = followers.filter((follow) =>
      existingUsers.find(
        (user) => user.liveLikeProfileId === follow.liveLikeProfileId,
      ),
    );

    return {
      count: filteredFollowers.length,
      followers: filteredFollowers,
    };
  }

  async getFollowing(userId: string) {
    const { Item: user } = await dbConfig.getItemFromDatabase(
      envVars.dynamoDB.USERS_TABLE_NAME,
      {
        id: userId,
      },
    );

    if (!user) {
      throw new NotFoundException(
        response({
          message: 'User not found',
          status: 0,
          data: null,
        }),
      );
    }

    const following = await this.followService.getFollowing(user);

    if (!following.length) {
      return {
        count: 0,
        following: [],
      };
    }

    const { expressionAttributeValues, filterExpression } =
      formatFilterExpressionForArray(
        following.map((follow) => follow.liveLikeProfileId),
      );
    const { Items: existingUsers } = await dbConfig.getAllItemFromDatabase(
      envVars.dynamoDB.USERS_TABLE_NAME,
      {
        TableName: envVars.dynamoDB.USERS_TABLE_NAME,
        FilterExpression: 'liveLikeProfileId IN (' + filterExpression + ')',
        ExpressionAttributeValues: expressionAttributeValues,
      },
    );
    const filteredFollowing = following.filter((follow) =>
      existingUsers.find(
        (user) => user.liveLikeProfileId === follow.liveLikeProfileId,
      ),
    );

    return {
      count: filteredFollowing.length,
      following: filteredFollowing,
    };
  }
}
