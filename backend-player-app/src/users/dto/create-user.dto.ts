import {
  <PERSON><PERSON><PERSON><PERSON>,
  IsBoolean,
  IsEmail,
  <PERSON><PERSON><PERSON>,
  <PERSON>NotEmpty,
  IsObject,
  IsOptional,
  IsPhoneN<PERSON>ber,
  IsString,
} from 'class-validator';

export enum UserType {
  ADMIN = 'ADMIN',
  PLAYER = 'PLAYER',
  MANAGER = 'MANAGER',
  NON_PLAYER = 'NON_PLAYER',
}

export enum ADMIN_GROUP {
  PlayerAppAdmins = 'PlayerAppAdmins',
}

export enum PlayerPostion {
  GOAL_KEEPERS = 'GOAL_KEEPERS',
  CENTRE_BACK = 'CENTRE_BACK',
  CENTRAL_MIDFIELD = 'CENTRAL_MIDFIELD',
  RIGHT_BACK = 'RIGHT_BACK',
  LEFT_BACK = 'LEFT_BACK',
  WING_BACK = 'WING_BACK',
  DEFENSIVE_MIDFIELD = 'DEFENSIVE_MIDFIELD',
  ATTACKING_MIDFIELD = 'ATTACKING_MIDFIELD',
  WINGER = 'WINGER',
  FORWARD = 'FORWARD',
  STRIKER = 'STRIKER',
}

export enum PreferredFoot {
  RIGHT_FOOTED = 'RIGHT_FOOTED',
  LEFT_FOOTED = 'LEFT_FOOTED',
  BOTH_FOOTED = 'BOTH_FOOTED',
}

export class CreateUserDto {
  @IsOptional()
  id: string;

  @IsOptional()
  userId: string;

  @IsString()
  @IsOptional()
  bio: string;

  @IsString()
  @IsNotEmpty()
  firstName: string;

  @IsString()
  @IsNotEmpty()
  lastName: string;

  @IsOptional()
  @IsPhoneNumber()
  phoneNumber: string;

  @IsString()
  @IsOptional()
  position: PlayerPostion;

  @IsString()
  @IsOptional()
  birthday: string;

  @IsString()
  @IsNotEmpty()
  location: string;

  @IsBoolean()
  @IsOptional()
  isManager: boolean;

  @IsString()
  @IsNotEmpty()
  gender: string;

  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsString()
  @IsOptional()
  cludId: string;

  @IsString()
  @IsNotEmpty()
  @IsEnum(UserType, {
    message: 'userType must be one of (ADMIN | PLAYER | MANAGER)',
  })
  userType: UserType;

  @IsString()
  @IsOptional()
  teamId: string;

  @IsObject()
  @IsOptional()
  addtionalProperties: any;

  @IsString()
  @IsOptional()
  photoUrl: string;

  @IsString()
  @IsOptional()
  teamName: string;

  @IsString()
  @IsOptional()
  clubName: string;

  @IsString()
  @IsOptional()
  wingNumber: string;

  @IsString()
  @IsOptional()
  preferredFoot: PreferredFoot;

  @IsString()
  @IsOptional()
  height: string;

  @IsOptional()
  experiences: any;

  @IsString()
  @IsOptional()
  liveLikeProfileId?: string;

  @IsBoolean()
  @IsOptional()
  allowPrivateClubsTeams?: boolean;

  @IsString()
  @IsOptional()
  liveLikeProfileToken?: string;

  @IsString()
  @IsOptional()
  banner?: string;

  @IsBoolean()
  @IsOptional()
  verified?: boolean;

  @IsString()
  @IsOptional()
  verifiedById?: string;

  @IsString()
  @IsOptional()
  representedBy?: string;

  @IsString()
  @IsOptional()
  contractExpiry?: string;

  @IsArray()
  @IsOptional()
  followingsIDs?: Array<any>;

  @IsArray()
  @IsOptional()
  nonPlayerRole?: Array<any>;

  @IsObject()
  @IsOptional()
  crown?: { crownType: string; crownColor?: string; crownText?: string };
}
