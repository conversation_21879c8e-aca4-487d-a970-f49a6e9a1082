import { IsOptional, IsString } from 'class-validator';

export class CreateUserPhysicalData {
  @IsOptional()
  id: string;

  @IsString()
  userId: string;

  @IsString()
  @IsOptional()
  sprintTime5M: string;

  @IsString()
  @IsOptional()
  sprintTime10M: string;

  @IsString()
  @IsOptional()
  sprintTime20M: string;

  @IsString()
  @IsOptional()
  runTime1Kmins: string;

  @IsString()
  @IsOptional()
  runTime1Ksecs: string;

  @IsString()
  @IsOptional()
  matchAvgDistance: string;

  @IsString()
  @IsOptional()
  counterMovementJump: string;

  // @IsString()
  // @IsOptional()
  // sprintTime30M: string;

  // @IsString()
  // @IsOptional()
  // runTime2K: string;

  // @IsString()
  // @IsOptional()
  // runTime5K: string;

  // @IsString()
  // @IsOptional()
  // runTime10K: string;

  // @IsString()
  // @IsOptional()
  // cod505Left: string;

  // @IsString()
  // @IsOptional()
  // cod505Right: string;

  // @IsString()
  // @IsOptional()
  // mas: string;

  // @IsString()
  // @IsOptional()
  // max: string;
}
