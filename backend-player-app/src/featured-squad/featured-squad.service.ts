import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { CreateFeaturedSquadDto } from './dto/create-featured-squad.dto';
import { envVars } from 'src/config/constants';
import { dbConfig } from 'src/config';
import { randomUUID } from 'crypto';
import { response } from 'src/utils/response';
import { pickObjectProperties } from 'src/utils/helpers';

@Injectable()
export class FeaturedSquadService {
  dbName = envVars.dynamoDB.FEATURED_SQUAD_TABLE_NAME;

  async create(createFeaturedSquadDto: CreateFeaturedSquadDto) {
    try {
      const squads = await Promise.all(
        createFeaturedSquadDto.squads.map(async (squad) => {
          const members = await Promise.all(
            squad.members.map(async (member) => {
              const user = await dbConfig.getItemFromDatabase(
                envVars.dynamoDB.USERS_TABLE_NAME,
                { id: member.userId },
              );
              const memberData = user?.Item?.id
                ? pickObjectProperties(user?.Item, [
                    'id',
                    'teamName',
                    'userType',
                    'lastName',
                    'clubName',
                    'cludId',
                    'teamId',
                    'firstName',
                    'photoUrl',
                    'location',
                    'gender',
                    'position',
                    'birthday',
                  ])
                : null;

              return {
                ...memberData,
                ...member,
              };
            }),
          );

          return { ...squad, members };
        }),
      );

      const data = await dbConfig.writeItemsToDatabase(this.dbName, {
        ...createFeaturedSquadDto,
        squads,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        id: randomUUID(),
      });

      return response({ message: 'Success', data, status: 1 });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async findAll() {
    try {
      const data = await dbConfig.getAllItemFromDatabase(this.dbName);

      return response({ message: 'Success', data: data.Items, status: 1 });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  async findOne(id: string) {
    try {
      const data = await dbConfig.getItemFromDatabase(this.dbName, { id });

      return response({ message: 'Success', data, status: 1 });
    } catch (error) {
      Logger.error(error.message);
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }

  // async updateOrRemoveMember(
  //   id: string,
  //   squadName: SquadNames,
  //   memberId: string,
  //   updatedMemberData?: Partial<SquadUserDto>,
  // ) {
  //   const featuredSquad = await dbConfig.getItemFromDatabase(this.dbName, {
  //     id,
  //   });

  //   if (!featuredSquad?.Item)
  //     throw new NotFoundException('Featured Squad not found');

  //   const squads = featuredSquad.Item.squads.map((squad) => {
  //     if (squad.name !== squadName) return squad;

  //     return {
  //       ...squad,
  //       members: squad.members
  //         .map((member) => {
  //           if (member.memberId !== memberId) return member;
  //           return updatedMemberData
  //             ? { ...member, ...updatedMemberData }
  //             : null; // for deletion
  //         })
  //         .filter(Boolean), // remove nulls if deleting
  //     };
  //   });

  //   await dbConfig.writeItemsToDatabase(this.dbName, {
  //     ...item.Item,
  //     squads,
  //     updatedAt: new Date().toISOString(),
  //   });

  //   return response({ message: 'Updated successfully', status: 1 });
  // }

  async remove(id: string) {
    try {
      const data = await dbConfig.removeItemFromDatabase(this.dbName, {
        id,
      });

      return response({ message: 'Deleted successfully', status: 1, data });
    } catch (error) {
      throw new InternalServerErrorException(
        response({ message: error.message, status: 0, data: null }),
      );
    }
  }
}
