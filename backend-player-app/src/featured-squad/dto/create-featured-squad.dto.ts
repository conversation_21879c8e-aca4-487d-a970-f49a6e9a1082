import { Type } from 'class-transformer';
import {
  IsDate,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
  IsArray,
  IsNumber,
} from 'class-validator';
import { UniqueFields } from './utils';

export enum SquadNames {
  GOALKEEPERS = 'GOALKEEPERS',
  DEFENDERS = 'DEFENDERS',
  MIDFIELDERS = 'MIDFIELDERS',
  ATTACKERS = 'ATTACKERS',
}

enum Status {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}

export class SquadUserDto {
  @IsString()
  teamLogo: string;

  @IsString()
  userId: string;
}

export class SquadDto {
  @IsEnum(SquadNames)
  name: SquadNames;

  @IsNotEmpty()
  @IsNumber()
  index: number;

  @IsArray()
  @ValidateNested()
  @Type(() => SquadUserDto)
  @UniqueFields('userId', {
    message: 'Each member in the squad must be unique by userId',
  })
  members: SquadUserDto[];
}

export class CreateFeaturedSquadDto {
  @IsOptional()
  @IsString()
  id?: string;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  updatedAt?: Date;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  createdAt?: Date;

  @IsString()
  internalTitle: string;

  @IsOptional()
  @IsString()
  heading?: string;

  @IsString()
  body: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SquadDto)
  @UniqueFields('name', {
    message: 'Each squad must be unique by name',
  })
  squads: SquadDto[];

  @Type(() => Date)
  @IsDate()
  startDate: Date;

  @Type(() => Date)
  @IsDate()
  endDate: Date;

  @IsEnum(Status, { message: `Status must be one of ACTIVE or INACTIVE` })
  status: Status;
}
