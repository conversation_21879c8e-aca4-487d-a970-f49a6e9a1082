import {
  registerDecorator,
  ValidationArguments,
  ValidationOptions,
} from 'class-validator';

export function UniqueSquadNames(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'UniqueSquadNames',
      target: object.constructor,
      propertyName,
      options: validationOptions,
      validator: {
        validate(squads: any[], args: ValidationArguments) {
          if (!Array.isArray(squads)) return false;

          const seen = new Set();
          for (const squad of squads) {
            if (!squad?.name) continue;
            if (seen.has(squad.name)) return false;
            seen.add(squad.name);
          }

          return true;
        },
        defaultMessage(args: ValidationArguments) {
          return `Squad names must be unique (no duplicates allowed).`;
        },
      },
    });
  };
}

export function UniqueFields(
  property: string,
  validationOptions?: ValidationOptions,
) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'UniqueMemberIds',
      target: object.constructor,
      propertyName,
      options: validationOptions,
      constraints: [property],
      validator: {
        validate(members: any[], args: ValidationArguments) {
          if (!Array.isArray(members)) return false;

          const seen = new Set();
          for (const member of members) {
            const id = member[args.constraints[0]];
            if (!id) continue;
            if (seen.has(id)) return false;
            seen.add(id);
          }

          return true;
        },
        defaultMessage(args: ValidationArguments) {
          return `All items must have unique ${args.constraints[0]}`;
        },
      },
    });
  };
}
