import { Test, TestingModule } from '@nestjs/testing';
import { FeaturedSquadController } from './featured-squad.controller';
import { FeaturedSquadService } from './featured-squad.service';

describe('FeaturedSquadController', () => {
  let controller: FeaturedSquadController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [FeaturedSquadController],
      providers: [FeaturedSquadService],
    }).compile();

    controller = module.get<FeaturedSquadController>(FeaturedSquadController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
