import { Controller, Get, Post, Body, Param, Delete } from '@nestjs/common';
import { FeaturedSquadService } from './featured-squad.service';
import { CreateFeaturedSquadDto } from './dto/create-featured-squad.dto';
// import { UpdateFeaturedSquadDto } from './dto/update-featured-squad.dto';

@Controller('featured-squad')
export class FeaturedSquadController {
  constructor(private readonly featuredSquadService: FeaturedSquadService) {}

  @Post('create')
  create(@Body() createFeaturedSquadDto: CreateFeaturedSquadDto) {
    return this.featuredSquadService.create(createFeaturedSquadDto);
  }

  @Get()
  findAll() {
    return this.featuredSquadService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.featuredSquadService.findOne(id);
  }

  // @Patch(':id')
  // update(@Param('id') id: string, @Body() updateFeaturedSquadDto: UpdateFeaturedSquadDto) {
  //   return this.featuredSquadService.update(+id, updateFeaturedSquadDto);
  // }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.featuredSquadService.remove(id);
  }
}
