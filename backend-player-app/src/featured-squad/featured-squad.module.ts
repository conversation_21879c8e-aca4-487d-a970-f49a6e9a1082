import {
  MiddlewareConsumer,
  Module,
  NestModule,
  RequestMethod,
} from '@nestjs/common';
import { FeaturedSquadService } from './featured-squad.service';
import { FeaturedSquadController } from './featured-squad.controller';
import {
  AdminAuthMiddleware,
  AuthMiddleware,
} from 'src/auth/guards/auth.middleware';
@Module({
  controllers: [FeaturedSquadController],
  providers: [FeaturedSquadService],
})
export class FeaturedSquadModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(AuthMiddleware)
      .forRoutes('featured-squad')
      .apply(AdminAuthMiddleware)
      .forRoutes({ path: 'featured-squad/create', method: RequestMethod.POST });
  }
}
