{"name": "backend-player-app", "version": "0.0.1", "description": "", "author": "", "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build --webpack", "build:watch": "nest build --webpack --watch", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "env-cmd -f .env sls offline start --noTimeout --reload<PERSON><PERSON>ler", "start:dev": "run-p build:watch start", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "sls-deploy:dev": "yarn build && sls deploy --stage dev --aws-profile playerapp", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest --config jest.config.ts ", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@aws-lambda-powertools/logger": "^2.19.0", "@aws-sdk/client-cognito-identity-provider": "^3.271.0", "@aws-sdk/client-dynamodb": "^3.267.0", "@aws-sdk/client-s3": "^3.750.0", "@aws-sdk/client-ses": "^3.297.0", "@aws-sdk/client-sqs": "^3.478.0", "@aws-sdk/lib-dynamodb": "^3.271.0", "@aws-sdk/s3-request-presigner": "^3.750.0", "@livelike/javascript": "^1.3.3", "@nestjs/common": "^9.0.0", "@nestjs/config": "^2.2.0", "@nestjs/core": "^9.0.0", "@nestjs/jwt": "^9.0.0", "@nestjs/mapped-types": "*", "@nestjs/platform-express": "^9.0.0", "@vendia/serverless-express": "^4.10.1", "aws-lambda": "^1.0.7", "aws-serverless-express": "^3.4.0", "class-transformer": "^0.5.1", "class-validator": "^0.13.2", "dayjs": "^1.11.13", "fluent-ffmpeg": "^2.1.2", "helmet": "^6.0.0", "jwk-to-pem": "^2.0.5", "lodash.groupby": "^4.6.0", "lodash.isequal": "^4.5.0", "lodash.merge": "^4.6.2", "lodash.uniqwith": "^4.5.0", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.2.0", "streamifier": "^0.1.1", "uuid": "^9.0.0"}, "devDependencies": {"@nestjs/cli": "^9.0.0", "@nestjs/schematics": "^9.0.0", "@nestjs/testing": "^9.0.0", "@serverless/dashboard-plugin": "6.2.3", "@serverless/platform-client": "4.3.2", "@types/express": "^4.17.13", "@types/fluent-ffmpeg": "^2.1.21", "@types/jest": "28.1.8", "@types/multer": "^1.4.7", "@types/node": "^16.0.0", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "aws-dynamodb-local": "^0.0.11", "env-cmd": "^10.1.0", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest": "28.1.3", "npm-run-all": "^4.1.5", "prettier": "^2.3.2", "serverless": "3.27.0", "serverless-dynamodb": "^0.2.56", "serverless-dynamodb-client": "^0.0.2", "serverless-offline": "13.6.0", "serverless-plugin-optimize": "^4.2.1-rc.1", "serverless-plugin-typescript": "^2.1.3", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "28.0.8", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "4.1.0", "typescript": "^4.7.4"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}