name: Deploy

on:
  push:
    branches:
      - development
    paths:
      - 'src/**'
      - 'package.json'
      - 'yarn.lock'
      - 'serverless.yml'
      - '.github/workflows/deploy.yaml'

  workflow_dispatch:

env:
  environment: ${{ github.ref_name == 'main' && 'production' || 'development' }}

jobs:
  deploy:
    if: github.ref_name == 'main' || github.ref_name == 'development'

    runs-on: ubuntu-latest

    environment: ${{ github.ref_name == 'main' && 'production' || 'development' }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'yarn'

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Install Serverless CLI
        run: yarn global add serverless

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Deploy (${{ env.environment }})
        run: yarn build && sls deploy --stage ${{ env.environment == 'production' && 'prod' || 'dev' }}
        env:
          AWS_REGION: ${{ secrets.AWS_REGION }}
          IMAGE_BUCKET: ${{ secrets.IMAGE_BUCKET }}
          VIDEO_BUCKET: ${{ secrets.VIDEO_BUCKET }}
          LIVELIKE_CLIENT_ID: ${{ secrets.LIVELIKE_CLIENT_ID }}
          LIVELIKE_TOKEN: ${{ secrets.LIVELIKE_TOKEN }}
          STREAMING_BUCKET: ${{ secrets.STREAMING_BUCKET }}
          SQS_URL: ${{ secrets.SQS_URL }}
          USER_POOL_ID: ${{ secrets.USER_POOL_ID }}
          AWS_COGNITO_CLIENT_ID: ${{ secrets.AWS_COGNITO_CLIENT_ID }}
          AWS_BACKEND_LAMBDA_SERVICE_NAME: ${{ secrets.AWS_BACKEND_LAMBDA_SERVICE_NAME }}
          IS_LOCAL: 'FALSE'
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
          SERVERLESS_ACCESS_KEY: ${{ secrets.SERVERLESS_ACCESS_KEY }}
          NODE_OPTIONS: --max_old_space_size=4096
